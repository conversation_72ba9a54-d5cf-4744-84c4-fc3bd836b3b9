<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=3184698" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ee;</span>
                <div class="name">拖拽</div>
                <div class="code-name">&amp;#xe6ee;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ef;</span>
                <div class="name">编组备份</div>
                <div class="code-name">&amp;#xe6ef;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f0;</span>
                <div class="name">Batch folding</div>
                <div class="code-name">&amp;#xe6f0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ea;</span>
                <div class="name">多行文本备份 2</div>
                <div class="code-name">&amp;#xe6ea;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6eb;</span>
                <div class="name">单行文本备份 2</div>
                <div class="code-name">&amp;#xe6eb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ec;</span>
                <div class="name">单选备份</div>
                <div class="code-name">&amp;#xe6ec;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ed;</span>
                <div class="name">多选备份</div>
                <div class="code-name">&amp;#xe6ed;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70d;</span>
                <div class="name">加班</div>
                <div class="code-name">&amp;#xe70d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70f;</span>
                <div class="name">财务</div>
                <div class="code-name">&amp;#xe70f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe710;</span>
                <div class="name">笔记本</div>
                <div class="code-name">&amp;#xe710;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe711;</span>
                <div class="name">财务 2</div>
                <div class="code-name">&amp;#xe711;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe712;</span>
                <div class="name">飞机</div>
                <div class="code-name">&amp;#xe712;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe713;</span>
                <div class="name">培训与交流</div>
                <div class="code-name">&amp;#xe713;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe714;</span>
                <div class="name">汽车</div>
                <div class="code-name">&amp;#xe714;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe715;</span>
                <div class="name">补</div>
                <div class="code-name">&amp;#xe715;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe716;</span>
                <div class="name">加班</div>
                <div class="code-name">&amp;#xe716;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe717;</span>
                <div class="name">审批</div>
                <div class="code-name">&amp;#xe717;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe718;</span>
                <div class="name">公文包</div>
                <div class="code-name">&amp;#xe718;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe719;</span>
                <div class="name">审批管理</div>
                <div class="code-name">&amp;#xe719;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71a;</span>
                <div class="name">请假</div>
                <div class="code-name">&amp;#xe71a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71b;</span>
                <div class="name">消息</div>
                <div class="code-name">&amp;#xe71b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71c;</span>
                <div class="name">日历</div>
                <div class="code-name">&amp;#xe71c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70a;</span>
                <div class="name">排序</div>
                <div class="code-name">&amp;#xe70a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70b;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe70b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71d;</span>
                <div class="name">删除-copy</div>
                <div class="code-name">&amp;#xe71d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f4;</span>
                <div class="name">全屏</div>
                <div class="code-name">&amp;#xe6f4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe670;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe670;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">工作信息</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e5;</span>
                <div class="name">链接</div>
                <div class="code-name">&amp;#xe6e5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe668;</span>
                <div class="name">未选中备份 13</div>
                <div class="code-name">&amp;#xe668;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e3;</span>
                <div class="name">未选中备份 19</div>
                <div class="code-name">&amp;#xe6e3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66a;</span>
                <div class="name">未选中备份 15</div>
                <div class="code-name">&amp;#xe66a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e1;</span>
                <div class="name">增</div>
                <div class="code-name">&amp;#xe6e1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e2;</span>
                <div class="name">减</div>
                <div class="code-name">&amp;#xe6e2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e0;</span>
                <div class="name">首页-删除</div>
                <div class="code-name">&amp;#xe6e0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6df;</span>
                <div class="name">移动</div>
                <div class="code-name">&amp;#xe6df;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6de;</span>
                <div class="name">日程完成</div>
                <div class="code-name">&amp;#xe6de;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe666;</span>
                <div class="name">Shape</div>
                <div class="code-name">&amp;#xe666;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe665;</span>
                <div class="name">编组 9</div>
                <div class="code-name">&amp;#xe665;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6dd;</span>
                <div class="name">切换</div>
                <div class="code-name">&amp;#xe6dd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6dc;</span>
                <div class="name">排序</div>
                <div class="code-name">&amp;#xe6dc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d2;</span>
                <div class="name">复制并创建</div>
                <div class="code-name">&amp;#xe6d2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d8;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe6d8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6db;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe6db;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6bf;</span>
                <div class="name">标签</div>
                <div class="code-name">&amp;#xe6bf;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6da;</span>
                <div class="name">描述</div>
                <div class="code-name">&amp;#xe6da;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6cb;</span>
                <div class="name">Group 104备份</div>
                <div class="code-name">&amp;#xe6cb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6cc;</span>
                <div class="name">时间</div>
                <div class="code-name">&amp;#xe6cc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6cd;</span>
                <div class="name">参与人</div>
                <div class="code-name">&amp;#xe6cd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ce;</span>
                <div class="name">地址</div>
                <div class="code-name">&amp;#xe6ce;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6cf;</span>
                <div class="name">提醒</div>
                <div class="code-name">&amp;#xe6cf;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d0;</span>
                <div class="name">日历</div>
                <div class="code-name">&amp;#xe6d0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d1;</span>
                <div class="name">展开</div>
                <div class="code-name">&amp;#xe6d1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d3;</span>
                <div class="name">未完成</div>
                <div class="code-name">&amp;#xe6d3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d4;</span>
                <div class="name">我的</div>
                <div class="code-name">&amp;#xe6d4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d5;</span>
                <div class="name">置顶</div>
                <div class="code-name">&amp;#xe6d5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d6;</span>
                <div class="name">新建日程</div>
                <div class="code-name">&amp;#xe6d6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d7;</span>
                <div class="name">展开2</div>
                <div class="code-name">&amp;#xe6d7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d9;</span>
                <div class="name">设置 2</div>
                <div class="code-name">&amp;#xe6d9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c9;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe6c9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c7;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe6c7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c5;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe6c5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe664;</span>
                <div class="name">形状</div>
                <div class="code-name">&amp;#xe664;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe662;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe662;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe663;</span>
                <div class="name">形状结合1</div>
                <div class="code-name">&amp;#xe663;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe661;</span>
                <div class="name">outline备份</div>
                <div class="code-name">&amp;#xe661;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65f;</span>
                <div class="name">路径 18</div>
                <div class="code-name">&amp;#xe65f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe660;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65d;</span>
                <div class="name">Shape备份</div>
                <div class="code-name">&amp;#xe65d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65e;</span>
                <div class="name">Shape</div>
                <div class="code-name">&amp;#xe65e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe658;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe658;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe659;</span>
                <div class="name">形状结合1</div>
                <div class="code-name">&amp;#xe659;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65a;</span>
                <div class="name">Shape1</div>
                <div class="code-name">&amp;#xe65a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65b;</span>
                <div class="name">形状结合2</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65c;</span>
                <div class="name">Shape</div>
                <div class="code-name">&amp;#xe65c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe656;</span>
                <div class="name">形状结合1</div>
                <div class="code-name">&amp;#xe656;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe657;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe657;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe653;</span>
                <div class="name">Shape Copy 2</div>
                <div class="code-name">&amp;#xe653;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe654;</span>
                <div class="name">Shape</div>
                <div class="code-name">&amp;#xe654;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe655;</span>
                <div class="name">Shape1</div>
                <div class="code-name">&amp;#xe655;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe650;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe650;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe651;</span>
                <div class="name">形状结合1</div>
                <div class="code-name">&amp;#xe651;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe652;</span>
                <div class="name">Fill 2</div>
                <div class="code-name">&amp;#xe652;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64f;</span>
                <div class="name">椭圆形</div>
                <div class="code-name">&amp;#xe64f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64e;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe64e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64d;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe647;</span>
                <div class="name">Shape备份 3</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe646;</span>
                <div class="name">Shape</div>
                <div class="code-name">&amp;#xe646;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe645;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe645;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe644;</span>
                <div class="name">Combined Shape</div>
                <div class="code-name">&amp;#xe644;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe643;</span>
                <div class="name">Combined Shape</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe642;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe641;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe641;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63e;</span>
                <div class="name">Shape</div>
                <div class="code-name">&amp;#xe63e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63d;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe63d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63c;</span>
                <div class="name">Shape备份</div>
                <div class="code-name">&amp;#xe63c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63b;</span>
                <div class="name">Shape备份</div>
                <div class="code-name">&amp;#xe63b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe639;</span>
                <div class="name">Shape备份</div>
                <div class="code-name">&amp;#xe639;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe638;</span>
                <div class="name">三角形备份 4</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe637;</span>
                <div class="name">Rectangle 271备份 2</div>
                <div class="code-name">&amp;#xe637;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe635;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe635;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe634;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe634;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe633;</span>
                <div class="name">蒙版</div>
                <div class="code-name">&amp;#xe633;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe631;</span>
                <div class="name">蒙版</div>
                <div class="code-name">&amp;#xe631;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62f;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe62f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62e;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe62e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62c;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62b;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe62b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62a;</span>
                <div class="name">Shape</div>
                <div class="code-name">&amp;#xe62a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe629;</span>
                <div class="name">Fill 2</div>
                <div class="code-name">&amp;#xe629;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe628;</span>
                <div class="name">Fill 2</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe627;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe627;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe626;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe626;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe625;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe625;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe624;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe624;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe623;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe623;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe622;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe622;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe621;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe621;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61e;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe61e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61f;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe61f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot?t=1687845556837'); /* IE9 */
  src: url('iconfont.eot?t=1687845556837#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2'),
       url('iconfont.woff?t=1687845556837') format('woff'),
       url('iconfont.ttf?t=1687845556837') format('truetype'),
       url('iconfont.svg?t=1687845556837#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-tuozhuai"></span>
            <div class="name">
              拖拽
            </div>
            <div class="code-name">.icon-tuozhuai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianzubeifen"></span>
            <div class="name">
              编组备份
            </div>
            <div class="code-name">.icon-bianzubeifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Batchfolding"></span>
            <div class="name">
              Batch folding
            </div>
            <div class="code-name">.icon-a-Batchfolding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-duohangwenbenbeifen2"></span>
            <div class="name">
              多行文本备份 2
            </div>
            <div class="code-name">.icon-a-duohangwenbenbeifen2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-danhangwenbenbeifen2"></span>
            <div class="name">
              单行文本备份 2
            </div>
            <div class="code-name">.icon-a-danhangwenbenbeifen2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-danxuanbeifen"></span>
            <div class="name">
              单选备份
            </div>
            <div class="code-name">.icon-danxuanbeifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-duoxuanbeifen"></span>
            <div class="name">
              多选备份
            </div>
            <div class="code-name">.icon-duoxuanbeifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiaban1"></span>
            <div class="name">
              加班
            </div>
            <div class="code-name">.icon-jiaban1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caiwu"></span>
            <div class="name">
              财务
            </div>
            <div class="code-name">.icon-caiwu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bijiben"></span>
            <div class="name">
              笔记本
            </div>
            <div class="code-name">.icon-bijiben
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-caiwu2"></span>
            <div class="name">
              财务 2
            </div>
            <div class="code-name">.icon-a-caiwu2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-feiji"></span>
            <div class="name">
              飞机
            </div>
            <div class="code-name">.icon-feiji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-peixunyujiaoliu"></span>
            <div class="name">
              培训与交流
            </div>
            <div class="code-name">.icon-peixunyujiaoliu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qiche"></span>
            <div class="name">
              汽车
            </div>
            <div class="code-name">.icon-qiche
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bu"></span>
            <div class="name">
              补
            </div>
            <div class="code-name">.icon-bu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiaban"></span>
            <div class="name">
              加班
            </div>
            <div class="code-name">.icon-jiaban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shenpi"></span>
            <div class="name">
              审批
            </div>
            <div class="code-name">.icon-shenpi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongwenbao"></span>
            <div class="name">
              公文包
            </div>
            <div class="code-name">.icon-gongwenbao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shenpiguanli"></span>
            <div class="name">
              审批管理
            </div>
            <div class="code-name">.icon-shenpiguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qingjia"></span>
            <div class="name">
              请假
            </div>
            <div class="code-name">.icon-qingjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiaoxi"></span>
            <div class="name">
              消息
            </div>
            <div class="code-name">.icon-xiaoxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rili1"></span>
            <div class="name">
              日历
            </div>
            <div class="code-name">.icon-rili1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-paixu1"></span>
            <div class="name">
              排序
            </div>
            <div class="code-name">.icon-paixu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchu2"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.icon-shanchu2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchu2-copy"></span>
            <div class="name">
              删除-copy
            </div>
            <div class="code-name">.icon-shanchu2-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quanping"></span>
            <div class="name">
              全屏
            </div>
            <div class="code-name">.icon-quanping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianji2"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.icon-bianji2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongzuoxinxi"></span>
            <div class="name">
              工作信息
            </div>
            <div class="code-name">.icon-gongzuoxinxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lianjie"></span>
            <div class="name">
              链接
            </div>
            <div class="code-name">.icon-lianjie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-weixuanzhongbeifen13"></span>
            <div class="name">
              未选中备份 13
            </div>
            <div class="code-name">.icon-a-weixuanzhongbeifen13
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-weixuanzhongbeifen19-copy"></span>
            <div class="name">
              未选中备份 19
            </div>
            <div class="code-name">.icon-a-weixuanzhongbeifen19-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-weixuanzhongbeifen151"></span>
            <div class="name">
              未选中备份 15
            </div>
            <div class="code-name">.icon-a-weixuanzhongbeifen151
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zeng"></span>
            <div class="name">
              增
            </div>
            <div class="code-name">.icon-zeng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jian"></span>
            <div class="name">
              减
            </div>
            <div class="code-name">.icon-jian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouye-shanchu"></span>
            <div class="name">
              首页-删除
            </div>
            <div class="code-name">.icon-shouye-shanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouye-yidong"></span>
            <div class="name">
              移动
            </div>
            <div class="code-name">.icon-shouye-yidong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-richengwancheng"></span>
            <div class="name">
              日程完成
            </div>
            <div class="code-name">.icon-richengwancheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape6"></span>
            <div class="name">
              Shape
            </div>
            <div class="code-name">.icon-Shape6
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-bianzu9"></span>
            <div class="name">
              编组 9
            </div>
            <div class="code-name">.icon-a-bianzu9
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qiehuan"></span>
            <div class="name">
              切换
            </div>
            <div class="code-name">.icon-qiehuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-paixu"></span>
            <div class="name">
              排序
            </div>
            <div class="code-name">.icon-paixu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fuzhibingchuangjian"></span>
            <div class="name">
              复制并创建
            </div>
            <div class="code-name">.icon-fuzhibingchuangjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchu1"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.icon-shanchu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianji1"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.icon-bianji1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-biaoqian1"></span>
            <div class="name">
              标签
            </div>
            <div class="code-name">.icon-biaoqian1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-miaoshu"></span>
            <div class="name">
              描述
            </div>
            <div class="code-name">.icon-miaoshu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Group104beifen"></span>
            <div class="name">
              Group 104备份
            </div>
            <div class="code-name">.icon-a-Group104beifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shijian"></span>
            <div class="name">
              时间
            </div>
            <div class="code-name">.icon-shijian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-canyuren"></span>
            <div class="name">
              参与人
            </div>
            <div class="code-name">.icon-canyuren
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dizhi"></span>
            <div class="name">
              地址
            </div>
            <div class="code-name">.icon-dizhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tixing"></span>
            <div class="name">
              提醒
            </div>
            <div class="code-name">.icon-tixing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rili"></span>
            <div class="name">
              日历
            </div>
            <div class="code-name">.icon-rili
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhankai"></span>
            <div class="name">
              展开
            </div>
            <div class="code-name">.icon-zhankai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weiwancheng"></span>
            <div class="name">
              未完成
            </div>
            <div class="code-name">.icon-weiwancheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wode"></span>
            <div class="name">
              我的
            </div>
            <div class="code-name">.icon-wode
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhiding"></span>
            <div class="name">
              置顶
            </div>
            <div class="code-name">.icon-zhiding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xinjianricheng"></span>
            <div class="name">
              新建日程
            </div>
            <div class="code-name">.icon-xinjianricheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhankai2"></span>
            <div class="name">
              展开2
            </div>
            <div class="code-name">.icon-zhankai2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-shezhi2"></span>
            <div class="name">
              设置 2
            </div>
            <div class="code-name">.icon-a-shezhi2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiazai"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.icon-xiazai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianji"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.icon-bianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchu"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.icon-shanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuang"></span>
            <div class="name">
              形状
            </div>
            <div class="code-name">.icon-xingzhuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe27"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe27
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe113"></span>
            <div class="name">
              形状结合1
            </div>
            <div class="code-name">.icon-xingzhuangjiehe113
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-outlinebeifen"></span>
            <div class="name">
              outline备份
            </div>
            <div class="code-name">.icon-outlinebeifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-lujing18"></span>
            <div class="name">
              路径 18
            </div>
            <div class="code-name">.icon-a-lujing18
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe26"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe26
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shapebeifen3"></span>
            <div class="name">
              Shape备份
            </div>
            <div class="code-name">.icon-Shapebeifen3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape5"></span>
            <div class="name">
              Shape
            </div>
            <div class="code-name">.icon-Shape5
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe24"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe24
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe112"></span>
            <div class="name">
              形状结合1
            </div>
            <div class="code-name">.icon-xingzhuangjiehe112
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape12"></span>
            <div class="name">
              Shape1
            </div>
            <div class="code-name">.icon-Shape12
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe25"></span>
            <div class="name">
              形状结合2
            </div>
            <div class="code-name">.icon-xingzhuangjiehe25
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape4"></span>
            <div class="name">
              Shape
            </div>
            <div class="code-name">.icon-Shape4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe111"></span>
            <div class="name">
              形状结合1
            </div>
            <div class="code-name">.icon-xingzhuangjiehe111
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe23"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe23
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-ShapeCopy2"></span>
            <div class="name">
              Shape Copy 2
            </div>
            <div class="code-name">.icon-a-ShapeCopy2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape3"></span>
            <div class="name">
              Shape
            </div>
            <div class="code-name">.icon-Shape3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape11"></span>
            <div class="name">
              Shape1
            </div>
            <div class="code-name">.icon-Shape11
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe22"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe22
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe110"></span>
            <div class="name">
              形状结合1
            </div>
            <div class="code-name">.icon-xingzhuangjiehe110
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Fill22"></span>
            <div class="name">
              Fill 2
            </div>
            <div class="code-name">.icon-a-Fill22
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuoyuanxing"></span>
            <div class="name">
              椭圆形
            </div>
            <div class="code-name">.icon-tuoyuanxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe21"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe21
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe20"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe20
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Shapebeifen3"></span>
            <div class="name">
              Shape备份 3
            </div>
            <div class="code-name">.icon-a-Shapebeifen3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape2"></span>
            <div class="name">
              Shape
            </div>
            <div class="code-name">.icon-Shape2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe19"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe19
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-CombinedShape1"></span>
            <div class="name">
              Combined Shape
            </div>
            <div class="code-name">.icon-a-CombinedShape1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-CombinedShape"></span>
            <div class="name">
              Combined Shape
            </div>
            <div class="code-name">.icon-a-CombinedShape
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe18"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe18
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe17"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe17
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape1"></span>
            <div class="name">
              Shape
            </div>
            <div class="code-name">.icon-Shape1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe16"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe16
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shapebeifen2"></span>
            <div class="name">
              Shape备份
            </div>
            <div class="code-name">.icon-Shapebeifen2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shapebeifen1"></span>
            <div class="name">
              Shape备份
            </div>
            <div class="code-name">.icon-Shapebeifen1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shapebeifen"></span>
            <div class="name">
              Shape备份
            </div>
            <div class="code-name">.icon-Shapebeifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-sanjiaoxingbeifen4"></span>
            <div class="name">
              三角形备份 4
            </div>
            <div class="code-name">.icon-a-sanjiaoxingbeifen4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Rectangle271beifen2"></span>
            <div class="name">
              Rectangle 271备份 2
            </div>
            <div class="code-name">.icon-a-Rectangle271beifen2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe15"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe15
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe14"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe14
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-mengban1"></span>
            <div class="name">
              蒙版
            </div>
            <div class="code-name">.icon-mengban1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-mengban"></span>
            <div class="name">
              蒙版
            </div>
            <div class="code-name">.icon-mengban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe13"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe13
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe12"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe12
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe11"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe11
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe10"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe10
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape"></span>
            <div class="name">
              Shape
            </div>
            <div class="code-name">.icon-Shape
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Fill21"></span>
            <div class="name">
              Fill 2
            </div>
            <div class="code-name">.icon-a-Fill21
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Fill2"></span>
            <div class="name">
              Fill 2
            </div>
            <div class="code-name">.icon-a-Fill2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe9"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe9
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe8"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe8
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe7"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe7
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe6"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe6
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe5"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe5
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe4"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe3"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe2"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe1"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuozhuai"></use>
                </svg>
                <div class="name">拖拽</div>
                <div class="code-name">#icon-tuozhuai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianzubeifen"></use>
                </svg>
                <div class="name">编组备份</div>
                <div class="code-name">#icon-bianzubeifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Batchfolding"></use>
                </svg>
                <div class="name">Batch folding</div>
                <div class="code-name">#icon-a-Batchfolding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-duohangwenbenbeifen2"></use>
                </svg>
                <div class="name">多行文本备份 2</div>
                <div class="code-name">#icon-a-duohangwenbenbeifen2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-danhangwenbenbeifen2"></use>
                </svg>
                <div class="name">单行文本备份 2</div>
                <div class="code-name">#icon-a-danhangwenbenbeifen2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-danxuanbeifen"></use>
                </svg>
                <div class="name">单选备份</div>
                <div class="code-name">#icon-danxuanbeifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-duoxuanbeifen"></use>
                </svg>
                <div class="name">多选备份</div>
                <div class="code-name">#icon-duoxuanbeifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiaban1"></use>
                </svg>
                <div class="name">加班</div>
                <div class="code-name">#icon-jiaban1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caiwu"></use>
                </svg>
                <div class="name">财务</div>
                <div class="code-name">#icon-caiwu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bijiben"></use>
                </svg>
                <div class="name">笔记本</div>
                <div class="code-name">#icon-bijiben</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-caiwu2"></use>
                </svg>
                <div class="name">财务 2</div>
                <div class="code-name">#icon-a-caiwu2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-feiji"></use>
                </svg>
                <div class="name">飞机</div>
                <div class="code-name">#icon-feiji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-peixunyujiaoliu"></use>
                </svg>
                <div class="name">培训与交流</div>
                <div class="code-name">#icon-peixunyujiaoliu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiche"></use>
                </svg>
                <div class="name">汽车</div>
                <div class="code-name">#icon-qiche</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bu"></use>
                </svg>
                <div class="name">补</div>
                <div class="code-name">#icon-bu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiaban"></use>
                </svg>
                <div class="name">加班</div>
                <div class="code-name">#icon-jiaban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenpi"></use>
                </svg>
                <div class="name">审批</div>
                <div class="code-name">#icon-shenpi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongwenbao"></use>
                </svg>
                <div class="name">公文包</div>
                <div class="code-name">#icon-gongwenbao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenpiguanli"></use>
                </svg>
                <div class="name">审批管理</div>
                <div class="code-name">#icon-shenpiguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qingjia"></use>
                </svg>
                <div class="name">请假</div>
                <div class="code-name">#icon-qingjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiaoxi"></use>
                </svg>
                <div class="name">消息</div>
                <div class="code-name">#icon-xiaoxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rili1"></use>
                </svg>
                <div class="name">日历</div>
                <div class="code-name">#icon-rili1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-paixu1"></use>
                </svg>
                <div class="name">排序</div>
                <div class="code-name">#icon-paixu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu2"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#icon-shanchu2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu2-copy"></use>
                </svg>
                <div class="name">删除-copy</div>
                <div class="code-name">#icon-shanchu2-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanping"></use>
                </svg>
                <div class="name">全屏</div>
                <div class="code-name">#icon-quanping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianji2"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#icon-bianji2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongzuoxinxi"></use>
                </svg>
                <div class="name">工作信息</div>
                <div class="code-name">#icon-gongzuoxinxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lianjie"></use>
                </svg>
                <div class="name">链接</div>
                <div class="code-name">#icon-lianjie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-weixuanzhongbeifen13"></use>
                </svg>
                <div class="name">未选中备份 13</div>
                <div class="code-name">#icon-a-weixuanzhongbeifen13</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-weixuanzhongbeifen19-copy"></use>
                </svg>
                <div class="name">未选中备份 19</div>
                <div class="code-name">#icon-a-weixuanzhongbeifen19-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-weixuanzhongbeifen151"></use>
                </svg>
                <div class="name">未选中备份 15</div>
                <div class="code-name">#icon-a-weixuanzhongbeifen151</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zeng"></use>
                </svg>
                <div class="name">增</div>
                <div class="code-name">#icon-zeng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jian"></use>
                </svg>
                <div class="name">减</div>
                <div class="code-name">#icon-jian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouye-shanchu"></use>
                </svg>
                <div class="name">首页-删除</div>
                <div class="code-name">#icon-shouye-shanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouye-yidong"></use>
                </svg>
                <div class="name">移动</div>
                <div class="code-name">#icon-shouye-yidong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-richengwancheng"></use>
                </svg>
                <div class="name">日程完成</div>
                <div class="code-name">#icon-richengwancheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape6"></use>
                </svg>
                <div class="name">Shape</div>
                <div class="code-name">#icon-Shape6</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-bianzu9"></use>
                </svg>
                <div class="name">编组 9</div>
                <div class="code-name">#icon-a-bianzu9</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiehuan"></use>
                </svg>
                <div class="name">切换</div>
                <div class="code-name">#icon-qiehuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-paixu"></use>
                </svg>
                <div class="name">排序</div>
                <div class="code-name">#icon-paixu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fuzhibingchuangjian"></use>
                </svg>
                <div class="name">复制并创建</div>
                <div class="code-name">#icon-fuzhibingchuangjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu1"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#icon-shanchu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianji1"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#icon-bianji1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-biaoqian1"></use>
                </svg>
                <div class="name">标签</div>
                <div class="code-name">#icon-biaoqian1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-miaoshu"></use>
                </svg>
                <div class="name">描述</div>
                <div class="code-name">#icon-miaoshu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Group104beifen"></use>
                </svg>
                <div class="name">Group 104备份</div>
                <div class="code-name">#icon-a-Group104beifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shijian"></use>
                </svg>
                <div class="name">时间</div>
                <div class="code-name">#icon-shijian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-canyuren"></use>
                </svg>
                <div class="name">参与人</div>
                <div class="code-name">#icon-canyuren</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dizhi"></use>
                </svg>
                <div class="name">地址</div>
                <div class="code-name">#icon-dizhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tixing"></use>
                </svg>
                <div class="name">提醒</div>
                <div class="code-name">#icon-tixing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rili"></use>
                </svg>
                <div class="name">日历</div>
                <div class="code-name">#icon-rili</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhankai"></use>
                </svg>
                <div class="name">展开</div>
                <div class="code-name">#icon-zhankai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weiwancheng"></use>
                </svg>
                <div class="name">未完成</div>
                <div class="code-name">#icon-weiwancheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wode"></use>
                </svg>
                <div class="name">我的</div>
                <div class="code-name">#icon-wode</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhiding"></use>
                </svg>
                <div class="name">置顶</div>
                <div class="code-name">#icon-zhiding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinjianricheng"></use>
                </svg>
                <div class="name">新建日程</div>
                <div class="code-name">#icon-xinjianricheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhankai2"></use>
                </svg>
                <div class="name">展开2</div>
                <div class="code-name">#icon-zhankai2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-shezhi2"></use>
                </svg>
                <div class="name">设置 2</div>
                <div class="code-name">#icon-a-shezhi2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiazai"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#icon-xiazai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianji"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#icon-bianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#icon-shanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuang"></use>
                </svg>
                <div class="name">形状</div>
                <div class="code-name">#icon-xingzhuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe27"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe27</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe113"></use>
                </svg>
                <div class="name">形状结合1</div>
                <div class="code-name">#icon-xingzhuangjiehe113</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-outlinebeifen"></use>
                </svg>
                <div class="name">outline备份</div>
                <div class="code-name">#icon-outlinebeifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-lujing18"></use>
                </svg>
                <div class="name">路径 18</div>
                <div class="code-name">#icon-a-lujing18</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe26"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe26</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shapebeifen3"></use>
                </svg>
                <div class="name">Shape备份</div>
                <div class="code-name">#icon-Shapebeifen3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape5"></use>
                </svg>
                <div class="name">Shape</div>
                <div class="code-name">#icon-Shape5</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe24"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe24</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe112"></use>
                </svg>
                <div class="name">形状结合1</div>
                <div class="code-name">#icon-xingzhuangjiehe112</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape12"></use>
                </svg>
                <div class="name">Shape1</div>
                <div class="code-name">#icon-Shape12</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe25"></use>
                </svg>
                <div class="name">形状结合2</div>
                <div class="code-name">#icon-xingzhuangjiehe25</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape4"></use>
                </svg>
                <div class="name">Shape</div>
                <div class="code-name">#icon-Shape4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe111"></use>
                </svg>
                <div class="name">形状结合1</div>
                <div class="code-name">#icon-xingzhuangjiehe111</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe23"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe23</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-ShapeCopy2"></use>
                </svg>
                <div class="name">Shape Copy 2</div>
                <div class="code-name">#icon-a-ShapeCopy2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape3"></use>
                </svg>
                <div class="name">Shape</div>
                <div class="code-name">#icon-Shape3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape11"></use>
                </svg>
                <div class="name">Shape1</div>
                <div class="code-name">#icon-Shape11</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe22"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe22</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe110"></use>
                </svg>
                <div class="name">形状结合1</div>
                <div class="code-name">#icon-xingzhuangjiehe110</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Fill22"></use>
                </svg>
                <div class="name">Fill 2</div>
                <div class="code-name">#icon-a-Fill22</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuoyuanxing"></use>
                </svg>
                <div class="name">椭圆形</div>
                <div class="code-name">#icon-tuoyuanxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe21"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe21</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe20"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe20</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Shapebeifen3"></use>
                </svg>
                <div class="name">Shape备份 3</div>
                <div class="code-name">#icon-a-Shapebeifen3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape2"></use>
                </svg>
                <div class="name">Shape</div>
                <div class="code-name">#icon-Shape2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe19"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe19</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-CombinedShape1"></use>
                </svg>
                <div class="name">Combined Shape</div>
                <div class="code-name">#icon-a-CombinedShape1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-CombinedShape"></use>
                </svg>
                <div class="name">Combined Shape</div>
                <div class="code-name">#icon-a-CombinedShape</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe18"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe18</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe17"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe17</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape1"></use>
                </svg>
                <div class="name">Shape</div>
                <div class="code-name">#icon-Shape1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe16"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe16</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shapebeifen2"></use>
                </svg>
                <div class="name">Shape备份</div>
                <div class="code-name">#icon-Shapebeifen2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shapebeifen1"></use>
                </svg>
                <div class="name">Shape备份</div>
                <div class="code-name">#icon-Shapebeifen1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shapebeifen"></use>
                </svg>
                <div class="name">Shape备份</div>
                <div class="code-name">#icon-Shapebeifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-sanjiaoxingbeifen4"></use>
                </svg>
                <div class="name">三角形备份 4</div>
                <div class="code-name">#icon-a-sanjiaoxingbeifen4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Rectangle271beifen2"></use>
                </svg>
                <div class="name">Rectangle 271备份 2</div>
                <div class="code-name">#icon-a-Rectangle271beifen2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe15"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe15</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe14"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe14</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-mengban1"></use>
                </svg>
                <div class="name">蒙版</div>
                <div class="code-name">#icon-mengban1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-mengban"></use>
                </svg>
                <div class="name">蒙版</div>
                <div class="code-name">#icon-mengban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe13"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe13</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe12"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe12</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe11"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe11</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe10"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe10</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape"></use>
                </svg>
                <div class="name">Shape</div>
                <div class="code-name">#icon-Shape</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Fill21"></use>
                </svg>
                <div class="name">Fill 2</div>
                <div class="code-name">#icon-a-Fill21</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Fill2"></use>
                </svg>
                <div class="name">Fill 2</div>
                <div class="code-name">#icon-a-Fill2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe9"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe9</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe8"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe8</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe7"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe7</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe6"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe6</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe5"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe5</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe4"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe3"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe2"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe1"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
