!function(e){var t={};function a(n){if(t[n])return t[n].exports;var r=t[n]={i:n,l:!1,exports:{__esModule: undefined}};return e[n].call(r.exports,r,r.exports,a),r.l=!0,r.exports}a.m=e,a.c=t,a.d=function(e,t,n){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(a.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)a.d(n,r,function(t){return e[t]}.bind(null,r));return n},a.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="",a(a.s=15)}([function(e,t,a){"use strict";t.__esModule=!0,t.addUrlParam=t.getQueryParam=t.getQueryMap=t.getQuery=t.cutUrl=t.getEntryUrl=t.getHref=void 0;t.getHref=function(){try{return location.href}catch(e){try{return document.URL}catch(e){}}return""};t.getEntryUrl=function(){var e=t.getHref();try{if(e.indexOf("xui.ptlogin2.qq.com")>-1){var a=e.match(/s_url=(.*?)&/);a&&(e=decodeURIComponent(a[1]))}}catch(n){}return e};t.cutUrl=function(e){if(e){var t=e.indexOf("?");if(t>0)return e.substring(0,t)}return e};t.getQuery=function(e){var t=e?1:0;try{return location.search.substr(t)}catch(r){try{var a=document.URL,n=a.indexOf("?");if(n>=0)return a.substr(n+t)}catch(r){}}return""};t.getQueryMap=function(){for(var e={},a=t.getQuery(!0).split("&"),n=0;n<a.length;n++){var r=/(.*?)=(.*)/.exec(a[n]);r&&(e[r[1]]=r[2])}return e};t.getQueryParam=function(e){return t.getQueryMap()[e]};var n=function(e,t,a){if(-1!=e.indexOf("?")){var n=new RegExp("(\\?|&"+t+")=[^&]*");e=n.test(e)?e.replace(n,"$1="+a):e+"&"+t+"="+a}else e=e+"?"+t+"="+a;return e};t.addUrlParam=function(e,t){var a;for(a in t)"undefined"!=typeof t[a]&&(e=n(e,encodeURIComponent(a),encodeURIComponent(""+t[a])));return e}},function(e,t,a){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{"default":e}};t.__esModule=!0,t.setVoiceOverFocus=t.bodyAriaHidden=t.addAriaModel=t.isTouchEventSupported=t.get$EventPosition=t.test$TouchEvent=t.supportsPassive=t.isWindow=t.getWindow=t.getOffset=t.isDarkMode=t.shakeEl=t.addOnceAnimationClass=t.animationEndName=t.removeClass=t.addClass=t.setCss=t.getCSS=t.supportsCSS=t.createGeneralIframe=void 0;var r=a(2),o=n(a(5));t.createGeneralIframe=function(e){var t=document.createElement("div");(null===e||void 0===e?void 0:e.name)?t.innerHTML='<iframe name="'+(null===e||void 0===e?void 0:e.name)+'" />':t.innerHTML="<iframe />";var a=t.childNodes[0];return(null===e||void 0===e?void 0:e.id)&&(a.id=e.id),(null===e||void 0===e?void 0:e.className)&&(a.className=e.className),a.setAttribute("frameborder","0"),a.setAttribute("border","0"),a.setAttribute("marginheight","0"),a.setAttribute("marginwidth","0"),a.setAttribute("scrolling","no"),a};t.supportsCSS=function(e,t){var a=document.createElement("div");return e in a.style&&"length"!==e&&"parentRule"!==e&&(a.style[e]=t,a.style[e]===t)};t.getCSS=function(e,t){return e.currentStyle?e.currentStyle[t]:window.getComputedStyle(e,null)[t]};t.setCss=function(e,t){if(e&&t&&r.isObject(t)){for(var a in t)try{e.style[a]=t[a]}catch(n){}return t}};t.addClass=function(e,t){if(e.classList)e.classList.add(t);else{var a=e.className,n=a+(""!==a?" ":"")+t;e.className=n}};t.removeClass=function(e,t){if(e.classList)return e.classList.remove(t);var a=" "+e.className+" ",n=(a=a.replace(/(\s+)/gi," ")).replace(" "+t+" "," ");n=n.replace(/(^\s+)|(\s+$)/g,""),e.className=n},t.animationEndName=function(){var e,t=document.createElement("fake"),a={animation:"animationend",mozAnimation:"mozAnimationEnd",webkitAnimation:"webkitAnimationEnd"};for(e in a)if(t.style[e]!==undefined)return a[e];return!1}();t.addOnceAnimationClass=function(e){var a=e.el,n=e.className,i=e.callback,c=e.duration;c||(c=400),r.isArray(a)||(a=[a]);for(var u=0;u<a.length;u++){var s=a[u];t.addClass(s,n)}var l=function(){r.isArray(a)||(a=[a]);for(var e=0;e<a.length;e++){var c=a[e];t.removeClass(c,n)}i(),t.animationEndName&&o["default"].remove(a[0],t.animationEndName,l)};t.animationEndName?o["default"].add(a[0],t.animationEndName,l):setTimeout(l,c)};t.shakeEl=function(e,a){t.addOnceAnimationClass({el:e,className:"shake",callback:a})};function i(e){return c(e)?e:9===e.nodeType&&(e.defaultView||e.parentWindow)}function c(e){return null!=e&&e==e.window}function u(e){return/^touch/.test(e.type)}t.isDarkMode=function(){var e;return null===(e=window.matchMedia)||void 0===e?void 0:e.call(window,"(prefers-color-scheme: dark)").matches},t.getOffset=function(e){var t,a,n={top:0,left:0},r=null===e||void 0===e?void 0:e.ownerDocument;if(r){t=r.documentElement,"undefined"!=typeof e.getBoundingClientRect&&(n=e.getBoundingClientRect());var o=0,c=0;return(a=i(r))&&(o=(a.pageYOffset||t.scrollTop)-(t.clientTop||0),c=(a.pageXOffset||t.scrollLeft)-(t.clientLeft||0)),{top:n.top+o,left:n.left+c}}},t.getWindow=i,t.isWindow=c,t.supportsPassive=function(){var e=!1;try{var t=Object.defineProperty({},"passive",{get:function(){e=!0}});window.addEventListener("testPassive",null,t),window.removeEventListener("testPassive",null,t)}catch(a){}return e}(),t.test$TouchEvent=u,t.get$EventPosition=function(e){if(u(e)){var t;e.originalEvent&&(t=e.originalEvent);var a=((null===t||void 0===t?void 0:t.touches)||[])[0];return a?{x:a.clientX,y:a.clientY}:null}return{x:e.pageX,y:e.pageY}},t.isTouchEventSupported=function(){return"ontouchstart"in document.createElement("div")},t.addAriaModel=function(e){try{e.setAttribute("role","dialog"),e.setAttribute("aria-modal","true"),e.setAttribute("aria-label","\u9a8c\u8bc1\u7801")}catch(t){}},t.bodyAriaHidden={hide:function(){try{document.body.setAttribute("aria-hidden","true")}catch(e){}},unhide:function(){try{document.body.setAttribute("aria-hidden","false")}catch(e){}}},t.setVoiceOverFocus=function(e){e.setAttribute("tabindex","0"),e.blur();var t=0,a=window.setInterval(function(){e.focus(),(t+=1)>=10&&window.clearInterval(a)},10)}},function(e,t,a){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,a,n){n===undefined&&(n=a),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[a]}})}:function(e,t,a,n){n===undefined&&(n=a),e[n]=t[a]}),r=this&&this.__exportStar||function(e,t){for(var a in e)"default"===a||Object.prototype.hasOwnProperty.call(t,a)||n(t,e,a)};t.__esModule=!0,r(a(1),t),r(a(4),t),r(a(0),t),r(a(10),t),r(a(11),t),r(a(12),t)},function(e,t,a){"use strict";var n=Object.prototype.hasOwnProperty,r=Object.prototype.toString,o=Object.defineProperty,i=Object.getOwnPropertyDescriptor,c=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===r.call(e)},u=function(e){if(!e||"[object Object]"!==r.call(e))return!1;var t,a=n.call(e,"constructor"),o=e.constructor&&e.constructor.prototype&&n.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!a&&!o)return!1;for(t in e);return void 0===t||n.call(e,t)},s=function(e,t){o&&"__proto__"===t.name?o(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},l=function(e,t){if("__proto__"===t){if(!n.call(e,t))return;if(i)return i(e,t).value}return e[t]};e.exports=function d(){var e,t,a,n,r,o,i=arguments[0],p=1,f=arguments.length,h=!1;for("boolean"==typeof i&&(h=i,i=arguments[1]||{},p=2),(null==i||"object"!=typeof i&&"function"!=typeof i)&&(i={});p<f;++p)if(null!=(e=arguments[p]))for(t in e)a=l(i,t),i!==(n=l(e,t))&&(h&&n&&(u(n)||(r=c(n)))?(r?(r=!1,o=a&&c(a)?a:[]):o=a&&u(a)?a:{},s(i,{name:t,newValue:d(h,o,n)})):void 0!==n&&s(i,{name:t,newValue:n}));return i}},function(e,t,a){"use strict";t.__esModule=!0,t.isArray=t.isObject=void 0;var n=function(e){return function(t){return Object.prototype.toString.call(t)==="[object "+e+"]"}};t.isObject=n("Object"),t.isArray=n("Array")},function(e,t,a){"use strict";t.__esModule=!0;t["default"]={add:function(e,t,a){e&&(e.addEventListener?e.addEventListener(t,a,!1):e.attachEvent?e.attachEvent("on"+t,a):e["on"+t]=a)},remove:function(e,t,a){e&&(e.removeEventListener?e.removeEventListener(t,a,!1):e.detachEvent?e.detachEvent("on"+t,a):e["on"+t]=null)}}},function(e,t,a){"use strict";function n(e,a,n){return"terror_"+t.ErrorCode[e]+"_"+a+"_"+Math.floor((new Date).getTime()/1e3)+(n?"_"+n:"")}function r(){return"@"+Math.random().toString(36).substr(2)}t.__esModule=!0,t.getRandStr=t.getErrorRes=t.getErrorTicket=t.ErrorCode=void 0,t.ErrorCode={ENTRYJS_LOAD_ERROR:1001,CAPTCHA_SHOW_TIMEOUT:1002,FRAMEJS_LOAD_TIMEOUT:1003,FRAMEJS_LOAD_ERROR:1004,FRAMEJS_RUN_ERROR:1005,GET_CAPTCHA_CONFIG_REQUEST_ERROR:1006,PRE_TEMPLATE_LOAD_TIMEOUT:1007,IFRAME_LOAD_TIMEOUT:1008,LIB_JQ_LOAD_ERROR:1009,CAPTCHA_JS_LOAD_ERROR:1010,CAPTCHA_JS_RUN_ERROR:1011,REFRESH_ERROR:1012,VERIFY_ERROR:1013},t.getErrorTicket=n,t.getErrorRes=function(e,a,o){return{ret:0,randstr:r(),ticket:n(e,a||"",o),errorCode:t.ErrorCode[e],errorMessage:e.toLowerCase()}},t.getRandStr=r},function(e,t,a){"use strict";t.__esModule=!0,t.all=t.keys=void 0;t.keys=["frame-verification","frame-back","frame-simple","frame-standard","frame-ok","aria-verification-simple","aria-verification-standard","aria-close","aria-standard","aria-simple","aria-feedback","aria-refresh","note-img-load-failed","note-verify-success","note-verify-timeout","note-verify-failed","note-verify-error","note-verify-failed-max","note-verify-default","note-appid-region-wrong"];var n={"zh-cn":["\u5b89\u5168\u9a8c\u8bc1","\u8fd4\u56de","\u6211\u4e0d\u4f1a","\u5e38\u89c4\u9a8c\u8bc1","\u786e\u5b9a","\u65e0\u969c\u788d\u9a8c\u8bc1","\u5e38\u89c4\u9a8c\u8bc1","\u5173\u95ed\u9a8c\u8bc1","\u5207\u6362\u4e3a\u5e38\u89c4\u9a8c\u8bc1\u65b9\u5f0f","\u6211\u4e0d\u4f1a\uff0c\u6362\u4e00\u79cd\u9a8c\u8bc1\u65b9\u5f0f","\u95ee\u9898\u53cd\u9988","\u5237\u65b0\u9a8c\u8bc1","\u56fe\u7247\u52a0\u8f7d\u5931\u8d25\uff0c\u8bf7\u70b9\u51fb\u5237\u65b0","\u9a8c\u8bc1\u6210\u529f\uff01","\u7f51\u7edc\u8d85\u65f6\uff0c\u8bf7\u91cd\u8bd5","\u9a8c\u8bc1\u9519\u8bef\uff0c\u8bf7\u91cd\u8bd5","\u60a8\u7684\u64cd\u4f5c\u8fc7\u4e8e\u9891\u7e41\uff0c\u8bf7\u7a0d\u540e\u518d\u8bd5","\u8fd9\u9898\u6709\u70b9\u96be\u5462\uff0c\u5df2\u4e3a\u60a8\u66f4\u6362\u9898\u76ee","\u7f51\u7edc\u604d\u60da\u4e86\u4e00\u4e0b(+)\uff0c\u518d\u8bd5\u4e00\u6b21\u5427","appid\u6240\u5c5e\u5730\u57df\u4e0e\u5b9e\u9645\u4f7f\u7528\u5730\u57df\u4e0d\u7b26\uff0c\u8bf7\u8054\u7cfb\u9a8c\u8bc1\u7801\u56e2\u961f\u5904\u7406"],"zh-hk":["\u5b89\u5168\u9a57\u8b49","\u8fd4\u56de","\u7121\u969c\u7919\u65b9\u5f0f","\u5e38\u898f\u9a57\u8b49","\u78ba\u5b9a","\u7121\u969c\u7919\u9a57\u8b49","\u5e38\u898f\u9a57\u8b49","\u95dc\u9589\u9a57\u8b49","\u5207\u63db\u70ba\u5e38\u898f\u9a57\u8b49\u65b9\u5f0f","\u6211\u4e0d\u6703\uff0c\u63db\u4e00\u7a2e\u9a57\u8b49\u65b9\u5f0f","\u610f\u898b\u53cd\u6620","\u5237\u65b0\u9a57\u8b49","\u7121\u6cd5\u52a0\u8f09\u5716\u7247\uff0c\u8acb\u9ede\u64ca\u5237\u65b0","\u9a57\u8b49\u6210\u529f\uff01","\u7db2\u7d61\u903e\u6642\uff0c\u8acb\u91cd\u8a66","\u9a57\u8b49\u932f\u8aa4\uff0c\u8acb\u91cd\u8a66","\u60a8\u7684\u64cd\u4f5c\u904e\u65bc\u983b\u7e41\uff0c\u8acb\u7a0d\u5f8c\u518d\u8a66","\u9019\u984c\u6709\u9ede\u96e3\uff0c\u5df2\u70ba\u4f60\u66f4\u63db\u984c\u76ee","\u7db2\u7d61\u505c\u9813\u4e86\u4e00\u4e0b(+)\uff0c\u518d\u8a66\u4e00\u6b21\u5427","appid\u6240\u5c6c\u5730\u57df\u8207\u5be6\u969b\u4f7f\u7528\u5730\u57df\u4e0d\u7b26\uff0c\u8acb\u806f\u7cfb\u9a57\u8b49\u78bc\u5718\u968a\u8655\u7406"],"zh-tw":["\u5b89\u5168\u9a57\u8b49","\u8fd4\u56de","\u7121\u969c\u7919\u65b9\u5f0f","\u5e38\u898f\u9a57\u8b49","\u78ba\u5b9a","\u7121\u969c\u7919\u9a57\u8b49","\u5e38\u898f\u9a57\u8b49","\u95dc\u9589\u9a57\u8b49","\u5207\u63db\u70ba\u5e38\u898f\u9a57\u8b49\u65b9\u5f0f","\u6211\u4e0d\u6703\uff0c\u63db\u4e00\u7a2e\u9a57\u8b49\u65b9\u5f0f","\u53cd\u6620\u610f\u898b","\u5237\u65b0\u9a57\u8b49","\u5716\u7247\u8f09\u5165\u5931\u6557\uff0c\u8acb\u9ede\u64ca\u91cd\u65b0\u6574\u7406","\u9a57\u8b49\u6210\u529f\uff01","\u7db2\u7d61\u903e\u6642\uff0c\u8acb\u91cd\u8a66","\u9a57\u8b49\u932f\u8aa4\uff0c\u8acb\u91cd\u8a66","\u60a8\u7684\u64cd\u4f5c\u904e\u65bc\u983b\u7e41\uff0c\u8acb\u7a0d\u5f8c\u518d\u8a66","\u9019\u984c\u6709\u9ede\u96e3\uff0c\u5df2\u70ba\u4f60\u66f4\u63db\u984c\u76ee","\u7db2\u8def\u4e2d\u65b7\u4e86\u4e00\u4e0b(+)\uff0c\u518d\u8a66\u4e00\u6b21\u5427","appid\u6240\u5c6c\u5730\u57df\u8207\u5be6\u969b\u4f7f\u7528\u5730\u57df\u4e0d\u7b26\uff0c\u8acb\u806f\u7cfb\u9a57\u8b49\u78bc\u5718\u968a\u8655\u7406"],en:["Verification","Back","Simple mode","Standard mode","OK","Simple mode","Standard mode","Quit verification","Switch to Standard mode","Too difficult? Switch to Simple mode","Feedback","Try a new captcha","Image loading failed. Click to refresh","Verification passed","Network timed out. Please try again.","Verification failed. Try again.","Operation too often. Please retry later.","Too hard? Try a new one","Network error (+). Please try again.","The AppID does not match the actual location. Please contact the Captcha team."],ja:["\u30bb\u30ad\u30e5\u30ea\u30c6\u30a3\u8a8d\u8a3c","\u623b\u308b","\u30b7\u30f3\u30d7\u30eb\u30e2\u30fc\u30c9","\u4e00\u822c\u30e2\u30fc\u30c9","OK","\u30b7\u30f3\u30d7\u30eb\u30e2\u30fc\u30c9","\u4e00\u822c\u30e2\u30fc\u30c9","\u9589\u3058\u308b","\u4e00\u822c\u30e2\u30fc\u30c9\u3078\u5207\u308a\u66ff\u3048\u308b","\u30b7\u30f3\u30d7\u30eb\u30e2\u30fc\u30c9\u306b\u5207\u308a\u66ff\u3048\u307e\u3059","\u30d5\u30a3\u30fc\u30c9\u30d0\u30c3\u30af","\u518d\u8aad\u307f\u8fbc\u307f","\u753b\u50cf\u306e\u8aad\u307f\u8fbc\u307f\u306b\u5931\u6557\u3057\u307e\u3057\u305f\u3002\u30af\u30ea\u30c3\u30af\u3057\u3066\u66f4\u65b0\u3057\u3066\u304f\u3060\u3055\u3044","\u8a8d\u8a3c\u306b\u6210\u529f\u3057\u307e\u3057\u305f","\u30a4\u30f3\u30bf\u30fc\u30cd\u30c3\u30c8\u63a5\u7d9a\u304c\u30bf\u30a4\u30e0\u30a2\u30a6\u30c8\u3057\u307e\u3057\u305f\u3002\u3082\u3046\u4e00\u5ea6\u304a\u8a66\u3057\u304f\u3060\u3055\u3044","\u8a8d\u8a3c\u306b\u5931\u6557\u3057\u307e\u3057\u305f\u3002\u3082\u3046\u4e00\u5ea6\u304a\u8a66\u3057\u304f\u3060\u3055\u3044","\u64cd\u4f5c\u304c\u983b\u5ea6\u306b\u884c\u308f\u308c\u3066\u3044\u307e\u3059\u306e\u3067\u3001\u5f8c\u3067\u3082\u3046\u4e00\u5ea6\u304a\u8a66\u3057\u304f\u3060\u3055\u3044","\u3053\u306e\u30af\u30a4\u30ba\u306f\u96e3\u3057\u3044\u306e\u3067\u3001\u5225\u306e\u30af\u30a4\u30ba\u306b\u30c1\u30e3\u30ec\u30f3\u30b8\u3057\u307e\u3057\u3087\u3046","\u30cd\u30c3\u30c8\u30ef\u30fc\u30af\u306b\u554f\u984c\u304c\u767a\u751f\u3057\u307e\u3057\u305f(+)\u3002\u3082\u3046\u4e00\u5ea6\u3084\u308a\u76f4\u3057\u3066\u304f\u3060\u3055\u3044","appid\u306e\u6240\u5c5e\u30ea\u30fc\u30b8\u30e7\u30f3\u306f\u5b9f\u969b\u306e\u5229\u7528\u30ea\u30fc\u30b8\u30e7\u30f3\u3068\u4e00\u81f4\u3057\u307e\u305b\u3093\u3002Captcha\u30c1\u30fc\u30e0\u306b\u304a\u554f\u3044\u5408\u308f\u305b\u304f\u3060\u3055\u3044"],ko:["\ubcf4\uc548 \uc778\uc99d","\ub3cc\uc544\uac00\uae30","\ub108\ubb34 \uc5b4\ub835\uc2b5\ub2c8\ub2e4.","\uc77c\ubc18 \uc778\uc99d","\ud655\uc778","\ub108\ubb34 \uc5b4\ub835\uc2b5\ub2c8\ub2e4.","\uc77c\ubc18 \uc778\uc99d","\uc778\uc99d \ub05d\ub0b4\uae30","\uc77c\ubc18 \uc778\uc99d \ubc29\uc2dd\uc73c\ub85c \uc804\ud658\ud558\uae30","\uc77c\ubc18 \uc778\uc99d \ubc29\uc2dd\uc73c\ub85c \uc804\ud658\ud558\uae30","\ud53c\ub4dc\ubc31","\uc774\ubbf8\uc9c0 \uc0c8\ub85c\uace0\uce68","\uc774\ubbf8\uc9c0 \ub85c\ub529\uc5d0 \uc2e4\ud328\ud588\uc2b5\ub2c8\ub2e4. \uc0c8\ub85c\uace0\uce68\ud558\uc138\uc694.","\uc778\uc99d \uc131\uacf5","\ub124\ud2b8\uc6cc\ud06c \uc2dc\uac04\uc774 \ucd08\uacfc\ub418\uc5c8\uc2b5\ub2c8\ub2e4. \ub2e4\uc2dc \uc2dc\ub3c4\ud558\uc138\uc694.","\uc778\uc99d \uc624\ub958\uc785\ub2c8\ub2e4. \ub2e4\uc2dc \uc2dc\ub3c4\ud558\uc138\uc694.","\uc2dc\ub3c4 \ud69f\uc218\uac00 \ub108\ubb34 \ub9ce\uc2b5\ub2c8\ub2e4. \uc7a0\uc2dc \ud6c4 \ub2e4\uc2dc \uc2dc\ub3c4\ud558\uc138\uc694.","\uc880 \ub354 \uc26c\uc6b4 \ubb38\uc81c\ub85c \uc2dc\ub3c4\ud574\ubcf4\uc138\uc694.","\ub124\ud2b8\uc6cc\ud06c \uc624\ub958\uc785\ub2c8\ub2e4(+). \ub2e4\uc2dc \uc2dc\ub3c4\ud558\uc138\uc694.","AppID \ub9ac\uc804\uacfc \uc2e4\uc81c \uc704\uce58\uc640 \uc77c\uce58\ud558\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4. Captcha\ud300\uc5d0 \ubb38\uc758\ud558\uc138\uc694."],pt:["Verifica\xe7\xe3o","Voltar","Modo simples","Modo padr\xe3o","OK","Modo simples","Modo padr\xe3o","Sair da verifica\xe7\xe3o","Mudar para o modo padr\xe3o","Muito dif\xedcil? Mude para o modo simples","Feedback","Tentar outro captcha","Falha no carregamento da imagem. Clique para atualizar","Verifica\xe7\xe3o conclu\xedda","A rede expirou. Tente novamente.","Falha na verifica\xe7\xe3o. Tente novamente.","Opera\xe7\xe3o realizada com muita frequ\xeancia. Tente novamente mais tarde.","Muito dif\xedcil? Tente outro","Erro de rede (+). Tente novamente.","O AppID n\xe3o corresponde ao local real. Entre em contato com a equipe do Captcha."],id:["Verifikasi","Kembali","Mode sederhana","Mode standar","Oke","Mode sederhana","Mode standar","Keluar verifikasi","Beralih ke mode Standar","Terlalu sulit? Beralih ke mode Sederhana","Masukan","Coba captcha baru","Gambar gagal dimuat. Klik untuk merefresh","Verifikasi berhasil","Jaringan kehabisan waktu. Coba lagi.","Verifikasi gagal. Coba lagi.","Operasi terlalu sering. Coba lagi nanti.","Terlalu sulit? Coba yang baru","Kesalahan jaringan (+). Coba lagi.","AppID tidak cocok dengan lokasi aktual. Harap hubungi tim Captcha."],ar:["\u0627\u0644\u062a\u062d\u0642\u0642","\u0631\u062c\u0648\u0639","\u0627\u0644\u0648\u0636\u0639 \u0627\u0644\u0628\u0633\u064a\u0637","\u0627\u0644\u0648\u0636\u0639 \u0627\u0644\u0642\u064a\u0627\u0633\u064a","\u062d\u0633\u0646\u064b\u0627","\u0627\u0644\u0648\u0636\u0639 \u0627\u0644\u0628\u0633\u064a\u0637","\u0627\u0644\u0648\u0636\u0639 \u0627\u0644\u0642\u064a\u0627\u0633\u064a","\u0645\u063a\u0627\u062f\u0631\u0629 \u0627\u0644\u062a\u062d\u0642\u0642","\u0627\u0644\u062a\u0628\u062f\u064a\u0644 \u0625\u0644\u0649 \u0627\u0644\u0648\u0636\u0639 \u0627\u0644\u0642\u064a\u0627\u0633\u064a","\u0635\u0639\u0628\u0629 \u0644\u0644\u063a\u0627\u064a\u0629\u061f \u0642\u0645 \u0628\u0627\u0644\u062a\u0628\u062f\u064a\u0644 \u0625\u0644\u0649 \u0627\u0644\u0648\u0636\u0639 \u0627\u0644\u0628\u0633\u064a\u0637","\u0627\u0644\u062a\u0639\u0644\u064a\u0642\u0627\u062a","\u062c\u0631\u0651\u0628 captcha \u062c\u062f\u064a\u062f\u0629","\u0641\u0634\u0644 \u062a\u062d\u0645\u064a\u0644 \u0627\u0644\u0635\u0648\u0631\u0629. \u0627\u0646\u0642\u0631 \u0644\u0644\u062a\u062d\u062f\u064a\u062b","\u0646\u062c\u062d \u0627\u0644\u062a\u062d\u0642\u0642","\u0627\u0646\u062a\u0647\u062a \u0645\u0647\u0644\u0629 \u0627\u0644\u0634\u0628\u0643\u0629. \u0623\u0639\u062f \u0627\u0644\u0645\u062d\u0627\u0648\u0644\u0629.","\u0641\u0634\u0644 \u0627\u0644\u062a\u062d\u0642\u0642. \u0623\u0639\u062f \u0627\u0644\u0645\u062d\u0627\u0648\u0644\u0629.","\u0645\u0631\u0627\u062a \u0627\u0644\u062a\u0634\u063a\u064a\u0644 \u0643\u062b\u064a\u0631\u0629 \u062c\u062f\u064b\u0627. \u062d\u0627\u0648\u0644 \u0644\u0627\u062d\u0642\u064b\u0627.","\u0635\u0639\u0628\u0629 \u0644\u0644\u063a\u0627\u064a\u0629\u061f \u062c\u0631\u0628 \u0648\u0627\u062d\u062f\u0629 \u0623\u062e\u0631\u0649","\u062e\u0637\u0623 \u0641\u064a \u0627\u0644\u0634\u0628\u0643\u0629 (+). \u0623\u0639\u062f \u0627\u0644\u0645\u062d\u0627\u0648\u0644\u0629.","\u0644\u0627 \u064a\u062a\u0637\u0627\u0628\u0642 \u0645\u0639\u0631\u0651\u0641 \u0627\u0644\u062a\u0637\u0628\u064a\u0642 \u0645\u0639 \u0627\u0644\u0645\u0648\u0642\u0639 \u0627\u0644\u0641\u0639\u0644\u064a. \u064a\u064f\u0631\u062c\u0649 \u0627\u0644\u062a\u0648\u0627\u0635\u0644 \u0645\u0639 \u0641\u0631\u064a\u0642 Captcha."],my:["\u1021\u1010\u100a\u103a\u1015\u103c\u102f\u1001\u103c\u1004\u103a\u1038","\u1014\u1031\u102c\u1000\u103a\u2026","\u101b\u102d\u102f\u1038\u101b\u103e\u1004\u103a\u1038\u1019\u102f\u1012\u103a","\u1015\u102f\u1036\u1019\u103e\u1014\u103a\u1019\u102f\u1012\u103a","\u1021\u102d\u102f\u1000\u1031","\u101b\u102d\u102f\u1038\u101b\u103e\u1004\u103a\u1038\u1019\u102f\u1012\u103a","\u1015\u102f\u1036\u1019\u103e\u1014\u103a\u1019\u102f\u1012\u103a","\u1021\u1010\u100a\u103a\u1015\u103c\u102f\u1001\u103c\u1004\u103a\u1038\u1019\u103e\u1011\u103d\u1000\u103a\u1015\u102b","\u1015\u102f\u1036\u1019\u103e\u1014\u103a\u1019\u102f\u1012\u103a\u101e\u102d\u102f\u1037 \u1015\u103c\u1031\u102c\u1004\u103a\u1038\u1015\u102b","\u1001\u1000\u103a\u1001\u1032\u101c\u103d\u1014\u103a\u1038\u101e\u101c\u102c\u1038\u104b \u101b\u102d\u102f\u1038\u101b\u103e\u1004\u103a\u1038\u1019\u102f\u1012\u103a\u101e\u102d\u102f\u1037 \u1015\u103c\u1031\u102c\u1004\u103a\u1038\u1015\u102b\u104b","\u1010\u102f\u1036\u1037\u1015\u103c\u1014\u103a\u1019\u103e\u102f","\u1000\u1000\u103a\u1015\u103a\u1001\u103b\u102c\u1021\u101e\u1005\u103a\u1016\u103c\u1004\u1037\u103a \u1000\u103c\u102d\u102f\u1038\u1005\u102c\u1038\u1015\u102b","\u1015\u102f\u1036\u1019\u1010\u1004\u103a\u1014\u102d\u102f\u1004\u103a\u1015\u102b\u104b \u101b\u102e\u1016\u101b\u1000\u103a\u101b\u103e\u103a\u101c\u102f\u1015\u103a\u101b\u1014\u103a\u1014\u103e\u102d\u1015\u103a","\u1021\u1010\u100a\u103a\u1015\u103c\u102f\u1001\u103c\u1004\u103a\u1038\u1021\u1031\u102c\u1004\u103a","\u1000\u103d\u1014\u103a\u101b\u1000\u103a\u1021\u1001\u103b\u102d\u1014\u103a\u101c\u103d\u1014\u103a\u1015\u103c\u102e\u104b \u1011\u1015\u103a\u101c\u102f\u1015\u103a\u1015\u102b\u104b","\u1021\u1010\u100a\u103a\u1019\u1015\u103c\u102f\u1014\u102d\u102f\u1004\u103a\u1015\u102b\u104b \u1011\u1015\u103a\u1000\u103c\u102d\u102f\u1038\u1005\u102c\u1038\u1015\u102b\u104b","\u101c\u102f\u1015\u103a\u1006\u1031\u102c\u1004\u103a\u1001\u103b\u1000\u103a\u1019\u103b\u102c\u1038\u101c\u103d\u1014\u103a\u1038\u101e\u100a\u103a\u104b \u1014\u1031\u102c\u1000\u103a\u1019\u103e\u101c\u102f\u1015\u103a\u1015\u102b\u104b","\u1001\u1000\u103a\u1001\u1032\u101c\u103d\u1014\u103a\u1038\u101e\u101c\u102c\u1038\u104b \u1021\u101e\u1005\u103a\u1015\u103c\u1031\u102c\u1004\u103a\u1038\u1015\u102b","\u1000\u103d\u1014\u103a\u101b\u1000\u103a\u1015\u103c\u103f\u1014\u102c (+)\u104b \u1011\u1015\u103a\u1000\u103c\u102d\u102f\u1038\u1005\u102c\u1038\u1015\u102b\u104b","AppID \u101e\u100a\u103a \u1021\u1019\u103e\u1014\u103a\u1010\u1000\u101a\u103a\u101b\u103e\u102d\u101e\u1031\u102c\u1014\u1031\u101b\u102c\u1014\u103e\u1004\u1037\u103a \u1019\u1000\u102d\u102f\u1000\u103a\u100a\u102e\u1015\u102b\u104b \u1000\u1000\u103a\u1015\u103a\u1001\u103b\u102c \u1021\u1016\u103d\u1032\u1037\u1000\u102d\u102f \u1006\u1000\u103a\u101e\u103d\u101a\u103a\u1015\u102b\u104b"],fr:["V\xe9rification","Retour","Mode simple","Mode standard","OK","Mode simple","Mode standard","Arr\xeater la v\xe9rification","Passez en mode standard","Trop difficile\xa0? Passez en mode simple","Commentaires","Essayez un nouveau captcha","Image non charg\xe9e. Actualisez","V\xe9rification faite","R\xe9seau interrompu. R\xe9essayez.","V\xe9rification \xe9chou\xe9e. R\xe9essayez","Op\xe9ration trop fr\xe9quente. R\xe9essayez apr\xe8s.","Trop dur\xa0? Essayez-en un autre","Erreur r\xe9seau (+). R\xe9essayez.","L'AppID ne correspond pas \xe0 l'emplacement r\xe9el. Contactez l'\xe9quipe Captcha."],de:["\xdcberpr\xfcfung","Zur\xfcck","Leichtmodus","Standardmodus","OK","Leichtmodus","Standardmodus","\xdcberpr\xfcfung beenden","In Standardmodus wechseln","Zu schwer? In Leichtmodus wechseln","R\xfcckmeldung","Versuchen Sie ein neues Captcha","Bild n. geladen. F\xfcr Aktual. klicken","\xdcberpr\xfcfung okay","Netzw-Timeout. Erneut versuchen.","\xdcberpr\xfcf. n. OK. Neu versuchen.","Vorgang zu oft. Sp\xe4ter erneut versuchen.","Zu schwer? Neuer Versuch","Netzwerkfehler (+). Neu versuchen.","AppID passt nicht zum aktuellen Standort. Wenden Sie sich an das Captcha-Team."],he:["\u05d0\u05d9\u05de\u05d5\u05ea","\u05d7\u05d6\u05e8\u05d4","\u05de\u05e6\u05d1 \u05e4\u05e9\u05d5\u05d8","\u05de\u05e6\u05d1 \u05e8\u05d2\u05d9\u05dc","\u05d0\u05d9\u05e9\u05d5\u05e8","\u05de\u05e6\u05d1 \u05e4\u05e9\u05d5\u05d8","\u05de\u05e6\u05d1 \u05e8\u05d2\u05d9\u05dc","\u05d9\u05e6\u05d9\u05d0\u05d4 \u05de\u05d0\u05d9\u05de\u05d5\u05ea","\u05de\u05e2\u05d1\u05e8 \u05dc\u05de\u05e6\u05d1 \u05e8\u05d2\u05d9\u05dc","\u05e7\u05e9\u05d4 \u05de\u05d3\u05d9? \u05e0\u05d9\u05ea\u05df \u05dc\u05e2\u05d1\u05d5\u05e8 \u05dc\u05de\u05e6\u05d1 \u05e4\u05e9\u05d5\u05d8","\u05de\u05e9\u05d5\u05d1","\u05d9\u05e9 \u05dc\u05e0\u05e1\u05d5\u05ea \u05d0\u05d9\u05de\u05d5\u05ea \u05d0\u05e0\u05d5\u05e9 \u05d7\u05d3\u05e9","\u05e0\u05db\u05e9\u05dc\u05d4 \u05d8\u05e2\u05d9\u05e0\u05ea \u05d4\u05ea\u05de\u05d5\u05e0\u05d4. \u05d9\u05e9 \u05dc\u05dc\u05d7\u05d5\u05e5 \u05db\u05d3\u05d9 \u05dc\u05e8\u05e2\u05e0\u05df","\u05d4\u05d0\u05d9\u05de\u05d5\u05ea \u05d4\u05e6\u05dc\u05d9\u05d7","\u05e4\u05d2 \u05d6\u05de\u05df \u05d4\u05e8\u05e9\u05ea \u05d4\u05e7\u05e6\u05d5\u05d1. \u05e0\u05d0 \u05dc\u05e0\u05e1\u05d5\u05ea \u05e9\u05d5\u05d1.","\u05d4\u05d0\u05d9\u05de\u05d5\u05ea \u05e0\u05db\u05e9\u05dc. \u05d9\u05e9 \u05dc\u05e0\u05e1\u05d5\u05ea \u05e9\u05d5\u05d1.","\u05d4\u05e4\u05e2\u05dc\u05d4 \u05ea\u05d3\u05d9\u05e8\u05d4 \u05de\u05d3\u05d9. \u05e0\u05d0 \u05dc\u05e0\u05e1\u05d5\u05ea \u05e9\u05d5\u05d1 \u05de\u05d0\u05d5\u05d7\u05e8 \u05d9\u05d5\u05ea\u05e8.","\u05e7\u05e9\u05d4 \u05de\u05d3\u05d9? \u05e0\u05d9\u05ea\u05df \u05dc\u05e0\u05e1\u05d5\u05ea \u05d7\u05d3\u05e9","\u05e9\u05d2\u05d9\u05d0\u05ea \u05e8\u05e9\u05ea (+). \u05e0\u05d0 \u05dc\u05e0\u05e1\u05d5\u05ea \u05e9\u05d5\u05d1.","\u05d4\u05de\u05d6\u05d4\u05d4 AppID \u05dc\u05d0 \u05de\u05ea\u05d0\u05d9\u05dd \u05dc\u05de\u05d9\u05e7\u05d5\u05dd \u05d4\u05de\u05de\u05e9\u05d9. \u05e0\u05d0 \u05dc\u05e4\u05e0\u05d5\u05ea \u05dc\u05e6\u05d5\u05d5\u05ea \u05d0\u05d9\u05de\u05d5\u05ea \u05d0\u05e0\u05d5\u05e9."],hi:["\u0938\u0924\u094d\u092f\u093e\u092a\u0928","\u092a\u0940\u091b\u0947","\u0906\u0938\u093e\u0928 \u092e\u094b\u0921","\u092e\u093e\u0928\u0915 \u092e\u094b\u0921","\u0920\u0940\u0915","\u0906\u0938\u093e\u0928 \u092e\u094b\u0921","\u092e\u093e\u0928\u0915 \u092e\u094b\u0921","\u0938\u0924\u094d\u092f\u093e\u092a\u0928 \u091b\u094b\u0921\u093c \u0926\u0947\u0902","\u092e\u093e\u0928\u0915 \u092e\u094b\u0921 \u0915\u0947 \u0932\u093f\u090f \u0938\u094d\u0935\u093f\u091a \u0915\u0930\u0947\u0902","\u0915\u093e\u092b\u093c\u0940 \u0915\u0920\u093f\u0928? \u0906\u0938\u093e\u0928 \u092e\u094b\u0921 \u0915\u0947 \u0932\u093f\u090f \u0938\u094d\u0935\u093f\u091a \u0915\u0930\u0947\u0902","\u092b\u0940\u0921\u092c\u0948\u0915","\u090f\u0915 \u0928\u092f\u093e \u0915\u0948\u092a\u094d\u091a\u093e \u0906\u091c\u092e\u093e\u090f\u0902","\u091b\u0935\u093f \u0932\u094b\u0921 \u0928\u0939\u0940\u0902\u0964 \u0915\u094d\u0932\u093f\u0915 \u0915\u0930\u0915\u0947 \u0930\u093f\u092b\u094d\u0930\u0947\u0936  \u0915\u0930\u0947\u0902","\u0938\u0924\u094d\u092f\u093e\u092a\u0928 \u092a\u093e\u0930\u093f\u0924","\u0928\u0947\u091f\u0935\u0930\u094d\u0915 \u0938\u092e\u092f \u0938\u092e\u093e\u092a\u094d\u0924\u0964 \u092b\u093f\u0930 \u0938\u0947 \u0915\u0930\u0947\u0902\u0964","\u0938\u0924\u094d\u092f\u093e\u092a\u0928 \u0935\u093f\u092b\u0932\u0964 \u092b\u093f\u0930 \u0915\u094b\u0936\u093f\u0936 \u0915\u0930\u0947\u0902\u0964","\u092a\u094d\u0930\u091a\u093e\u0932\u0928 \u0915\u093e \u0905\u0915\u094d\u0938\u0930 \u0939\u094b\u0928\u093e\u0964 \u092c\u093e\u0926 \u092e\u0947\u0902 \u0915\u094b\u0936\u093f\u0936 \u0915\u0930\u0947\u0902\u0964","\u0915\u093e\u092b\u093c\u0940 \u0915\u0920\u094b\u0930? \u090f\u0915 \u0928\u092f\u093e \u0906\u091c\u092e\u093e\u090f\u0902","\u0928\u0947\u091f\u0935\u0930\u094d\u0915 \u0924\u094d\u0930\u0941\u091f\u093f (+)\u0964 \u092b\u093f\u0930 \u0915\u094b\u0936\u093f\u0936 \u0915\u0930\u0947\u0902\u0964","\u0935\u093e\u0938\u094d\u0924\u0935\u093f\u0915 \u0938\u094d\u0925\u093e\u0928 \u0915\u0940 \u0910\u092a \u0906\u0908\u0921\u0940 \u0938\u0947 \u092e\u0947\u0932 \u0928\u0939\u0940\u0902 \u0916\u093e\u0924\u0940\u0964 \u0915\u0943\u092a\u092f\u093e \u0915\u0948\u092a\u094d\u091a\u093e \u091f\u0940\u092e \u0938\u0947 \u0938\u0902\u092a\u0930\u094d\u0915 \u0915\u0930\u0947\u0902\u0964"],it:["Verifica","Indietro","Mod. semplice","Mod. standard","OK","Mod. semplice","Mod. standard","Esci dalla verifica","Passa alla mod. standard","Troppo diffic.? Passa alla mod. semplice","Feedback","Prova un altro captcha","Imp. caric. imm. Clicc. per aggiorn.","Verifica superata","Timeout rete. Riprovare.","Verif. non riusc. Riprovare.","Oper. ripet. troppe volte. Riprovare dopo.","Troppo diffic.? Prova un altro","Errore rete ({{errore}}). Riprovare.","L'ID dell'app non corrisp. alla posiz. corr. Contattare il team dei captcha."],lo:["\u0e81\u0eb2\u0e99\u0e81\u0ea7\u0e94\u0eaa\u0ead\u0e9a","\u0e81\u0eb1\u0e9a\u0e84\u0eb7\u0e99","\u0ec2\u0edd\u0e94\u0e87\u0ec8\u0eb2\u0e8d","\u0ec2\u0edd\u0e94\u0ea1\u0eb2\u0e94\u0e95\u0eb0\u0e96\u0eb2\u0e99","\u0e95\u0ebb\u0e81\u0ea5\u0ebb\u0e87","\u0ec2\u0edd\u0e94\u0e87\u0ec8\u0eb2\u0e8d","\u0ec2\u0edd\u0e94\u0ea1\u0eb2\u0e94\u0e95\u0eb0\u0e96\u0eb2\u0e99","\u0ead\u0ead\u0e81\u0e81\u0eb2\u0e99\u0e81\u0ea7\u0e94\u0eaa\u0ead\u0e9a","\u0eaa\u0eb1\u0e9a\u0e9b\u0ec8\u0ebd\u0e99\u0ec0\u0e9b\u0eb1\u0e99\u0ec2\u0edd\u0e94\u0ea1\u0eb2\u0e94\u0e95\u0eb0\u0e96\u0eb2\u0e99","\u0e8d\u0eb2\u0e81\u0ec0\u0e81\u0eb5\u0e99\u0ec4\u0e9b\u0e9a\u0ecd? \u0eaa\u0eb1\u0e9a\u0e9b\u0ec8\u0ebd\u0e99\u0ec0\u0e9b\u0eb1\u0e99\u0ec2\u0edd\u0e94\u0e87\u0ec8\u0eb2\u0e8d","\u0e84\u0eb3\u0ec0\u0eab\u0eb1\u0e99\u0e95\u0eb4\u0e8a\u0ebb\u0ea1","\u0ea5\u0ead\u0e87\u0ec3\u0e8a\u0ec9\u0ec1\u0e84\u0eb1\u0e9a\u0e88\u0eb2\u0ec3\u0edd\u0ec8","\u0e81\u0eb2\u0e99\u0ec2\u0eab\u0ebc\u0e94\u0eae\u0eb9\u0e9a\u0e9a\u0ecd\u0ec8\u0eaa\u0eb3\u0ec0\u0ea5\u0eb1\u0e94. \u0e84\u0ea5\u0eb4\u0e81\u0ec0\u0e9e\u0eb7\u0ec8\u0ead\u0ea3\u0eb5\u0ec0\u0e9f\u0ea3\u0e8a","\u0e9c\u0ec8\u0eb2\u0e99\u0e81\u0eb2\u0e99\u0e81\u0ea7\u0e94\u0eaa\u0ead\u0e9a\u0ec1\u0ea5\u0ec9\u0ea7","\u0edd\u0ebb\u0e94\u0ec0\u0ea7\u0ea5\u0eb2\u0ec0\u0e84\u0eb7\u0ead\u0e82\u0ec8\u0eb2\u0e8d. \u0e81\u0eb0\u0ea5\u0eb8\u0e99\u0eb2\u0ea5\u0ead\u0e87\u0ec3\u0edd\u0ec8.","\u0e81\u0eb2\u0e99\u0e81\u0ea7\u0e94\u0eaa\u0ead\u0e9a\u0e9a\u0ecd\u0ec8\u0eaa\u0eb3\u0ec0\u0ea5\u0eb1\u0e94. \u0ea5\u0ead\u0e87\u0ec3\u0edd\u0ec8\u0ead\u0eb5\u0e81.","\u0e81\u0eb2\u0e99\u0e94\u0eb3\u0ec0\u0e99\u0eb5\u0e99\u0e81\u0eb2\u0e99\u0ec0\u0ea5\u0eb7\u0ec9\u0ead\u0ec6\u0ec0\u0e81\u0eb5\u0e99\u0ec4\u0e9b. \u0ea5\u0ead\u0e87\u0ec3\u0edd\u0ec8\u0e9e\u0eb2\u0e8d\u0eab\u0ebc\u0eb1\u0e87.","\u0e8d\u0eb2\u0e81\u0ec0\u0e81\u0eb5\u0e99\u0ec4\u0e9b\u0e9a\u0ecd? \u0ea5\u0ead\u0e87\u0ead\u0eb1\u0e99\u0ec3\u0edd\u0ec8","\u0ec0\u0e84\u0eb7\u0ead\u0e82\u0ec8\u0eb2\u0e8d\u0e82\u0eb1\u0e94\u0e82\u0ec9\u0ead\u0e87 (+). \u0e81\u0eb0\u0ea5\u0eb8\u0e99\u0eb2\u0ea5\u0ead\u0e87\u0ec3\u0edd\u0ec8.","AppID \u0e9a\u0ecd\u0ec8\u0e81\u0ebb\u0e87\u0e81\u0eb1\u0e9a\u0e88\u0eb8\u0e94\u0e97\u0eb5\u0ec8\u0e95\u0eb1\u0ec9\u0e87\u0e95\u0ebb\u0ea7\u0e88\u0eb4\u0e87. \u0e81\u0eb0\u0ea5\u0eb8\u0e99\u0eb2\u0e95\u0eb4\u0e94\u0e95\u0ecd\u0ec8\u0e97\u0eb5\u0ea1\u0ec1\u0e84\u0eb1\u0e9a\u0e88\u0eb2."],ms:["Pengesahan","Kembali","Mod mudah","mod standard","OK","Mod mudah","mod standard","Hentikan pengesahan","Tukar ke mod Standard","Terlalu sukar? Tukar ke mod Mudah","Maklum balas","Cuba captcha baharu","Imej gagal dimuat. Klik utk disegarkan","Lulus pengesahan","Rangkaian tamat masa. Cuba lagi.","Pengesahan gagal. Cuba lagi","Operasi terlalu kerap. Cuba lagi kemudian.","Terlalu sukar? Cuba yang baharu","Ralat rangkaian (+). Sila cuba lagi.","AppID tidak sepadan dengan lokasi sebenar. Sila hubungi pasukan Captcha."],pl:["Weryfikacja","Wstecz","Tryb prosty","Tryb standar.","OK","Tryb prosty","Tryb standar.","Zako\u0144cz weryfikacj\u0119","Prze\u0142\u0105cz do trybu standardowego","Zbyt trudne? Prze\u0142\u0105cz do trybu prostego.","Opinie","Spr\xf3buj nowe captcha","Nie pobrano obrazu. Od\u015bwie\u017c (kliknij).","Zweryfikowano","Limit czasu sieci. Pon\xf3w.","Nie zweryfikowano. Pon\xf3w.","Zbyt cz\u0119ste operacje. Spr\xf3buj p\xf3\u017aniej.","Zbyt trudne? Spr\xf3buj ponownie.","B\u0142\u0105d sieciowy (+). Spr\xf3buj ponownie.","AppID nie pasuje do faktycznej lokalizacji. Skontaktuj si\u0119 z zespo\u0142em Captcha."],ru:["\u041f\u0440\u043e\u0432\u0435\u0440\u043a\u0430","\u041d\u0430\u0437\u0430\u0434","\u041f\u0440\u043e\u0441\u0442\u043e\u0439 \u0440\u0435\u0436\u0438\u043c","\u0421\u0442\u0430\u043d\u0434\u0430\u0440\u0442","OK","\u041f\u0440\u043e\u0441\u0442\u043e\u0439 \u0440\u0435\u0436\u0438\u043c","\u0421\u0442\u0430\u043d\u0434\u0430\u0440\u0442","\u0412\u044b\u0439\u0442\u0438 \u0438\u0437 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0438","\u041f\u0435\u0440\u0435\u043a\u043b\u044e\u0447\u0438\u0442\u044c\u0441\u044f \u0432 \u0440\u0435\u0436\u0438\u043c \u0421\u0442\u0430\u043d\u0434\u0430\u0440\u0442","\u0421\u043b\u0438\u0448\u043a\u043e\u043c \u0441\u043b\u043e\u0436\u043d\u043e? \u041f\u0435\u0440\u0435\u043a\u043b\u044e\u0447\u0438\u0442\u0435\u0441\u044c \u0432 \u043f\u0440\u043e\u0441\u0442\u043e\u0439 \u0440\u0435\u0436\u0438\u043c","\u041e\u0431\u0440\u0430\u0442\u043d\u0430\u044f \u0441\u0432\u044f\u0437\u044c","\u041e\u0431\u043d\u043e\u0432\u0438\u0442\u044c \u043a\u0430\u043f\u0447\u0443","\u0421\u0431\u043e\u0439 \u0437\u0430\u0433\u0440\u0443\u0437\u043a\u0438 \u0438\u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u044f. \u041e\u0431\u043d\u043e\u0432\u0438\u0442\u0435.","\u041f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u0432\u044b\u043f\u043e\u043b\u043d\u0435\u043d\u0430","\u0412\u0440\u0435\u043c\u044f \u0438\u0441\u0442\u0435\u043a\u043b\u043e. \u041f\u043e\u0432\u0442\u043e\u0440\u0438\u0442\u0435 \u043f\u043e\u043f\u044b\u0442\u043a\u0443.","\u0421\u0431\u043e\u0439 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0438. \u041f\u043e\u0432\u0442\u043e\u0440\u0438\u0442\u0435.","\u0421\u043b\u0438\u0448\u043a\u043e\u043c \u043c\u043d\u043e\u0433\u043e \u043f\u043e\u0432\u0442\u043e\u0440\u043e\u0432. \u0412\u0435\u0440\u043d\u0438\u0442\u0435\u0441\u044c \u043f\u043e\u0437\u0436\u0435.","\u0421\u043b\u0438\u0448\u043a\u043e\u043c \u0441\u043b\u043e\u0436\u043d\u043e? \u0412\u044b\u0431\u0435\u0440\u0438\u0442\u0435 \u0434\u0440\u0443\u0433\u043e\u0439","\u041e\u0448\u0438\u0431\u043a\u0430 \u0441\u0435\u0442\u0438 (+). \u041f\u043e\u0432\u0442\u043e\u0440\u0438\u0442\u0435 \u043f\u043e\u0437\u0436\u0435.","AppID \u043d\u0435 \u0441\u043e\u043e\u0442\u0432\u0435\u0442\u0441\u0442\u0432\u0443\u0435\u0442 \u043c\u0435\u0441\u0442\u043e\u043f\u043e\u043b\u043e\u0436\u0435\u043d\u0438\u044e. \u0421\u0432\u044f\u0436\u0438\u0442\u0435\u0441\u044c \u0441 \u043a\u043e\u043c\u0430\u043d\u0434\u043e\u0439 Captcha."],es:["Verificaci\xf3n","Atr\xe1s","Modo sencillo","Modo normal","Aceptar","Modo sencillo","Modo normal","Verificaci\xf3n r\xe1pida","Cambiar a modo normal","\xbfDemasiado dif\xedcil? Cambiar a modo sencillo","Comentarios","Probar nuevo captcha","Fallo al cargar imagen. Clic para actualizar","Verificaci\xf3n aprobada","Red caducada. Pruebe de nuevo.","Fall\xf3 verificaci\xf3n. Pruebe de nuevo.","Operaci\xf3n muy repetida. Pruebe m\xe1s tarde.","\xbfDemasiado dif\xedcil? Pruebe otro","Erro de la red (+). Pruebe de nuevo.","El AppID no coincide con la posici\xf3n actual. Contacte con el equipo Captcha."],th:["\u0e01\u0e32\u0e23\u0e22\u0e37\u0e19\u0e22\u0e31\u0e19","\u0e22\u0e49\u0e2d\u0e19\u0e01\u0e25\u0e31\u0e1a","\u0e42\u0e2b\u0e21\u0e14\u0e2d\u0e22\u0e48\u0e32\u0e07\u0e07\u0e48\u0e32\u0e22","\u0e42\u0e2b\u0e21\u0e14\u0e21\u0e32\u0e15\u0e23\u0e10\u0e32\u0e19","\u0e15\u0e01\u0e25\u0e07","\u0e42\u0e2b\u0e21\u0e14\u0e2d\u0e22\u0e48\u0e32\u0e07\u0e07\u0e48\u0e32\u0e22","\u0e42\u0e2b\u0e21\u0e14\u0e21\u0e32\u0e15\u0e23\u0e10\u0e32\u0e19","\u0e2d\u0e2d\u0e01\u0e08\u0e32\u0e01\u0e01\u0e32\u0e23\u0e22\u0e37\u0e19\u0e22\u0e31\u0e19","\u0e40\u0e1b\u0e25\u0e35\u0e48\u0e22\u0e19\u0e40\u0e1b\u0e47\u0e19\u0e42\u0e2b\u0e21\u0e14\u0e21\u0e32\u0e15\u0e23\u0e10\u0e32\u0e19","\u0e22\u0e32\u0e01\u0e40\u0e01\u0e34\u0e19\u0e44\u0e1b\u0e43\u0e0a\u0e48\u0e44\u0e2b\u0e21 \u0e40\u0e1b\u0e25\u0e35\u0e48\u0e22\u0e19\u0e40\u0e1b\u0e47\u0e19\u0e42\u0e2b\u0e21\u0e14\u0e2d\u0e22\u0e48\u0e32\u0e07\u0e07\u0e48\u0e32\u0e22","\u0e02\u0e49\u0e2d\u0e40\u0e2a\u0e19\u0e2d\u0e41\u0e19\u0e30","\u0e25\u0e2d\u0e07 Captcha \u0e43\u0e2b\u0e21\u0e48","\u0e42\u0e2b\u0e25\u0e14\u0e20\u0e32\u0e1e\u0e44\u0e21\u0e48\u0e2a\u0e33\u0e40\u0e23\u0e47\u0e08 \u0e04\u0e25\u0e34\u0e01\u0e40\u0e1e\u0e37\u0e48\u0e2d\u0e23\u0e35\u0e40\u0e1f\u0e23\u0e0a","\u0e1c\u0e48\u0e32\u0e19\u0e01\u0e32\u0e23\u0e22\u0e37\u0e19\u0e22\u0e31\u0e19","\u0e40\u0e04\u0e23\u0e37\u0e2d\u0e02\u0e48\u0e32\u0e22\u0e2b\u0e21\u0e14\u0e40\u0e27\u0e25\u0e32 \u0e42\u0e1b\u0e23\u0e14\u0e25\u0e2d\u0e07\u0e2d\u0e35\u0e01\u0e04\u0e23\u0e31\u0e49\u0e07","\u0e01\u0e32\u0e23\u0e22\u0e37\u0e19\u0e22\u0e31\u0e19\u0e44\u0e21\u0e48\u0e2a\u0e33\u0e40\u0e23\u0e47\u0e08 \u0e25\u0e2d\u0e07\u0e2d\u0e35\u0e01\u0e04\u0e23\u0e31\u0e49\u0e07","\u0e14\u0e33\u0e40\u0e19\u0e34\u0e19\u0e01\u0e32\u0e23\u0e1a\u0e48\u0e2d\u0e22\u0e40\u0e01\u0e34\u0e19\u0e44\u0e1b \u0e42\u0e1b\u0e23\u0e14\u0e25\u0e2d\u0e07\u0e2d\u0e35\u0e01\u0e04\u0e23\u0e31\u0e49\u0e07\u0e20\u0e32\u0e22\u0e2b\u0e25\u0e31\u0e07","\u0e22\u0e32\u0e01\u0e40\u0e01\u0e34\u0e19\u0e44\u0e1b\u0e43\u0e0a\u0e48\u0e44\u0e2b\u0e21 \u0e25\u0e2d\u0e07\u0e20\u0e32\u0e1e\u0e43\u0e2b\u0e21\u0e48","\u0e40\u0e04\u0e23\u0e37\u0e2d\u0e02\u0e48\u0e32\u0e22\u0e21\u0e35\u0e02\u0e49\u0e2d\u0e1c\u0e34\u0e14\u0e1e\u0e25\u0e32\u0e14 (+) \u0e42\u0e1b\u0e23\u0e14\u0e25\u0e2d\u0e07\u0e2d\u0e35\u0e01\u0e04\u0e23\u0e31\u0e49\u0e07","AppID \u0e44\u0e21\u0e48\u0e15\u0e23\u0e07\u0e01\u0e31\u0e1a\u0e15\u0e33\u0e41\u0e2b\u0e19\u0e48\u0e07\u0e17\u0e35\u0e48\u0e15\u0e31\u0e49\u0e07\u0e08\u0e23\u0e34\u0e07 \u0e42\u0e1b\u0e23\u0e14\u0e15\u0e34\u0e14\u0e15\u0e48\u0e2d\u0e17\u0e35\u0e21 Captcha"],tr:["Do\u011frulama","Geri","Basit mod","Standart mod","Tamam","Basit mod","Standart mod","Do\u011frulamadan \xe7\u0131k","Standart moda ge\xe7","\xc7ok mu zor? Basit moda ge\xe7in","Geri bildirim","Yeni bir captcha deneyin","G\xf6r\xfcnt\xfc y\xfcklenmedi. T\u0131klay\u0131p yenileyin","Do\u011frulama ba\u015far\u0131l\u0131","A\u011f zaman a\u015f\u0131m\u0131. Tekrar deneyin.","Do\u011frulanamad\u0131. Tekrar deneyin.","\xc7ok s\u0131k i\u015flem yap\u0131ld\u0131. Daha sonra deneyin.","\xc7ok mu zar? Yeni birini deneyin","A\u011f hatas\u0131 (+). Tekrar deneyin.","Uygulama kimli\u011fi ger\xe7ek konumla e\u015fle\u015fmiyor. L\xfctfen Captcha ekibine ula\u015f\u0131n."],vi:["X\xe1c th\u1ef1c","Quay l\u1ea1i","Ch\u1ebf \u0111\u1ed9 \u0111\u01a1n gi\u1ea3n","Ch\u1ebf \u0111\u1ed9 chu\u1ea9n","OK","Ch\u1ebf \u0111\u1ed9 \u0111\u01a1n gi\u1ea3n","Ch\u1ebf \u0111\u1ed9 chu\u1ea9n","H\u1ee7y x\xe1c th\u1ef1c","Chuy\u1ec3n sang Ch\u1ebf \u0111\u1ed9 chu\u1ea9n","G\u1eb7p kh\xf3 \u01b0? H\xe3y chuy\u1ec3n sang Ch\u1ebf \u0111\u1ed9 \u0111\u01a1n gi\u1ea3n xem","G\xf3p \xfd","Th\u1eed captcha m\u1edbi","Kh\xf4ng th\u1ec3 t\u1ea3i \u1ea3nh. Nh\u1ea5n \u0111\u1ec3 l\xe0m m\u1edbi","X\xe1c th\u1ef1c th\xe0nh c\xf4ng","H\u1ebft phi\xean k\u1ebft n\u1ed1i. H\xe3y th\u1eed l\u1ea1i.","Ch\u01b0a th\u1ec3 x\xe1c th\u1ef1c. H\xe3y th\u1eed l\u1ea1i.","Thao t\xe1c qu\xe1 nhi\u1ec1u l\u1ea7n. Vui l\xf2ng th\u1eed l\u1ea1i.","G\u1eb7p kh\xf3 \u01b0? H\xe3y th\u1eed c\xe1i m\u1edbi xem","L\u1ed7i m\u1ea1ng (+). Vui l\xf2ng th\u1eed l\u1ea1i.","AppID kh\xf4ng tr\xf9ng kh\u1edbp v\u1edbi v\u1ecb tr\xed th\u1ef1c t\u1ebf. Vui l\xf2ng li\xean h\u1ec7 \u0111\u1ed9i ng\u0169 Captcha."],fil:["Pag-verify","Bumalik","Simple mode","Standard mode","OK","Simple mode","Standard mode","Tapusin ang pagpapatunay","Lumipat sa Standard mode","Napakahirap? Lumipat sa Simpleng mode","Feedback","Subukan ang bagong captcha","Hindi nag-load. I-click para i-refresh","Na-verify","Huminto ang network. Pakiulit.","Hindi na-verify. Pakiulit.","Madalas na operasyon.\nSubukan ulit mamaya.","Napakahirap? Sumubok ng bago","Network error (+). Pakiulit.","Di tugma ang AppID sa tunay na lugar. Pakikontak ang Captcha team."],ur:["\u062a\u0635\u062f\u06cc\u0642","\u067e\u06cc\u0686\u06be\u06d2","\u0633\u0627\u062f\u06c1 \u0645\u0648\u0688","\u0645\u0639\u06cc\u0627\u0631\u06cc \u0645\u0648\u0688","\u0679\u06be\u064a\u06a9\u06c1\u06d2","\u0633\u0627\u062f\u06c1 \u0645\u0648\u0688","\u0645\u0639\u06cc\u0627\u0631\u06cc \u0645\u0648\u0688","\u062a\u0635\u062f\u06cc\u0642 \u0633\u06d2 \u062a\u0631\u06a9 \u06a9\u0631\u06cc\u06ba","\u0645\u0639\u06cc\u0627\u0631\u06cc \u0645\u0648\u0688 \u067e\u0631 \u062c\u0627\u0626\u06cc\u06ba","\u0645\u0634\u06a9\u0644 \u06c1\u06d2\u061f \u0633\u0627\u062f\u06c1 \u0645\u0648\u0688 \u067e\u0631 \u062c\u0627\u0626\u06cc\u06ba","\u062a\u0627\u062b\u0631\u0627\u062a \u062f\u06cc\u06ba","\u0646\u06cc\u0627 \u06a9\u06cc\u067e\u0686\u0627 \u062f\u0627\u062e\u0644 \u06a9\u0631\u06cc\u06ba","\u062a\u0635\u0648\u06cc\u0631\u0644\u0648\u0688\u0646\u06c1\u06cc \u06c1\u0648\u0626\u06cc \u0631\u0641\u0631\u06cc\u0634 \u06a9\u0644\u06a9 \u06a9\u0631\u06cc\u06ba","\u062a\u0635\u062f\u06cc\u0642 \u06c1\u0648 \u06af\u0626\u06cc","\u0646\u06cc\u0679 \u0648\u0631\u06a9 \u06a9\u0627 \u0648\u0642\u062a \u062e\u062a\u0645 \u067e\u06be\u0631\u06a9\u0648\u0634\u0634 \u06a9\u0631\u06cc\u06d2","\u062a\u0635\u062f\u06cc\u0642 \u0646\u0627\u06a9\u0627\u0645 \u067e\u06be\u0631\u0633\u06d2 \u06a9\u0648\u0634\u0634 \u06a9\u0631\u06cc\u06ba","\u062a\u062c\u0627\u0648\u0632 \u06c1\u0648 \u06af\u06cc\u0627. \u0628\u0639\u062f\u0645\u06cc\u06ba \u062f\u0648\u0628\u0627\u0631\u06c1 \u06a9\u0648\u0634\u0634 \u06a9\u0631\u06cc\u06ba.","\u0645\u0634\u06a9\u0644 \u06c1\u06d2\u061f\u067e\u06be\u0631 \u06a9\u0648\u0634\u0634 \u06a9\u0631\u06cc\u06ba","\u0646\u06cc\u0679 \u0648\u0631\u06a9 \u0641\u06cc\u0644 (+). \u062f\u0648\u0628\u0627\u0631\u06c1 \u06a9\u0648\u0634\u0634 \u06a9\u0631\u06cc\u06ba.","\u0627\u06cc\u067e \u0622\u0626\u06cc \u0688\u06cc \u0627\u0635\u0644 \u0645\u062d\u0644 \u0648\u0642\u0648\u0639 \u0633\u06d2 \u0645\u06cc\u0644 \u0646\u06c1\u06cc\u06ba \u06a9\u06be\u0627\u062a\u0627.\u0628\u0631\u0627\u06c1 \u06a9\u0631\u0645 \u06a9\u06cc\u067e\u0686\u0627 \u0679\u06cc\u0645 \u0633\u06d2 \u0631\u0627\u0628\u0637\u06c1 \u06a9\u0631\u06cc\u06ba"]};t.all=n,n.iw=n.he,n["in"]=n.id,n.zh=n["zh-cn"]},function(e,t,a){"use strict";var n=function(){var e=1,t=/subsid=(\d+)/.exec(location.href);t&&(e=parseInt(t[1],10)+1);var a=function(t,a){var n=a||e;return t=/subsid=\d+/.test(t)?t.replace(/subsid=\d+/g,"subsid="+n):t+"&subsid="+n,a||e++,t};return a.get=function(){return e},a.bind=function(){var t=e++;return function(e){return a(e,t)}},a}();e.exports=n},function(e,t,a){"use strict";t.__esModule=!0,t.getScriptUrl=t.getScript=void 0,t.getScript=function n(e,t){var a=3,r=e.src,o=e.successCheck,i=e.success,c=e.error,u=e.crossOrigin,s=Boolean(e.inHead)?document.getElementsByTagName("head").item(0):document.getElementsByTagName("body").item(0),l=!1,d=document.createElement("script");function p(e){if(!l){var t=!1;e&&"type"in e&&(t="load"===e.type),"readyState"in this&&/^(loaded|complete)$/.test(this.readyState)&&(t=!0),t&&(!o||o()?(h(),l=!0,null===i||void 0===i||i()):f())}}function f(){l||(h(),l=!0,(t=t||1)>=a?null===c||void 0===c||c():n({src:r,successCheck:o,success:i,error:c},t+1))}function h(){try{s&&d&&s.removeChild(d)}catch(c){}}d.type="text/javascript",d.async=!0,d.src=r,u&&d.setAttribute("crossorigin",u),"onload"in d?d.onload=p:d.onreadystatechange=p,d.onerror=f,null===s||void 0===s||s.appendChild(d)},t.getScriptUrl=function(){try{throw new Error("check own domain")}catch(a){var e=null===a||void 0===a?void 0:a.stack;if(!e)return;var t=(""+e).match(/(https?:\/\/.*\.js)/);return{url:null===t||void 0===t?void 0:t[1],stack:e}}}},function(e,t,a){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{"default":e}};t.__esModule=!0,t.shrinkArraySizeFromRuisun=t.shrinkArraySize=t.extend=void 0;var r=n(a(3));t.extend=Object.assign||r["default"],t.shrinkArraySize=function(e,t,a){var n=e.length;if(n<=t)return e;var r=[];(a=a||{}).keepStart&&(t-=1,n-=1,r.push(e[0])),a.keepLast&&(n-=1,t-=1);for(var o=t/n,i=0,c=0;c<n;c++)(i+=o)>=1&&(r.push(e[c]),i-=1);return a.keepLast&&r.push(e[e.length-1]),r},t.shrinkArraySizeFromRuisun=function(e,t,a){var n=e.length;if(n<=t)return e;(a=a||{}).keepStart&&(n-=1),a.keepLast&&(n-=1,t-=1);for(var r=Math.floor(n/(t-1)),o=0,i=[];i.length<t;)i.push(e[o]),o+=r;return a.keepLast&&i.push(e[e.length-1]),i}},function(e,t,a){"use strict";t.__esModule=!0,t.isWebWorkerSupport=t.setWebworkerSupportCache=t.getWebworkerSupportCache=void 0;var n="captcha_webworker_supported";t.getWebworkerSupportCache=function(){try{return localStorage.getItem(n)}catch(e){}return!1},t.setWebworkerSupportCache=function(e){try{localStorage.setItem(n,e)}catch(t){}},t.isWebWorkerSupport=function(){var e=self||window;try{try{var t=void 0;try{t=new e.Blob([""])}catch(o){(t=new(e.BlobBuilder||e.WebKitBlobBuilder||e.MozBlobBuilder||e.MSBlobBuilder)).append(""),t=t.getBlob()}var a=e.URL||e.webkitURL,n=a.createObjectURL(t),r=new e.Worker(n);return a.revokeObjectURL(n),r}catch(o){return new e.Worker("data:application/javascript,".concat(encodeURIComponent("")))}}catch(o){return null}}},function(e,t,a){"use strict";t.__esModule=!0,t.langTransform=t.getWxLang=t.getBrowserLang=void 0;var n=a(0),r=a(7);function o(){var e=n.getQuery(),t=/(wehcat_real_lang|wechat_real_lang)=([a-zA-Z_-]+)/.exec(e);if(t)return t[2];for(var a=document.getElementsByTagName("script"),r=0;r<a.length;r++){var o=a[r].src;if(o&&/TCapIframeApi/i.test(o)){var i=/lang=([a-zA-Z_-]+)/.exec(o);if(i)return i[1].toLowerCase()}}return n.getQueryParam("wxLang")||""}t.getBrowserLang=function(){var e=navigator.languages?navigator.languages[0]:navigator.language||navigator.userLanguage||"";if(/MicroMessenger/.test(navigator.userAgent)){var t=o();t&&(e=t)}return e},t.getWxLang=o,t.langTransform=function(e){var t=e.toLowerCase().replace(/_/,"-"),a=t;return/-/.test(a)&&(a=a.split("-")[0]),r.all[t]?t:a}},function(module,exports,__webpack_require__){"use strict";var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};"object"!==("undefined"==typeof JSON?"undefined":_typeof(JSON))&&(JSON={}),function(){var rx_one=/^[\],:{}\s]*$/,rx_two=/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,rx_three=/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,rx_four=/(?:^|:|,)(?:\s*\[)+/g,rx_escapable=/[\\"\u0000-\u001f\u007f-\u009f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,rx_dangerous=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,gap,indent,meta,rep;function f(e){return e<10?"0"+e:e}function this_value(){return this.valueOf()}function quote(e){return rx_escapable.lastIndex=0,rx_escapable.test(e)?'"'+e.replace(rx_escapable,function(e){var t=meta[e];return"string"==typeof t?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+e+'"'}function str(e,t){var a,n,r,o,i,c=gap,u=t[e];switch(u&&"object"===(void 0===u?"undefined":_typeof(u))&&"function"==typeof u.toJSON&&(u=u.toJSON(e)),"function"==typeof rep&&(u=rep.call(t,e,u)),void 0===u?"undefined":_typeof(u)){case"string":return quote(u);case"number":return isFinite(u)?String(u):"null";case"boolean":case"null":return String(u);case"object":if(!u)return"null";if(gap+=indent,i=[],"[object Array]"===Object.prototype.toString.apply(u)){for(o=u.length,a=0;a<o;a+=1)i[a]=str(a,u)||"null";return r=0===i.length?"[]":gap?"[\n"+gap+i.join(",\n"+gap)+"\n"+c+"]":"["+i.join(",")+"]",gap=c,r}if(rep&&"object"===(void 0===rep?"undefined":_typeof(rep)))for(o=rep.length,a=0;a<o;a+=1)"string"==typeof rep[a]&&(r=str(n=rep[a],u))&&i.push(quote(n)+(gap?": ":":")+r);else for(n in u)Object.prototype.hasOwnProperty.call(u,n)&&(r=str(n,u))&&i.push(quote(n)+(gap?": ":":")+r);return r=0===i.length?"{}":gap?"{\n"+gap+i.join(",\n"+gap)+"\n"+c+"}":"{"+i.join(",")+"}",gap=c,r}}"function"!=typeof Date.prototype.toJSON&&(Date.prototype.toJSON=function(){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+f(this.getUTCMonth()+1)+"-"+f(this.getUTCDate())+"T"+f(this.getUTCHours())+":"+f(this.getUTCMinutes())+":"+f(this.getUTCSeconds())+"Z":null},Boolean.prototype.toJSON=this_value,Number.prototype.toJSON=this_value,String.prototype.toJSON=this_value),"function"!=typeof JSON.stringify&&(meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},JSON.stringify=function(e,t,a){var n;if(gap="",indent="","number"==typeof a)for(n=0;n<a;n+=1)indent+=" ";else"string"==typeof a&&(indent=a);if(rep=t,t&&"function"!=typeof t&&("object"!==(void 0===t?"undefined":_typeof(t))||"number"!=typeof t.length))throw new Error("JSON.stringify");return str("",{"":e})}),"function"!=typeof JSON.parse&&(JSON.parse=function(text,reviver){var j;function walk(e,t){var a,n,r=e[t];if(r&&"object"===(void 0===r?"undefined":_typeof(r)))for(a in r)Object.prototype.hasOwnProperty.call(r,a)&&((n=walk(r,a))!==undefined?r[a]=n:delete r[a]);return reviver.call(e,t,r)}if(text=String(text),rx_dangerous.lastIndex=0,rx_dangerous.test(text)&&(text=text.replace(rx_dangerous,function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})),rx_one.test(text.replace(rx_two,"@").replace(rx_three,"]").replace(rx_four,"")))return j=eval("("+text+")"),"function"==typeof reviver?walk({"":j},""):j;throw new SyntaxError("JSON.parse")})}()},function(e,t,a){"use strict";function n(e){if(Array.isArray(e)){for(var t=0,a=Array(e.length);t<e.length;t++)a[t]=e[t];return a}return Array.from(e)}var r,o,i=a(8),c={ERROR_TYPE_FRAMEJS_DOWNLOAD_FAIL:16,ERROR_TYPE_JSONP_PREHANDLE:17,ERROR_TYPE_FRAMEJS_CODE_ERROR:18,CALLBACK_NAME:19,IFRAME_CREATE_ERROR:25,IFRAME_POPUP_CREATE_ERROR:26,IFRAME_FULL_CREATE_ERROR:27,OTHER_DOMAIN_MONITOR:38,FRAME_JS_LOAD_TIMEOUT:40},u={ERROR_TYPE_FRAMEJS_DOWNLOAD_FAIL:"ERROR_TYPE_FRAMEJS_DOWNLOAD_FAIL",ERROR_TYPE_JSONP_PREHANDLE:"ERROR_TYPE_JSONP_PREHANDLE",ERROR_TYPE_FRAMEJS_CODE_ERROR:"ERROR_TYPE_FRAMEJS_CODE_ERROR",CALLBACK_NAME:"CALLBACK_NAME"},s=(r=document.referrer,o=location.href||document.URL,r=r.length>512?r.substr(0,512):r,o=o.length>1024?o.substr(0,1024):o,["referer="+encodeURIComponent(r),"href="+encodeURIComponent(o)].join("&")),l=void 0,d=[];function p(e,t,a){try{if(void 0===l)return void d.push([e,t,a]);if(l)return;(a=a||u[e]).length>1024&&a.substr(0,1024);var n=["type="+(e=c[e]),"appid="+t,"reason="+encodeURIComponent(a)],r=(window.AqSCodeCapDomain||"")+"/cap_monitor?"+s+"&"+n.join("&");(new Image).src=i(r)}catch(o){}}e.exports={type:c,send:p,perfectStack:function(e){var t="";e&&e.stack&&(t=e.stack.replace(/\n/gi,"").split(/\bat\b/).join("\n").replace(/\?[^:]+/gi,""));try{var a=e.toString();t.indexOf(a)<0&&(t=a+"@"+t)}catch(n){}return t},setGlobalStatus:function(e){if(!(l=e))for(var t=0;t<d.length;t++){var a=d[t];p.apply(undefined,n(a))}}}},function(module,exports,__webpack_require__){"use strict";var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};if(!0===window.__TencentCaptchaExists__)throw new Error("\u8bf7\u52ff\u591a\u6b21\u5f15\u7528\u817e\u8baf\u9a8c\u8bc1\u7801\u7684\u63a5\u5165js");window.__TencentCaptchaExists__=!0;var _require=__webpack_require__(6),getErrorRes=_require.getErrorRes,Executor=__webpack_require__(16),_require2=__webpack_require__(9),getScriptUrl=_require2.getScriptUrl,_require3=__webpack_require__(0),getHref=_require3.getHref;__webpack_require__(13);var domReady=__webpack_require__(17),btoa=window.btoa||__webpack_require__(18),extend=Object.assign||__webpack_require__(3),loadFrameJs=__webpack_require__(19),eventListener=__webpack_require__(20),isIEVar=__webpack_require__(21),isElement=__webpack_require__(22),getScriptDomain=__webpack_require__(23),scriptDomain=getScriptDomain()||"",isWechatDevtools=navigator.userAgent&&/wechatdevtools/.test(navigator.userAgent),isWindows=/windows/i.test(navigator.userAgent),isMobile=!isWindows&&("ontouchstart"in window||"ontouchstart"in document.createElement("div")||isWechatDevtools),capDomain="https://t.captcha.qq.com";capDomain||(capDomain=scriptDomain),window.TCaptchaGlobal=false,"https://t-captcha.gjacky.com"!==scriptDomain&&"https://sg.captcha.qcloud.com"!==scriptDomain||(window.TCaptchaGlobal=!0);var cdnDomain=window.TCaptchaGlobal?"https://global.captcha.gtimg.com":"https://captcha.gtimg.com/1";window.AqSCodeCapDomain=capDomain,window.AqSCodeCdnDomain=cdnDomain,window.TCaptchaPreload=!1;var grayscaleFrameJs="/tcaptcha-frame.7ddab543.js",capError=__webpack_require__(14),capObj=void 0,initQueue=[],initQueueCount=0,loadErrorTimeout=!1,defaultOpt={type:"popup",pos:isIEVar(6)?"absolute":"fixed",lang:2052,showHeader:isMobile,needFeedBack:true,themeColor:"",tcaptchaFlag:!0,gettype:"cap_union_prehandle",domain:capDomain,htdoc_path:capDomain,global:window.TCaptchaGlobal},noop=function(){},callbackWrap=function(e,t){return function(a){"object"===(void 0===a?"undefined":_typeof(a))&&(a=extend({bizState:e.options.bizState,appid:e.options.appid},a)),t&&t(a)}},executor=new Executor(function(){if(initQueue.length>0){for(var e=0;e<initQueue.length;e++)initQueue[e].instance.show();initQueue.length=0}},0),Captcha=function Captcha($btn,appid,_tcallback_,opts){var bizState=void 0;if(isElement($btn))if(appid&&"object"===(void 0===appid?"undefined":_typeof(appid)))opts=appid,appid=null,_tcallback_=null;else{if(appid=appid||$btn.getAttribute("data-appid"),!_tcallback_){var callbackName=$btn.getAttribute("data-cbfn");try{_tcallback_=eval("window."+callbackName)}catch(e){throw new Error("Lost `callback`")}}bizState=$btn.getAttribute("data-biz-state")}else{if("string"!=typeof $btn||"function"!=typeof appid)throw new Error("The parameter of the constructor of the Captcha is passed incorrectly, please refer to the documentation to see the specification of passing parameters");opts=_tcallback_,_tcallback_=appid,appid=$btn,$btn=null}opts=opts||{},opts.callback=_tcallback_||opts.callback,opts.start=opts.start||noop,opts.end=opts.end||noop;var readyCallback="function"==typeof opts.ready?opts.ready:noop;if(opts.ready=function(e){readyCallback.call(this,e)},opts.appid=appid||opts.appid,opts.bizState=bizState||opts.bizState,opts.fwidth=parseFloat(opts.fwidth)||0,opts.sdkOpts=opts.sdkOpts||null,opts.ele=$btn,this.options=opts,"function"!=typeof opts.callback)throw new Error("Lost `callback`");if(this.initOpts=extend({},defaultOpt,opts),this.langFun(),$btn){var that=this;eventListener.add($btn,"click",function(){that.show()})}this.checkInOwnDomain(appid),capError.setGlobalStatus(!0===opts.global)},showTimeout=null;Captcha.prototype={show:function(){var e=this;if(executor.isDone)this.__show__(),null!==showTimeout&&(clearTimeout(showTimeout),showTimeout=null);else{for(var t=0;t<initQueue.length;t++)if(initQueue[t].instance===this)return;var a=initQueueCount+=1;initQueue.push({instance:this,index:a}),null===showTimeout&&(showTimeout=setTimeout(function(){showTimeout=null;for(var t=0;t<initQueue.length;t++){if(initQueue[t].index===a)return initQueue.splice(t,1),e.initOpts.callback(getErrorRes("CAPTCHA_SHOW_TIMEOUT",e.initOpts.appid))}},2e4))}},__show__:function(){var e=this.initOpts;if("undefined"==typeof window.AqSCode)return loadErrorTimeout?(capError.send("FRAME_JS_LOAD_TIMEOUT","",grayscaleFrameJs),e.callback(getErrorRes("FRAMEJS_LOAD_TIMEOUT",e.appid))):(capError.send("ERROR_TYPE_FRAMEJS_DOWNLOAD_FAIL","",grayscaleFrameJs),e.callback(getErrorRes("FRAMEJS_LOAD_ERROR",e.appid)));this.destroy();var t=[];t.push("aid="+e.appid),t.push("protocol=https"),t.push("accver=1"),t.push("showtype="+e.type),t.push("ua="+encodeURIComponent(btoa((navigator.userAgent||"").replace(/[\u00ff-\uffff]+/g,"")))),t.push("noheader="+(""+e.showHeader=="false"?"1":"0"));var a=e.needFeedBack;"string"==typeof a&&t.push("fbUrl="+a),t.push("fb="+(a?"1":"0"));var n=e.enableDarkMode;"force"!==n&&(n=n?"1":"0");var r=e.enableAged;t.push("aged="+("force"===r?"1":"0")),t.push("enableAged="+(r?"1":"0")),t.push("enableDarkMode="+n),e.deviceID&&t.push("deviceID="+e.deviceID),e.sid&&t.push("sid="+e.sid),e.uid=""+(e.uin||""),t.push("grayscale=1"),e.grayscale=1,isMobile&&"point"!==e.type?(t.push("clientype=1"),e.clientype=1,e.forceLang&&t.push("lang="+e.forceLang),e.params="?"+t.join("&")):(t.push("clientype=2"),e.clientype=2,e.forceLang&&t.push("lang="+e.forceLang),e.params="?"+t.join("&"),e.s_type_suffix="?"+t.join("&"),e.src=capDomain+"/template/placeholder_v2.html"+e.s_type_suffix,e.s_type=capDomain+"/cap_union_prehandle"+e.s_type_suffix,e.slide_src=capDomain+"/template/new_slide_placeholder.html"+e.s_type_suffix),e.fb=""+e.needFeedBack=="false"?"0":"1",e.fbUrl="string"==typeof e.needFeedBack&&encodeURIComponent(e.needFeedBack),e.frameJs=grayscaleFrameJs;try{(capObj=new AqSCode(e)).listen(callbackWrap(this,e.callback),callbackWrap(this,e.ready)),capObj.create()}catch(o){capError.send("ERROR_TYPE_FRAMEJS_CODE_ERROR",e.appid,"entry create err: "+o.message)}},langFun:function(){if(this.initOpts.forceLang){var e={"zh-cn":"2052","zh-hk":"1028",en:"1033"},t=this.initOpts.forceLang;e[t]?(this.initOpts.lang=e[t],this.initOpts.forceLang=e[t],this.initOpts.forcestyle=1):this.initOpts.forceLang=""}},destroy:function(){capObj&&capObj.destroy&&capObj.destroy()},refresh:function(){capObj&&capObj.refresh&&capObj.refresh()},getTicket:function(){if(capObj&&capObj.getTicket){var e=null;return callbackWrap(this,function(t){e=t})(capObj.getTicket()),e}return null},checkInOwnDomain:function(e){try{var t=[/localhost:8302/,/captcha\.qq\.com/,/\.captcha\.qcloud\.com/,/captcha\.myqcloud\.com/,/captcha\.253\.com/,/t-captcha\.gjacky\.com/,/captcha\.gtimg\.com/],a=getScriptUrl();if(!a)return;for(var n=a.stack,r=!1,o=0;o<t.length;o++){var i=t[o];if(n.match(i)){r=!0;break}}r||capError.send("OTHER_DOMAIN_MONITOR",e,"href:"+getHref()+";stack:"+n)}catch(c){}}};var autoBindBtn=function(){var e=document.getElementById("TencentCaptcha");e&&new Captcha(e)},domReadyCb=function(){try{autoBindBtn()}catch(e){}};executor.exec(function(e){var t=!1;domReady(function(){t||(t=!0,domReadyCb(),e())})}),executor.exec(function(e){loadFrameJs(grayscaleFrameJs,cdnDomain,capDomain,function(){return"undefined"!=typeof window.AqSCode},function(){e()},function(t){loadErrorTimeout=!0===t,e()})}),window.TencentCaptcha=Captcha},function(e,t,a){"use strict";var n=function(e,t){this.i=0,this.doneCallback=e,this.isDone=!1,this.timeout=t||0;var a=this;this.immediateExec=setTimeout(function(){a.doneCheck()},0)};n.prototype={doneCheck:function(e,t){try{e&&e(t)}catch(a){}if(this.i--,this.i<=0)try{this.isDone=!0,this.doneCallback()}catch(a){}},exec:function(e,t,a){clearTimeout(this.immediateExec);var n=!1,r="",o=this;this.i++,this.i>0&&(this.isDone=!1);try{(r=e(function(e){n||(n=!0,setTimeout(function(){o.doneCheck(t,e)},0))}))&&(n=!0,setTimeout(function(){o.doneCheck(t,r)},0))}catch(i){}(a=a||this.timeout||0)>0&&setTimeout(function(){n||(n=!0,o.doneCheck(t,""))},a)}},e.exports=n},function(e,t,a){e.exports=function(e){var t,a=[],n=document,r=n.documentElement,o=r.doScroll,i=(o?/^loaded|^c/:/^loaded|c/).test(n.readyState);function c(e){for(i=1;e=a.shift();)e()}return n.addEventListener&&n.addEventListener("DOMContentLoaded",t=function(){n.removeEventListener("DOMContentLoaded",t,!1),c()},!1),o&&n.attachEvent("onreadystatechange",t=function(){/^c/.test(n.readyState)&&(n.detachEvent("onreadystatechange",t),c())}),e=o?function(t){self!=top?i?t():a.push(t):function(){try{r.doScroll("left")}catch(a){return setTimeout(function(){e(t)},50)}t()}()}:function(e){i?e():a.push(e)}}()},function(e,t,a){"use strict";var n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r=function(e){var t=[0,2,1][e.length%3],a=e.charCodeAt(0)<<16|(e.length>1?e.charCodeAt(1):0)<<8|(e.length>2?e.charCodeAt(2):0);return[n.charAt(a>>>18),n.charAt(a>>>12&63),t>=2?"=":n.charAt(a>>>6&63),t>=1?"=":n.charAt(63&a)].join("")};e.exports=function(e){return e.replace(/[\s\S]{1,3}/g,r)}},function(e,t,a){"use strict";var n=2,r=15e3;e.exports=function o(e,t,a,i,c,u){var s=arguments.length>6&&arguments[6]!==undefined?arguments[6]:0,l=!1,d=document.createElement("script");function p(e){l||(e&&"load"===e.type||/^(loaded|complete)$/.test(this.readyState))&&(i()?(l=!0,c&&c()):f())}d.type="text/javascript",d.async=!0,d.src=(s>0?a:t)+e,"onload"in d?d.onload=p:d.onreadystatechange=p;var f=function(r){l||(l=!0,document.getElementsByTagName("head").item(0).removeChild(d),s>=n?u&&u(r):o(e,t,a,i,c,u,s+1))};d.onerror=f,document.getElementsByTagName("head").item(0).appendChild(d),setTimeout(function(){f(!0)},r)}},function(e,t,a){"use strict";e.exports={add:function(e,t,a){e&&(e.addEventListener?e.addEventListener(t,a,!1):e.attachEvent?e.attachEvent("on"+t,a):e["on"+t]=a)},remove:function(e,t,a){e&&(e.removeEventListener?e.removeEventListener(t,a,!1):e.detachEvent?e.detachEvent("on"+t,a):e["on"+t]=null)}}},function(e,t,a){"use strict";e.exports=function(e){var t=document.createElement("b");return t.innerHTML="\x3c!--[if IE "+e+"]><i></i><![endif]--\x3e",t.getElementsByTagName("i")&&1===t.getElementsByTagName("i").length}},function(e,t,a){"use strict";var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};e.exports=function(e){try{return e instanceof HTMLElement}catch(t){return"object"===(void 0===e?"undefined":n(e))&&1===e.nodeType&&"object"===n(e.style)&&"object"===n(e.ownerDocument)}}},function(e,t,a){"use strict";var n=a(2).getHref,r=function(e){var t=e.split("?");if(2!==t.length)return{};for(var a=t[1]&&t[1].split("&"),n={},r=0;r<a.length;r++){var o=a[r].split("=");o[1]&&(n[o[0]]=decodeURIComponent(o[1]))}return n},o=function(){for(var e=document.getElementsByTagName("script"),t=0;t<e.length;t++){var a=e[t];if(a.src.match(/captcha\.js/i))return a}};e.exports=function(){try{var e=document.getElementById("CaptchaScript")||o()||"";if(!e)return"";var t=e.src,a=r(t).domain;if(a)return a;var i=/(https?\:\/\/[0-9a-zA-Z-:\.]+)\//i,c=t.match(i)||n().match(i);return c?c[1]:""}catch(u){return""}}}]);