* {
    padding: 0;
    margin: 0;
}

html, body, ul, li, ol, dl, dd, dt, p, h1, h2, h3, h4, h5, h6, form, fieldset, legend, img {
    margin: 0;
    padding: 0;
}

div, input {
    box-sizing: border-box;
}

fieldset, img {
    border: none;
}

img {
    display: block;
}

address, caption, cite, code, dfn, th, var {
    font-style: normal;
    font-weight: normal;
}

ul, ol {
    list-style: none;
}

input {
    padding-top: 0;
    padding-bottom: 0;
    font-family: "微软雅黑", "宋体";
}

input::-moz-focus-inner {
    border: none;
    padding: 0;
}

select, input {
    vertical-align: middle;
}

select, input, textarea {
    font-size: 12px;
    margin: 0;
}

input[type="text"], input[type="password"], textarea {
    outline-style: none;
    -webkit-appearance: none;
    /* border: none; */
}

textarea {
    resize: none;
}

table {
    border-collapse: collapse;
}

body {
    color: #333;
    font-size: 12px;
    font-family: "Microsoft YaHei", \5FAE\8F6F\96C5\9ED1, Arial, Helvetica, sans-serif;
    background-color: #F4F5F7;
    -moz-user-select:none;/*火狐*/
    -webkit-user-select:none;/*webkit浏览器*/
    -ms-user-select:none;/*IE10*/
    -khtml-user-select:none;/*早期浏览器*/
    user-select:none;
}

.clearfix:after {
    content: ".";
    display: block;
    height: 0;
    visibility: hidden;
    clear: both;
}

.clearfix {
    zoom: 1;
}

.clearit {
    clear: both;
    height: 0;
    font-size: 0;
    overflow: hidden;
}

a {
    color: #666;
    text-decoration: none;
}

a:visited {
    color: #666;
}

a:hover, a:active, a:focus {
    color: #ff8400;
    text-decoration: underline;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

.posr {
    position: relative;
}

.posa {
    position: absolute;
}
