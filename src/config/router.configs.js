/* eslint-disable */
/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-23 11:22:03
 * @LastEditors: jingrou
 * @LastEditTime: 2023-06-10 17:04:28
 */
import { BasicLayout, UserLayout, ColumnsLayout } from "@/layouts/index.js";

// import CodeFrom from '@/views/login/codeFrom'
// import AccountFrom from '@/views/login/accountFrom'

// hideInMenu 控制是否显示在菜单里
// hideChildrenInMenu 控制是否显示子集路由
export const asyncRouterMap = [
    {
        // component:  BasicLayout,
        component: ColumnsLayout || BasicLayout,
        meta: {
            title: "首页",
        },
        redirect: "/dashboard",
        children: [
            {
                path: "/dashboard",
                name: "dashboard",
                meta: {
                    title: "首页",
                    icon: "icon-banjishouye",
                },
                hideChildrenInMenu: true,
                component: () => import("@/views/home/<USER>"),
                children: [
                    {
                        path: "/dashboard/componentLibrary",
                        name: "componentLibrary",
                        hideInMenu: true,
                        meta: {
                            title: "组件库",
                        },
                        component: () =>
                            import("@/views/home/<USER>/index.vue"),
                    },
                    {
                        path: "/dashboard/layoutManage",
                        name: "layoutManage",
                        hideInMenu: true,
                        meta: {
                            title: "布局管理",
                        },
                        component: () =>
                            import("@/views/home/<USER>/index.vue"),
                    },
                ],
            },
            {
                path: "/director",
                name: "director",
                meta: {
                    title: "教务管理",
                    icon: "icon-wodexuesheng1",
                },
                hideChildrenInMenu: false,
                component: () => import("@/views/director/index.vue"),
                redirect: "/director/home",
                children: [
                    {
                        path: "/director/home",
                        name: "directorHome",
                        hideInMenu: true,
                        meta: {
                            title: "教务首页",
                            icon: "icon-banjishouye",
                        },
                        component: () =>
                            import("@/views/director/page/homePage/index.vue"),
                    },
                    {
                        meta: {
                            title: "组件库",
                        },
                        path: "/director/home/<USER>",
                        name: "directorCompStore",
                        hideInMenu: true,
                        component: () =>
                            import("@/views/director/page/compStore/index.vue"),
                    },
                    {
                        meta: {
                            title: "布局管理",
                        },
                        path: "/director/home/<USER>",
                        name: "directorlayout",
                        hideInMenu: true,
                        component: () =>
                            import(
                                "@/views/director/page/layoutManage/index.vue"
                            ),
                    },
                ],
            },
            {
                path: "/classManager",
                name: "classManager",
                meta: {
                    title: "班级管理",
                    icon: "icon-wodexuesheng1",
                },
                // hideChildrenInMenu: true,
                component: () => import("@/views/classManager/home/<USER>"),
                children: [
                    {
                        path: "/classManager/classManagerHome",
                        name: "classManagerHome",
                        meta: {
                            title: "班级首页",
                            icon: "icon-banjishouye",
                        },
                        component: () =>
                            import("@/views/classManager/home/<USER>"),
                    },
                    {
                        meta: {
                            title: "班级信息",
                            icon: "icon-banjixinxi",
                        },
                        path: "/classManager/clbumInfo",
                        name: "clbumInfo",
                        component: () =>
                            import(
                                "@/views/classManager/page/clbumInfo/index.vue"
                            ),
                    },
                    {
                        meta: {
                            title: "班级德育",
                            icon: "icon-banjideyu",
                        },
                        path: "/classManager/classMoral",
                        name: "classMoral",
                        component: () =>
                            import(
                                "@/views/classManager/page/classMoral/index.vue"
                            ),
                    },
                    {
                        meta: {
                            title: "班级通知",
                            icon: "icon-banjitongzhi",
                        },
                        path: "/classManager/classInform",
                        name: "classInform",
                        component: () =>
                            import(
                                "@/views/classManager/page/classInform/index.vue"
                            ),
                        children: [
                            {
                                path: "/classManager/classInform/schoolMessage",
                                name: "schoolMessage",
                                meta: {
                                    title: "学校通知",
                                },
                                component: () =>
                                    import(
                                        "@/views/classManager/page/classInform/schoolMessage/index.vue"
                                    ),
                            },
                            {
                                path: "/classManager/classInform/classMessage",
                                name: "classMessage",
                                meta: {
                                    title: "班级通知",
                                },
                                component: () =>
                                    import(
                                        "@/views/classManager/page/classInform/classMessage/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        meta: {
                            title: "学生考勤",
                            icon: "icon-xueshengkaoqin",
                        },
                        path: "/classManager/studentChecking",
                        name: "studentChecking",
                        component: () =>
                            import(
                                "@/views/classManager/page/studentChecking/index.vue"
                            ),
                        children: [
                            {
                                path: "/classManager/studentChecking/statisticsDbinfo",
                                name: "statisticsDbinfo",
                                meta: {
                                    title: "考勤统计",
                                },
                                component: () =>
                                    import(
                                        "@/views/classManager/page/studentChecking/statisticsDbinfo/index.vue"
                                    ),
                            },
                            {
                                path: "/classManager/studentChecking/incident",
                                name: "incident",
                                meta: {
                                    title: "事件考勤",
                                },
                                component: () =>
                                    import(
                                        "@/views/classManager/page/studentChecking/incident/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        meta: {
                            title: "班级课表",
                            icon: "icon-bjkb12",
                        },
                        path: "/classManager/schoolTimetable",
                        name: "schoolTimetable",
                        component: () =>
                            import(
                                "@/views/classManager/page/schoolTimetable/index.vue"
                            ),
                    },
                    {
                        meta: {
                            title: "我的学生",
                            icon: "icon-wodexuesheng",
                        },
                        path: "/classManager/myStudent",
                        name: "myStudent",
                        component: () =>
                            import(
                                "@/views/classManager/page/myStudent/index.vue"
                            ),
                        children: [
                            {
                                path: "/classManager/myStudent/studentManage",
                                name: "studentManage",
                                meta: {
                                    title: "学生管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/classManager/page/myStudent/studentManage/index.vue"
                                    ),
                            },
                            {
                                path: "/classManager/myStudent/committee",
                                name: "committee",
                                meta: {
                                    title: "班委管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/classManager/page/myStudent/committee/index.vue"
                                    ),
                            },
                            {
                                path: "/classManager/myStudent/grouping",
                                name: "grouping",
                                meta: {
                                    title: "分组管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/classManager/page/myStudent/grouping/index.vue"
                                    ),
                            },
                            {
                                path: "/classManager/myStudent/humanFace",
                                name: "humanFace",
                                meta: {
                                    title: "人脸库",
                                },
                                component: () =>
                                    import(
                                        "@/views/classManager/page/myStudent/humanFace/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        meta: {
                            title: "班级相册",
                            icon: "icon-banjixiangce",
                        },
                        path: "/classManager/photoAlbum",
                        name: "photoAlbum",
                        component: () =>
                            import(
                                "@/views/classManager/page/photoAlbum/index.vue"
                            ),
                    },
                    {
                        meta: {
                            title: "班级视频",
                            icon: "icon-banjishipin",
                        },
                        path: "/classManager/classVideo",
                        name: "classVideo",
                        component: () =>
                            import(
                                "@/views/classManager/page/classVideo/index.vue"
                            ),
                    },
                    {
                        meta: {
                            title: "班级荣誉",
                            icon: "icon-banjirongyu",
                        },
                        path: "/classManager/classHonor",
                        name: "classHonor",
                        component: () =>
                            import(
                                "@/views/classManager/page/classHonor/index.vue"
                            ),
                        children: [
                            {
                                path: "/classManager/classHonor/photoWall",
                                name: "photoWall",
                                meta: {
                                    title: "荣誉照片墙",
                                },
                                component: () =>
                                    import(
                                        "@/views/classManager/page/classHonor/photoWall/index.vue"
                                    ),
                            },
                            {
                                path: "/classManager/classHonor/promulgate",
                                name: "promulgate",
                                meta: {
                                    title: "颁发荣誉",
                                },
                                component: () =>
                                    import(
                                        "@/views/classManager/page/classHonor/promulgate/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        meta: {
                            title: "值日生",
                            icon: "icon-zhirisheng",
                        },
                        path: "/classManager/studentOnDuty",
                        name: "studentOnDuty",
                        component: () =>
                            import(
                                "@/views/classManager/page/studentOnDuty/index.vue"
                            ),
                        children: [
                            {
                                path: "/classManager/studentOnDuty/DutySet",
                                name: "DutySet",
                                meta: {
                                    title: "值日生设置",
                                },
                                component: () =>
                                    import(
                                        "@/views/classManager/page/studentOnDuty/DutySet/index.vue"
                                    ),
                            },
                            // {
                            //     path: "/classManager/studentOnDuty/DutyItem",
                            //     name: "DutyItem",
                            //     meta: {
                            //         title: "项目设置",
                            //     },
                            //     component: () =>
                            //         import(
                            //             "@/views/classManager/page/studentOnDuty/DutyItem/index.vue"
                            //         ),
                            // },
                        ],
                    },
                    {
                        meta: {
                            title: "倒计时",
                            icon: "icon-daojishi",
                        },
                        path: "/classManager/classCountDown",
                        name: "classCountDown",
                        component: () =>
                            import(
                                "@/views/classManager/page/countDown/index.vue"
                            ),
                        children: [
                            {
                                path: "/classManager/classCountDown/classLevel",
                                name: "classLevel",
                                meta: {
                                    title: "班级倒计时",
                                },
                                component: () =>
                                    import(
                                        "@/views/classManager/page/countDown/classLevel/index.vue"
                                    ),
                            },
                            {
                                path: "/classManager/classCountDown/schoolLevel",
                                name: "schoolLevel",
                                meta: {
                                    title: "校级倒计时",
                                },
                                component: () =>
                                    import(
                                        "@/views/classManager/page/countDown/schoolLevel/index.vue"
                                    ),
                            },
                        ],
                    },

                    {
                        meta: {
                            title: "学生作品",
                            icon: "icon-banjideyu",
                        },
                        path: "/classManager/studentWorks",
                        name: "studentWorks",
                        component: () =>
                            import(
                                "@/views/classManager/page/studentWorks/index.vue"
                            ),
                    },
                ],
            },
            {
                path: "/teachManager",
                name: "teachManager",
                meta: {
                    title: "任教管理",
                    icon: "icon-renjiaoguanli",
                },
                component: () => import("@/views/teachManager/index.vue"),
                children: [
                    {
                        path: "/teachManager/home",
                        name: "teachManagerHome",
                        meta: {
                            title: "任教首页",
                            icon: "icon-banjishouye",
                        },
                        component: () =>
                            import("@/views/teachManager/home/<USER>"),
                        children: [
                            {
                                path: "/teachManager/page/homePage",
                                name: "teachManagerHomePage",
                                meta: {
                                    title: "任教",
                                },
                                component: () =>
                                    import(
                                        "@/views/teachManager/page/homePage/index.vue"
                                    ),
                            },
                            {
                                meta: {
                                    title: "组件库",
                                },
                                path: "/teachManager/page/compStore",
                                name: "teachManagerCompStore",
                                component: () =>
                                    import(
                                        "@/views/teachManager/page/compStore/index.vue"
                                    ),
                            },
                            {
                                meta: {
                                    title: "布局管理",
                                },
                                path: "/teachManager/page/layout",
                                name: "teachManagerLayout",
                                component: () =>
                                    import(
                                        "@/views/teachManager/page/layoutManage/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        meta: {
                            title: "我的课表",
                            icon: "icon-bjkb12",
                        },
                        path: "/teachManager/MyTable",
                        name: "teMyTable",
                        component: () =>
                            import(
                                "@/views/teachManager/page/teMyTable/index.vue"
                            ),
                    },
                    {
                        meta: {
                            title: "课程考勤",
                            icon: "icon-xueshengkaoqin",
                        },
                        path: "/teachManager/check",
                        name: "teCheck",
                        component: () =>
                            import(
                                "@/views/teachManager/page/teCheck/index.vue"
                            ),
                    },
                    {
                        meta: {
                            title: "作业管理",
                            icon: "icon-zuoyeguanli",
                        },
                        path: "/teachManager/job",
                        name: "teJob",
                        component: () =>
                            import("@/views/teachManager/page/teJob/index.vue"),
                    },
                    {
                        meta: {
                            title: "考试管理",
                            icon: "icon-kaoshiguanli",
                        },
                        path: "/teachManager/testAdmin",
                        name: "testAdmin",
                        component: () =>
                            import(
                                "@/views/teachManager/page/testAdmin/index.vue"
                            ),
                    },
                    {
                        meta: {
                            title: "我的学生",
                            icon: "icon-wodexuesheng",
                        },
                        path: "/teachManager/myStudent",
                        name: "teStudentn",
                        component: () =>
                            import(
                                "@/views/teachManager/page/teStudentn/index.vue"
                            ),
                        children: [
                            {
                                path: "/teachManager/teStudentn/admin",
                                name: "teStudentnAdmin",
                                meta: {
                                    title: "学生管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/teachManager/page/teStudentn/teStudentnAdmin/index.vue"
                                    ),
                            },
                            {
                                path: "/teachManager/teStudentn/teCouncil",
                                name: "teCouncil",
                                meta: {
                                    title: "班委管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/teachManager/page/teStudentn/teCouncil/index.vue"
                                    ),
                            },
                            {
                                path: "/teachManager/teStudentn/teGroup",
                                name: "teGroup",
                                meta: {
                                    title: "分组管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/teachManager/page/teStudentn/teGroup/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        meta: {
                            title: "打卡任务",
                            icon: "icon-dakarenwu",
                        },
                        path: "/teachManager/punch",
                        name: "tePunch",
                        component: () =>
                            import(
                                "@/views/teachManager/page/tePunch/index.vue"
                            ),
                    },
                    // {
                    //     meta: {
                    //         title: "调课管理",
                    //         icon: "icon-tiaokeguanli",
                    //     },
                    //     path: "/teachManager/adjust",
                    //     name: "teAdjust",
                    //     component: () =>
                    //         import(
                    //             "@/views/teachManager/page/teAdjust/index.vue"
                    //         ),
                    // },
                    // {
                    //     meta: {
                    //         title: "备课管理",
                    //         icon: "icon-beikeguanli",
                    //     },
                    //     path: "/teachManager/setout",
                    //     name: "teSetout",
                    //     component: () =>
                    //         import(
                    //             "@/views/teachManager/page/setout/index.vue"
                    //         ),
                    // },
                    // {
                    //     meta: {
                    //         title: "成绩管理",
                    //         icon: "icon-chengjiguanli",
                    //     },
                    //     path: "/teachManager/result",
                    //     name: "teResult",
                    //     component: () =>
                    //         import(
                    //             "@/views/teachManager/page/result/index.vue"
                    //         ),
                    // },
                    // {
                    //     meta: {
                    //         title: "学生德育",
                    //         icon: "icon-banjideyu",
                    //     },
                    //     path: "/teachManager/moral",
                    //     name: "teMoral",
                    //     component: () =>
                    //         import(
                    //             "@/views/teachManager/page/teMoral/index.vue"
                    //         ),
                    // },
                ],
            },
            {
                path: "/framework",
                name: "framework",
                meta: {
                    title: "组织管理",
                    icon: "icon-wodexuesheng1",
                },
                component: () => import("@/views/framework/index.vue"),
                redirect: "/framework/workers",
                children: [
                    {
                        path: "/framework/workers",
                        name: "workers",
                        meta: {
                            title: "教职工管理",
                            icon: "icon-jiaozhigongguanli",
                        },
                        component: () =>
                            import("@/views/framework/workers/index.vue"),
                    },
                    {
                        path: "/framework/schoolRoll",
                        name: "schoolRoll",
                        meta: {
                            title: "学籍管理",
                            icon: "icon-xuejiguanli",
                        },
                        component: () =>
                            import("@/views/framework/schoolRoll/index.vue"),
                    },
                    {
                        path: "/framework/role",
                        name: "role",
                        meta: {
                            title: "角色管理",
                            icon: "icon-jiaoseguanli",
                        },
                        component: () =>
                            import("@/views/framework/role/index.vue"),
                    },
                    {
                        path: "/framework/user",
                        name: "user",
                        meta: {
                            title: "用户管理",
                            icon: "icon-yonghuguanli",
                        },
                        hideChildrenInMenu: true,
                        redirect: "/framework/user/student",
                        component: () =>
                            import("@/views/framework/user/index.vue"),
                        children: [
                            {
                                path: "/framework/user/student",
                                name: "userListStudent",
                                meta: {
                                    title: "学生",
                                },
                                component: () =>
                                    import(
                                        "@/views/framework/user/student/index.vue"
                                    ),
                            },
                            {
                                path: "/framework/user/eltern",
                                name: "userListEltern",
                                meta: {
                                    title: "家长",
                                },
                                component: () =>
                                    import(
                                        "@/views/framework/user/eltern/index.vue"
                                    ),
                            },
                            {
                                path: "/framework/user/teacher",
                                name: "userListTeacher",
                                meta: {
                                    title: "教职工",
                                },
                                component: () =>
                                    import(
                                        "@/views/framework/user/teacher/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/framework/outsiders",
                        name: "outsiders",
                        meta: {
                            title: "外部人员",
                            icon: "icon-waiburenyuan",
                        },
                        hideChildrenInMenu: true,

                        // redirect: '/framework/outsiders',
                        component: () =>
                            import("@/views/framework/outsiders/index.vue"),
                        children: [],
                    },
                    {
                        path: "/framework/historicalUser",
                        name: "historicalUser",
                        meta: {
                            title: "历史成员",
                            icon: "icon-lishichengyuan",
                        },
                        hideChildrenInMenu: true,
                        redirect: "/framework/historicalUser/student",
                        component: () =>
                            import(
                                "@/views/framework/historicalUser/index.vue"
                            ),
                        children: [
                            {
                                path: "/framework/historicalUser/student",
                                name: "historicalStudent",
                                meta: {
                                    title: "学生",
                                },
                                component: () =>
                                    import(
                                        "@/views/framework/historicalUser/components/student/index.vue"
                                    ),
                            },
                            {
                                path: "/framework/historicalUser/eltern",
                                name: "historicalEltern",
                                meta: {
                                    title: "家长",
                                },
                                component: () =>
                                    import(
                                        "@/views/framework/historicalUser/components/eltern/index.vue"
                                    ),
                            },
                            {
                                path: "/framework/historicalUser/teacher",
                                name: "historicalTeacher",
                                meta: {
                                    title: "教职工",
                                },
                                component: () =>
                                    import(
                                        "@/views/framework/historicalUser/components/teacher/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/framework/basicSetting",
                        name: "basicSetting",
                        meta: {
                            title: "基础设置",
                            icon: "icon-waiburenyuan",
                        },
                        hideChildrenInMenu: true,

                        // redirect: '/framework/outsiders',
                        component: () =>
                            import("@/views/framework/basicSetting/index.vue"),
                        children: [],
                    },
                ],
            },
            {
                path: "/basicManagement",
                name: "basicManagement",
                meta: {
                    title: "基础管理",
                    icon: "icon-zhirisheng",
                },
                component: () => import("@/views/basicManagement/index.vue"),
                redirect: "/basicManagement/schoolIntroduction",
                children: [
                    {
                        path: "/basicManagement/schoolIntroduction",
                        name: "schoolIntroduction",
                        meta: {
                            title: "学校介绍",
                            icon: "icon-xuexiaojieshao",
                        },
                        component: () =>
                            import(
                                "@/views/basicManagement/schoolIntroduction/index.vue"
                            ),
                    },
                    {
                        path: "/basicManagement/subjectInformation",
                        name: "subjectInformation",
                        meta: {
                            title: "科目信息",
                            icon: "icon-kemuxinxi",
                        },
                        component: () =>
                            import(
                                "@/views/basicManagement/subjectInformation/index.vue"
                            ),
                    },
                    {
                        path: "/basicManagement/academicYearSemester",
                        name: "academicYearSemester",
                        meta: {
                            title: "学年学期",
                            icon: "icon-icon-xuenxq2",
                        },
                        component: () =>
                            import(
                                "@/views/basicManagement/academicYearSemester/index.vue"
                            ),
                    },
                    {
                        path: "/basicManagement/schedule",
                        name: "schedule",
                        meta: {
                            title: "作息表",
                            icon: "icon-zuoxibiao",
                        },
                        component: () =>
                            import(
                                "@/views/basicManagement/schedule/index.vue"
                            ),
                    },
                    {
                        path: "/basicManagement/classSchedule",
                        name: "classSchedule",
                        meta: {
                            title: "课程表",
                            icon: "icon-kechengbiao",
                        },
                        component: () =>
                            import(
                                "@/views/basicManagement/classSchedule/index.vue"
                            ),
                    },
                    {
                        path: "/basicManagement/site",
                        name: "site",
                        meta: {
                            title: "场地",
                            icon: "icon-icon-changde",
                        },
                        redirect: "/basicManagement/site/field",
                        hideChildrenInMenu: true,
                        component: () =>
                            import("@/views/basicManagement/site/index.vue"),
                        children: [
                            {
                                path: "/basicManagement/site/field",
                                name: "siteField",
                                hideInMenu: true,
                                meta: {
                                    title: "场地列表",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/site/field/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/site/siteType",
                                name: "siteType",
                                hideInMenu: true,
                                meta: {
                                    title: "场地类型",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/site/siteType/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/site/sceneType",
                                name: "sceneType",
                                hideInMenu: true,
                                meta: {
                                    title: "场景管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/site/sceneType/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/site/siteEquipment",
                                name: "siteEquipment",
                                hideInMenu: true,
                                meta: {
                                    title: "场地设备",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/site/siteEquipment/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/site/addEditSite",
                                name: "siteCreate",
                                hideInMenu: true,
                                meta: {
                                    title: "快速创建",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/site/addEditSite/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/site/particulars",
                                name: "siteParticulars",
                                hideInMenu: true,
                                meta: {
                                    title: "场地详情",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/site/particulars/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/basicManagement/faceLibrary",
                        name: "faceLibrary",
                        meta: {
                            title: "人脸库",
                            icon: "icon-renlianku",
                        },
                        hideChildrenInMenu: true,
                        redirect: "/basicManagement/faceLibrary/student",
                        component: () =>
                            import(
                                "@/views/basicManagement/faceLibrary/index.vue"
                            ),
                        children: [
                            {
                                path: "/basicManagement/faceLibrary/student",
                                name: "faceLibraryStudent",
                                meta: {
                                    title: "学生组",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/faceLibrary/base/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/faceLibrary/teacher",
                                name: "faceLibraryTeacher",
                                meta: {
                                    title: "老师组",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/faceLibrary/base/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/faceLibrary/outsiders",
                                name: "faceLibraryOutsiders",
                                meta: {
                                    title: "外部人员",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/faceLibrary/outsiders/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/faceLibrary/custom",
                                name: "faceLibraryCustom",
                                meta: {
                                    title: "自定义组",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/faceLibrary/base/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/faceLibrary/sync",
                                name: "faceLibrarySync",
                                meta: {
                                    title: "人脸同步",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/faceLibrary/sync/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/basicManagement/deviceManagement",
                        name: "deviceManagement",
                        meta: {
                            title: "设备管理",
                            icon: "icon-shebeiguanli",
                        },
                        hideChildrenInMenu: true,
                        // redirect:
                        //     '/basicManagement/deviceManagement/faceMachine',
                        component: () =>
                            import(
                                "@/views/basicManagement/deviceManagement/index.vue"
                            ),
                        children: [
                            {
                                path: "/basicManagement/deviceManagement/faceMachine",
                                name: "managementFaceMachine",
                                meta: {
                                    title: "人脸机",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/faceMachine/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/deviceManagement/classCard",
                                name: "managementClassCard",
                                meta: {
                                    title: "班牌",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/classCard/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/deviceManagement/attendanceMachine",
                                name: "managementAttendance",
                                meta: {
                                    title: "考勤机",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/attendanceMachine/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/deviceManagement/allInOne",
                                name: "managementAllInOne",
                                meta: {
                                    title: "一体机",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/allInOne/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/deviceManagement/borrowAndReturnBooks",
                                name: "borrowAndReturnBooks",
                                meta: {
                                    title: "自助借还书机",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/borrowAndReturnBooks/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/deviceManagement/libStaffWorkstation",
                                name: "libStaffWorkstation",
                                meta: {
                                    title: "馆员工作站",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/libStaffWorkstation/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/deviceManagement/libInformationKiosk",
                                name: "libInformationKiosk",
                                meta: {
                                    title: "查询机",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/libInformationKiosk/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/deviceManagement/libInventoryCart",
                                name: "libInventoryCart",
                                meta: {
                                    title: "盘点车",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/libInventoryCart/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/deviceManagement/securityGate",
                                name: "securityGate",
                                meta: {
                                    title: "安全门",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/securityGate/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/deviceManagement/visitorMachine",
                                name: "visitorMachine",
                                meta: {
                                    title: "访客机",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/visitorMachine/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/deviceManagement/entranceGuardMachine",
                                name: "entranceGuardMachine",
                                meta: {
                                    title: "门禁机",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/accessControl/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/deviceManagement/meetingMachine",
                                name: "meetingMachine",
                                meta: {
                                    title: "会议机",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/conferenceSystem/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/deviceManagement/microLibrary",
                                name: "microLibrary",
                                meta: {
                                    title: "微型图书馆",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/microLibrary/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/deviceManagement/educationExchange",
                                name: "educationExchange",
                                meta: {
                                    title: "德育兑换机",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/educationExchange/index.vue"
                                    ),
                            },
                            {
                                path: "/basicManagement/deviceManagement/libHandInventoryCart",
                                name: "libHandInventoryCart",
                                meta: {
                                    title: "手持盘点仪",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/libHandInventoryCart/index.vue"
                                    ),
                            },

                            {
                                path: "/basicManagement/deviceManagement/vms",
                                name: "vms",
                                meta: {
                                    title: "vms",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/vms/index.vue"
                                    ),
                            },

                            {
                                path: "/basicManagement/deviceManagement/paintedScreens",
                                name: "paintedScreens",
                                meta: {
                                    title: "画屏管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/paintedScreens/index.vue"
                                    ),
                            },

                            {
                                path: "/basicManagement/deviceManagement/stealthSecurityGate",
                                name: "stealthSecurityGate",
                                meta: {
                                    title: "隐形门禁",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/stealthSecurityGate/index.vue"
                                    ),
                            },
                            

                            {
                                path: "/basicManagement/deviceManagement/canteenMachine",
                                name: "canteenMachine",
                                meta: {
                                    title: "智慧点餐系统",
                                },
                                component: () =>
                                    import(
                                        "@/views/basicManagement/deviceManagement/canteenMachine/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/basicManagement/loginLog",
                        name: "loginLog",
                        meta: {
                            title: "系统日志",
                            icon: "icon-xitongrizhi",
                        },
                        hideChildrenInMenu: true,
                        component: () =>
                            import(
                                "@/views/basicManagement/loginLog/index.vue"
                            ),
                    },
                ],
            },
            {
                path: "/appModel",
                name: "appModel",
                meta: {
                    title: "应用中心",
                    icon: "icon-yingyongzhongxin",
                },
                hideChildrenInMenu: false,
                component: () => import("@/views/appModel/index.vue"),
                // redirect: '/appModel/list',
                children: [
                    {
                        path: "/appModel/list",
                        name: "appModelList",
                        meta: {
                            title: "应用中心",
                        },
                        hideInMenu: true,
                        component: () =>
                            import("@/views/appModel/list/index.vue"),
                    },
                    // {
                    //     path: "/appModel/:name",
                    //     name: "appThirdParty",
                    //     hideInMenu: true,
                    //     meta: { title: "应用" },
                    //     component: () =>
                    //         import("@/views/appModel/thirdParty/index.vue"),
                    // },

                    {
                        path: "/appModel/oaApproveAdmin",
                        name: "oaApproveAdmin",
                        hideInMenu: true,
                        hideChildrenInMenu: true,
                        meta: { title: "审批流" },
                        component: () =>
                            import("@/views/appModel/oaApproveAdmin/index.vue"),
                    },
                    {
                        path: "/appModel/approveManage",
                        name: "approveManage",
                        hideInMenu: true,
                        hideChildrenInMenu: true,
                        meta: {
                            title: "审批管理",
                        },
                        redirect: "/appModel/approve",
                        component: () =>
                            import("@/views/appModel/views/approve/index.vue"),
                        children: [
                            {
                                path: "/appModel/approve",
                                name: "approveApprove",
                                hideInMenu: true,
                                meta: {
                                    title: "审批列表",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/views/approve/ApprovalManagement/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/approveRecord",
                                name: "approveRecord",
                                hideInMenu: true,
                                meta: {
                                    title: "审批记录",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/views/approve/ApprovalRecord/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/approveEdit",
                                name: "approveEdit",
                                hideInMenu: true,
                                meta: {
                                    title: "编辑",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/views/approve/EditApproval/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/appModel/notice",
                        name: "notice",
                        hideInMenu: true,
                        meta: {
                            title: "信息发布",
                        },
                        component: () =>
                            import("@/views/appModel/views/notice/index.vue"),
                        children: [
                            {
                                path: "/appModel/notice/addRelease",
                                name: "addRelease",
                                hideInMenu: true,
                                meta: {
                                    title: "新增发布",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/views/notice/addRelease/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/notice/myReceive",
                                name: "myReceive",
                                hideInMenu: true,
                                meta: {
                                    title: "我收到的",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/views/notice/myReceive/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/notice/myPublish",
                                name: "myPublish",
                                hideInMenu: true,
                                meta: {
                                    title: "我发布的",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/views/notice/myPublish/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/notice/allPublish",
                                name: "allPublish",
                                hideInMenu: true,
                                meta: {
                                    title: "所有发布",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/views/notice/allPublish/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/notice/messTemplate",
                                name: "messTemplate",
                                hideInMenu: true,
                                meta: {
                                    title: "资源模版",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/views/notice/messTemplate/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/notice/messageReview",
                                name: "messageReview",
                                hideInMenu: true,
                                meta: {
                                    title: "信发审核",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/views/notice/messageReview/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/appModel/questionnaire",
                        name: "questionnaire",
                        hideInMenu: true,
                        meta: {
                            title: "问卷系统",
                        },
                        component: () =>
                            import(
                                "@/views/appModel/views/questionnaire/index.vue"
                            ),
                        redirect: "/appModel/questionnaire/escalationDetails",
                        children: [
                            {
                                path: "/appModel/questionnaire/escalationDetails",
                                name: "escalationDetails",
                                hideInMenu: true,
                                meta: {
                                    title: "上报信息",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/views/questionnaire/escalationDetails/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/questionnaire/escalationForm",
                                name: "escalationForm",
                                hideInMenu: true,
                                meta: {
                                    title: "上报信息",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/views/questionnaire/escalationForm/index.vue"
                                    ),
                            },
                        ],
                    },

                    {
                        path: "/appModel/schoolAttendanceSystem",
                        name: "schoolAttendanceSystem",
                        hideInMenu: true,
                        meta: {
                            title: "校园考勤系统",
                            icon: "icon-wodexuesheng1",
                        },
                        component: () =>
                            import(
                                "@/views/appModel/schoolAttendanceSystem/index.vue"
                            ),
                    },
                    {
                        path: "/appModel/schoolDormSystem",
                        name: "schoolDormSystem",
                        hideInMenu: true,
                        meta: {
                            title: "宿舍管理系统",
                            icon: "icon-wodexuesheng1",
                        },
                        component: () =>
                            import(
                                "@/views/appModel/schoolAttendanceSystem/index.vue"
                            ),
                    },
                    {
                        path: "/appModel/schoolLibrarySystem",
                        name: "schoolLibrarySystem",
                        hideInMenu: true,
                        meta: {
                            title: "图书馆管理系统",
                            icon: "icon-wodexuesheng1",
                        },
                        component: () =>
                            import(
                                "@/views/appModel/schoolAttendanceSystem/index.vue"
                            ),
                    },
                    {
                        path: "/appModel/adminTraffic",
                        name: "adminTraffic",
                        hideInMenu: true,
                        redirect: "/appModel/adminTraffic/settingTrafficType",
                        meta: {
                            title: "通行",
                        },
                        component: () =>
                            import("@/views/appModel/traffic/index.vue"),
                        children: [
                            {
                                path: "/appModel/adminTraffic/settingTrafficType",
                                name: "settingTrafficType",
                                meta: {
                                    title: "通行类型设置",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/traffic/settingTrafficType/index.vue"
                                    ),
                            },
                        ],
                    },

                    {
                        path: "/appModel/traffic",
                        name: "traffic",
                        hideInMenu: true,
                        redirect: "/appModel/traffic/trafficRule",
                        meta: {
                            title: "校门口",
                        },
                        component: () =>
                            import("@/views/appModel/traffic/index.vue"),
                        children: [
                            {
                                path: "/appModel/traffic/trafficRule",
                                name: "trafficRule",
                                hideInMenu: true,
                                meta: {
                                    title: "通行规则",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/traffic/trafficRule/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/traffic/trafficRecord",
                                name: "trafficRecord",
                                hideInMenu: true,
                                meta: {
                                    title: "通行记录",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/traffic/trafficRecord/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/traffic/trafficStatistics",
                                name: "trafficStatistics",
                                hideInMenu: true,
                                meta: {
                                    title: "通行统计",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/traffic/trafficStatistics/index.vue"
                                    ),
                            },

                            {
                                path: "/appModel/traffic/trafficPower",
                                name: "trafficPower",
                                hideInMenu: true,
                                meta: {
                                    title: "权限配置",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/traffic/trafficPower/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/traffic/trafficDataShow",
                                name: "trafficDataShow",
                                hideInMenu: true,
                                meta: {
                                    title: "数据看板",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/traffic/trafficDataShow/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/traffic/trafficFaceLibrary",
                                name: "trafficFaceLibrary",
                                meta: {
                                    title: "设备人脸库",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/traffic/trafficFaceLibrary/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/appModel/schoolcalendar",
                        name: "schoolcalendar",
                        hideInMenu: true,
                        meta: {
                            title: "校历",
                        },
                        component: () =>
                            import("@/views/appModel/schoolcalendar/index.vue"),
                    },
                    {
                        path: "/views/fullcalendar",
                        name: "fullcalendar",
                        hideInMenu: true,
                        meta: {
                            title: "日程",
                        },
                        component: () =>
                            import("@/views/appModel/views/fullcalendar"),
                    },
                    {
                        path: "/appModel/toDo",
                        name: "toDo",
                        hideInMenu: true,
                        meta: {
                            title: "待办",
                        },
                        component: () =>
                            import("@/views/appModel/toDo/index.vue"),
                    },
                    {
                        path: "/appModel/teacherAttendance",
                        name: "teacherAttendance",
                        hideInMenu: true,
                        meta: {
                            title: "教师考勤",
                        },
                        redirect: "/appModel/teacherAttendance/group",
                        component: () =>
                            import(
                                "@/views/appModel/teacherAttendance/index.vue"
                            ),
                        children: [
                            {
                                path: "/appModel/teacherAttendance/group",
                                name: "attendanceGroup",
                                meta: {
                                    title: "考勤组管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/teacherAttendance/attendanceGroup/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/teacherAttendance/working",
                                name: "attendanceWorking",
                                meta: {
                                    title: "考勤排班管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/teacherAttendance/attendanceWorking/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/teacherAttendance/rule",
                                name: "attendanceRule",
                                meta: {
                                    title: "考勤规则管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/teacherAttendance/attendanceRule/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/teacherAttendance/statistics",
                                name: "attendanceStatistics",
                                meta: {
                                    title: "考勤统计",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/teacherAttendance/attendanceStatistics/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/teacherAttendance/teacherFaceDevice",
                                name: "teacherFaceDevice",
                                meta: {
                                    title: "设备人脸库",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/teacherAttendance/teacherFaceDevice/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/appModel/studentAttendance",
                        name: "studentAttendance",
                        hideInMenu: true,
                        hideChildrenInMenu: true,
                        redirect: "/appModel/studentAttendance/inAndOut",
                        meta: {
                            title: "学生考勤",
                        },
                        component: () =>
                            import(
                                "@/views/appModel/studentAttendance/index.vue"
                            ),
                        children: [
                            {
                                path: "/appModel/studentAttendance/inAndOut",
                                name: "sAttendanceInAndOut",
                                hideInMenu: true,
                                meta: {
                                    title: "出入校",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/studentAttendance/inAndOut/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/studentAttendance/event",
                                name: "sAttendanceEvent",
                                hideInMenu: true,
                                meta: {
                                    title: "事件考勤",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/studentAttendance/event/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/studentAttendance/course",
                                name: "sAttendanceCourse",
                                hideInMenu: true,
                                meta: {
                                    title: "课程考勤",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/studentAttendance/course/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/studentAttendance/statistics",
                                name: "sAttendanceStatistics",
                                hideInMenu: true,
                                meta: {
                                    title: "考勤统计",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/studentAttendance/statistics/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/studentAttendance/details",
                                name: "sAttendanceDetails",
                                hideInMenu: true,
                                meta: {
                                    title: "数据明细",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/studentAttendance/details/index.vue"
                                    ),
                            },

                            // {
                            //     path: '/appModel/studentAttendance/goClassAtt',
                            //     name: 'sAttendanceGoClassAtt',
                            //     hideInMenu: true,
                            //     meta: {
                            //         title: '走班考勤统计'
                            //     },
                            //     component: () =>
                            //         import(
                            //             '@/views/appModel/studentAttendance/goClassAtt/index.vue'
                            //         )
                            // },
                            // {
                            //     path: '/appModel/studentAttendance/goClassInfo',
                            //     name: 'sAttendanceGoClassInfo',
                            //     hideInMenu: true,
                            //     meta: {
                            //         title: '走班数据明细'
                            //     },
                            //     component: () =>
                            //         import(
                            //             '@/views/appModel/studentAttendance/goClassInfo/index.vue'
                            //         )
                            // }
                        ],
                    },
                    {
                        path: "/appModel/adminStudentAttendance",
                        name: "adminStudentAttendance",
                        hideInMenu: true,
                        hideChildrenInMenu: true,
                        meta: {
                            title: "考勤",
                        },
                        redirect:
                            "/appModel/adminStudentAttendance/settingAttendanceType",
                        component: () =>
                            import(
                                "@/views/appModel/studentAttendance/index.vue"
                            ),
                        children: [
                            {
                                path: "/appModel/adminStudentAttendance/settingAttendanceType",
                                name: "settingAttendanceType",
                                meta: {
                                    title: "考勤类型设置",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/studentAttendance/adminStudentAttendance/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/appModel/smartClassSign",
                        name: "smartClassSign",
                        hideInMenu: true,
                        meta: {
                            title: "智慧班牌",
                        },
                        redirect: "/appModel/smartClassSign/classInfo",
                        component: () =>
                            import("@/views/appModel/smartClassSign/index.vue"),
                        children: [
                            {
                                path: "/appModel/smartClassSign/classInfo",
                                name: "classInfo",
                                meta: {
                                    title: "班级信息",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/smartClassSign/classInfo/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/smartClassSign/Remind",
                                name: "classRemind",
                                meta: {
                                    title: "提醒功能",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/smartClassSign/classRemind/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/smartClassSign/classLayout",
                                name: "classLayout",
                                meta: {
                                    title: "班牌布局",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/smartClassSign/classLayout/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/smartClassSign/classCardSet",
                                name: "classCardSet",
                                meta: {
                                    title: "班牌设置",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/smartClassSign/classCardSet/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/smartClassSign/customModule",
                                name: "customModule",
                                meta: {
                                    title: "自定义模块",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/smartClassSign/customModule/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/smartClassSign/managementModule",
                                name: "managementModule",
                                meta: {
                                    title: "自定义霸屏",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/smartClassSign/managementModule/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/smartClassSign/deviceFaceLibrary",
                                name: "deviceFaceLibrary",
                                meta: {
                                    title: "设备人脸库",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/smartClassSign/deviceFaceLibrary/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/smartClassSign/jurisdiction",
                                name: "jurisdiction",
                                meta: {
                                    title: "权限管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/smartClassSign/jurisdiction/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/smartClassSign/openRecord",
                                name: "openRecord",
                                meta: {
                                    title: "开门记录",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/smartClassSign/openRecord/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/appModel/moralEducationEvaluation",
                        name: "moralEducationEvaluation",
                        hideInMenu: true,
                        meta: {
                            title: "德育评价",
                        },
                        redirect:
                            "/appModel/moralEducationEvaluation/classInfo",
                        component: () =>
                            import(
                                "@/views/appModel/moralEducationEvaluation/index.vue"
                            ),
                        children: [
                            {
                                path: "/appModel/moralEducationEvaluation/classInfo",
                                name: "moralEducationEvaluationclassInfo",
                                meta: {
                                    title: "班级德育",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/moralEducationEvaluation/classInfo/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/moralEducationEvaluation/set",
                                name: "moralEducationEvaluationSet",
                                meta: {
                                    title: "德育评价设置",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/moralEducationEvaluation/set/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/moralEducationEvaluation/personnelManag",
                                name: "personnelManag",
                                meta: {
                                    title: "设备人员管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/moralEducationEvaluation/personnelManag/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/moralEducationEvaluation/activityDetailsCom",
                                name: "activityDetailsCom",
                                meta: {
                                    title: "“优秀学生评比”活动评比详情",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/moralEducationEvaluation/classInfo/activityDetailsCom/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/appModel/moralEducationEvaluation/classInfoEdit",
                        name: "moralEducationEvaluationedit",
                        hideInMenu: true,
                        meta: {
                            title: "德育评价",
                        },
                        redirect:
                            "/appModel/moralEducationEvaluation/classInfoEdit/activity",
                        component: () =>
                            import(
                                "@/views/appModel/moralEducationEvaluation/classInfo/edit/index.vue"
                            ),
                        children: [
                            {
                                path: "/appModel/moralEducationEvaluation/classInfoEdit/activity",
                                name: "moralEducationEvaluationactivity",
                                hideInMenu: true,
                                meta: {
                                    title: "新增评比活动",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/moralEducationEvaluation/classInfo/activity/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/appModel/moralEducationEvaluation/activityDetails",
                        name: "activityDetails",
                        hideInMenu: true,
                        meta: {
                            title: "德育评价活动详情",
                        },
                        redirect:
                            "/appModel/moralEducationEvaluation/activityDetails/activityDetailsCom",
                        component: () =>
                            import(
                                "@/views/appModel/moralEducationEvaluation/classInfo/activityDetails/index.vue"
                            ),
                        children: [
                            {
                                path: "/appModel/moralEducationEvaluation/activityDetails/activityDetailsCom",
                                name: "activityDetailsCom",
                                hideInMenu: true,
                                meta: {
                                    title: "“优秀学生评比”活动评比详情",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/moralEducationEvaluation/classInfo/activityDetailsCom/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/appModel/siteBooking",
                        name: "siteBooking",
                        hideInMenu: true,
                        meta: {
                            title: "场地预约",
                        },
                        redirect: "/appModel/siteBooking/siteBook",
                        component: () =>
                            import("@/views/appModel/siteBooking/index.vue"),
                        children: [
                            {
                                path: "/appModel/siteBooking/siteBook",
                                name: "siteBook",
                                meta: {
                                    title: "场地预约",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/siteBooking/siteBook/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/siteBooking/appointmentRecord",
                                name: "appointmentRecord",
                                meta: {
                                    title: "预约记录",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/siteBooking/appointmentRecord/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/siteBooking/siteIdleList",
                                name: "siteIdleList",
                                meta: {
                                    title: "场地闲置一览表",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/siteBooking/siteIdleList/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/siteBooking/appointmentReview",
                                name: "appointmentReview",
                                meta: {
                                    title: "预约审核",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/siteBooking/appointmentReview/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/siteBooking/appointmentType",
                                name: "appointmentType",
                                meta: {
                                    title: "预约类型管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/siteBooking/appointmentType/index.vue"
                                    ),
                            },
                        ],
                    },
                    // 活动报名
                    {
                        path: "/appModel/activityRegistration",
                        name: "activityRegistration",
                        hideInMenu: true,
                        meta: {
                            title: "活动报名",
                        },
                        redirect: "/appModel/activityRegistration",
                        component: () =>
                            import(
                                "@/views/appModel/activityRegistration/index.vue"
                            ),
                        children: [
                            {
                                path: "/appModel/activityRegistration/singUp",
                                name: "singUp",
                                meta: {
                                    title: "活动报名",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/activityRegistration/signUp/index.vue"
                                    ),
                            },
                        ],
                    },
                    // 活动报名
                    {
                        path: "/appModel/studentWorks",
                        name: "studentWorks",
                        hideInMenu: true,
                        meta: {
                            title: "学生作品",
                        },
                        component: () =>
                            import("@/views/appModel/studentWorks/index.vue"),
                    },
                    {
                        path: "/appModel/toBeOnDuty",
                        name: "toBeOnDuty",
                        hideInMenu: true,
                        meta: {
                            title: "值日生",
                        },
                        component: () =>
                            import("@/views/appModel/toBeOnDuty/index.vue"),
                    },
                    {
                        path: "/appModel/videoAlbum",
                        name: "videoAlbum",
                        hideInMenu: true,
                        meta: {
                            title: "校园视频",
                        },
                        component: () =>
                            import("@/views/appModel/videoAlbum/index.vue"),
                    },
                    {
                        path: "/appModel/countDown",
                        name: "countDown",
                        hideInMenu: true,
                        meta: {
                            title: "倒计时",
                        },
                        component: () =>
                            import("@/views/appModel/countDown/index.vue"),
                    },
                    {
                        path: "/appModel/campusStyle",
                        name: "campusStyle",
                        hideInMenu: true,
                        meta: {
                            title: "校园风采",
                        },
                        component: () =>
                            import("@/views/appModel/campusStyle/index.vue"),
                    },
                    {
                        path: "/appModel/punchTheClock",
                        name: "punchTheClock",
                        hideInMenu: true,
                        hideChildrenInMenu: true,
                        meta: {
                            title: "打卡",
                        },
                        component: () =>
                            import(
                                "@/views/appModel/views/punchTheClock/index.vue"
                            ),
                        redirect: "/appModel/punchTheClock/task",
                        children: [
                            {
                                path: "/appModel/punchTheClock/task",
                                name: "task",
                                meta: {
                                    title: "任务",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/views/punchTheClock/task/index.vue"
                                    ),
                            },

                            {
                                path: "/appModel/punchTheClock/details",
                                name: "details",
                                meta: {
                                    title: "任务详情",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/views/punchTheClock/details/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/appModel/borrowBooks",
                        name: "borrowingReturningMachine",
                        hideInMenu: true,
                        hideChildrenInMenu: true,
                        meta: {
                            title: "自助借还书机",
                        },
                        reactive: "/appModel/borrowBooks/borrowBooksInfo",
                        component: () =>
                            import("@/views/appModel/borrowBooks/index.vue"),
                        children: [
                            {
                                path: "/appModel/borrowBooks/borrowBooksInfo",
                                name: "borrowBooksInfo",
                                meta: {
                                    title: "自助借还书机信息",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/borrowBooks/borrowBooksInfo/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/borrowBooks/booksFaceDatabase",
                                name: "booksFaceDatabase",
                                meta: {
                                    title: "设备人脸库",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/borrowBooks/booksFaceDatabase/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/appModel/leaveWord",
                        name: "leaveWord",
                        hideInMenu: true,
                        hideChildrenInMenu: true,
                        meta: {
                            title: "意见箱",
                        },
                        component: () =>
                            import(
                                "@/views/appModel/views/leaveWord/index.vue"
                            ),
                    },
                    {
                        path: "/appModel/finance",
                        name: "finance",
                        hideInMenu: true,
                        hideChildrenInMenu: true,
                        meta: {
                            title: "财务报表",
                        },
                        component: () =>
                            import("@/views/appModel/views/finance/index.vue"),
                    },
                    {
                        path: "/appModel/studentEvaluation",
                        name: "studentEvaluation",
                        hideInMenu: true,
                        meta: {
                            title: "学生评价",
                        },
                        component: () =>
                            import(
                                "@/views/appModel/studentEvaluation/index.vue"
                            ),
                        children: [
                            {
                                path: "/appModel/studentEvaluation/activity",
                                name: "sEvaluationActivity",
                                meta: {
                                    title: "学生评价评比活动",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/studentEvaluation/activity/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/studentEvaluation/setup",
                                name: "sEvaluationSetup",
                                meta: {
                                    title: "奖章设置" || "学生评价指标设置",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/studentEvaluation/setup/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/appModel/honorWall",
                        name: "honorWall",
                        hideInMenu: true,
                        meta: {
                            title: "荣誉墙",
                        },
                        component: () =>
                            import("@/views/appModel/honorWall/index.vue"),
                    },
                    {
                        path: "/appModel/vote",
                        name: "vote",
                        hideInMenu: true,
                        meta: {
                            title: "投票",
                        },
                        component: () =>
                            import("@/views/appModel/vote/index.vue"),
                        children: [
                            {
                                path: "/appModel/vote/voteList",
                                name: "voteList",
                                meta: {
                                    title: "投票列表",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/vote/voteList/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/vote/voteList/operationVote",
                                name: "operationVote",
                                meta: {
                                    title: "投票操作",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/vote/voteList/operationVote/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/vote/voteList/statistics",
                                name: "statistics",
                                meta: {
                                    title: "投票统计",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/vote/voteList/statistics/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/appModel/examManage",
                        name: "examManage",
                        hideInMenu: true,
                        hideChildrenInMenu: true,
                        meta: {
                            title: "考试",
                        },
                        redirect: "/appModel/examManage/list",
                        component: () =>
                            import("@/views/appModel/examManage/index.vue"),
                        children: [
                            {
                                path: "/appModel/examManage/list",
                                name: "examList",
                                meta: {
                                    title: "考试列表",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/examManage/examList/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/examManage/append",
                                name: "examAddOrEdit",
                                meta: {
                                    title: "考试管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/examManage/examAddOrEdit/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/examManage/signTable",
                                name: "signTable",
                                meta: {
                                    title: "签到表",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/examManage/signTable/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/examManage/affair",
                                name: "affair",
                                meta: {
                                    title: "考务管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/examManage/affair/index.vue"
                                    ),
                                children: [
                                    {
                                        path: "/appModel/examManage/affair/supervisionTable",
                                        name: "supervisionTable",
                                        meta: {
                                            title: "监考安排表",
                                        },
                                        component: () =>
                                            import(
                                                "@/views/appModel/examManage/affair/supervisionTable/index.vue"
                                            ),
                                    },
                                    {
                                        path: "/appModel/examManage/affair/examSeatTable",
                                        name: "examSeatTable",
                                        meta: {
                                            title: "考场座位表",
                                        },
                                        component: () =>
                                            import(
                                                "@/views/appModel/examManage/affair/examSeatTable/index.vue"
                                            ),
                                    },
                                    {
                                        path: "/appModel/examManage/affair/seatPinupTable",
                                        name: "seatPinupTable",
                                        meta: {
                                            title: "考场座位贴",
                                        },
                                        component: () =>
                                            import(
                                                "@/views/appModel/examManage/affair/seatPinupTable/index.vue"
                                            ),
                                    },
                                    {
                                        path: "/appModel/examManage/affair/examInfoTable",
                                        name: "examInfoTable",
                                        meta: {
                                            title: "考场信息表",
                                        },
                                        component: () =>
                                            import(
                                                "@/views/appModel/examManage/affair/examInfoTable/index.vue"
                                            ),
                                    },
                                ],
                            },
                        ],
                    },
                    {
                        path: "/appModel/patrol",
                        name: "patrol",
                        hideInMenu: true,
                        hideChildrenInMenu: true,
                        meta: {
                            title: "场地巡查",
                        },
                        redirect: "/appModel/patrol/patrolPlan",
                        component: () =>
                            import("@/views/appModel/patrol/index.vue"),
                        children: [
                            {
                                path: "/appModel/patrol/patrolPlan",
                                name: "patrolPlan",
                                meta: {
                                    title: "巡查计划",
                                },
                                redirect: "/appModel/patrol/patrolPlan",
                                children: [
                                    {
                                        path: "/appModel/patrol/patrolPlan",
                                        name: "patrolPlan",
                                        meta: {
                                            title: "巡查计划列表",
                                        },
                                        component: () =>
                                            import(
                                                "@/views/appModel/patrol/patrolPlan/index.vue"
                                            ),
                                    },
                                    {
                                        path: "/appModel/patrol/patrolPlan/planInfo",
                                        name: "planInfo",
                                        meta: {
                                            title: "查看任务",
                                        },
                                        component: () =>
                                            import(
                                                "@/views/appModel/patrol/patrolPlan/planInfo/index.vue"
                                            ),
                                    },
                                ],
                            },
                            {
                                path: "/appModel/patrol/patrolTask",
                                name: "patrolTask",
                                meta: {
                                    title: "巡查任务",
                                },
                                redirect: "/appModel/patrol/patrolTask",
                                children: [
                                    {
                                        path: "/appModel/patrol/patrolTask",
                                        name: "patrolTask",
                                        meta: {
                                            title: "巡查任务列表",
                                        },
                                        component: () =>
                                            import(
                                                "@/views/appModel/patrol/patrolTask/index.vue"
                                            ),
                                    },
                                    {
                                        path: "/appModel/patrol/patrolTask/taskInfo",
                                        name: "taskInfo",
                                        meta: {
                                            title: "巡查详情与巡查记录",
                                        },
                                        component: () =>
                                            import(
                                                "@/views/appModel/patrol/patrolTask/taskInfo/index.vue"
                                            ),
                                    },
                                ],
                            },
                            {
                                path: "/appModel/patrol/deviceMage",
                                name: "deviceMage",
                                meta: {
                                    title: "设备管理",
                                },
                            },
                        ],
                    },
                    {
                        path: "/appModel/visitorSystem", // 访客系统
                        name: "visitorSystem",
                        hideInMenu: true,
                        hideChildrenInMenu: true,
                        meta: {
                            title: "访客系统",
                        },
                        component: () =>
                            import("@/views/appModel/visitorSystem/index.vue"),
                        redirect:
                            "/appModel/visitorSystem/visitorTrafficRecords",
                        children: [
                            {
                                path: "/appModel/visitorSystem/visitorTrafficRecords",
                                name: "visitorTrafficRecords",
                                meta: {
                                    title: "通行记录",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/visitorSystem/visitorTrafficRecords/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/visitorSystem/visitorSystemManag",
                                name: "visitorSystemManag",
                                meta: {
                                    title: "系统管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/visitorSystem/visitorSystemManag/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/visitorSystem/visitorStats",
                                name: "visitorStats",
                                meta: {
                                    title: "数据统计",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/visitorSystem/visitorStats/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/visitorSystem/visitingAircraftManag",
                                name: "visitingAircraftManag",
                                meta: {
                                    title: "访客机管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/visitorSystem/visitingAircraftManag/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/appModel/entranceGuardMachine", // 门禁系统
                        name: "entranceGuardMachine",
                        hideInMenu: true,
                        hideChildrenInMenu: true,
                        meta: {
                            title: "门禁系统",
                        },
                        component: () =>
                            import("@/views/appModel/entranceGuard/index.vue"),
                    },
                    {
                        path: "/appModel/meetingSystem", // 会议系统
                        name: "meetingSystem",
                        hideInMenu: true,
                        hideChildrenInMenu: true,
                        meta: {
                            title: "会议系统",
                        },
                        component: () =>
                            import(
                                "@/views/appModel/conferenceSystem/index.vue"
                            ),
                    },
                    {
                        path: "/appModel/paintedScreen", // 画屏管理
                        name: "paintedScreen",
                        hideInMenu: true,
                        hideChildrenInMenu: true,
                        meta: {
                            title: "画屏管理",
                        },
                        component: () =>
                            import("@/views/appModel/paintedScreen/index.vue"),
                        redirect: "",
                        children: [
                            {
                                path: "/appModel/paintedScreen/screenLayout",
                                name: "screenLayout",
                                meta: {
                                    title: "画屏布局",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/paintedScreen/screenLayout/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/paintedScreen/screenSet",
                                name: "screenSet",
                                meta: {
                                    title: "画屏设置",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/paintedScreen/screenSet/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/appModel/scoreManage", // 成绩管理
                        name: "scoreManage",
                        hideInMenu: true,
                        hideChildrenInMenu: true,
                        meta: {
                            title: "成绩管理",
                        },
                        component: () =>
                            import("@/views/appModel/scoreManage/index.vue"),
                        redirect: "",
                        children: [
                            {
                                path: "/appModel/scoreManage/examList",
                                name: "examList",
                                meta: {
                                    title: "考试列表",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/scoreManage/examList/index.vue"
                                    ),
                            },

                            {
                                path: "/appModel/scoreManage/noticeSet",
                                name: "noticeSet",
                                meta: {
                                    title: "通知设置",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/scoreManage/noticeSet/index.vue"
                                    ),
                            },

                            {
                                path: "/appModel/scoreManage/scoreReview",
                                name: "scoreReview",
                                meta: {
                                    title: "成绩审核",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/scoreManage/scoreReview/index.vue"
                                    ),
                            },
                        ],
                    },
                    {
                        path: "/appModel/clubManage", // 社团管理
                        name: "clubManage",
                        hideInMenu: true,
                        hideChildrenInMenu: true,
                        meta: {
                            title: "社团管理",
                        },
                        component: () =>
                            import("@/views/appModel/clubManage/index.vue"),
                        redirect: "",
                        children: [
                            {
                                path: "/appModel/clubManage/classification",
                                name: "classification",
                                meta: {
                                    title: "社团分类",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/clubManage/classification/index.vue"
                                    ),
                            },

                            {
                                path: "/appModel/clubManage/administration",
                                name: "administration",
                                meta: {
                                    title: "社团管理",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/clubManage/administration/index.vue"
                                    ),
                            },

                            {
                                path: "/appModel/clubManage/rule",
                                name: "rule",
                                meta: {
                                    title: "社团规则",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/clubManage/rule/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/clubManage/examine",
                                name: "examine",
                                meta: {
                                    title: "社团审核",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/clubManage/examine/index.vue"
                                    ),
                            },
                        ],
                    },

                    {
                        path: "/appModel/leaveSchool", // 壹起放学
                        name: "leaveSchool",
                        hideInMenu: true,
                        hideChildrenInMenu: true,
                        meta: {
                            title: "壹起放学",
                        },
                        component: () =>
                            import("@/views/appModel/leaveSchool/index.vue"),
                        redirect: "",
                        children: [
                            {
                                path: "/appModel/leaveSchool/bulletinBoard",
                                name: "bulletinBoard",
                                meta: {
                                    title: "放学看板",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/leaveSchool/bulletinBoard/index.vue"
                                    ),
                            },
                            {
                                path: "/appModel/leaveSchool/record",
                                name: "schoolRecord",
                                meta: {
                                    title: "放学记录",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/leaveSchool/record/index.vue"
                                    ),
                            },

                            {
                                path: "/appModel/leaveSchool/configure",
                                name: "configure",
                                meta: {
                                    title: "放学配置",
                                },
                                component: () =>
                                    import(
                                        "@/views/appModel/leaveSchool/configure/index.vue"
                                    ),
                            },
                        ],
                    },
                ],
            },
            {
                path: "/chatAi",
                name: "chatAi",
                meta: {
                    title: "小壹AI智能",
                    icon: "icon-yingyongzhongxin",
                },
                component: () => import("@/views/chatAi/index.vue"),
            },
            {
                path: "/kanbanMDC",
                name: "kanbanMDC",
                meta: {
                    title: "数据中心",
                    icon: "icon-yingyongzhongxin",
                },
                component: () => import("@/views/kanbanMDC/index.vue"),
            },
            {
                path: "/account/center",
                name: "userCenter",
                hideInMenu: true,
                meta: {
                    title: "个人中心",
                    icon: "user-outlined",
                },
                component: () => import("@/views/users/center/index.vue"),
            },
            {
                path: "/noauth401",
                name: "noauth401",
                hideInMenu: true,
                component: () => import("@/views/error/401.vue"),
                meta: {
                    title: "无权限",
                },
            },
        ],
    },
    {
        path: "/help",
        name: "help",
        component: UserLayout,
        redirect: "/help/doc",
        children: [
            {
                path: "/help/doc",
                name: "helpDoc",
                meta: {
                    title: "帮助中心",
                },
                component: () => import("@/views/help/index.vue"),
            },
        ],
    },
];

export const constantRoutes = [
    {
        path: "/m",
        name: "models",
        component: () => import("@/layouts/AppLayout.vue"),
        meta: {
            title: "应用",
        },
        children: [
            {
                path: "/appModel/:pathMatch(.*)*",
                name: "model",
                component: () => import("@/views/appModel/app/index.vue"),
                meta: {
                    title: "应用",
                },
                children: [],
            },
            {
                path: "/appModel/oaApproveAdmin",
                name: "oaApproveAdmin",
                component: () => import("@/views/appModel/oaApproveAdmin/index.vue"),
                meta: {
                    title: "审批",
                },
                children: [],
            },
        ],
    },
    {
        path: "/updatePassw",
        name: "updatePassw",
        component: () => import("@/views/updatePassw/index.vue"),
        meta: {
            title: "修改密码",
        },
    },
    {
        path: "/login",
        name: "login",
        component: () => import("@/views/login/index.vue"),
        meta: {
            title: "登录",
        },
    },
    {
        path: "/ddLogin",
        name: "ddLogin",
        component: () => import("@/views/ddLogin/index.vue"),
        meta: {
            title: "登录",
        },
    },
    {
        path: "/son",
        name: "son",
        component: () => import("@/views/son/login.vue"),
        meta: {
            title: "登录",
        },
    },
    {
        path: "/401",
        name: "noauth",
        component: () => import("@/views/error/401.vue"),
        meta: {
            title: "无权限",
        },
    },
    {
        path: "/404",
        name: "notFound",
        component: () => import("@/views/error/404.vue"),
        meta: {
            title: "NotFound",
        },
    },
    {
        path: "/socialLogin",
        name: "socialLogin",
        component: () => import("@/views/login/socialLogin.vue"),
        meta: {
            title: "登录",
        },
    },
    {
        path: "/releaseQRCode",
        name: "releaseQRCode",
        component: () =>
            import("@/views/appModel/traffic/trafficRelease/releaseQRCode.vue"),
        meta: {
            title: "releaseQRCode",
        },
    },
];
