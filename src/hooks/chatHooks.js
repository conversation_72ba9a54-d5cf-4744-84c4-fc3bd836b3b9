import { computed, reactive } from "vue";
import { useRoute, useRouter } from "vue-router";
import http from "@/utils/request";
import { ls } from "@/utils/util";
import { EventSourceParserStream } from "eventsource-parser/stream";
import config from "@/config/config.js";
import { message } from "ant-design-vue";

async function fetchSSE(url, data, onMessage) {
    let token = ls.get(config.ACCESS_TOKEN);
    if (token.indexOf("Bearer") == -1) {
        token = `Bearer ${token}`;
    } else {
        token = token;
    }
    const response = await fetch(`${import.meta.env.VITE_BASE_API}${url}`, {
        method: "POST",
        body: JSON.stringify(data),
        headers: {
            Authorization: token || null,
            "Content-Type": "application/json",
        },
    });
    const res = response.clone().json();
    const reader = response?.body
        ?.pipeThrough(new TextDecoderStream())
        .pipeThrough(new EventSourceParserStream())
        .getReader();
    while (true) {
        const x = await reader?.read();
        if (x) {
            const { done, value } = x;
            try {
                const val = value?.data ? JSON.parse(value.data) : "";
                if (val?.event !== "ping") {
                    onMessage({
                        ...val,
                    });
                }
            } catch (e) {
                console.warn(e);
            }
            if (done) {
                console.info("done");
                break;
            }
        }
    }
}
// 创建当前会话消息
const route = useRoute();
const router = useRouter();
const agentId = ref("default"); // 智能体ID
const chatId = computed(() => "defaultChat"); // 对话ID
export const stateHooks = reactive({
    historyList: [{ id: 1234567, name: "默认对话" }], // 历史对话
    // 对话详情(智能体配置里面需要一个默认)
    agentDetails: {
        selectKnowledgeBase: false,
        model: null,
        knowledgeBaseIds: [],
        knowledgeBaseList: [],
        properties: {
            welcomeMessage: "",
            instructions: {
                blankReply: "",
                role: "",
                skills: "",
                workflow: "",
                restrictions: "",
            },
            samplePrompts: [],
        },
    }, // 智能体详情
    modelList: [], // 模型列表
    selectModelId: null, // 选择的模型Id
    selectModel: {}, // 选择的模型
    selectChat: {}, // 选择的对话
    chatData: [], // 对话内容
    answer: "",
    loading: false,
    reasoningLoading: false,
    paperStatus: "default", // 默认状态
});

// 停止对话
export const stopChat = async () => {
    await http.post("/ai/chat/session/stop", { id: chatId.value });
};

// 设置选择的模型 isUpdateChat: 是否刷新为默认初始页
export const setSelectModel = (model, { isUpdateChat }) => {
    stateHooks.selectModelId = model.id;
    stateHooks.selectModel = model;
    stateHooks.chatData = [];
};

// 获取模型
export const getChatModels = async () => {
    const { data } = await http.post("/ai/chat/models", {});
    stateHooks.modelList = data;
    setSelectModel(data[0], { isUpdateChat: false });
};

// 新增对话
export const createChat = async (cb) => {
    const { data } = await http.post("/ai/chat/session/create", {
        assistantId: agentId.value,
        model: stateHooks.selectModelId,
        isPreview: false,
    });
    stateHooks.selectChat = data;
    cb &&
        cb({
            agentId: agentId.value,
            chatId: data.id,
        });
};

// 发送对话
export const setAnswer = (v = {}) => {
    if (v.code != 0) {
        stateHooks.answer = v;
        console.log(stateHooks.answer);
    } else {
        stateHooks.loading = false;
        message.error(v.message || "服务器异常");
    }
};
// 输入对话内容
const getStreamChat = async (content, fileIds = [], imageUrls = []) => {
    const params = {
        sessionId: stateHooks.selectChat.id,
        prompt: content,
        fileIds,
        imageUrls,
    };
    try {
        await fetchSSE("/ai/chat/completion/stream", params, setAnswer);
    } catch (e) {
        stateHooks.loading = false;
    }
};
const mapId = ref("");
// 滚动到底部
const setScrollBottom = () => {
    nextTick(() => {
        const domList = document.getElementById("chatListRef");
        if (domList) {
            domList.scrollTo({
                top: domList.scrollHeight,
                behavior: "smooth",
            });
        }
    });
};
export const setChatData = (content, fileList = [], imageUrls = []) => {
    const successFile = fileList?.filter(
        (i) => i.status == "PARSE_SUCCESS" || i.status == "FILE_IS_READY"
    );
    const fileIds = successFile?.map((i) => i.id);
    const newContent = ref(content);
    if (stateHooks.agentDetails?.subType == "code_gen") {
        const codeForm = JSON.parse(content);
        newContent.value = `系统名称：${codeForm["系统名称"]} ，功能简介：${codeForm["功能简介"]}`;
    }
    mapId.value = Math.random().toString(36).substring(2);

    stateHooks.chatData?.push({
        role: "user",
        content: newContent.value,
        sessionId: chatId.value,
        reasoning: "",
        additional: { imageUrls, files: successFile },
    });

    stateHooks.chatData.push({
        role: "assistant",
        id: mapId.value,
        content: "",
        loading: true,
        reasoning: "",
        additional: {
            outputFiles: [],
        },
    });
    stateHooks.loading = true;
    getStreamChat(content, fileIds, imageUrls);
};
watch(
    () => stateHooks.answer,
    (val) => {
        if (val && val.event == "cmpl") {
            console.log({ val });
            stateHooks.chatData.forEach((item) => {
                if (item.id == mapId.value) {
                    item.reasoning += val?.text || "";
                }
            });
            setScrollBottom();
        } else {
            stateHooks.loading = false;
        }
    },
    { deep: true, immediate: true }
);
