import http from "@/utils/request";

export const useList = (url, other = {}) => {
    const query = reactive({
        pageNo: 1,
        pageSize: 10,
        ...other,
    })
    const page = reactive({
        // 加载状态
        loading: false,
        list: [],
        // 分页
        pagination: {
            pageNo: 1,
            pageSize: 10,
            total: 0,
        }
    })
    const getList = async params => {
        query.pageNo = 1
        params && Object.assign(query, params)

        try {
            page.loading = true
            const { data: {
                list,
                pageNo,
                pageSize,
                total,
            } } = await http.post(url, query)

            Object.assign(page, { list, pagination: { pageNo, pageSize, total } })
        } catch (error) {
            console.log('getList error: ', error)
        } finally {
            page.loading = false
        }
    }
    // 如果有默认搜索条件 op
    const reset = op => {
        const pageSize = query.pageSize
        for (const key in query) {
            query[key] = null
        }
        query.pageNo = 1
        query.pageSize = pageSize
        op && Object.assign(query, op)
        getList()
    }
    const paginationChange = ({ current: pageNo, pageSize }) => {
        if (pageSize == query.pageSize) {
            getList({ pageNo, pageSize })
        } else {
            getList({ pageSize, pageNo: 1 })
        }
    }

    const updateByDelete = (len = 1) => {
        let pageNo = query.pageNo
        if (page.list.length <= len) {
            // 当前页数完全删除
            pageNo = pageNo > 1 ? pageNo - 1 : 1
        }
        getList({ pageNo })
    }

    return { page, query, getList, reset, paginationChange, updateByDelete }
}
