<template>
    <a-tabs
        v-model:activeKey="state.activeKey"
        @change="handleChangeTabs"
        v-bind="$attrs"
    >
        <template #leftExtra v-if="props.showBack">
            <slot name="leftExtra">
                <a-button type="text" class="tabs_back" @click="back">
                    <i class="iconfont icon-xingzhuangjiehe19"></i>
                    <span>{{ leftExtraText }}</span>
                </a-button>
            </slot>
        </template>
        <a-tab-pane
            v-for="item in state.tabsList"
            :key="item.path"
            :tab="item.title || item.meta?.title"
        >
        </a-tab-pane>
    </a-tabs>
    <router-view />
</template>

<script setup>
import { reactive, onMounted, computed, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useStore } from "vuex";
const modules = import.meta.glob("../../views/**/index.vue");
import ViewCom from "@/router/view.vue";
const props = defineProps({
    backRout: {
        type: String,
        default: "",
    },
    activeKey: {
        type: String,
        default: "",
    },
    showBack: {
        type: Boolean,
        default: false,
    },
    leftExtraText: {
        type: String,
        default: "返回",
    },
    tabsList: {
        type: Array,
        default: () => [],
    },
    hasAuthTabs: {
        type: Boolean,
        default: false,
    },
    // 如果要getRouters
    authRoutesName: {
        type: String,
        default: "",
    },
    // 定制应用跳入
    _activeKey: {
        type: String,
        default: "",
    },
});

const state = reactive({
    activeKey: "",
    tabsList: [],
    systemTabsObj: {},
});
const store = useStore();
const router = useRouter();
const route = useRoute();
const back = () => {
    if (props.backRout) {
        router.push({ name: props.backRout });
    } else {
        router.push({ path: "/app/list" });
    }
};
const handleChangeTabs = (v) => {
    router.replace({
        path: v,
        query: { ...route.query },
    });
    state.activeKey = v;
};

const authRoutes = computed(() => {
    const allJurisdiction = store.state.base.allJurisdiction;
    let newData = [];
    allJurisdiction.forEach((v) => {
        if (v.code === props.authRoutesName) {
            newData = v.list[0].children;
        }
    });
    return newData;
});
const regRoute = (arr) => {
    let list = [];
    arr?.forEach((i) => {
        if (!(i.type == "C" || i.type == "A" || i.type == "J")) return;
        let item = {
            path: i.path,
            name: i.component,
            meta: {
                title: i.name,
                icon: i.icon,
                tabList: i.tabList,
                customParams: i.customParams,
            },
            hideInMenu: i.type != "C",
            redirect: i.children.length ? i.children[0].path : "",
        };
        if (i.path.indexOf("/appModel") !== -1) {
            const paths = i.path.split("/");
            item.meta.code = paths[2];
        }
        if (i.children.length > 0) {
            item.children = regRoute(i.children);
        }
        if (i.filePath) {
            item.component = modules[i.filePath] || ViewCom;
        } else {
            item.component = ViewCom;
        }
        list.push(item);
    });
    return list;
};
const initRoute = () => {
    if (props.hasAuthTabs) {
        state.tabsList = props.tabsList;
        return;
    }
    state.tabsList = [];
    const curpath = route.path;
    const len = curpath.lastIndexOf("/");
    const ppath = curpath.substring(0, len);
    const allrouter = router.getRoutes().filter((item) => item.path === ppath);
    let allrouterSosn = allrouter[0]?.children || [];
    // 父组件传来的固定菜单
    if (props.tabsList.length && allrouterSosn.length) {
        const dataRount = [];
        props.tabsList.forEach((j) => {
            allrouterSosn.forEach((v) => {
                if (
                    v.path.includes(j.key) ||
                    v.path.includes(j.path) ||
                    v.name.includes(j.path)
                ) {
                    const meta = j.meta?.title ? j.meta : v.meta;
                    dataRount.push({
                        ...j,
                        ...v,
                        meta,
                    });
                }
                if (curpath === v.path) {
                    state.activeKey = v.path;
                }
            });
        });
        allrouterSosn = dataRount;
    } else {
        state.activeKey = curpath;
    }
    if (props.authRoutesName) {
        state.tabsList = allrouterSosn.map((v) => {
            authRoutes.value.forEach((k) => {
                if (v.name === k.path) {
                    v.meta.title = k.name;
                }
            });
            return v;
        });
    } else {
        state.tabsList = allrouterSosn;
    }
};

watch(
    () => props.tabsList,
    (val) => {
        if (val.length) {
            initRoute();
        }
    }
);

onMounted(async () => {
    await initRoute();
    router.push({
        path: route.path,
        query: {
            ...route.query,
        },
    });
    // state.tabsList?.length > 1 && (state.activeKey = route.path)
});

const showBackStyle = computed(() => (props.showBack ? "0px" : "24px"));
</script>

<style lang="less" scoped>
:deep(.ant-tabs-nav) {
    padding-left: v-bind(showBackStyle);
    margin-bottom: 0;
    border-bottom: 1px solid @border-color-base;
    line-height: 33px;
    min-height: 57px;
}

.faceLibrary_warp_pageitem {
    height: calc(100% - 57px);
}
</style>
