<template>
    <a-tabs v-model:activeKey="activeKey">
        <template #leftExtra v-if="props.showBack">
            <slot name="leftExtra">
                <a-button type="text" class="tabs_back" @click="back">
                    <i class="iconfont icon-xingzhuangjiehe19"></i>
                    <span>{{ leftExtraText }}</span>
                </a-button>
            </slot>
        </template>
        <a-tab-pane
            v-for="item in props.tabsList"
            :key="item.perms"
            :tab="item.name"
        >
        </a-tab-pane>
    </a-tabs>
    <slot></slot>
</template>

<script setup>
import { useRouter } from "vue-router";
const props = defineProps({
    backRout: {
        type: String,
        default: "",
    },
    activeKey: {
        type: String,
    },
    showBack: {
        type: Boolean,
        default: true,
    },
    leftExtraText: {
        type: String,
        default: "返回",
    },
    tabsList: {
        type: Array,
        default: () => [],
    },
});
const activeKey = computed({
    get() {
        return props.activeKey;
    },
    // setter
    set(newValue) {
        emit("update:activeKey", newValue);
    },
});
const router = useRouter();
const emit = defineEmits(["update:activeKey"]);
const back = () => {
    if (props.backRout) {
        router.push({ name: props.backRout });
    } else {
        router.back(-1);
    }
};
</script>

<style lang="less" scoped>
:deep(.ant-tabs-nav) {
    padding-left: v-bind(showBackStyle);
    margin-bottom: 0;
    border-bottom: 1px solid @border-color-base;
    line-height: 33px;
    min-height: 57px;
}

.faceLibrary_warp_pageitem {
    height: calc(100% - 57px);
}
</style>
