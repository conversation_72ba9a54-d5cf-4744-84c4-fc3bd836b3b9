<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-11-14 16:43:06
 * @LastEditors: jingrou
 * @LastEditTime: 2022-11-14 17:03:14
-->
<template>
    <a-modal
        v-model:visible="isVisible"
        :footer="isFooter ? null : undefined"
        :cancelText="cancelText"
        :title="determineTitle"
        :maskClosable="isMaskClosable"
        :okText="okText"
        :destroyOnClose="isDestroyOnClose"
        :confirm-loading="confirmLoading"
        @cancel="handleCancel"
        @ok="handleOk"
    >
        <slot />
    </a-modal>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
    /* 取消文字 */
    cancelText: {
        type: String,
        default: '取消'
    },
    okText: {
        type: String,
        default: '确定'
    },
    /* title文字 */
    determineTitle: {
        type: String,
        default: '提示 '
    },
    /* 是否关闭时销毁 Modal 里的子元素 */
    isDestroyOnClose: {
        type: Boolean,
        default: true
    },
    /* 点击蒙层是否允许关闭 */
    isMaskClosable: {
        type: Boolean,
        default: false
    },
    /* 是否显示页脚 */
    isFooter: {
        tyle: Boolean,
        default: false
    },

    /* 是否显示模态框 */
    isVisible: {
        tyle: Boolean,
        default: false
    }
})

const emit = defineEmits([
    'update:isVisible',
    'emitHandleOk',
    'emithandleCancel'
])

const cancelText = computed(() => {
    return props.cancelText
})
const okText = computed(() => {
    return props.okText
})
const determineTitle = computed(() => {
    return props.determineTitle
})
const isDestroyOnClose = computed(() => {
    return props.isDestroyOnClose
})

const isMaskClosable = computed(() => {
    return props.isMaskClosable
})

const isFooter = computed(() => {
    return props.isFooter
})
const isVisible = computed(() => {
    return props.isVisible
})
// 加载
const confirmLoading = ref(false)
/* 确定提交 */
const handleOk = () => {
    emit('emitHandleOk')
}
// 取消关闭
const handleCancel = () => {
    emit('emithandleCancel')
}
</script>

<style scoped lang="less"></style>
