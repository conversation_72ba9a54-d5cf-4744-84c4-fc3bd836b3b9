<!--
 * @Descripttion:Echarts
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-04-02 15:27:54
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-05-23 15:43:45
-->
<template>
    <div
        :id="id || randomId"
        style="width: 100%; height: 100%; position: relative"
    ></div>
</template>
<script setup>
import { nextTick, onMounted, watch } from 'vue'
import * as echarts from 'echarts'
function randomString(len) {
    len = len || 32
    const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
    const maxPos = chars.length
    let pwd = ''
    for (let i = 0; i < len; i++) {
        pwd += chars.charAt(Math.floor(Math.random() * maxPos))
    }
    return pwd
}

const randomId = randomString(8)

const prose = defineProps({
    id: {
        type: String,
        default: ''
    },
    option: {
        type: Object,
        default: () => {}
    }
})
let myChart = null
const emit = defineEmits(['click'])
function init() {
    const { id, option } = prose
    myChart = echarts.init(document.getElementById(id || randomId))
    myChart.setOption(option)
    myChart.on('click', (params) => {
        emit('click', params)
    })

    const dom = document.getElementById(id || randomId)

    myChart = echarts.init(dom)

    myChart.setOption(option)
}
watch(prose.option, (v) => {
    myChart.setOption(v)
})

onMounted(() => {
    nextTick(() => {
        init()
    })

    window.addEventListener('resize', () => {
        myChart && myChart.resize()
    })
})


</script>
