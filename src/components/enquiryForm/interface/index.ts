/**
 * 定义接口来定义对象的类型
 * `stores` 全部类型定义在这里
 */
// 表单查询 表单数值
export interface FormState {
    city: null | string
    district: null | string
    school: null | string
    monitoringRoom: null | string
    type: null | string
    name?: string
}

// 用于查询表单 标签类型
export interface FormDataItme {
    label: string
    type: string
    attributeValue?: Array<Object>
    modelValue: null | string
    placeholder: string | Array<string>
    fieldNames?: Object
}
