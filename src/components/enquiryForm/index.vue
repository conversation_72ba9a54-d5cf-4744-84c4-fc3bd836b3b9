<template>
    <a-form
        class="enquiry-form"
        :layout="formLayout"
        ref="enquiryFormRef"
        :model="state.formStates"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
    >
        <a-row :gutter="gutter">
            <template v-if="enquiryFormItme.length">
                <a-col
                    v-for="(item, idx) in enquiryFormItme"
                    :span="item.colSpan || colSpan"
                    :key="idx"
                >
                    <a-form-item :label="item.label" :name="item.modelValue">
                        <slot
                            v-if="item.type === 'slot'"
                            :name="item.modelValue"
                            :item="item"
                            :formStates="state.formStates"
                            v-bind="item"
                        ></slot>
                        <component
                            v-else
                            class="enquiry-form-item"
                            :is="item.type"
                            v-model:value="state.formStates[item.modelValue]"
                            :options="item.attributeValue"
                            :changeOnSelect="item.attributeValue ? true : false"
                            :disabled="item.disabled"
                            :picker="item.picker ? item.picker : ''"
                            :fieldNames="item.fieldNames"
                            :valueFormat="item.format ? item.format : ''"
                            :format="item.format ? item.format : ''"
                            :placeholder="item.placeholder"
                            v-bind="item.attrs"
                            @change="
                                enquiryChange($event, item, state.formStates)
                            "
                        />
                    </a-form-item>
                </a-col>
            </template>
            <a-col
                class="enquiry-footer-btn"
                :style="footerBtnStype"
                v-if="isQuery"
            >
                <a-form-item label="">
                    <a-button type="primary" @click="enquiryFormSubmit">
                        <template #icon>
                            <SearchOutlined v-if="isIcon" />
                        </template>
                        {{ okText }}
                    </a-button>
                    <a-button
                        style="margin-left: 10px"
                        @click="enquiryFormReset"
                    >
                        <template #icon>
                            <redoOutlined v-if="isIcon" />
                        </template>
                        {{ resetText }}
                    </a-button>
                </a-form-item>
            </a-col>
        </a-row>
    </a-form>
</template>

<script setup>
const props = defineProps({
    // 查询参数
    formState: {
        type: Object,
        default: () => {},
    },
    // 查詢条件
    enquiryFormItme: {
        type: Array,
        default: () => [],
        //  Object as PropType<FormState>
    },
    rules: {
        type: Object,
        default: () => {},
    },
    colSpan: {
        type: Number,
        default: 4,
    },
    isQuery: {
        type: Boolean,
        default: true,
    },
    formLayout: {
        type: String,
        default: "vertical",
    },
    footerBtnStype: {
        // 自定义页脚样式
        type: Object,
        default: () => {},
    },
    resetText: {
        type: String,
        default: "重置",
    },
    okText: {
        type: String,
        default: "查询",
    },
    isIcon: {
        type: Boolean,
        default: true,
    },
    gutter: {
        type: [Number, Number],
        default: [24, 4],
    },
    labelCol: {
        type: Object,
        default: () => {},
    },
    wrapperCol: {
        type: Object,
        default: () => {},
    },
});
const enquiryFormRef = shallowRef();
const state = reactive({
    formStates: {},
    // ...toRaw(props.formState),
});
state.formStates = computed(() => props.formState);
const emit = defineEmits(["emitEnquiryFormSubmitReset", "emitEnquiryChange"]);

/**
 * @description: 提交表单查询数据
 * @return {*}
 */
const enquiryFormSubmit = () => {
    if (props.rules && Object.values(props.rules).length) {
        enquiryFormRef.value
            .validate()
            .then(() => {
                emit("emitEnquiryFormSubmitReset", state.formStates);
            })
            .catch(() => {
                emit("emitEnquiryFormSubmitReset", null);
            });
    } else {
        emit("emitEnquiryFormSubmitReset", state.formStates);
    }
};

/**
 * @description: 重置表单查询数据
 * @return {*}
 */
const enquiryFormReset = () => {
    enquiryFormRef.value.resetFields();
    emit("emitEnquiryFormSubmitReset", state.formStates, true);
};
const enquiryChange = (event, item) => {
    emit("emitEnquiryChange", event, item, state.formStates);
};
const validateFields = (item) => {
    enquiryFormRef.value.validateFields(item);
};
defineExpose({
    enquiryFormRef,
    enquiryFormReset,
    enquiryFormSubmit,
    validateFields,
});
</script>

<style scoped lang="less">
.enquiry-footer-btn {
    min-width: 200px;
}
</style>
