export const IntegratedRouting = {
    // 出入校
    sAttendanceInAndOut: defineAsyncComponent(() =>
        import("@/views/appModel/studentAttendance/inAndOut/index.vue")
    ),
    // 事件考勤
    sAttendanceEvent: defineAsyncComponent(() =>
        import("@/views/appModel/studentAttendance/event/index.vue")
    ),
    // 课程考勤 sAttendanceCourse
    sAttendanceCourse: defineAsyncComponent(() =>
        import("@/views/appModel/studentAttendance/course/index.vue")
    ),
    // 考勤统计
    sAttendanceStatistics: defineAsyncComponent(() =>
        import("@/views/appModel/studentAttendance/statistics/index.vue")
    ),
    // 数据明细
    sAttendanceDetails: defineAsyncComponent(() =>
        import("@/views/appModel/studentAttendance/details/index.vue")
    ),
    // 通行规则
    trafficRule: defineAsyncComponent(() =>
        import("@/views/appModel/traffic/trafficRule/index.vue")
    ),
    //  通行记录
    trafficRecord: defineAsyncComponent(() =>
        import("@/views/appModel/traffic/trafficRecord/index.vue")
    ),
    //  通行统计
    trafficStatistics: defineAsyncComponent(() =>
        import("@/views/appModel/traffic/trafficStatistics/index.vue")
    ),
    //  权限配置
    trafficPower: defineAsyncComponent(() =>
        import("@/views/appModel/traffic/trafficPower/index.vue")
    ),
    // 数据看板
    trafficDataShow: defineAsyncComponent(() =>
        import("@/views/appModel/traffic/trafficDataShow/index.vue")
    ),
    // 设备人脸库
    trafficFaceLibrary: defineAsyncComponent(() =>
        import("@/views/appModel/traffic/trafficFaceLibrary/index.vue")
    ),

    // 门禁设置
    deviceManagements: defineAsyncComponent(() =>
        import("@/views/appModel/entranceGuard/entranceSet.vue")
    ),

    // 通行记录
    visitorTrafficRecords: defineAsyncComponent(() =>
        import("@/views/appModel/visitorSystem/visitorTrafficRecords/index.vue")
    ),

    // 数据统计
    visitorStats: defineAsyncComponent(() =>
        import("@/views/appModel/visitorSystem/visitorStats/index.vue")
    ),

    // 访客机管理
    visitingAircraftManag: defineAsyncComponent(() =>
        import("@/views/appModel/visitorSystem/visitingAircraftManag/index.vue")
    ),

    // 系统管理
    visitorSystemManag: defineAsyncComponent(() =>
        import("@/views/appModel/visitorSystem/visitorSystemManag/index.vue")
    ),
    
    // 放行
    trafficRelease: defineAsyncComponent(() =>
        import("@/views/appModel/traffic/trafficRelease/index.vue")
    ),
};
