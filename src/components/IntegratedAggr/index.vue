<!--
 * @Description: tabs 路由
 * @Date: 2022-01-10 11:06:43
 * @LastEditors: DESKTOP-3F105TN
 * @FilePath: \laihq-web-servef:\code\cloud-system-2.0\src\components\RouterTabs\index.vue
-->

<template>
    <div class="integrated_aggr">
        <a-tabs v-model:activeKey="integrationParams.activeKey" v-bind="$attrs">
            <template #leftExtra v-if="integrationParams.showBack">
                <slot name="leftExtra">
                    <a-button type="text" class="tabs_back 1" @click="back" v-if="integrationParams.showBack">
                        <i class="iconfont icon-xingzhuangjiehe19"></i>
                        <span v-if="props.leftExtraText">
                            {{ props.leftExtraText }}
                        </span>
                    </a-button>
                </slot>
            </template>

            <a-tab-pane v-for="(item, idx) in integrationParams.tabsList" :key="item.component"
                :tab="item.title || item.meta?.title || item.name">
                <component class="conainer_box" v-if="
                    item.path == integrationParams.activeKey ||
                    item.component == integrationParams.activeKey" :is="IntegratedRouting[integrationParams.activeKey]"
                    :key="idx" :_types="_types" :systemCode="integrationParams.systemCode"
                    :passId="integrationParams.passId || passIds">
                </component>
            </a-tab-pane>
        </a-tabs>
    </div>
</template>

<script setup>
import { computed, inject, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { IntegratedRouting } from "./IntegratedRouting.js";
import { listPassSite } from "@/api/trafficRules.js";
const passIds = shallowRef("");
// 侧边栏接口
const getListPassSite = async (name) => {
    await listPassSite().then(({ data }) => {
        const item = data.find((v) => v.name == name);
        passIds.value = item.id || "";
    });
};
// Applayout || schoolAttendanceSystem 传递过来的参数

const integrationParams = inject("integrationParams");
const props = defineProps({
    backRout: {
        type: String,
        default: "",
    },
    leftExtraText: {
        type: String,
        default: "返回",
    },
    twoLevelId: {
        type: String,
        default: "",
    },
    customParam: {
        type: String,
        default: "",
    },
    _types: {
        type: String,
        default: "",
    },
});
const route = useRoute();
const router = useRouter();
const back = () => {
    if (props.backRout) {
        router.push({ name: props.backRout });
    } else {
        router.back(-1);
    }
};

const showBackStyle = computed(() =>
    integrationParams.showBack ? "0px" : "24px"
);
watch(
    () => props.customParam,
    (val) => {
        if (val == "校门口") {
            getListPassSite(val);
        }
    },
    { immediate: true }
);
// watch(
//     () => integrationParams.twoLevelId,
//     (val) => {
//         passIds.value = val || "";
//     },
//     { immediate: true }
// );
</script>

<style lang="less" scoped>
:deep(.ant-tabs-nav) {
    padding-left: v-bind(showBackStyle);
    margin-bottom: 0;
    border-bottom: 1px solid @border-color-base;
    line-height: 33px;
    min-height: 57px;
}

.faceLibrary_warp_pageitem {
    height: calc(100% - 57px);
}

.conainer_box {
    // 不要改这里  校园系统会有间隙
    padding: 0 10px 0 10px;
    // height: calc(100vh - 56px);
}

.integrated_aggr {
    background: @body-background;
    border-radius: 12px;
    margin: 20px;
    // min-height: auto; //
    min-height: calc(100% - 57px);
}
</style>
