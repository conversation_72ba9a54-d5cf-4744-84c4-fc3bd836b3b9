<!--
 * @Author: error: git config user.name && git config user.email & please set dead value or install git
 * @Date: 2022-09-02 17:52:28
 * @LastEditors: yjx
 * @LastEditTime: 2024-01-03 16:42:29
 * @FilePath: \合并考试\cloud-system-2.0\src\components\Tables\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <a-spin :spinning="spinning">
        <a-table
            :columns="columns"
            :data-source="dataSource"
            :align="'center'"
            :bordered="bordered"
            :pagination="state.paginationAttr"
            :position="positionSort"
            @change="change"
            :scroll="scrolls"
            :row-key="rowKeyId"
            :row-selection="
                rowSelection
                    ? {
                          selectedRowKeys: state.selectedRowKeys,
                          onChange: onSelectChange,
                          type: isRadio ? 'radio' : 'checkbox'
                      }
                    : null
            "
        >
            <template #headerCell="{ column, text, record, index }">
                <slot
                    name="headerCell"
                    :column="column"
                    :text="text"
                    :record="record"
                    :index="index"
                />
            </template>
            <template #bodyCell="{ column, text, record, index }">
                <span
                    v-if="
                        (column.dataIndex !== 'index' &&
                            column.dataIndex !== 'operation' &&
                            text == null &&
                            text == undefined) ||
                        text === ''
                    "
                    >-</span
                >
                <slot
                    name="bodyCell"
                    :column="column"
                    :text="text"
                    :record="record"
                    :index="index"
                />
            </template>
            <template #emptyText>
                <slot name="emptyText">
                    <a-empty
                        image="/image/empty.png"
                        :image-style="{ width: '100%', height: '180px' }"
                    />
                </slot>
            </template>
        </a-table>
    </a-spin>
</template>

<script setup>
import { reactive, watch, shallowRef } from 'vue'
const props = defineProps({
    // hander 数据
    columns: {
        type: Array,
        default: () => []
    },
    // boby 数据
    dataSource: {
        type: Array,
        default: () => []
    },
    rowKeyId: {
        type: String,
        default: 'id'
    },
    //  勾选框单选:true 多选 false
    isRadio: {
        type: Boolean,
        default: false
    },
    //  勾选框
    rowSelection: {
        type: Boolean,
        default: false
    },
    selectedRowKeys: {
        type: Array,
        default: () => []
    },
    // 分页条数
    paginations: {
        type: Object,
        default: () => {
            return {
                pageNo: 1, // 页码
                pageSize: 10, // 条数
                total: 0
            }
        }
    },
    // loing 加载
    spinning: {
        type: Boolean,
        default: false
    },
    // 是否显示边框
    bordered: {
        type: Boolean,
        default: true
    },
    scrolls: {
        type: Object,
        default: () => {}
    }
})

const state = reactive({
    selectedRowKeys: props.selectedRowKeys || [],
    paginationAttr: {
        total: 0,
        current: 1,
        pageSize: 10,
        hideOnSinglePage: true,
        showLessItems: true,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '30', '40', '100'],
        ...props.paginations,
        showTotal: (total) => `共 ${total}  条`
    }
})
watch(
    () => props.paginations,
    ({ pageNo, pageSize, total }) => {
        state.paginationAttr.current = pageNo
        state.paginationAttr.pageSize = pageSize
        state.paginationAttr.total = total
        state.paginationAttr.hideOnSinglePage = total < 11
    },
    {
        deep: true,
        immediate: true
    }
)

watch(
    () => props.selectedRowKeys,
    (val) => {
        state.selectedRowKeys = val
    }
)

const positionSort = shallowRef(['bottomLeft'])
const emit = defineEmits(['change'])
// 分页事件
const change = (data, filters, sorter) => {

    const {field,  order } = sorter
    const { current, pageSize, total } = data
    emit('change', { pageNo: current, pageSize, field,order })
}

const onSelectChange = (selectedRowKeys, data) => {
    state.selectedRowKeys = selectedRowKeys
    emit('selectChange', data)
}
</script>
