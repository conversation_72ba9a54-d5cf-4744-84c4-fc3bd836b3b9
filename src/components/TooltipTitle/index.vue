<!-- eslint-disable -->
<!-- 这是用来装一些 只显示一行的文字 超出多就显示省略号 鼠标移入才显示完整提示的组件 -->
<!-- 其实是没必要的 -->
<template>
    <div>
        <a-tooltip :placement="placement">
            <template #title>
                <slot name="title"></slot>
            </template>
            <div class="TooltipTitle">
                <slot name="title"></slot>
            </div>
        </a-tooltip>
    </div>
</template>

<script setup name="TooltipTitle">
/* eslint-disable */
import { reactive, onMounted, ref } from "vue";
const props = defineProps({
    width: {
        type: String,
        default: "200px",
    },
    lineNumber: {
        type: Number,
        default: 1,
    },
    placement: {
        type: String,
        default: "top",
    }
});
const state = reactive({
    width: props.width,
    lineNumber: props.lineNumber
});
</script>

<style lang="less" scoped>
// 定义 trim mixin
.trim(@numLines: null) {
    & when not (@numLines =null) {
        display: -webkit-box;
        -webkit-line-clamp: @numLines;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    & when (@numLines =null) {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        display: block;
    }
}

// 使用 trim mixin
.TooltipTitle {
    width: v-bind("state.width");
    .trim(v-bind("state.lineNumber")); // 将文本限制在规定的行内
}
</style>
