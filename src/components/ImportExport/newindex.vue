<!--
 * @Descripttion: 导入导出  newindex
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-01-20 09:12:39
 * @LastEditors: jingrou
 * @LastEditTime: 2023-02-25 16:05:53
-->
<template>
    <a-modal
        :width="800"
        :bodyStyle="{ padding: 0 }"
        class="import-modal"
        :maskClosable="false"
        :okText="state.okText"
        :title="actionParams.title"
        :confirm-loading="state.loading"
        :ok-button-props="{ disabled: actionParams.spinning }"
        v-model:visible="actionParams.visible"
        @ok="handleOk"
        @cancel="handleCancel"
    >
        <!-- <a-spin :spinning="actionParams.spinning"> -->
        <div class="content newImport">
            <!-- 特别提示 -->
            <div class="hot-tip">
                <h2>{{ state.hotTip.title }}</h2>
                <ul v-if="actionParams.importExportType === 'importList'">
                    <!-- <li>
                        1.导入过程中如发现相同数据，则新数据将覆盖掉现有数据；
                    </li> -->
                    <li>
                        {{
                            actionParams.hotTip ||
                            "1.你可在下方自主选择需要导出的数据信息。"
                        }}
                    </li>
                </ul>
                <ul v-else>
                    <li>你可在下方自主选择需要导出的数据信息。</li>
                </ul>
            </div>
            <!-- 导入导出步骤 -->
            <div class="import-export-steps">
                <BulkImport />
            </div>
        </div>
        <!-- </a-spin> -->
    </a-modal>
</template>

<script setup lang="ts">
import { reactive, inject, createVNode } from "vue";
import BulkImport from "./bulkImport.vue";

import { Modal } from "ant-design-vue";

import { ExclamationCircleOutlined } from "@ant-design/icons-vue"; // 批量导入
const emit = defineEmits(["emitCallback"]);

const actionParams = inject("actionParams")();
const state = reactive({
    visible: false,
    loading: false,
    okText: "确认",
    hotTip: {
        title: "特别提示",
    },
});
// 确认
const handleOk = () => {
    actionParams.visible = false;
    emit("emitCallback");
};
// 取消
const handleCancel = () => {
    actionParams.visible = true;
    if (actionParams.isUploading) {
        Modal.confirm({
            title: "提示",
            icon: createVNode(ExclamationCircleOutlined),
            content: "文件上传中，关闭弹窗将终止文件上传",
            okText: "确认",
            cancelText: "取消",
            onOk() {
                actionParams.visible = false;
                emit("emitCallback");
            },
        });
    } else {
        actionParams.visible = false;
    }
};
</script>

<style scoped lang="less">
.content {
    padding: 24px;
    max-height: calc(100vh - 300px);
    overflow: hidden auto;
    .hot-tip {
        background: @gray-background;
        padding: 24px;
        border-radius: 4px;
        h2 {
            font-weight: 700;
        }
        ul li {
            margin: 6px 0;
        }
    }
}
</style>
