<!--
 * @Descripttion: 导入导出 ImportExport
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-01-20 09:12:39
 * @LastEditors: jingrou
 * @LastEditTime: 2023-02-18 16:07:26
-->
<template>
    <a-modal
        :width="800"
        v-model:visible="modalVisible"
        :maskClosable="false"
        :title="state.modalTitle"
        :confirm-loading="state.modalConfirmLoading"
        @ok="handleOk"
        @cancel="handleOk"
    >
        <div class="data-import_body" v-show="showCurrentDom(0)">
            <transition name="fade-in-linear">
                <div class="data-import_base__info">
                    <h5 class="dibi_title">填写导入数据信息</h5>
                    <p class="dibi_hint">
                        请按照数据模板的格式准备导入数据，模板中的表头名称不可更改，表头行不能删除
                    </p>
                    <a
                        :href="`${action.download}?t=${new Date().getTime()}`"
                        download
                    >
                        <a-button>
                            <template #icon>
                                <DownloadOutlined class="icon-color" />
                            </template>
                            下载文件
                        </a-button>
                    </a>
                </div>
            </transition>
            <transition name="fade-in-linear">
                <div class="data-export_base__info">
                    <h5 class="dibi_title">填写导入数据信息</h5>
                    <p class="dibi_hint">
                        请按照数据模板的格式准备导入数据，模板中的表头名称不可更改，表头行不能删除
                    </p>
                    <a-upload
                        :accept="accept"
                        ref="uploads"
                        :action="action.upload"
                        :multiple="false"
                        :auto-upload="false"
                        :file-list="fileList"
                        :limit="1"
                        @change="handleChange"
                    >
                        <a-button>
                            <template #icon>
                                <UploadOutlined class="icon-color" />
                            </template>
                            上传文件
                        </a-button>
                    </a-upload>
                </div>
            </transition>

            <!-- <div class="tip">
                <p>
                    <exclamation-circle-filled
                        style="margin-right: 10px; color: #faad14"
                    />特别提示
                </p>
                <p class="help">
                    导入过程中如发现相同数据，则新数据将覆盖掉现有数据
                </p>
            </div> -->
        </div>
        <!-- 导入将异常数据 -->
        <transition name="el-fade-in-linear">
            <div class="data-import_abnormal" v-show="showCurrentDom(1)">
                <div class="abnormal_hint" v-if="importData.errorCount">
                    <exclamation-circle-filled
                        style="margin-right: 10px; color: #faad14"
                    />
                    <span>继续导入将忽略异常数据</span>
                </div>
                <div class="abnormal_data__num">
                    <div class="abnormal_data__numtext">
                        正常数据条数:
                        <span class="abnormal_data__successnum">{{
                            importData.successCount
                        }}</span
                        >条
                    </div>
                    <div
                        class="abnormal_data__numtext"
                        v-if="importData.errorCount"
                    >
                        异常数据条数:
                        <span class="abnormal_data__errornum">{{
                            importData.errorCount
                        }}</span
                        >条
                    </div>
                </div>
                <div class="abnormal_data__list" v-if="importData.errorCount">
                    <div class="dia-adl_title">异常提示：</div>
                    <ul class="dia-adl_list">
                        <li
                            v-for="(item, idx) in importData.errorMsg"
                            :key="idx"
                        >
                            {{ item }}
                        </li>
                    </ul>
                </div>
            </div>
        </transition>

        <!-- loading -->
        <transition name="el-fade-in-linear">
            <div class="data-import_loading" v-show="showCurrentDom(2)">
                <div>
                    <span>{{ percentage }}%</span>
                    <a-progress
                        class="dil_progress"
                        :percentage="percentage"
                        color="#00B781"
                        :show-text="false"
                        :stroke-width="10"
                    ></a-progress>
                    <p class="dil_hint">正在导入数据</p>
                </div>
            </div>
        </transition>
        <!-- 导入成功 -->
        <transition name="el-fade-in-linear">
            <div class="data-import_success" v-show="showCurrentDom(3)">
                <div>
                    <check-circle-filled
                        style="
                            margin-right: 10px;
                            color: var(--primary-color);
                            font-size: 40px;
                        "
                    />
                    <h3 class="dis_title">数据导入完成</h3>
                    <p class="dis_hint">
                        您已成功导入
                        <span class="dis_hint__success">{{
                            importData.successCount
                        }}</span
                        >条数据，未导入
                        <span class="dis_hint__danger">{{
                            importData.errorCount
                        }}</span
                        >条数据
                    </p>
                </div>
            </div>
        </transition>
        <template #footer>
            <a-button
                key="back"
                :type="bodyStatus !== 3 ? '' : 'primary'"
                @click="handleOk"
                >{{ bodyStatus !== 3 ? "取消" : "确定" }}</a-button
            >
            <a-button @click="uploadAgain" v-show="showCurrentDom(1)"
                >重新上传</a-button
            >
            <a-button
                type="primary"
                :disabled="!importData.successCount"
                @click="handleImportData"
                :loading="loading"
                v-show="showCurrentDom(1)"
                >继续导入</a-button
            >
            <a-button
                key="submit"
                type="primary"
                :loading="loading"
                @click="handleAmportExport"
                v-show="showCurrentDom(0)"
                >下一步</a-button
            >
        </template>
    </a-modal>
</template>

<script setup lang="ts">
import { reactive, ref, watch, getCurrentInstance, computed } from "vue";
import api from "@/api/index";
import { message } from "ant-design-vue";
import type { UploadChangeParam, UploadProps } from "ant-design-vue";
const { proxy } = getCurrentInstance() as any;
const emit = defineEmits(["emitCallback"]);
const modalVisible = ref<boolean>(false);
const loading = ref<boolean>(false);
const visible = ref<boolean>(false);
const bodyStatus = ref<number>(0);
const percentage = ref<number>(0);
const fileList = ref<any>([]);
const time = ref<any>(null);
const importData = ref<any>({
    successCount: 0,
    errorCount: 0,
    errorMsg: [],
    id: "",
});
const props = defineProps({
    action: {
        type: Object,
        default: () => {},
    },
    accept: {
        type: String,
        default: () => {},
    },
    semesterId: {
        type: String,
        default: () => {},
    },
});
const state = reactive({
    modalConfirmLoading: false,
    modalTitle: "数据导入",
});
// 下一步
const handleAmportExport = async () => {
    const formData = new FormData();
    fileList.value.forEach((file: any) => {
        formData.append("file", file.originFileObj);
    });
    loading.value = true;
    proxy.$defHttp
        .post(proxy.action.upload, formData)
        .then((res: any) => {
            const { code, message, data } = res;
            if (!code) {
                setTimeout(() => {
                    loading.value = false;
                    visible.value = false;
                    // proxy.$message['success'](message)
                    importData.value = data;
                    // if (!data.errorCount) {
                    //     bodyStatus.value = 3
                    // } else {
                    bodyStatus.value = 1;
                    // }
                }, 100);
            } else {
                proxy.$message.error(message);
            }
        })
        .catch((error: any) => {
            loading.value = false;
        });
};

// 继续导入
const handleImportData = () => {
    // if (importData.value.successCount) {
    // percentage.value = 0
    // clearInterval(time.value)
    // time.value = setInterval(() => {
    // if (percentage.value !== 100) {
    loading.value = true;
    const obj = {
        importId: importData.value.id,
        semesterId: props.semesterId,
    };
    proxy.$defHttp
        .get(props.action.continueImport, obj)
        .then((res: any) => {
            const { code, message, data } = res;
            !code && (bodyStatus.value = 3);
            importData.value.successCount = data.successCount;
            importData.value.errorCount = data.errorCount;
            proxy.$message[!code ? "success" : "error"](message);
            loading.value = false;
            fileList.value = [];
        })
        .catch((error: any) => {
            loading.value = false;
        });
    // } else {
    //     clearInterval(time.value)
    // }
    // }, 1500)
    // }
};
// 重新上传
const uploadAgain = () => {
    fileList.value = [];
    bodyStatus.value = 0;
};
const handleChange = (info: UploadChangeParam) => {
    let resFileList = [...info.fileList];
    resFileList = resFileList.slice(-1);
    resFileList = resFileList.map((file) => {
        if (file.response) {
            file.url = file.response.url;
        }
        return file;
    });
    fileList.value = resFileList;
};

const handleOk = () => {
    bodyStatus.value == 3 && emit("emitCallback");
    uploadAgain();
    fileList.value = [];
    modalVisible.value = false;
};
defineExpose({
    modalVisible,
});
const showCurrentDom = computed(() => {
    return (state: any) => bodyStatus.value == state;
});
</script>

<style scoped lang="less">
.data-export_base__info,
.data-import_base__info {
    margin-bottom: 20px;
    background: @gray-background;
    border: 1px dashed @border-color-base;
    padding: 32px;

    &:hover {
        border-color: @link-color;
    }

    .dibi_hint {
        padding: 10px 0;
        color: @suggestive-color;
    }

    .icon-color {
        color: @primary-color;
    }
}

// 异常数据
.data-import_abnormal {
    display: flex;
    flex-direction: column;
    height: 100%;

    .abnormal_hint {
        height: 48px;
        line-height: 48px;
        background: #fdf6ec;
        border: 1px solid #faecd8;
        color: #e6a23c;
        border-radius: 4px;
        padding: 0 20px;

        .dis_icon {
            padding-right: 10px;
            font-size: 16px;
        }
    }

    .abnormal_data__num {
        height: 98px;
        margin: 15px 0;
        border: 1px solid rgba(0, 0, 0, 0.15);
        padding: 0 20px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        border-radius: 2px;
        color: #303133;
        font-weight: bold;

        .abnormal_data__numtext {
            &:first-child {
                margin-bottom: 10px;
            }
        }

        .abnormal_data__successnum {
            color: #2c8aff;
            padding-right: 3px;
        }

        .abnormal_data__errornum {
            color: #f56c6c;
            padding-right: 3px;
        }
    }

    .abnormal_data__list {
        flex: 1;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 2px;
        padding: 20px;
        overflow-y: auto;
    }

    .dia-adl_title {
        color: #303133;
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .dia-adl_list {
        overflow: hidden auto;
        max-height: 300px;

        li {
            padding: 3px 0;
            color: #909399;
        }
    }
}

// 导入成功
.data-import_success {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: center;
    text-align: center;

    .dis_icon {
        font-size: 42px;
        color: #2c8aff;
    }

    .dis_title {
        font-size: 24px;
        color: #303133;
        margin: 12px 0;
    }

    .dis_hint {
        font-size: 14px;
        color: #303133;
    }

    .dis_hint__danger {
        color: #f56c6c;
        padding-right: 3px;
    }

    .dis_hint__success {
        color: #2c8aff;
        padding-right: 3px;
    }
}

// 进度条
.data-import_loading {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: center;
    text-align: center;

    .dil_progress {
        margin: 12px 0;
    }
}

.data-export_base__info {
    // border-color: @error-color;
}

.tip {
    position: relative;
    height: 50px;
}
</style>
