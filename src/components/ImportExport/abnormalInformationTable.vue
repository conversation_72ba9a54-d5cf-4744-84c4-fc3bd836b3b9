<!-- abnormalInformationTable 异常数据信息表 -->
<template>
    <div class="abnormal">
        <div class="abnormal-total">
            <div class="success">
                <check-circle-filled style="color: #00b781" />
                正常数据：<span class="success-total">
                    {{ status.successCount }}
                </span>
                条
            </div>
            <div class="error">
                <exclamation-circle-filled style="color: #faad14" />
                异常数据：<span class="error-total">
                    {{ status.errorCount }}
                </span>
                条
            </div>
        </div>
        <!-- 异常提示 -->
        <div class="abnormal-tip-list">
            <div class="hande">
                <h1>异常提示</h1>
                <a-button
                    type="link"
                    style="color: #00b781"
                    @click="getExportErrorData"
                >
                    下载异常数据
                </a-button>
            </div>
            <YTable
                :columns="state.column"
                :dataSource="status.newErrorMsg"
                :spinLoad="state.spinLoad"
                :rowSelection="false"
                :totals="state.pagination"
                @onSelectedRowKeys="handerSelectedRowKeys"
            >
                <template #headerCell="{ column }">
                    <span class="table-title">{{ column.title }}</span>
                </template>
            </YTable>
        </div>
    </div>
</template>

<script setup>
import { reactive, inject } from "vue"
import YTable from "@/components/YTable/index.vue"

import { exportData, importErrorLog } from "@/api/defHttp"
import api from "@/api/index"

defineProps({
    status: {
        type: Object,
        default: () => {
            return {
                newErrorMsg: [],
                errorMsg: [],
                errorCount: 0,
                successCount: 0
            }
        }
    }
})
const state = reactive({
    column: [
        {
            title: "行数",
            width: 100,
            dataIndex: "rows",
            key: "rows"
        },
        {
            title: "错误提示",
            dataIndex: "errorTips",
            key: "errorTips"
        }
    ],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
    },
    spinLoad: false
})
const actionParams = inject("actionParams")()

// 下载导入错误数据
const getExportErrorData = () => {
    const { errorUrl, importId, downloadText, importType } = actionParams
    if (errorUrl) {
        const params = {
            importId,
            importType
        }
        importErrorLog(errorUrl, params, `${downloadText}异常数据`)
    } else {
        const Url = `${api.exportErrorData}/${importId}`
        exportData(Url, {}, `${downloadText}异常数据`)
    }
}

const handerSelectedRowKeys = (data) => {
    const { current, pageSize, total } = data
    state.pagination = { pageNo: current, pageSize, total }
}
</script>

<style scoped lang="less">
.abnormal {
    .abnormal-total {
        background: @gray-background;
        padding: 6px 24px;
        border-radius: 4px;

        .success,
        .error {
            font-weight: 700;
            margin: 8px 0;

            .success-total {
                color: @primary-color;
            }

            .error-total {
                color: @warning-color;
            }
        }
    }

    .abnormal-tip-list {
        border-radius: 4px;

        .hande {
            display: flex;
            justify-content: space-between;
            align-items: center;

            h1 {
                font-weight: 700;
                margin: 14px 0;
            }
        }

        .table-title {
            color: @suggestive-color;
        }
    }

    :deep(.ant-pagination) {
        margin: 0;
        height: 60px;
        li {
            margin-left: 6px;
        }
    }
}
</style>
