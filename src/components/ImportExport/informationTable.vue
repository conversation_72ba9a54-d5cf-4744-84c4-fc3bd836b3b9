<!-- informationTable 数据信息表 -->
<template>
    <div class="information-table">
        <div class="hand">
            <h1>
                {{ themeTitle }}信息表数据信息
                <span>（你可自主选择想导出的数据信息）</span>
            </h1>
            <a-button type="link" style="color: #00b781" @click="downloadFile">
                下载
            </a-button>
        </div>
        <div class="information-item">
            <div>
                <a-checkbox
                    v-model:checked="state.checkAll"
                    :indeterminate="state.indeterminate"
                    @change="onCheckAllChange"
                >
                    全选
                </a-checkbox>
            </div>

            <a-checkbox-group
                v-model:value="state.checkedList"
                class="checkbox-group"
            >
                <a-row>
                    <a-col
                        :span="6"
                        v-for="item in state.plainOptions"
                        :key="item.value"
                    >
                        <a-checkbox :value="item" :disabled="item.default">
                            <span class="ellipsis">
                                {{ item.label }}
                            </span>
                        </a-checkbox>
                    </a-col>
                </a-row>
            </a-checkbox-group>
        </div>
    </div>
</template>

<script setup>
import { reactive, watch, computed, toRaw } from "vue"
import { useStore } from "@/store/index"
defineProps({
    themeTitle: {
        type: String,
        default: "教职工"
    }
})
const store = useStore()
const emit = defineEmits(["emitDownloadFile"])
const commentJsonKey = () => {
    const checked = []
    let isIndeterminate = false
    return computed(() => {
        const initTableList =
            store.state.base.commentJsonKeys.INIT_STUDENT_IMPORT_EXPORT_LIST
        initTableList.forEach((v) => {
            if (v.default) {
                checked.push(v)
                isIndeterminate = true
            }
        })
        return {
            checkedList: toRaw(checked),
            plainOptions: toRaw(initTableList),
            indeterminate: isIndeterminate
        }
    })
}
const { checkedList, plainOptions, indeterminate } = commentJsonKey().value

const state = reactive({
    checkAll: false,
    indeterminate,
    checkedList,
    plainOptions
})

const downloadFile = () => {
    emit("emitDownloadFile", state.checkedList)
}
// 全选
const onCheckAllChange = (e) => {
    Object.assign(state, {
        checkedList: e.target.checked ? state.plainOptions : checkedList,
        indeterminate: false
    })
}
// 监听选中状态
watch(
    () => state.checkedList,
    (val) => {
        state.indeterminate =
            !!val.length && val.length < state.plainOptions.length
        state.checkAll = val.length === state.plainOptions.length
    }
)
</script>

<style scoped lang="less">
.information-table {
    border: 1px dashed @border-color-base;
    border-radius: 4px;

    .hand {
        background: @gray-background;
        padding: 12px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h1 {
            font-weight: 800;

            span {
                font-weight: 400;
                color: @suggestive-color;
            }
        }
    }

    .information-item {
        padding: 12px 16px;

        :deep(.checkbox-group) {
            width: 100%;

            .ant-col {
                margin: 8px 0;
            }

            .ellipsis {
                width: 144px;
                display: block;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
            }
        }
    }
}
</style>
