<!-- bulkImport 批量导入 -->
<template>
    <div class="bulk-import">
        <ul class="item">
            <li
                v-for="(item, idx) in state[actionParams.importExportType]"
                :key="idx"
            >
                <h1>{{ item.text }}</h1>
                <template v-if="item.value === 'import'">
                    <!-- 下载文件 -->
                    <div class="import-btn">
                        <a-button
                            v-for="(item, indx) in actionParams.importBtn"
                            :class="{ btn: indx }"
                            :key="item.value"
                            type="primary"
                            ghost
                            :disabled="actionParams.spinning"
                            :loading="state.btnLoading"
                            @click="showSDownloadFile"
                        >
                            <template #icon>
                                <DownloadOutlined />
                            </template>
                            {{ item.text }}
                        </a-button>
                    </div>
                    <!-- 数据信息表 -->
                    <InformationTable
                        v-if="state.isInformationShow"
                        :themeTitle="actionParams.themeTitle"
                        @emitDownloadFile="downloadFile"
                    />
                </template>
                <template v-if="item.value === 'export'">
                    <!-- 上传文件 -->
                    <div class="export-btn">
                        <a-upload
                            ref="uploads"
                            :multiple="false"
                            :auto-upload="false"
                            v-model:file-list="state.fileList"
                            :limit="1"
                            :before-upload="beforeUpload"
                            @change="handleChange"
                        >
                            <a-button
                                v-for="(item, indx) in state.exportBtn"
                                :class="{ btn: indx }"
                                :loading="state.impBtnLoading"
                                :disabled="actionParams.spinning"
                                :key="item.value"
                                type="primary"
                                ghost
                            >
                                <template #icon>
                                    <UploadOutlined />
                                </template>
                                {{ item.text }}
                            </a-button>
                        </a-upload>
                        <div v-if="state.isPercent && state.fileList.length">
                            <a-progress
                                :percent="state.percent"
                                size="small"
                                :strokeColor="state.strokeColor"
                                :showInfo="false"
                            />
                            <span style="color: #00000073">
                                <close-circle-filled
                                    :style="{ color: state.strokeColor }"
                                    v-if="state.progresStatus === 'exception'"
                                />
                                <check-circle-filled
                                    :style="{ color: state.strokeColor }"
                                    v-if="
                                        state.progresStatus === 'active' &&
                                        state.percent == 100
                                    "
                                />
                                {{ state.progresStatusText }}</span
                            >
                        </div>
                    </div>

                    <!-- 异常数据信息表 -->
                    <AbnormalInformationTable
                        v-if="state.status.errorCount && state.fileList.length"
                        :status="state.status"
                    />
                </template>
            </li>
        </ul>
    </div>
</template>

<script setup>
import { reactive, inject, watch, toRaw, computed } from 'vue'
import InformationTable from './informationTable'
import AbnormalInformationTable from './abnormalInformationTable'
import {
    postUpload,
    importErrorLog,
    getProgresst,
    getImport
} from '@/api/defHttp'
import { useStore } from '@/store/index'
const store = useStore()

const actionParams = inject('actionParams')()
const state = reactive({
    strokeColor: '#00b781',
    progresStatus: 'active',
    progresStatusText: '正在上传中...',
    percent: 0,
    isPercent: false,
    status: {
        errorMsg: [],
        errorCount: 0,
        successCount: 0
    },
    time: null,
    isInformationShow: false,
    impBtnLoading: false,
    btnLoading: false,
    fileList: [],
    importList: [
        { text: '一、下载导入模板，批量填写数据信息', value: 'import' },
        { text: '二、上传填写好的信息表', value: 'export' }
    ],
    exportList: [
        { text: '一、导出数据信息', value: 'import' }
        // { text: '二、上传修改好的信息表', value: 'export' }
    ],
    importBtn: actionParams.importBtn,
    exportBtn: [{ value: '', text: '上传文件' }]
})
// 进度条 成功失败样式
const successError = (staus) => {
    if (staus) {
        state.progresStatus = 'active'
        state.strokeColor = '#00B781'
        state.progresStatusText = '上传成功'
    } else {
        state.percent = 100
        state.progresStatus = 'exception'
        state.strokeColor = '#f5222d'
        state.progresStatusText = '上传失败'
    }
    actionParams.isUploading = false
}
const commentJsonKey = (key) => {
    return computed(() =>
        toRaw(store.state.base.commentJsonKeys.INIT_IMPORT_EXPORT_LIST[key])
    )
}
// 下载文件
const downloadFile = (items) => {
    state.isInformationShow = false
    const { downloadUrl, importType, downloadText, importParam, btnType } =
        actionParams
    state.btnLoading = true
    const params = {
        code: btnType || '',
        params: importParam // 查询 参数
    }
    if (items && items.length) {
        params.fields = items
    }
    importErrorLog(downloadUrl, params, downloadText, importType).finally(
        () => {
            state.btnLoading = false
        }
    )
}
// 下载文件 按钮事件（设计如此 002B2A4B.png）
const showSDownloadFile = () => {
    // 教职工管理  零时
    if (actionParams.init) {
        window.location.href = actionParams.downloadUrl
        return
    }
    if (
        [
            'rollColumn',
            'deptColumn',
            'subjectColumn',
            'employeeColumn',
            'subjectTeacherColumn'
        ].includes(actionParams.btnType)
    ) {
        downloadFile()
        return
    }
    if (commentJsonKey(actionParams.btnType).value) {
        state.isInformationShow = true
    }
}
const beforeUpload = (file) => {
    state.fileList = [...state.fileList, file]
    return false
}
// 上传文件
const handleChange = ({ file, fileList }) => {
    state.status.errorCount = 0
    state.impBtnLoading = false
    const resFileList = fileList.slice(-1)
    state.fileList = resFileList
    const formData = new FormData()
    state.fileList.forEach((file) => {
        formData.append('file', file.originFileObj)
    })
    const { uploadUrl, importType } = actionParams
    if (importType) {
        formData.append('importType', importType)
    }
    state.percent = 0
    state.isPercent = true
    state.progresStatus = 'active'
    state.strokeColor = '#00B781'
    state.progresStatusText = '正在上传中...'
    postUpload(uploadUrl, formData)
        .then((res) => {
            const { data } = res
            data && getUploadprogres(data)
        })
        .catch(() => {
            successError(false)
            state.impBtnLoading = false
        })
}

// 获取上传进度
const getUploadprogres = (importId) => {
    actionParams.importId = importId
    const { progressUrl, importType } = actionParams
    // 大学版
    let fetch = null
    if (importType) {
        fetch = getProgresst(progressUrl, { importId })
    } else {
        fetch = getImport(`${progressUrl}/${importId}`)
    }
    fetch
        .then((res) => {
            const { data } = res
            backUpload(data, importId)
        })
        .catch(() => {
            successError(false)
        })
}

const backUpload = (data, importId) => {
    const { schedule, errorCount, errorMsg } = data
    actionParams.spinning = true
    state.percent = schedule
    actionParams.isUploading = true
    if (schedule !== 100) {
        state.time = setTimeout(() => {
            getUploadprogres(importId)
        }, 1000)
    } else {
        // state.fileList = []
        // state.isPercent = false
        successError(!errorCount)
        actionParams.spinning = false
        state.impBtnLoading = false
        let newErrorMsg = []
        if (errorCount) {
            newErrorMsg = errorMsg.map((v) => {
                const [rows, errorTips] = v.split('行: ')
                return {
                    rows: rows + '行',
                    errorTips
                }
            })
        }
        state.status = {
            ...data,
            newErrorMsg
        }
        clearInterval(state.time)
    }
}
// 监听上传窗口是否要关闭并且是否在上传：如是则 清除定时器 终止轮循
watch(
    () => actionParams.visible,
    (val) => {
        state.fileList = []
        state.status = {
            errorMsg: [],
            errorCount: 0,
            successCount: 0
        }
        if (!val) {
            state.btnLoading = false
            if (actionParams.spinning) {
                actionParams.spinning = false
                actionParams.isUploading = false
                clearInterval(state.time)
            }
        } else {
            state.fileList = []
            state.isInformationShow = false
        }
    }
)
watch(
    () => state.fileList,
    (val) => {
        if (!val.length) {
            actionParams.spinning = false
            clearInterval(state.time)
        }
    }
)
</script>

<style scoped lang="less">
:deep(.item) {
    li {
        margin: 24px 0;
    }

    h1 {
        font-weight: 800;
    }
}

:deep(.export-btn),
:deep(.import-btn) {
    margin: 24px 0;

    .btn {
        margin-left: 24px;
    }
}
</style>
