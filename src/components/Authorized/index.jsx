/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-04-15 09:36:52
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-04-15 11:49:49
 */
// eslint-disable-next-line vue/multi-word-component-names
// 权限组件
import { defineComponent, computed } from "vue";
import router from "@/router";
export default defineComponent({
    functional: true,
    props: {
        auth: {
            type: String,
            required: true,
        },
    },
    setup(props, { slots }) {
        let permissions = [];
        const btnList = router.currentRoute.value.meta?.btnList;
        if (btnList) {
            permissions = btnList.map((i) => i.value);
        }

        const _slot = computed(() => slots.default);
        return permissions.includes(props.auth) ? _slot.value : null;
    },
});
