<template>
    <editor
        class="引用主题文件"
        v-model="content"
        tag-name="div"
        :init="init"
        @keydown="handleKeydown"
    />
</template>

<script>
import Editor from "@tinymce/tinymce-vue";
import tinymce from "tinymce/tinymce";
import "tinymce/themes/silver/theme"; // 引用主题文件
import "tinymce/icons/default"; // 引用图标文件
import "tinymce/plugins/link";
// import "tinymce/plugins/table";
import "tinymce/plugins/lists";
import "tinymce/plugins/advlist";
import "tinymce/plugins/anchor";
import "tinymce/plugins/autolink"; // 锚点
import "tinymce/plugins/autoresize";
import "tinymce/plugins/autosave";
import "tinymce/plugins/charmap"; // 特殊字符
import "tinymce/plugins/code"; // 查看源码
import "tinymce/plugins/codesample"; // 插入代码
import "tinymce/plugins/directionality"; //
// import 'tinymce/plugins/fullpage' // 页面属性编辑
import "tinymce/plugins/fullscreen"; // 全屏
import "tinymce/plugins/help"; // 帮助
import "tinymce/plugins/hr"; // 横线
import "tinymce/plugins/image"; // 图片
import "tinymce/plugins/imagetools"; // 图片工具
import "tinymce/plugins/importcss"; // 图片工具
import "tinymce/plugins/insertdatetime"; // 时间插入
// import "tinymce/plugins/media"; //媒体插入
import "tinymce/plugins/nonbreaking"; //
import "tinymce/plugins/noneditable"; // 不间断空格
import "tinymce/plugins/pagebreak"; // 分页
import "tinymce/plugins/paste"; // 粘贴
import "tinymce/plugins/preview"; // 预览
import "tinymce/plugins/print"; // 打印
import "tinymce/plugins/quickbars"; // 快捷菜单
import "tinymce/plugins/save"; // 保存
import "tinymce/plugins/searchreplace"; // 查询替换
import "tinymce/plugins/spellchecker"; // 拼写检查
import "tinymce/plugins/tabfocus"; //
import "tinymce/plugins/template"; // 插入模板
import "tinymce/plugins/textpattern"; //
import "tinymce/plugins/toc"; //
import "tinymce/plugins/visualblocks"; //
import "tinymce/plugins/visualchars"; //
import "tinymce/plugins/wordcount"; // 数字统计
import "tinymce/skins/ui/oxide/skin.css";

import { getFileUpload } from "@/api/notice.js";

export default {
    props: {
        modelValue: String,
        emitKeydown: {
            type: Boolean,
            default: false,
        },

        isNum: {
            type: Number,
            default: 10,
        },
        toolbar: {
            type: String,
            default: "",
        },
    },
    components: {
        editor: Editor,
    },

    emits: { "update:modelValue": null, "update:emitKeydown": false },

    setup(props, context) {
        const init = {
            language_url: "/tinymce/langs/zh_CN.js", // 中文语言包路径
            language: "zh_CN",
            // 一个汉字算一个字符，为了统计相对准确
            wordcount_countregex:
                /([\w\u2019\x27\-\u00C0-\u1FFF]+)|([^\x00-\xff])/g,
            skin_url: "tinymce/skins/ui/oxide", // 编辑器皮肤样式
            content_css: "tinymce/skins/content/default/content.css",
            menubar: false, // 隐藏菜单栏
            autoresize_bottom_margin: 50,
            max_height: 500,
            min_height: 160,
            //   height: 320,
            content_style: "img {max-width:1920px;}",
            // content_style: 'img {max-width:100%;}',
            toolbar_mode: "undo redo | link media",
            file_picker_types: "media",
            plugins:
                "visualchars visualblocks toc textpattern template tabfocus spellchecker searchreplace save  print preview paste pagebreak  nonbreaking insertdatetime importcss imagetools image hr help fullscreen directionality codesample code charmap link lists advlist anchor autolink autoresize autosave", // 插件需要import进来
            toolbar:
                props.toolbar ||
                "formats undo redo fontsizeselect fontselect image indent aligncenter alignleft alignright alignjustify lineheight blockquote numlist bullist forecolor backcolor bold italic  | fullscreen wordcount",
            // toolbar:
            // "wordcount formats undo redo paste print fontsizeselect fontselect template fullpage| ltr rtl visualchars visualblocks toc spellchecker searchreplace|save  pagebreak nonbreaking|media image|outdent indent aligncenter alignleft alignright alignjustify lineheight  underline quicklink h2 h3 blockquote numlist bullist  removeformat forecolor backcolor bold italic strikethrough cut codesample code |anchor fullscreen|help",
            // hr charmap link insertdatetime|subscript superscript
            // content_style: "p {margin: 5px 0; font-size: 14px}",
            fontsize_formats: "12px 14px 16px 18px 24px 36px 48px 56px 72px",
            font_formats:
                "微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;",
            branding: false,
            elementpath: false,
            resize: false, // 禁止改变大小
            statusbar: false, // 隐藏底部状态栏
            placeholder: "按ctrl+i，唤起AI", // 占位符文本
            media_url_resolver(data, resolve) {
                try {
                    const videoUri = encodeURI(data.url);
                    const embedHtml = `<p>
						<span
							data-mce-selected="1"
							data-mce-object="video"
							data-mce-p-controls="controls"
							data-mce-p-controlslist="nodownload"
							data-mce-p-allowfullscreen="true"
							style="width: 200px;height:120px;display: block;"
							data-mce-p-src=${videoUri} >
							<video src=${videoUri} width="100%" height="100%" controls="controls" controlslist="nodownload">
							</video>
						</span>
						</p>
						<p style="text-align: left;"></p>`;
                    resolve({ html: embedHtml });
                } catch (e) {
                    resolve({ html: "" });
                }
            },
            file_picker_callback: async (cb, value, meta) => {
                //     //当点击meidia图标上传时,判断meta.filetype == 'media'有必要，因为file_picker_callback是media(媒体)、image(图片)、file(文件)的共同入口
                if (meta.filetype === "media") {
                    // 模拟上传本地视频
                    const input = document.createElement("input");
                    input.setAttribute("type", "file");
                    input.setAttribute("accept", ".mp4");
                    input.onchange = function () {
                        const file = this.files[0];
                        const fd = new FormData();
                        fd.append("img", file);
                        getFileUpload(fd).then((res) => {
                            // const { data } = res
                            // const url = data[0]?.url
                            callback(rr.filePath);
                        });
                    };
                    input.click();
                }
            },
            //   图片上传
            images_upload_handler: async (blobInfo, success, failure) => {
                try {
                    const formData = new FormData();
                    const blob = blobInfo.blob();
                    formData.append("file", blob, blobInfo.filename());
                    const isLt2M = Number(blob.size / 1024 / 1024);
                    if (isLt2M > props.isNum) {
                        failure("图像大小不得超过" + props.isNum + "M！");
                        return;
                    }
                    const { data } = await getFileUpload(formData);
                    const url = data[0]?.url;
                    success(url);
                } catch (error) {
                    success("");
                }
                // var reader = new FileReader();
                // reader.readAsDataURL(blobInfo.blob());
                // reader.onload = function () {
                //   success(this.result);
                // };
            },
        };
        tinymce.init; // 初始化
        const revert_data = (content) => {
            context.emit("update:modelValue", content);
        };

        return {
            init,
            revert_data,
        };
    },
    data(props) {
        return {
            content: this.modelValue || props.modelValue,
        };
    },
    methods: {
        handleKeydown(event) {
            // 检测 Ctrl+I 组合键（event.keyCode: I 对应 73，i 对应 105）
            const isCtrlI =
                event.ctrlKey &&
                (event.key === "i" ||
                    event.keyCode === 73 ||
                    event.keyCode === 105);
            if (isCtrlI) {
                event.preventDefault(); // 阻止默认行为（如斜体）
                console.log("Ctrl+I 被拦截！");
                this.$emit("update:emitKeydown", true);
                // 执行自定义操作
                // editor.insertContent("【自定义快捷键内容】");
            }
        },
    },
    mounted() {},
    watch: {
        content() {
            this.revert_data(this.content);
        },
        modelValue(val) {
            if (val) {
                this.content = val;
            }
        },
    },
};
</script>

<style>
.tox .tox-dialog-wrap__backdrop {
    background: none;
}

.ant-form-item-control-input-content .tox-fullscreen {
    top: 48px !important;
}
</style>
