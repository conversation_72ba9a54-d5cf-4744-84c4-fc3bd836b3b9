<template>
    <a-sub-menu v-if="item.children && item.children.length" :key="item.path">
        <template #title>
            <!-- <component :is="item.meta && item.meta.icon"></component> -->
            <i class="iconfont" :class="item.meta.icon"></i>
            <span
                :class="{
                    menu_circular: !item.meta.icon,
                    menu_circular__activation: $route.path == item.path,
                }"
                >{{ item.meta.title }}</span
            >
        </template>
        <SubMenu
            :item="citem"
            v-for="citem in item.children"
            :key="citem.path"
        />
    </a-sub-menu>
    <a-menu-item v-else :key="item.path">
        <!-- <component :is="item.meta && item.meta.icon"></component> -->
        <i class="iconfont" :class="item.meta.icon"></i>
        <span
            :class="{
                menu_circular: !item.meta.icon,
                menu_circular__activation: $route.path == item.path,
            }"
            >{{ item.meta && item.meta.title }}</span
        >
    </a-menu-item>
</template>

<script>
export default {
    name: "SubMenu",
    props: {
        item: {
            type: Object,
            required: true,
        },
    },
};
</script>

<style lang="less" scoped>
.menu_circular {
    position: relative;
    padding-left: 8px;
    &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background: #000;
        display: block;
        margin-top: -2px;
    }
}
.menu_circular__activation {
    &::before {
        background: @primary-color;
    }
}
.ant-menu-inline-collapsed {
    .ant-menu-title-content {
        span {
            display: none;
        }
    }
}
</style>
