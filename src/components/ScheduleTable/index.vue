<!--
 * @Author: laihq
 * @Date: 2022-02-17 11:46:16
 * @LastEditTime: 2022-03-24 12:28:54
 * @LastEditors: <EMAIL>
 * @Description:
 * @FilePath: \laihq-web-servef:\code\cloud-system\src\views\solution\schedule\index.vue
-->
<template>
    <div class="calendar-info" :style="{ height: tableH }" ref="containerRef">
        <FullCalendar class="demo-app-calendar" ref="fullCalendarRef" :options="calendarOptions">
            <template #eventContent="arg">
                <span>{{ arg.timeText }}</span>
                <span style="padding-left: 4px">{{ arg.event.title }}</span>
            </template>
        </FullCalendar>
        <!-- <a-row style="margin: 15px">
            <a-col :span="24" class="page-title-box">
                <h4 class="page-title">日历</h4>
                <div class="page-title-right">
                    <div style="margin-right: 10px">
                        <a-date-picker v-model:value="pickDate" type="date" placeholder="选择日期"></a-date-picker>
                    </div>
                    <a-button>刷新</a-button>
                    <a-button type="primary" style="margin-left: 10px">搜索</a-button>
                </div>
            </a-col>
        </a-row> -->
        <a-row>
            <a-col :span="24">
                <a-card>
                    <a-row>
                        <!-- <a-col :span="6">
                            <a-button type="primary" style="width: 258.25px; height: 42px"
                                @click="handleCreateEvent">创建新的事件</a-button>
                            <div>
                                <br />
                                <p class="text-muted text-left">拖放活动或在日历中单击</p>
                            </div>
                            <div ref="containerRef">
                                <div v-for="item in taskList" :key="item.id" class="external-events"
                                    :class="[item.classNames]">
                                    <i class="list-circle"></i> {{ item.title }}
                                </div>
                            </div>
                            <div style="text-align: left">
                                <a-checkbox v-model:checked="checked" size="large">拖动后删除</a-checkbox>
                            </div>
                            <div style="text-align: left">
                                <a-checkbox v-model:checked="togglechecked" size="large"
                                    @change="handleWeekendsToggle">不显示星期六和星期日</a-checkbox>
                            </div>
                        </a-col> -->
                        <a-col :span="18">
                            <!-- <FullCalendar class="demo-app-calendar" :options="calendarOptions">
                                <template #eventContent="arg">
                                    <span>{{ arg.timeText }}</span>
                                    <span style="padding-left:4px;">{{ arg.event.title }}</span>
                                </template>
                            </FullCalendar> -->
                        </a-col>
                    </a-row>
                </a-card>
            </a-col>
        </a-row>
        <a-modal v-model:visible="dialogFormVisible" title="添加事项" width="450px">
            <a-form>
                <a-form-item label="请输入事项" :laba-width="formLabelWidth" v-bind="validateInfos.title">
                    <a-input v-model:value="form.title" autocomplete="off" style="width: 189.5px"></a-input>
                </a-form-item>
                <a-form-item label="选择事项颜色" :laba-width="formLabelWidth" v-bind="validateInfos.className">
                    <a-select v-model:value="form.className" placeholder="Please select category color">
                        <a-option label="Success" value="bg-success"></a-option>
                        <a-option label="Danger" value="bg-danger"></a-option>
                        <a-option label="Info" value="bg-info"></a-option>
                        <a-option label="Warning" value="bg-warning"></a-option>
                        <a-option label="Dark" value="bg-dark"></a-option>
                    </a-select>
                </a-form-item>
            </a-form>
            <template #footer>
                <span class="dialog-footer">
                    <a-button @click="dialogFormVisible = false">关闭</a-button>
                    <a-button type="primary" @click="handleSaveCategory()">保存</a-button>
                </span>
            </template>
        </a-modal>
    </div>
</template>
<script setup>
// https://fullcalendar.io/docs/plugin-index
// https://www.helloweba.net/javascript/445.html

import { onMounted, reactive, ref, watch, nextTick } from 'vue'

import { Modal, message, Form } from 'ant-design-vue'

import dayjs from 'dayjs'

import '@fullcalendar/core/vdom' // solve problem with Vite
import FullCalendar from '@fullcalendar/vue3'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import interactionPlugin, { Draggable } from '@fullcalendar/interaction'

import tippy from 'tippy.js'
import 'tippy.js/dist/tippy.css'

import resourceTimeGridPlugin from '@fullcalendar/resource-timegrid'

import { calendar } from '@/utils/calendar'

import { INITIAL_EVENTS, createEventId } from './event-utils'

const useForm = Form.useForm
// eslint-disable-next-line no-unused-vars
const props = defineProps({
    initialEvents: {
        type: Array,
        default: () => []
    }
})

let currentEvents

const tableH = ref('572px')
const containerRef = ref()
const fullCalendarRef = ref()

// const calendarRef = ref()
// const init = ref(false)

const newDate = ref('')
const pickDate = ref('')
const checked = ref(false)
const togglechecked = ref(false)
const dialogFormVisible = ref(false)
const formLabelWidth = ref(120)

const form = reactive({
    upKey: 0,
    title: '',
    className: 'bg-success'
})
const rules = reactive({
    title: [
        {
            required: true,
            message: '请输入活动标题',
            trigger: 'blur'
        }
    ],
    className: [
        {
            required: true,
            message: '请选择类别颜色',
            trigger: 'blur'
        }
    ]
})

const { resetFields, validate, validateInfos } = useForm(form, rules)

const taskList = reactive([
    { title: '课程', classNames: 'bg-success', id: createEventId() },
    { title: '已预约', classNames: 'bg-danger', id: createEventId() },
    { title: '审核中', classNames: 'bg-warning', id: createEventId() },
    { title: '可预约', classNames: 'bg-info', id: createEventId() },
    { title: '不可预约', classNames: 'bg-dark', id: createEventId() }
])

/**
 * @description 选中某天处理事件
 */
const handleDateSelect = (selectInfo) => {
    // eslint-disable-next-line no-alert
    // const title = prompt('请输入任务标题')
    // const calendarApi = selectInfo.view.calendar
    // calendarApi.unselect() // clear date selection
    // if (title) {
    //     calendarApi.addEvent({
    //         id: createEventId(),
    //         title,
    //         start: selectInfo.startStr,
    //         end: selectInfo.endStr,
    //         allDay: selectInfo.allDay
    //     })
    // }
}
/**
 * @description 创建事件框
 *
 */
const handleCreateEvent = () => {
    dialogFormVisible.value = true
}

/**
 * @description 选中当前任务事件
 */
const handleEventClick = (clickInfo) => {
    // Modal.confirm({
    //     content: '确定删除当前任务吗？',
    //     cancelText: '取消',
    //     onOk() {
    //         clickInfo.event.remove()
    //         message.success('已删除')
    //     },
    //     onCancel() {
    //         //
    //     }
    // })
}
/**
 * @description 选中当前事件
 */
const handleEvents = (events) => {
    currentEvents = events
}

/**
 * 得到开始和结束日期，得到中间所有天返回数组
 * @param {String} string 开始日期 date
 * @param {String} String 结束日期 date
 * @return {Array} ['2021-07-01', '2021-07-01'...., '2021-08-01']
 */
function getDayArr(startDay, endDay) {
    let startVal = dayjs(startDay).format('YYYY-M-D')
    const dayArr = []
    while (dayjs(startVal).isBefore(dayjs(endDay))) {
        dayArr.push(startVal)
        // 自增
        startVal = dayjs(startVal).add(1, 'day').format('YYYY-M-D')
    }
    // 将结束日期的天放进数组
    dayArr.push(dayjs(endDay).format('YYYY-M-D'))
    return dayArr
}

/**
 * @description:初始化数据
 */
const initEvents = (selectInfo, successCallback) => {
    const { start, end } = selectInfo

    const list = getDayArr(start, end)
    const evlist = []

    list.forEach((time) => {
        const [y, m, d] = dayjs(time).format('YYYY-M-D').split('-')
        // 农历,节日，节气
        const { festival, isTerm, Term, date } = calendar.solar2lunar(y, m, d)
        if (isTerm) {
            evlist.push({
                id: createEventId(),
                title: Term,
                start: new Date(date).toISOString().replace(/T.*$/, ''),
                // classNames: ["bg-success"],
                backgroundColor: '#FDEBC9', // 该事件的背景颜色
                borderColor: '#FDEBC9', // 该事件的边框颜色
                textColor: '#F9AE26' // 该事件的文字颜色
            })
        }
        if (festival) {
            evlist.push({
                id: createEventId(),
                title: festival,
                start: new Date(date).toISOString().replace(/T.*$/, ''),
                // classNames: ["bg-success"]
                backgroundColor: '#FDEBC9', // 该事件的背景颜色
                borderColor: '#FDEBC9', // 该事件的边框颜色
                textColor: '#F9AE26' // 该事件的文字颜色
            })
        }
    })
    successCallback(evlist)
}

const calendarOptions = reactive({
    upKey: 0,
    locale: 'zh-cn',
    showNonCurrentDates: true, // 在月视图中，是否显示上个月和下个月的日期
    allDayText: '全天', // 周区域左侧的文本内容
    buttonText: {
        // 设置日历头部各按钮的显示文本信息，
        today: '今天',
        month: '月',
        week: '周',
        day: '天'
    },
    plugins: [
        dayGridPlugin,
        timeGridPlugin,
        interactionPlugin, // needed for dateClick,
        resourceTimeGridPlugin
    ],
    // 默认为那个视图(月: dayGridMonth,周: timeGridWeek,日:timeGridDay,list: '列表视图')
    // resourceTimeGridDay天视图 配合 resourceTimeGridPlugin插件使用
    initialView: 'timeGridWeek',
    // resource配置
    resources: [],
    dayMaxEvents: 3,
    moreLinkText: (val) => `还有${val}个日程`,
    dayMaxEventRows: true,
    handleWindowResize: true, // 是否随浏览器窗口大小变化而自动变化。
    slotEventOverlap: false, // 相同时间段的多个日程视觉上是否允许重叠，默认true允许
    firstDay: '1', // 设置一周中显示的第一天是哪天，周日是0，周一是1，类推
    weekNumberCalculation: 'ISO', // local, ISO 设置为'ISO'，则此选项默认为1（星期一）
    displayEventTime: true, // 是否显示时间
    allDaySlot: true, // 周，日视图时，all-day 不显示
    slotDuration: '00:15:00', // 时间间隔
    slotMinTime: '08:00:00', // 时隙最小时间
    //   slotMaxTime: '23:00:00', // 时隙最大时间
    //   scrollTimeReset: true, // 滚动时间重置
    //   scrollTime: dayjs().format('YYYY-MM-DD HH:mm:ss'), // 时间定位
    nowIndicator: false, // 周/日视图中显示今天当前时间点（以红线标记），默认false不显示
    now: handleNowDate, // 指示器时间，可随着时间自行移动，可接受字符串或处理函数
    nowIndicatorClassNames: 'nowIndicator', // 指示器类名
    // resourceAreaWidth: '20%',// 高级功能配置Column宽度
    slotLabelFormat: {
        // 垂直轴上显示的时间文本
        // second: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false, // 设置时间为24小时
        omitZeroMinute: false,
        meridiem: false
    },
    // 不可预约背幕设置
    // businessHours: [
    // 	{
    // 		daysOfWeek: [1, 2, 3, 4, 5, 6, 7],
    // 		startTime: this.nowHours(), // 可接受字符串或处理函数
    // 		endTime: '24:00'
    // 	}
    // ],

    // 高级功能许可key，需要用到高级功能时候添加上，避免左下角出现警告提醒
    schedulerLicenseKey: 'CC-Attribution-NonCommercial-NoDerivatives',
    noEventsMessage: '暂无数据!', // listview视图下，无数据时显示提示
    //   snapDuration: '05:00:00', // 其实就是动态创建一个日程时，默认创建多长的时间块
    //   initialDate: dayjs().format('YYYY-MM-DD'), // 初始 年月日
    //   timezone: 'America/Chicago', // false：默认，没有时区 'local'：客户机时区 'UTC'：世界标准时间
    // dayHeaders: true, // 日标头
    // height: '100%', // 设置日历的高度，包括header日历头部，默认未设置，高度根据aspectRatio值自适应。
    contentHeight: 800, // 设置日历主体内容的高度，不包括header部分，默认未设置，高度根据aspectRatio值自适应。
    windowResizeDelay: 300,
    weekNumbersWithinDays: false, // 在月/基本视图中显示周数的样式
    selectable: true, // 是否可以选中日历格
    headerToolbar: {
        header: false,
        // today
        left: 'prev,next',
        // left: '',
        center: '',
        right: ''
        // right: 'dayGridMonth,timeGridWeek,timeGridDay'
    },
    droppable: true,
    drop(info) {
        if (checked.value) {
            // if so, remove the element from the "Draggable Events" list
            // eslint-disable-next-line no-unused-expressions
            info.draggedEl.parentNode.removeChild(info.draggedEl)
        }
    },
    views: {
        // 对应月视图调整
        dayGridMonth: {
            dayMaxEventRows: 4,
            dayCellContent(item) {
                const [y, m, d] = dayjs(item.date).format('YYYY-M-D').split('-')
                // 农历,节日，节气
                const { lDay, IDayCn, festival, isTerm, Term, IMonthCn } =
                    calendar.solar2lunar(y, m, d)
                // 是否今天
                const isToday = `<span style="font-size: 16px;color: #fff;background: #fa5c7c;border-radius: 50%;width: 28px;height: 28px;text-align: center;line-height: 28px;">${d}</span>`
                // 月初
                const isOne =
                    d == 1
                        ? `<span style="font-size: 18px;color: #333;">${`${m}月${d}日`}</span>`
                        : `<span style="font-size: 18px;color: #333;;">${d}</span>`
                // 显示农历
                const showSolar2lunar = `<span style="font-size: 14px;">${lDay == 1
                    ? `<span style="border-bottom: 1px solid #fa5c7c;padding: 4px 0;">${IMonthCn}${IDayCn}</span>`
                    : IDayCn
                    }</span>`

                return {
                    html: `<div style="padding: 4px 8px;width: 100%;display: flex;justify-content: space-between;align-items: center;">
						${showSolar2lunar}
						${item.isToday ? isToday : isOne}
					</div>`
                }
            }
        }
        // 周视图
        // timeGridWeek:{

        // },
        // // 日视图
        // timeGrid:{},
    },
    // 定义可在headerToolbar / footerToolbar中使用的自定义按钮
    customButtons: {
        // today: {
        //     text: '今天', // 按钮的展示文本
        //     click: handleTodayClick // 点击按钮触发的事件，这里要注意的是当按钮绑定了事件之后该按钮原本自带的事件将会失效
        // },
        next: {
            click: () => {
                handleNextClick()
            }
        },
        prev: {
            click: () => {
                handlePrevClick()
            }
        }
    },
    //   windowResize: () => {}, // 浏览器窗体变化时触发
    initialEvents: props.initialEvents, // 可选项，可以从远程接口返回初始化数据
    editable: true,
    selectMirror: true,
    dayMaxEvents: true,
    weekends: true,
    eventMouseEnter: handleEventMouseEnter, // 用户将鼠标悬停在事件上时触发
    select: handleDateSelect, // 拖动，可传处理函数
    eventClick: handleEventClick,
    eventsSet: handleEvents,
    events: initEvents // 可以是[]也可以时个函数
    /* 可以通过触发以下事件来更新远程数据库
          eventAdd:
          eventChange:
          eventRemove:
          */
})

const emit = defineEmits(['nextWeeks', 'prevWeeks'])

function handleNextClick() {
    fullCalendarRef.value.getApi().next()
    emit('nextWeeks', fullCalendarRef.value.getApi().currentData.viewTitle)
}
function handlePrevClick() {
    fullCalendarRef.value.getApi().prev()
    emit('prevWeeks', fullCalendarRef.value.getApi().currentData.viewTitle)
}
defineExpose({
    calendarOptions,
    newDate,
    form
})
/**
 * @description 保存事项
 *
 */
const handleSaveCategory = () => {
    // eslint-disable-next-line consistent-return
    validate()
        .then(() => {
            const evObj = {
                id: createEventId(),
                title: form.title,
                classNames: form.className
            }
            taskList.push(evObj)

            calendarOptions.events.push({
                id: createEventId(),
                title: form.title,
                start: new Date(),
                end: new Date(),
                classNames: form.className
            })
            dialogFormVisible.value = false
        })
        .catch(() => { })
}

const handleWeekendsToggle = () => {
    calendarOptions.weekends = !calendarOptions.weekends // update a property
}

/**
 * @description:  用户将鼠标悬停在事件上时触发
 */
// function handleEventMouseEnter(info) {
//     console.log(info.el, '_______el')
//     tippy(info.el, {
//         content: '99999'
//         // content: info.event.extendedProps.name,
//         // placement: "top-start",
//         // arrow: false,
//         // 鼠标放在提示中提示不消失
//         // interactive: true,
//     })
// }
function handleEventMouseEnter(info) {
    const content = '预约时间:' + info.el.innerText
    tippy(info.el, {
        content
        // content: info.event.extendedProps.name,
        // placement: "top-start",
        // arrow: false,
        // 鼠标放在提示中提示不消失
        // interactive: true,
    })
}

/**
 * @description: 指示器时间，可随着时间自行移动，可接受字符串或处理函数
 */
function handleNowDate() {
    return new Date().getTime()
}

onMounted(() => {
    // eslint-disable-next-line no-new
    new Draggable(containerRef.value, {
        itemSelector: '.external-events',
        eventData(eventEl) {
            const className = eventEl.className.split(' ')[1]
            return {
                title: eventEl.innerText,
                classNames: [className]
            }
        }
    })
})
</script>

<style lang="less" scoped>
.calendar-info {
    color: black;
    margin-top: 20px;
    padding-top: 10px;
    background-color: #fafbfe;

    .page-title-box {
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        color: #6c757d;

        .page-title {
            font-size: 18px;
            margin: 0;
            line-height: 65px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: inherit;
            font-weight: 700;
        }

        .page-title-right {
            display: flex;
            flex-direction: row;
            justify-content: space-around;
            align-items: center;
        }

        .text-muted {
            color: #98a6ad;
        }

        .text-left {
            text-align: left;
        }
    }

    :deep(.fc .fc-daygrid-day.fc-day-today) {
        background: #f3f6f8;
    }

    :deep(.fc .fc-button-primary) {
        background-color: #409effab;
        border-color: transparent;
    }

    :deep(.fc .fc-button-primary:not(:disabled).fc-button-active) {
        background-color: #409eff;
        border-color: transparent;
    }

    :deep(.bg-success) {
        background-color: #0acf97 !important;
    }

    :deep(.bg-info) {
        background-color: #39afd1 !important;
    }

    :deep(.bg-danger) {
        background-color: #fa5c7c !important;
    }

    :deep(.bg-dark) {
        background-color: #313a46 !important;
    }

    :deep(.bg-warning) {
        background-color: #ffbc00 !important;
    }

    :deep(.external-events) {
        cursor: move;
        margin: 10px 0;
        padding: 8px 10px;
        color: #fff;
        text-align: left;
        width: 258.25px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .list-circle {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background-color: white;
            display: inline-block;
            margin-right: 10px;
        }
    }

    :deep(.fc-timeline-event) {
        margin-right: 0;
    }

    :deep(.fc-header-toolbar) {
        display: flex;
        flex-direction: row-reverse;
        margin-bottom: 10px !important;
    }

    :deep(.fc-prev-button):nth-child(1) {
        margin-right: 20px !important;
    }

    :deep(.fc-button-primary):nth-child(1) {
        border: 1px solid #d9d9d9;
        background-color: #ffffff !important;
        width: 86px !important;
        height: 32px !important;
        line-height: 26px !important;
        border-radius: 6px !important;
    }

    :deep(.fc-icon-chevron-left::before) {
        content: '\e900上一周';
    }

    :deep(.fc-icon-chevron-right::before) {
        content: '下一周\e901';
    }

    :deep(.fc-button-primary):nth-child(2) {
        border: 1px solid #d9d9d9;
        background-color: #ffffff !important;
        width: 86px !important;
        height: 32px !important;
        line-height: 26px !important;
        border-radius: 6px !important;
    }

    :deep(.fc-icon) {
        width: unset !important;
        color: rgba(0, 0, 0, 0.65);
        font-weight: 400 !important;
        font-size: 14px !important;
        display: flex;
        align-items: center;
    }

    :deep(.fc .fc-button-primary) {
        margin: 0 2px;
        &:focus {
            border: none;
            box-shadow: 0 0 0 0.05rem @border-color-base !important;
            &.fc-next-button {
                margin-right: 2px;
            }
        }

        &:hover {
            box-shadow: 0 0 0 0.03rem @primary-color !important;
            border-color: @primary-color !important;

            .fc-icon {
                color: @primary-color !important;
            }
        }

        &:not(:disabled) {
            &.fc-button-active,
            &:active {
                border-color: transparent !important;
            }
        }
    }
}
</style>
