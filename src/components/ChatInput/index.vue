<template>
    <div class="chat_input">
        <!-- 输入框 -->
        <a-textarea
            :disabled="loading"
            v-model:value.trim="content"
            :placeholder="`问问${agentDetails?.name || '小壹'}`"
            :auto-size="{ minRows: 2, maxRows: maxRows }"
            :bordered="false"
            @pressEnter="handleSend"
        />
        <!-- 功能按钮 -->
        <div class="flex-justify-between" style="margin-top: 10px">
            <div class="footer_left" v-if="isXiaoYi">
                <!-- 选择模型 -->
                <a-select
                    :disabled="loading || uploadLoading"
                    v-model:value="selectModelId"
                    style="width: 160px"
                    placeholder="请选择语言模型"
                    @change="handleModel"
                    key="selectModel"
                >
                    <a-select-option
                        :value="item.id"
                        v-for="item in modelList"
                        :key="item.id"
                        :modelItem="item"
                    >
                        <a-tooltip placement="right" color="#fff">
                            <template #title>
                                <span style="color: #333">
                                    {{ item.description }}</span
                                >
                            </template>
                            <span> {{ item.name }}</span>
                        </a-tooltip>
                    </a-select-option>
                </a-select>

                <!-- 只是一个提示 不能点击 -->
                <div class="select_item" v-if="isDeepThinking">
                    <i class="iconfont icon-shendusikao"></i>
                    <span>深度思考</span>
                </div>
                <div class="select_item">
                    <i class="iconfont icon-hulianwang"></i>
                    <span>联网搜索</span>
                </div>
            </div>
            <!-- 上传和发送 -->
            <div class="footer_right">
                <i
                    v-if="loading"
                    class="iconfont icon-zanting stop_icon"
                    @click="emit('stop')"
                ></i>
                <div
                    v-else
                    class="send btn"
                    @click="handleSend"
                    :class="{
                        disabled: !content.length || loading || uploadLoading,
                    }"
                >
                    <ArrowUpOutlined />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {
    PictureOutlined,
    PaperClipOutlined,
    ArrowUpOutlined,
} from "@ant-design/icons-vue";
import { POST } from "@/api/getPost";
import { message } from "ant-design-vue";
import { modalConfirm } from "@/utils/util";
import { ExclamationCircleFilled } from "@ant-design/icons-vue";

const emit = defineEmits(["send", "editModel", "stop"]);
const props = defineProps({
    agent: {
        type: Object,
        default: () => {},
    },
    modelList: {
        type: Array,
    },
    selectModelId: {
        type: String,
        default: null,
    },
    selectModel: {
        type: Object,
        default: () => {},
    },
    isXiaoYi: {
        type: Boolean,
        default: () => false,
    },
    loading: {
        type: Boolean,
        default: () => false,
    },
    maxRows: {
        type: Number,
        default: () => 5,
    },
});

const agentDetails = computed(() => props.agent);
const selectModelId = computed(() => props.selectModelId);
const selectModel = computed(() => props.selectModel);
const modelList = computed(() => props.modelList);
const isXiaoYi = computed(() => props.isXiaoYi);
const loading = computed(() => props.loading);
const maxRows = computed(() => props.maxRows);

const uploadLoading = ref(false);

// 是否有深度提问
const isDeepThinking = computed(() => {
    if (selectModel.value && selectModel.value.capabilities) {
        return selectModel.value.capabilities?.some((i) => i === "reasoning");
    }
    return false;
});

// 是否有上传图片
const isUploadImage = computed(() => {
    if (selectModel.value && selectModel.value.capabilities) {
        return selectModel.value.capabilities?.some((i) => i === "image");
    }
    return false;
});

// 是否有上传文件
const isUploadFile = computed(() => {
    if (selectModel.value && selectModel.value.capabilities) {
        return selectModel.value.capabilities?.some((i) => i === "document");
    }
    return false;
});

const pictureFileList = ref([]);
const pictureList = ref([]);
const fileList = ref([]);
const pictureLoading = ref(false);
const content = ref("");

// 上次到我们自己的服务器文件
async function uploadFileFn(file) {
    pictureLoading.value = true;
    const url = await uploadFile(file, "libBookUrl");
    pictureList.value.push(url);
    pictureLoading.value = false;
    console.log(pictureList.value);
}

// INIT("待解析"),
// PARSING("解析中"),
// PARSE_SUCCESS("解析完成"),
// PARSE_FAILED("解析失败"),
// SAFE_CHECKING("安全检测中"),
// SAFE_CHECK_FAILED("安全检测失败"),
// INDEX_BUILDING("索引构建中"),
// INDEX_BUILD_SUCCESS("索引构建成功"),
// INDEX_BUILDING_FAILED("索引构建失败"),
// INDEX_DELETED("文件索引已删除"),
// FILE_IS_READY("文件准备完毕"),
// FILE_EXPIRED("文件已过期"),

let getTimer = null;
const statusList = ["SAFE_CHECKING", "PARSING", "INDEX_BUILDING"];
const statusErr = [
    "PARSE_FAILED",
    "INDEX_BUILDING_FAILED",
    "INDEX_DELETED",
    "FILE_EXPIRED",
    "SAFE_CHECK_FAILED",
];

// 获取文件信息
function getFileFinish(fileId) {
    POST("/ai/chat/file/get", { id: fileId })
        .then((res) => {
            fileList.value[fileList.value.length - 1] = res.data;
            if (
                res.data.status === "PARSE_SUCCESS" ||
                res.data.status === "FILE_IS_READY" ||
                statusErr.includes(res.data.status)
            ) {
                clearInterval(getTimer);
                uploadLoading.value = false;
            }
        })
        .catch((err) => {
            clearInterval(getTimer);
            uploadLoading.value = false;
        });
}

// 完成上传文件
function uploadFileFinish(leaseId) {
    POST("/ai/chat/file/upload-finish", { leaseId })
        .then((res) => {
            getTimer = setInterval(() => {
                getFileFinish(res.data);
            }, 2000);
        })
        .catch((err) => {
            fileList.value.splice(fileList.value.length - 1, 1);
            message.error("上传失败，请重新上传文件！");
            uploadLoading.value = false;
        });
}

// 申请文件上传租约
async function uploadLease(file, fileInfo) {
    const parameter = {
        size: fileInfo.size,
        fileName: fileInfo.name,
        md5: await fileToMd5(file),
    };
    uploadLoading.value = true;
    POST("/ai/chat/file/upload-lease", parameter)
        .then(async (res) => {
            if (res.data.id) {
                setTimeout(() => {
                    uploadFileFinish(res.data.id);
                }, 1500);
            }
            const formData = new FormData();
            formData.append("image", file);
            await fetch(res.data.url, {
                method: res.data.method,
                body: formData,
                headers: res.data.headers,
            });
        })
        .catch((err) => {
            uploadLoading.value = false;
            fileList.value.splice(fileList.value.length - 1, 1);
            console.log(err);
        });
}

function pictureChange(fileItem) {
    const file = fileItem.file;
    uploadFileFn(file);
}

function fileChange(fileItem) {
    if (loading.value) return;
    const file = fileItem.file;
    const fileInfo = fileItem.fileList[fileItem.fileList?.length - 1];
    uploadLease(file, fileInfo);
}

async function pictureBeforeUpload(file) {
    let flag = true;
    const isLt5M = file.size / 1024 / 1024 < 100;
    if (fileList.value && fileList.value.length) {
        modalConfirm(
            "提示",
            "已存在文件，上传文件后图片将会被覆盖",
            ExclamationCircleFilled
        ).then((res) => {
            fileList.value = [];
        });
        // flag = await yConfirm(
        //     "提示",
        //     "已存在文件，上传文件后图片将会被覆盖",
        //     "确认覆盖"
        // );
        // if (flag) fileList.value = [];
    }
    if (!flag) {
        message.error("已取消覆盖上传");
        return true;
    }
    if (!isLt5M) {
        message.error("文件大于100M，请重新上传");
        return true;
    }
    return false;
}

async function fileBeforeUpload(file) {
    let flag = true;
    if (pictureList.value && pictureList.value.length) {
        // flag = await yConfirm(
        //     "提示",
        //     "已存在图片，上传图片后文件将会被覆盖",
        //     "确认覆盖"
        // );
        // if (flag) pictureList.value = [];
        modalConfirm(
            "提示",
            "已存在图片，上传图片后文件将会被覆盖",
            ExclamationCircleFilled
        ).then((res) => {
            pictureList.value = [];
        });
    }
    if (!flag) {
        message.error("已取消覆盖上传");
        return true;
    }
    const isName = fileList.value.some((i) => i.fileName == file.name);
    if (isName) {
        message.error("不可重复上传同一文件");
        setTimeout(() => {
            fileList.value.splice(fileList.value.length - 1, 1);
        }, 500);
        return true;
    }
    const isLt5M = file.size / 1024 / 1024 < 100;
    if (!isLt5M) {
        message.error("文件大于100M，请重新上传");
        return true;
    }
    return false;
}
// 选择模型
function handleModel(e, item) {
    emit("editModel", item.modelItem);
}

function deleteFile(index) {
    fileList.value?.splice(index, 1);
}

function deletePicture(index) {
    pictureList.value?.splice(index, 1);
}

// 发送
function handleSend() {
    if (!content.value.length || loading.value || uploadLoading.value) return;
    emit("send", content.value, fileList.value, pictureList.value);
    setTimeout(() => {
        content.value = "";
        fileList.value = [];
        pictureList.value = [];
    }, 200);
}
</script>

<style lang="less" scoped>
.chat_input {
    padding: 16px;
    min-height: 120px;
    background: #ffffff;
    border-radius: 10px;
    border: 2px solid #dcdcdc;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    :deep(.ant-input) {
        padding: 0px;
    }
    .picture_box {
        display: flex;

        .picture_list {
            margin-bottom: 10px;
            display: flex;
            .image {
                min-width: 56px;
                height: 56px;
                border-radius: 4px;
                position: relative;
                margin-right: 16px;
                img {
                    min-width: 56px;
                    height: 56px;
                }
                .delete_icon {
                    display: none;
                    position: absolute;
                    top: 0px;
                    font-size: 12px;
                    right: 0px;
                    color: #999;
                    cursor: pointer;
                }
                &:hover {
                    .delete_icon {
                        display: block;
                    }
                }
            }
        }
        .image_loading {
            color: #878aab99;
            border: 1px solid #dcdcdc;
            border-radius: 6px;
            width: 90px;
            height: 56px;
            font-size: 12px;
            line-height: 20px;
            display: flex;
            padding: 0px 6px;
            align-items: center;
        }
    }

    .file_list {
        margin-bottom: 10px;
        display: flex;
        .file {
            border: 1px solid #dcdcdc;
            margin-right: 10px;
            display: flex;
            width: 200px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            padding: 0px 4px;
            position: relative;
            .loading_span {
                color: #878aab99;
                font-size: 12px;
                line-height: 20px;
            }
            .icon {
                font-size: 28px;
            }
            .right_text {
                display: flex;
                flex-direction: column;
                justify-content: center;
                .name {
                    color: #26244c;
                    font-size: 12px;
                    line-height: 20px;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1; /* 指定要显示的行数 */
                    overflow: hidden;
                }
                .size {
                    color: #878aab99;
                    font-size: 10px;
                }
                .status {
                    color: rgba(135, 138, 171, 0.6);
                    font-size: 10px;
                }
                .errColor {
                    color: #e63224;
                }
            }
            .delete_icon {
                display: none;
                position: absolute;
                top: 4px;
                font-size: 12px;
                right: 4px;
                color: #999;
                cursor: pointer;
            }
            &:hover {
                .delete_icon {
                    display: block;
                }
            }
        }
    }
    .flex-justify-between {
        display: flex;
        justify-content: space-between;
    }

    .footer_left {
        display: flex;
        align-items: center;

        .select_item {
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            line-height: 20px;
            width: 96px;
            height: 32px;
            background: #ffffff;
            border-radius: 4px;
            border: 1px solid #ededed;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;

            span {
                padding-left: 4px;
                color: #00b781;
            }
            .iconfont {
                color: #00b781;
            }
        }
    }

    .footer_right {
        display: flex;
        align-items: center;
        font-size: 22px;
        color: #999999;
        .icon {
            font-size: 22px;
            color: #999999;
            cursor: pointer;
        }
        .stop_icon {
            color: #00b781;
            background: none !important;
            font-size: 30px;
            margin-left: 14px;
            cursor: pointer;
        }

        .btn {
            cursor: pointer;
            margin-left: 14px;
        }

        .send {
            background: #00b781;
            color: #fff;
            border-radius: 6px;
            font-size: 16px;
            padding:4px 8px;
        }
        .disabled {
            cursor: not-allowed;
            background: #dcdcdc;
        }
        .disabled_icon {
            cursor: not-allowed;
        }
    }
}
</style>
