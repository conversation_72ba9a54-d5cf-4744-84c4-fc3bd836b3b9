.y-markdown {
    display: flex;
    margin-bottom: 20px;
    position: relative;
    // margin-left: 24px;
    &.active {
        justify-content: end;
        .avatar {
            right: 0;
        }
        .y-markdown-content {
            margin: 0 35px 0 0;
        }
    }
    .y-markdown-content {
        min-height: auto !important;
        margin: 0 35px;
        table {
            width: 100%;
            margin-bottom: 10px;
        }
        td,
        th {
            border: 1px solid #333333;
            padding: 2px 8px;
            width: 100px;
        }
    }
    .avatar {
        width: 24px;
        height: 24px;
        margin-right: 4px;
        border-radius: 50%;
        margin-bottom: 10px;
        position: absolute;
        top: 0;
    }
    .adopt {
        position: absolute;
        bottom: 0;
        right: 0;
        padding: 0;
        margin-bottom: 10px;
    }
}
