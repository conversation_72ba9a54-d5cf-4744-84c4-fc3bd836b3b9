<template>
    <a-range-picker
        format="YYYY-MM-DD"
        class="w-full"
        v-model:value="time"
        @change="timeChange"
        :placeholder="['开始时间', '结束时间']"
        v-bind="$attrs"
    />
</template>
<script setup>
import dayjs from "dayjs";
import { defineProps, defineEmits, ref, watch, useAttrs } from "vue";
const props = defineProps(["startTime", "endTime"]);
const emit = defineEmits(["update:startTime", "update:endTime"]);
const time = ref([]);
const attrs = useAttrs();
watch(
    () => props.startTime,
    () => {
        time.value = [];
        if (props.startTime) {
            editTime();
        }
    },
    {
        immediate: true,
    }
);

function editTime() {
    if (props.startTime) {
        time.value.push(dayjs(props.startTime));
    }
    if (props.endTime) {
        time.value.push(dayjs(props.endTime));
    }
}

editTime();

const timeChange = (time) => {
    if (attrs.valueFormat) {
        // 指定输出格式
        emit("update:startTime", time ? time[0] : null);
        emit("update:endTime", time ? time[1] : null);
    } else {
        // 默认输出格式
        emit("update:startTime", time ? time[0].format("YYYY-MM-DD") : null);
        emit("update:endTime", time ? time[1].format("YYYY-MM-DD") : null);
    }
};
</script>
<style lang="less" scoped></style>
