<!--
 * @Author: 舒志建 <EMAIL>
 * @Date: 2023-06-25 20:02:09
 * @LastEditors: 舒志建 <EMAIL>
 * @LastEditTime: 2023-06-25 21:00:31

 * @FilePath: \cloud-system-2.0\src\components\Tooltip.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <a-tooltip class="_tooltip" :title="props.title" :overlayStyle="state.overlayStyle" @mouseenter="showToolTip">
        <span class="tooltip_span">{{ tooltipTitle }}</span>
    </a-tooltip>
</template>

<script setup>
import { reactive, computed } from "vue";
const props = defineProps({
    title: [String, Number, Object],
    maxWidth: {
        type: Number,
        default: 0,
    }
});
const state = reactive({
    overlayStyle: {
        color: "#000",
        maxWidth: props.maxWidth ? props.maxWidth + "px" : "initial",
    },
});
// 控制Tooltip显隐
const showToolTip = (e) => {
    if (e.target.clientWidth >= e.target.scrollWidth) {
        e.target.style.pointerEvents = "none";
        // 阻止鼠标事件
    }
};
const tooltipTitle = computed(() => {
    const { title } = props
    if (title !== '' && title !== null && title !== undefined) {
        return title
    }
    return '--'
})
</script>

<style scoped lang="less">
._tooltip {
    min-height: 22px;
}

.tooltip_span {
    display: block;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
}

:deep(.ant-tooltip) {
    max-width: max-content !important;

    .ant-tooltip-inner {
        width: auto !important;
    }
}
</style>
