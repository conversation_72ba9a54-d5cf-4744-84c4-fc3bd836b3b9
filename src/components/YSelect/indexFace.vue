<!--
 * @Descripttion:选人控件
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-02-28 15:30:04
 * @LastEditors: 舒志建 <EMAIL>

-->
<template>
    <div class="y_select">
        <span class="def_slot" @click="handleSlotClick">
            <slot />
        </span>

        <a-modal
            :maskClosable="false"
            destroyOnClose
            v-bind="$attrs"
            v-model:visible="state.visible"
            class="y_select__modal"
            width="980px"
            :ok-button-props="{
                disabled: state.selectedList.length ? false : true
            }"
            @ok="handleOk"
            @cancel="handleCancel"
        >
            <template #title>
                <a-tabs
                    v-model:activeKey="state.activeKey"
                    class="y_select__tabs"
                    @tabClick="onChangeTabs"
                >
                    <a-tab-pane
                        v-for="item in tabs"
                        :key="item.key"
                        :tab="`${item.tab}`"
                    />
                </a-tabs>
            </template>
            <a-row type="flex" style="height: 574px">
                <a-col flex="auto" class="userselect_warp">
                    <div class="userselect_search">
                        <a-input-search
                            v-model:value="state.searchValue"
                            placeholder="请输入关键字"
                            enter-button
                            @search="search"
                            allow-clear
                        />

                        <a-button
                            v-if="state.isSearchMode"
                            type="link"
                            @click="handleCancelSearch"
                            >取消</a-button
                        >
                    </div>
                    <div class="userselect">
                        <!-- 搜索模式 -->
                        <template v-if="state.isSearchMode">
                            <div style="max-height: 500px; overflow-y: scroll">
                                <a-table
                                    rowKey="id"
                                    size="small"
                                    :row-selection="{
                                        selectedRowKeys:
                                            state.selectedSearchTableIds,
                                        onChange: onSelectChangeTable,
                                        onSelect: onSelectSelectTable,
                                        hideSelectAll: true
                                    }"
                                    :pagination="state.searchPagination"
                                    style="margin: 8px"
                                    :dataSource="state.searchData"
                                    :columns="columns"
                                    :loading="state.searchLoading"
                                    @change="handleSearchTableChange"
                                />
                            </div>
                        </template>
                        <!-- 正常 -->
                        <a-row v-else type="flex" style="min-height: 100%">
                            <a-col flex="50%" class="userselect_tree">
                                <y-tree
                                    v-if="state._treeDatas.length"
                                    :key="state.activeKey"
                                    v-model:selectedKeys="state.selectedKeys"
                                    :tree-data="state._treeDatas"
                                    :field-names="fieldNames"
                                    @select="select"
                                    default-expand-all
                                    @expand="onExpand"
                                >
                                    <template #title="{ dataRef }">
                                        <i
                                            class="iconfont icon-yellow icon-a-Fill21"
                                        />
                                        <span
                                            v-if="
                                                (
                                                    dataRef.showName ||
                                                    dataRef.name
                                                ).indexOf(state.searchValue) >
                                                -1
                                            "
                                        >
                                            {{
                                                (
                                                    dataRef.showName ||
                                                    dataRef.name
                                                ).substr(
                                                    0,
                                                    (
                                                        dataRef.showName ||
                                                        dataRef.name
                                                    ).indexOf(state.searchValue)
                                                )
                                            }}
                                            <span style="color: red">{{
                                                state.searchValue
                                            }}</span>
                                            {{
                                                (
                                                    dataRef.showName ||
                                                    dataRef.name
                                                ).substr(
                                                    (
                                                        dataRef.showName ||
                                                        dataRef.name
                                                    ).indexOf(
                                                        state.searchValue
                                                    ) + state.searchValue.length
                                                )
                                            }}
                                        </span>
                                        <span v-else>{{
                                            dataRef[fieldNames.title] ||
                                            dataRef.name
                                        }}</span>
                                    </template>
                                </y-tree>
                            </a-col>
                            <!-- 中间池数据 -->
                            <a-col flex="50" class="userselect_checklist">
                                <template v-if="state.notSelectedList.length">
                                    <a-row type="flex" justify="space-between">
                                        <a-col
                                            style="
                                                color: rgba(0, 0, 0, 0.85);
                                                padding: 0 8px;
                                                margin-bottom: 8px;
                                            "
                                        >
                                            {{ multiple ? `全` : `单` }}选
                                            <span
                                                style="font-size:12px;rgba(0, 0, 0, 0.85)"
                                                >共{{
                                                    dataUnit(
                                                        state.notSelectedList
                                                            .length
                                                    )
                                                }}</span
                                            >
                                        </a-col>
                                        <a-col
                                            v-if="
                                                multiple &&
                                                state.notSelectedList.length
                                            "
                                            style="padding-right: 12px"
                                        >
                                            <a-checkbox
                                                v-model:checked="
                                                    state.selectedAll.checked
                                                "
                                                :indeterminate="
                                                    state.selectedAll
                                                        .indeterminate
                                                "
                                                :disabled="
                                                    state.selectedAll.disabled
                                                "
                                                @change="onCheckAllChange"
                                            />
                                        </a-col>
                                    </a-row>
                                    <ul v-if="multiple">
                                        <li
                                            v-for="(
                                                item, index
                                            ) in state.notSelectedList"
                                            :key="index"
                                            :class="{
                                                selected_item: true,
                                                selected_item_activate:
                                                    item._checked
                                            }"
                                        >
                                            <label>
                                                <a-row
                                                    type="flex"
                                                    justify="space-between"
                                                    style="cursor: pointer"
                                                    :style="{
                                                        display:
                                                            state.slots &&
                                                            state.activeKey ==
                                                                2 &&
                                                            'flow-root'
                                                    }"
                                                >
                                                    <a-col
                                                        style="
                                                            overflow: hidden;
                                                            text-overflow: ellipsis;
                                                            white-space: nowrap;
                                                            flex: 1;
                                                        "
                                                    >
                                                        <i
                                                            class="iconfont icon-yellow icon-a-Fill21"
                                                        />
                                                        {{
                                                            item[
                                                                fieldNames.title
                                                            ] || item.name
                                                        }}
                                                        <!-- 家长信息 -->
                                                        <!-- <div
                                                            v-for="it in item.elterns"
                                                            class="slots-selecte-elterns"
                                                            :key="it.id"
                                                        >
                                                            <a-col
                                                                style="
                                                                    padding-left: 26px;
                                                                "
                                                            >
                                                                {{ it.name }}
                                                                <span
                                                                    class="relations"
                                                                >
                                                                    {{
                                                                        relations(
                                                                            it.relations
                                                                        )
                                                                    }}
                                                                </span>
                                                            </a-col>
                                                        </div> -->
                                                    </a-col>
                                                    <a-col>
                                                        <!-- 中间池单选 -->
                                                        <a-checkbox
                                                            v-model:checked="
                                                                item._checked
                                                            "
                                                            :disabled="
                                                                item.disable
                                                            "
                                                        />
                                                    </a-col>
                                                </a-row>
                                            </label>
                                        </li>
                                    </ul>
                                    <ul v-else>
                                        <a-radio-group
                                            v-model:value="state._selectedList"
                                            style="width: 100%"
                                            @change="onCheckRadioChange"
                                        >
                                            <li
                                                v-for="(
                                                    item, index
                                                ) in state.notSelectedList"
                                                :key="index"
                                                :class="{
                                                    selected_item: true,
                                                    selected_item_activate:
                                                        item._checked
                                                }"
                                            >
                                                <label>
                                                    <a-row
                                                        type="flex"
                                                        justify="space-between"
                                                        style="cursor: pointer"
                                                    >
                                                        <a-col
                                                            style="
                                                                font-size: 14px;
                                                                overflow: hidden;
                                                                text-overflow: ellipsis;
                                                                white-space: nowrap;
                                                                margin-right: 10px;
                                                                flex: 1;
                                                            "
                                                        >
                                                            <i
                                                                class="iconfont icon-yellow icon-a-Fill21"
                                                            />
                                                            {{
                                                                item[
                                                                    fieldNames
                                                                        .title
                                                                ] || item.name
                                                            }}
                                                        </a-col>
                                                        <a-col>
                                                            <a-radio
                                                                v-model:checked="
                                                                    item._checked
                                                                "
                                                                :value="
                                                                    item[
                                                                        fieldNames
                                                                            .key
                                                                    ]
                                                                "
                                                                :label="item.id"
                                                                :disabled="
                                                                    item._disabled
                                                                "
                                                            />
                                                        </a-col>
                                                    </a-row>
                                                </label>
                                            </li>
                                        </a-radio-group>
                                    </ul>
                                </template>
                                <div v-else class="hide_empty">
                                    <a-image
                                        :width="120"
                                        :height="120"
                                        src="/image/empty.png"
                                        :preview="false"
                                    />
                                    <div>暂无数据</div>
                                </div>
                            </a-col>
                        </a-row>
                    </div>
                </a-col>
                <a-col flex="280px" class="usercheck_warp">
                    <a-row
                        type="flex"
                        justify="space-between"
                        style="align-items: center"
                    >
                        <a-col
                            style="color: rgba(0, 0, 0, 0.85); padding: 0 8px"
                        >
                            已选
                            <span style="font-size:12px;rgba(0, 0, 0, 0.85)">
                                {{ dataUnit(state.selectedList.length) }}
                            </span>
                        </a-col>
                        <a-col v-if="state.selectedList.length">
                            <a-button
                                class="empty_allselected"
                                type="link"
                                @click="hadleEmptyAllSelected"
                                >清空</a-button
                            >
                        </a-col>
                    </a-row>
                    <a-row
                        v-for="(item, index) in state.selectedList"
                        :key="index"
                        type="flex"
                        justify="space-between"
                        class="selected_item"
                    >
                        <a-col
                            style="
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                                margin-right: 10px;
                                flex: 1;
                            "
                        >
                            <i class="iconfont icon-yellow icon-a-Fill21" />
                            {{
                                item[fieldNames.title] ||
                                item.name ||
                                item.realname
                            }}
                        </a-col>
                        <!-- 家长信息 -->
                        <!-- <template v-if="item.elterns && item.elterns.length">
                            <a-col
                                class="slots-selecte"
                                v-for="(it, cindex) in item.elterns"
                                :key="it.id"
                            >
                                <span class="slots-selecte-user">
                                    {{ it.name }}
                                    <span class="relations">
                                        {{ relations(it.relations) }}
                                    </span>
                                </span>
                            </a-col>
                            <close-outlined
                                style="color: #d9d9d9"
                                class="elterns_del"
                                title="删除"
                                @click="delSelecteItem(item, index)"
                            />
                        </template> -->
                        <a-col>
                            <close-outlined
                                style="color: #d9d9d9"
                                title="删除"
                                @click="delSelecteItem(item, index)"
                            />
                        </a-col>
                    </a-row>
                </a-col>
            </a-row>
        </a-modal>
    </div>
</template>

<script setup>
import { reactive, watch, toRaw, computed, ref, toRefs } from 'vue'
import YTree from '@/components/YTree/index.vue'
import {
    getDepartmentPersonnel,
    getSchoolRollPersonnel
} from '@/api/faceLibrary'
// 获取人员人脸
import { getFaceInfo } from '@/api/classSign'
import { useStore } from '@/store/index'
import { useRouter } from 'vue-router'
const router = useRouter()
const emits = defineEmits([
    'handleOk',
    'update:mode',
    'update:visible',
    'update:checked',
    'emitChangeTabs',
    'emitSelectLeft'
])
const store = useStore()
const props = defineProps({
    // 是否是只选家长
    // isParents: {
    //     type: Boolean,
    //     default: false
    // },
    // 是否选择家长
    checkParents: {
        type: Boolean,
        default: false
    },

    treeData: {
        type: Array,
        default: () => []
    },
    // 自定义组的数据
    customTree: {
        type: Array,
        default: () => []
    },
    deviceId: {
        type: String,
        default: ''
    },
    fieldNames: {
        type: Object,
        default: () => {
            return {
                children: 'children',
                title: 'showName',
                key: 'id'
            }
        }
    },
    // 树选中的key
    selectedKeys: {
        type: Array,
        default: () => []
    },
    // 树展开的key
    expandedKeys: {
        type: Array,
        default: () => []
    },
    // 单选 多选
    multiple: {
        type: Boolean,
        default: true
    },
    visible: {
        type: Boolean,
        default: false
    },
    tabs: {
        type: Array,
        default: () => [
            { tab: '教职工', key: 1, checked: false },
            { tab: '学生组', key: 2, checked: true, disabled: [] }, // disabled[0,1,2,3,4] 禁止选择的学籍类型
            { tab: '按场地', key: 3, checked: true, disabled: [] },
            { tab: '自定义组', key: 4, checked: true, disabled: [] }
        ]
    },
    // 回显数据
    checked: {
        type: Array,
        default: () => []
    },
    // 当选择人员时回显的key Id
    checkedKey: {
        type: String,
        default: 'id'
    },
    // 选人 personnel 选部门depa
    mode: {
        type: String,
        default: 'personnel',
        validator(value) {
            return ['personnel', 'depa'].includes(value)
        }
    },
    // false 只显示学生  true: 显示学生及家长
    queryEltern: {
        type: Boolean,
        default: true
    }
})
const state = reactive({
    isParents: false,
    isParentTab: '',
    identity: 0,
    // slots-不知道是什么变量
    slots: '',
    searchValue: '',
    selectedKeys: [],
    autoExpandParent: true,
    _selectedKeys: [],
    expandedKeys: [],
    _treeDatas: [],
    activeKey: 1,
    //
    selectedAll: {
        indeterminate: false,
        checked: false,
        disabled: false
    },
    notSelectedList: [],
    // 选中
    selectedList: [],
    _selectedList: [],
    //
    visible: false,
    // 搜索模式
    isSearchMode: false,
    selectedSearchTableIds: [],
    searchData: [],
    searchPagination: {
        total: 0,
        size: 10,
        current: 1,
        showTotal: (total) => `共 ${total} 条`,
        showSizeChanger: false
    },
    searchLoading: false
})

const deviceId = computed(() => {
    return props.deviceId
})

const columns = [
    {
        title: '姓名',
        dataIndex: 'name',
        width: '100px'
    },
    {
        title: '部门/班级',
        dataIndex: 'demp',
        width: '260px',
        // ellipsis: true,
        customRender: ({ record }) => {
            if (props.mode === 'personnel' && state.activeKey === 1) {
                return record.deptVOList?.map((i) => i.name).join(',')
            }
            if (props.mode === 'personnel' && state.activeKey === 2) {
                return record.className
            }
        }
    }
]

const dataUnit = computed(() => {
    const unit = {
        personnel: '人',
        depa: '组'
    }
    return (len) => {
        return `${len}${unit[props.mode]}`
    }
})

const _depaPersonnelMap = new Map()
const _schoolPersonnelMap = new Map()

const relations = computed(() => {
    return (item) => {
        let text = ''
        store.state.selectSource.dictionary.relations.forEach(
            (j) => j.value == item && (text = j.label)
        )
        return text
    }
})
watch(
    () => props.selectedKeys,
    (v) => {
        state.selectedKeys = [...v]
        state._selectedKeys = [...v]
    }
)

// 中间池选项
watch(
    () => state.notSelectedList,
    (v) => {
        if (props.multiple) {
            // 多选
            selestMultipleList(toRaw(v))
        } else {
            // 单选
            // 旧
            state.selectedList = v.filter(
                (item) => item[props.checkedKey] === state._selectedList
            )
            // let newSelectData = []
            // if (v.length) {

            //     for (let i = 0; i < v.length; i++) {
            //         const item = v[i]
            //         if (state.selectedKeys.length) {
            //             if (
            //                 item[props.checkedKey] === state._selectedList ||
            //                 item[props.checkedKey] ===
            //                     state.notSelectedList[0].id
            //             ) {
            //                 newSelectData = [item]
            //                 break
            //             }
            //         } else {
            //             if (
            //                 item[props.checkedKey] === state._selectedList ||
            //                 item[props.checkedKey] ===
            //                     props.checked[0][props.checkedKey]
            //             ) {
            //                 newSelectData = [item]
            //                 break
            //             }
            //         }
            //     }
            // v.forEach((item) => {
            //     if (state.selectedKeys.length) {
            //         if (item[props.checkedKey] === state._selectedList) {
            //             newSelectData = [item]
            //         }
            //     } else {
            //         return (
            //             item[props.checkedKey] === state._selectedList ||
            //             item[props.checkedKey] ===
            //                 props.checked[0][props.checkedKey]
            //         )
            //     }
            // })
            // if (newSelectData.length) {
            //     state.selectedList = newSelectData
            // }
            // state.selectedList
            // }
        }
        // if (state.activeKey === 4) {
        //     state.notSelectedList = props.customTree
        // }
    },
    { deep: true }
)

watch(
    () => props.checked,
    (v) => {
        state.selectedList = toRaw(v)
    },
    { deep: true }
)

watch(
    () => props.treeData,
    (val) => {
        state._treeDatas = ref(val)
    },
    {
        deep: true
    }
)
// watch(
//     () => props.customTree,
//     (val) => {
//         if (val.length > 0) {
//             state.notSelectedList = val
//         }
//         // state._treeDatas = ref(val)
//     }
// )
watch(
    () => props.visible,
    (v) => {
        // 取消选中
        state.selectedKeys = []
        state.visible = v
        if (v) {
            state.isParents = false
            state.selectedList = props.checked.map((i) => i)
            init()
        }
    },
    {
        deep: true
    }
)

function init() {
    state.isSearchMode = false
    state.searchValue = ''
    //
    let tab = null
    // 设置选中的tabs
    if (props.tabs.length) {
        tab = props.tabs.find((item) => item.checked)
        state.identity = tab.identity
        state.activeKey = tab.key || props.tabs[0].key || 1
    }
    state.notSelectedList = []
    state._selectedList = []
    setCurrentTabsTreeData(tab.key, tab.isParty)
}

// 打开弹框
function handleSlotClick() {
    init()
    state.selectedList = props.checked.map((i) => i)
    state.visible = true
    emits('update:visible', true)
}

function setCurrentTabsTreeData(key, item, params = null) {
    // 如果没有传入tree = 根据 tabs 选中的 初始数据

    if (!props.treeData.length) {
        if (key === 1) {
            const data = [
                {
                    id: '2121211',
                    leader: '323232322',
                    name: '全校',
                    showName: '全校',
                    sort: -1,
                    type: -1,
                    children: toRaw(store.state.base.departmentTree)
                }
            ]
            state._treeDatas = ref(data)
            // state._treeDatas = shallowReactive(data)
            return
        }
        state._treeDatas = computed(() => store.state.base.schoolRollTree)
        // 需要继续判断 selectedKeys 和 展开的可以，没传则需要使用默认的
    } else {
        if (item) {
            state._treeDatas = []
            if (key === 2) {
                // 按班级/家长
                store.dispatch(item).then((res) => {
                    const { data } = res
                    state._treeDatas = data
                })
                return
            }

            if (item === 'base/getCustomMethod') {
                params = { equipmentType: 2 }
            }
            // 按场地/教职工
            store.dispatch(item, params).then((res) => {
                const { data } = res
                if (data?.length) {
                    const data = [
                        {
                            id: '2121211',
                            leader: '323232322',
                            name: '全校',
                            showName: '全校',
                            sort: -1,
                            type: -1,
                            children: toRaw(store.state.base.departmentTree)
                        }
                    ]
                    state._treeDatas = ref(data)
                } else {
                    state._treeDatas = [data]
                }
            })
        }
    }
    if (key === 4) {
    }
}
// tabs切换
function onChangeTabs(key) {
    state.isSearchMode = false
    // 用于多个弹框选项
    let isParty = null
    const params = null
    props.tabs.forEach((v, idx) => {
        if (v.key === key) {
            state.isParents = v.tab === '家长'
            state.isParentTab = v.tab
            isParty = v.isParty
            state.identity = v.identity
            v.paramsKey && (v.paramsKey = v.paramsValue)
        }
    })
    if (key !== 3) {
        setCurrentTabsTreeData(key, isParty, params)
    }
    state.notSelectedList = []
    emits('emitChangeTabs', key)
    // TODO 如有key 需要更新 selectedKeys 和 expandedKeys

    // 搜索重置
    state.searchPagination.total = 0
    state.searchPagination.current = 1
    state.searchData = []
}
// 多选
function selestMultipleList(v) {
    v.forEach((i) => {
        const index = state.selectedList.findIndex(
            (c) => c[props.checkedKey] === i[props.checkedKey]
        )
        // 是否存在只允许选择的部门，
        if (props.checkParents && state.activeKey == 2) {
            // let checkParentList = []
            // state.isParents &&
            // if (i.elterns.length) {
            //     checkParentList = i.elterns.filter((j) => j._checked)
            //     if (checkParentList?.length) {
            //         i._checked = true
            //         if (i._checked) {
            //             // 如果已选择没有当前的，则添加
            //             if (index === -1) {
            //                 const params = {
            //                     ...i,
            //                     elterns: checkParentList
            //                 }
            //                 state.selectedList.push(params)
            //                 i._checked = false
            //             } else {
            //                 state.selectedList[index].elterns = checkParentList
            //             }
            //         } else {
            //             // 如果已选择的存在当前的，则删除
            //             if (index !== -1) {
            //                 state.selectedList.splice(index, 1)
            //             }
            //         }
            //     } else {
            //         i._checked = false
            //         i.elterns?.length &&
            //             i.elterns.forEach((i) => (i._checked = false))
            //         if (index !== -1) {
            //             state.selectedList.splice(index, 1)
            //         }
            //     }
            // }
            if (v.length) {
                // 如果选中
                if (i._checked) {
                    // 如果已选择没有当前的，则添加
                    if (index === -1) {
                        state.selectedList.push(i)
                    }
                } else {
                    // 如果已选择的存在当前的，则删除
                    if (index !== -1) {
                        state.selectedList.splice(index, 1)
                    }
                }
            }
        } else {
            // 如果选中
            if (i._checked) {
                // 如果已选择没有当前的，则添加
                if (index === -1) {
                    state.selectedList.push(i)
                }
            } else {
                // 如果已选择的存在当前的，则删除
                if (index !== -1) {
                    state.selectedList.splice(index, 1)
                }
            }
        }
    })
    // 处理全选
    if (state.selectedList.length) {
        const curCheck = v.filter((i) => i._checked === true)
        if (curCheck.length) {
            state.selectedAll.indeterminate = curCheck.length !== v.length
            state.selectedAll.checked = curCheck.length === v.length
        } else {
            resetSelectedAll()
        }
    } else {
        resetSelectedAll()
    }
    state.selectedAll.disabled = state.notSelectedList.every((i) => i._disabled)
        ? true
        : !v.length
}
// 重置全选状态
function resetSelectedAll() {
    state.selectedAll.indeterminate = false
    state.selectedAll.checked = false
    state.selectedAll.disabled = false
}
// 删除选中的
function delSelecteItem(item, index, cindex) {
    const existIndex = state.notSelectedList.findIndex(
        (c) => c[props.checkedKey] === item[props.checkedKey]
    )

    if (existIndex !== -1) {
        state.notSelectedList[existIndex]._checked = false

        // 删除学生家长时
        if (cindex !== undefined) {
            const obj = item.elterns[cindex]
            state.notSelectedList[existIndex].elterns.forEach((j) => {
                if (j.id === obj.id) {
                    j._checked = false
                }
            })
        }
    }

    if (props.checkParents && cindex !== undefined) {
        // if(item.elterns.length==1){
        //     state.selectedList.splice(index, 1);
        // }
        // if(item.elterns.length>1){
        //     item.elterns.splice(cindex,1)
        // }
    } else {
        state.selectedList.splice(index, 1)
    }

    // 如果是单选
    if (!props.multiple) {
        state._selectedList = ''
    }
    // 搜索时
    if (state.isSearchMode) {
        const idx = state.selectedSearchTableIds.findIndex(
            (id) => id === item.id
        )
        if (idx > -1) {
            state.selectedSearchTableIds.splice(idx, 1)
        }
    }
}
// 清空
function hadleEmptyAllSelected() {
    state._selectedList = []
    state.selectedList = []
    state.notSelectedList.forEach((item) => {
        item._checked = false
        if (props.checkParents && item.elterns?.length) {
            item.elterns.forEach((j) => (j._checked = false))
        }
    })

    if (state.isSearchMode) state.selectedSearchTableIds = []
}
// 左侧树 点击事件------------------------
const select = async(selectedKeys, e) => {
    if (e.node.type === '-1') return
    if (selectedKeys.length) {
        state._selectedKeys = selectedKeys
        state.selectedKeys = selectedKeys
    } else {
        state.selectedKeys = state._selectedKeys
    }
    const item = e.node
    // 当前为学籍
    const isXueJi = state.activeKey == 2
    const disabledType = props.tabs.find((i) => i.key == 2)?.disabled
    let disabled = false
    if (props.mode === 'personnel') {
        const tabsActive = props.tabs.findIndex((v) => v.key == state.activeKey)
        const params = {
            id: item.id,
            type: item.type,
            item
        }
        let result = []
        if (props.tabs[tabsActive].selectType === 'outsiders') {
            // 获取外部人员
            const { data } = await getFaceInfo({
                deviceId: deviceId.value,
                rollType: e.node.type,
                id: selectedKeys[0] || e.node.id,
                name: '',
                pageNo: 1,
                pageSize: 100,
                status: 1,
                type: 4
            })
            result = data?.page.list || []
        } else {
            result = await getPersonne(params)
        }
        state.notSelectedList = result.map((item) => {
            const index = state.selectedList.findIndex(
                (c) => c[props.checkedKey] === item[props.checkedKey]
            )
            item._checked = false
            // 选择家长时候
            if (props.checkParents && item.elterns?.length) {
                item.elterns.forEach((i) => {
                    if (index == -1) {
                        i._checked = false
                    } else {
                        const obj = state.selectedList[index]?.elterns.find(
                            (j) => j.id === i.id
                        )
                        i._checked = !!obj
                    }
                })
            }

            return {
                ...item,
                showName: item.showName || item.name || item.userName,
                _checked: index > -1,
                _disabled: disabled,
                _source: state.activeKey
            }
        })
    } else {
        const children = item.children || []
        state.notSelectedList = children.map((item) => {
            const index = state.selectedList.findIndex(
                (c) => c[props.checkedKey] === item[props.checkedKey]
            )
            disabled = isXueJi && disabledType?.includes(item.type)
            return {
                ...item,
                showName: item.showName || item.name,
                _checked: index > -1,
                _disabled: disabled,
                _source: state.activeKey
            }
        })
        emits('emitSelectLeft', state.notSelectedList)
    }

    if (state.activeKey === 4) {
        getFaceInfo({
            deviceId: deviceId.value,
            rollType: e.node.type,
            id: selectedKeys[0] || e.node.id,
            name: '',
            pageNo: 1,
            pageSize: 100,
            status: 1,
            type: 3
        }).then((res) => {
            state.notSelectedList = res.data.page.list
        })
    }
}

// eslint-disable-next-line camelcase
const { roll_status_yes_id, emp_status_yes_id } =
    store.state.selectSource.dictionary || {}

// 获取人员
async function getPersonne({ id, type, item }, callback) {
    let result = []
    // 教职工
    if (state.activeKey === 1) {
        try {
            const deptId = id
            const {
                data: { list }
            } = await getDepartmentPersonnel({
                deviceId: deviceId.value,
                statusList: emp_status_yes_id,
                deptId,
                pageNo: 1,
                pageSize: 999,
                code: router.currentRoute.value.meta.code
            })
            result = list
            _depaPersonnelMap.set(deptId, result)
        } catch (error) {}
    }
    const classes = judgmentLevels.value[type]?.key || ''

    // 学生组 且是班级 = 获取人员
    if (state.activeKey == 2 && classes === 'classes') {
        try {
            const classesId = id
            const params = {
                deviceId: deviceId.value,
                statusList: roll_status_yes_id,
                classesId,
                code: router.currentRoute.value.meta.code
            }
            params.filterNonEltern = state.isParentTab === '家长'
            // false 只显示学生  true: 显示学生及家长
            params.queryEltern = props.queryEltern
            const res = await getSchoolRollPersonnel(params)
            result = res.data
            _schoolPersonnelMap.set(classesId, result)
        } catch (error) {}
    }
    return result
}
// 全选
function onCheckAllChange(e) {
    // 1 过滤掉已经关联的数据 disable:true
    const arr = state.notSelectedList.filter((item) => !item.disable)
    if (arr.length) {
        arr.forEach((i) => {
            i._checked = e.target.checked

            if (props.checkParents && i.elterns?.length) {
                i.elterns.forEach((i) => (i._checked = e.target.checked))
            }
        })
    }
}

const judgmentLevels = computed(() => {
    const newData = {}
    const rollList = store.state.base.commentJsonKeys.INIT_ROLL_LIST
    rollList.forEach((v) => (newData[v.level] = v))
    return newData
})
// 单选
function onCheckRadioChange(e) {
    const findIndex = state.notSelectedList.findIndex(
        (item) => item[props.checkedKey] === e.target.value
    )
    state.notSelectedList.forEach((item, index) => {
        item._checked = findIndex === index
    })
}

function handleOk() {
    emits('update:checked', toRaw(state.selectedList))
    state.visible = false
    emits('update:visible', false)
    emits('handleOk', toRaw(state.selectedList))
    emits('emitChangeTabs', 1)
    //
    state.searchPagination.total = 0
    state.searchPagination.current = 1
    state.searchValue = ''
}

const handleCancel = () => {
    state.visible = false
    emits('emitChangeTabs', 1)
    emits('update:visible', false)
}
// const elternsCheckbox = (item, itChildren) => {
//     itChildren._checked = !itChildren._checked
//     const newItem =JSON.parse(JSON.stringify(item))
//     if(!newItem.children?.length) newItem.children = []
//     if(itChildren._checked) {
//         newItem.children.push(itChildren);
//         state.selectedList.push(toRaw(newItem))
//     }
// }
// 取消搜索
function handleCancelSearch() {
    state.isSearchMode = false
    state.searchPagination.total = 0
    state.searchPagination.current = 1
    state.searchData = []
    state.searchValue = ''
}

const getParentKey = (key, tree) => {
    let parentKey = null
    for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.children) {
            if (
                node.children.some(
                    (item) => (item.showName || item.name) === key
                )
            ) {
                parentKey = node.id
            } else if (getParentKey(key, node.children)) {
                parentKey = getParentKey(key, node.children)
            }
        }
    }
    return parentKey
}

function flatTree(data = [], treeMap = [], depth = 0) {
    if (!(data && data.length)) return []
    depth++
    return data.reduce((acc, cur) => {
        cur.depth = depth
        acc.push(cur)
        if (cur.children && cur.children.length) {
            flatTree(cur.children, treeMap, depth)
        }
        return acc
    }, treeMap)
}

// 搜索分页
function handleSearchTableChange(pagination) {
    state.searchPagination.current = pagination.current
    search(state.searchValue, 'pagination')
}

async function search(value, isPagination) {
    state.searchValue = value
    if (isPagination !== 'pagination') {
        state.searchPagination.current = 1
    }
    // 如果找部门，则去获取对应的 parent
    if (props.mode === 'depa') {
        const list = toRaw(state._treeDatas)
        const dataList = flatTree(list[0]?.children)
        const expanded = dataList
            .map((i) => {
                const name = i.showName || i.name
                if (name.indexOf(value) > -1) {
                    return getParentKey(name, list[0]?.children)
                }
                return null
            })
            .filter((item, i, self) => item && self.indexOf(item) === i)

        // TODO 由于 expandedKeys  使用后导致 defaultExpandAll失效，现如搜索节点在收起时无法直动展开，赋值expanded后 页面进入又无法全部展开， 后期优化
        // https://www.antdv.com/components/tree-cn/#API
        state.expandedKeys = expanded
        state.autoExpandParent = true
    }
    // 如果找人, 切换到列表，判断tabs 是找教职工还是学生
    if (props.mode === 'personnel') {
        // 切换到搜索页面,
        state.isSearchMode = true
        state.selectedSearchTableIds = state.selectedList.map(
            (i) => i[props.fieldNames.key]
        )
        state.searchLoading = true
        // 如果是教职工
        if (Number.parseInt(state.activeKey) === 1) {
            const dempUser = await getDepartmentPersonnel({
                statusList: emp_status_yes_id,
                pageNo: state.searchPagination.current,
                pageSize: state.searchPagination.size,
                name: value,
                code: router.currentRoute.value.meta.code
            })
            state.searchPagination.total = dempUser.data.total
            state.searchData = dempUser.data.list
        }
        // 如果是学生
        if (Number.parseInt(state.activeKey) === 2) {
            const studentUser = await getSchoolRollPersonnel({
                statusList: roll_status_yes_id,
                pageNo: 1,
                pageSize: 10,
                name: value,
                code: router.currentRoute.value.meta.code
            })
            state.searchData = studentUser.data || []
        }

        state.searchLoading = false
    }
}
function onSelectChangeTable(ids) {
    if (props.multiple) {
        state.selectedSearchTableIds = ids
    } else {
        state.selectedSearchTableIds = [ids[ids.length - 1]]
    }
}
function onSelectSelectTable(record) {
    const item = toRaw(record)
    const index = state.selectedList.findIndex(
        (i) => i[props.fieldNames.key] === item.id
    )
    if (index === -1) {
        item._checked = true
        if (props.multiple) {
            state.selectedList.push({
                ...item,
                showName: item.showName || item.name || item.userName,
                _source: state.activeKey
            })
        } else {
            state.selectedList = [
                {
                    ...item,
                    showName: item.showName || item.name || item.userName,
                    _source: state.activeKey
                }
            ]
        }
    } else {
        state.selectedList.splice(index, 1)
    }
}
defineExpose({
    ...toRefs(state)
})
function onExpand(keys) {
    // state.expandedKeys = keys
    // state.autoExpandParent = false
}
</script>

<style lang="less" scoped>
.border() {
    border-bottom: 1px solid @border-color-base;
}

.userselect_warp {
    .border();
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    .userselect_search {
        padding: 14px 24px;
        display: flex;
        align-items: center;
    }

    .userselect {
        flex: 1;
        border-top: 1px solid @border-color-base;
        position: relative;
        // height: 514px;
        // overflow: hidden;

        .userselect_tree {
            border-right: 1px solid @border-color-base;
            padding: 14px 16px;
            overflow: scroll;
            height: 513px;
        }

        .userselect_checklist {
            padding: 14px 8px;
            overflow: scroll;
            height: 513px;
            max-width: 350px;

            .student-select {
                display: flex;
                justify-content: space-between;
                flex: 1;
            }

            .slots-selecte-elterns {
                display: flex;
                justify-content: space-between;
            }
        }
    }
}

.usercheck_warp {
    border-left: 1px solid @border-color-base;
    .border();
    padding: 16px 12px;
    height: 575px;
    box-sizing: border-box;
    overflow: scroll;
}

.selected_item {
    min-height: 24px;
    user-select: none;
    padding: 8px 12px;
    transition: all 0.25s;
    box-sizing: border-box;
    position: relative;

    &:hover {
        background-color: @gray-background;
    }

    .elterns_del {
        position: absolute;
        top: 13px;
        right: 12px;
    }
}

.selected_item_activate {
    color: @primary-color;
}

.empty_allselected {
    color: @primary-color;
}

.hide_empty {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate3d(-50%, -50%, 0);
    text-align: center;
}

.relations {
    border: 1px solid @primary-color;
    color: @primary-color;
    padding: 0px 4px;
    font-size: 12px;
    border-radius: 4px;
}

.slots-selecte {
    display: flex;
    width: 100%;
    justify-content: inherit;
    align-items: center;
    margin: 5px 0;

    .slots-selecte-user {
        padding-left: 24px;
    }
}
</style>
<style lang="less">
.y_select__modal {
    .ant-modal-header {
        padding-bottom: 0;
        border-bottom: 1px solid @border-color-base;
    }

    .ant-modal-body {
        padding: 0;
    }

    .ant-modal-footer {
        // border-top: 1px solid @border-color-base;
        border-top: none;
    }

    .ant-radio-wrapper {
        margin-right: 0px;
    }
}

.y_select__tabs .ant-tabs-nav {
    margin: 0;

    &::before {
        content: none;
    }
}
</style>
