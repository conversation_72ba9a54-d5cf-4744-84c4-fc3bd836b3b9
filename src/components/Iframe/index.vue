<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-04-21 17:06:03
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-04-21 19:28:23
-->
<template>
    <a-spin
        :tip="tip"
        :spinning="state.spinning"
        wrapperClassName="iframe_warp"
    >
        <iframe
            :src="src"
            frameborder="0"
            class="my_iframe"
            id="_iframe"
        ></iframe>
    </a-spin>
</template>

<script setup>
import { reactive, onMounted, nextTick } from 'vue'

defineProps({
    src: String,
    tip: {
        type: String,
        default: ''
    }
})

const state = reactive({
    spinning: true
})

function init() {
    const iframe = document.getElementById('_iframe')
    if (!iframe) return false
    iframe.onload = () => {
        state.spinning = false
    }
}

onMounted(() => nextTick(() => init()))
</script>

<style lang="less">
.iframe_warp {
    width: 100%;
    height: 100%;

    .ant-spin-container,
    .my_iframe {
        width: 100%;
        height: 100%;
    }
}
</style>
