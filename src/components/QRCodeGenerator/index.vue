<template>
    <canvas ref="qrcodeRef" class="code_img"></canvas>
</template>

<script setup>
import { shallowRef, watch } from 'vue'
import QRCode from 'qrcode'

const props = defineProps({
    value: {
        type: String,
        default: ''
    }
})
const qrcodeRef = shallowRef()
// 初始化生成二维码
const initQrcode = () => {
    const { value } = props
    QRCode.toCanvas(qrcodeRef.value, value)
}
watch(
    () => props.value,
    (val, oldVal) => {
        if (val || oldVal) {
            initQrcode()
        }
    }
)
defineExpose({ qrcodeRef })
</script>
<style lang="less" scoped>
.code_img {
    width: 160px !important;
    height: 160px !important;
    margin: 10px auto;
}
</style>
