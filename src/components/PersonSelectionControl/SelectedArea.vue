<template>
    <div class="select-area">
        <header>
            <div class="label">
                已选
                <span class="count" v-if="departPeople.people"
                    >{{ departPeople.people }} 人</span
                >
                <span class="count" v-if="departPeople.depart"
                    >{{ departPeople.depart }} 部门</span
                >
            </div>
            <a-button
                type="link"
                class="btn-link-color"
                v-if="list.length"
                @click="clear"
                >清空</a-button
            >
        </header>

        <ul class="selected-list">
            <li v-for="(item, index) in list" :key="item[fieldNames.value]">
                <div class="selected-list-item">
                    <div class="selected-list-user">
                        <a-avatar
                            shape="square"
                            :src="(!isPerson(item) && structure) || 'arrow'"
                            :size="24"
                        >
                            <span v-if="isPerson(item)">
                                {{
                                    item.studentName?.slice(-2) ||
                                    item.name?.slice(-2)
                                }}
                            </span>
                        </a-avatar>
                        <div
                            class="label ellipsis"
                            :title="item[fieldNames.label]"
                        >
                            {{ item.showName || item[fieldNames.label] }}
                        </div>
                    </div>

                    <template v-if="item.elterns?.length">
                        <div
                            class="selected-list-elter"
                            v-for="it in item.elterns"
                            :key="it.id"
                        >
                            <a-avatar
                                shape="square"
                                :src="(!isPerson(it) && structure) || 'arrow'"
                                :size="24"
                            >
                                <span v-if="isPerson(it)">
                                    {{ it.name?.slice(-2) }}
                                </span>
                            </a-avatar>
                            <div class="label ellipsis">
                                {{ it.showName || it.name }}
                                <span class="identity">
                                    {{ relations(it.relations) }}
                                </span>
                            </div>
                        </div>
                    </template>
                </div>

                <CloseOutlined class="delete-icon" @click="deleteItem(index)" />
            </li>
        </ul>
    </div>
</template>

<script setup>
import structure from "/image/icon-structure.png";
import arrow from "/image/icon-arrow.png";
import { Checkbox as ACheckbox, Radio as ARadio } from "ant-design-vue";
import { SELECT_TYPE } from "./constants";

import { useStore } from "@/store/index";

const store = useStore();
// *********************
// Hooks Function
// *********************

const props = defineProps({
    list: {
        type: Array,
        default: [],
    },
    type: {
        type: String,
        default: SELECT_TYPE.DEPARTMENT,
    },
    tabs: {
        type: Array,
        default: () => [],
    },
    fieldNames: {
        type: Object,
        default: () => {
            return { label: "name", value: "id" };
        },
    },
});

const relations = computed(() => {
    return (item) => {
        let text = "";
        store.state.selectSource.dictionary.relations.forEach(
            (j) => j.value == item && (text = j.label)
        );
        return text;
    };
});
const departPeople = computed(() => {
    const depart = [];
    const people = [];
    // 学籍时返回：班级 classes，学生：student，家长：eltern 部门时返回：部门:dept，部门员工：people_dept 角色时返回：角色：role ，
    // 角色员工：people_role 外部人员返回：组：external ， 组成员：people_external 人脸自定义组：face_group，人脸自定义人员：people_face_group
    // 装的人员身份
    const isPersonnel = [
        "student",
        "eltern",
        "people_dept",
        "people_role",
        "people_external",
        "people_face_group",
    ];
    // 装的部门
    const isDep = ["classes", "dept", "role", "external", "face_group"];
    props.list.forEach((v) => {
        // 应对旧数据userId没值也带上的问题 没有就销毁
        // if(!v.userId) {
        //     delete v.userId
        // }
        if (
            (v.hasOwnProperty("pid") || isDep.includes(v.typeValue)) &&
            v.typeValue !== "student"
        ) {
            depart.push(v);
        } else {
            people.push(v);
        }
    });
    return { depart: depart.length, people: people.length };
});
const emit = defineEmits(["clear", "deleteItem"]);

// 单位
const unit = computed(() => {
    if (props.type === SELECT_TYPE.DEPARTMENT) {
        // 选部门
        return "部门";
    } else if (props.type === SELECT_TYPE.PEOPLE) {
        return "人";
    } else if (
        [SELECT_TYPE.CLASS, SELECT_TYPE.ALL].includes(props.type) ===
        SELECT_TYPE.CLASS
    ) {
        // 班级、宿舍、选人和选部门
        return "项";
    }
});

const isPerson = computed(() => {
    return (item) => {
        if ([SELECT_TYPE.PEOPLE, SELECT_TYPE.ALL].includes(props.type)) {
            // 或者直接判断对象是否存在改字段（例如：userId  人员特有的）
            // item.hasOwnProperty(tab.personField?.key)
            // $ tab有一个符合就算是人（切换tab,保持头像必须不变）
            return props.tabs.some(
                (tab) =>
                    tab.personField?.value.includes(
                        item[tab.personField.key]
                    ) || item.hasOwnProperty(tab.personField?.key)
            );
        }
        return false;
    };
});

// *********************
// Service Function
// *********************

const clear = () => {
    emit("clear");
};

const deleteItem = (index) => {
    emit("deleteItem", index);
};
</script>

<style lang="less" scoped>
.select-area {
    display: flex;
    flex-direction: column;
    flex: 1;
    padding: 15px 12px;
    border-left: 1px solid #d9d9d9;
    box-sizing: border-box;
    overflow: hidden;

    header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;
        height: 24px;

        .label {
            font-size: 14px;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
        }

        .count {
            font-size: 12px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.85);
            margin-left: 8px;
        }
    }

    .selected-list {
        display: flex;
        flex-wrap: wrap;
        overflow: hidden auto;

        li {
            box-sizing: border-box;
            max-width: 100%;
            display: flex;
            align-items: center;
            padding: 4px;
            background: #f6f6f6;
            border-radius: 4px;
            margin: 0 10px 10px 0;
        }

        .label {
            flex: 1;
            font-size: 12px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.85);
            margin: 0 4px;
        }

        .ellipsis {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .delete-icon {
            color: #c2c3c3;
            cursor: pointer;
        }

        .selected-list-user {
            display: flex;
        }

        .selected-list-elter {
            display: flex;
            margin: 6px 0;

            .label {
                color: #999;
            }

            .identity {
                border: 1px solid var(--primary-color);
                color: var(--primary-color);
                border-radius: 5px;
                padding: 0 4px;
                font-size: 10px;
            }
        }
    }

    :deep(.ant-avatar) {
        font-size: 14px;
        background: var(--primary-color);
    }

    :deep(.ant-avatar-image) {
        background: transparent;
    }
}
</style>
