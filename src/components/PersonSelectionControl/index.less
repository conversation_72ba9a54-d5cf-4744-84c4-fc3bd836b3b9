
.mSelect-wrap {
    display: flex;
    height: 540px;
    font-size: 14px;
    line-height: 20px;
    font-family:
        PingFangSC-Regular,
        PingFang SC;
    font-weight: 400;
    color: @heading-color;

    .section {
        display: flex;
        flex-direction: column;
        width: 60%;
        padding: 16px 0;

        &:last-child {
            border-left: 1px solid @border-color-base;
        }
        &.active {
            padding-top: 0;
            .select-wrap {
                margin-top: 0;
                border-top: none;
            }
        }
        &.section-selected {
            width: 40%;
        }
    }

    .ant-input-search {
        display: block;
        width: auto;
        margin: 0 16px;
    }

    .select-wrap {
        flex: 1;
        display: flex;
        flex-direction: column;
        border-top: 1px solid @border-color-base;
        margin-top: 16px;
        padding: 16px 16px 0;
        overflow: hidden;
    }

    .ant-breadcrumb {
        margin-top: 16px;
        :deep(ol) {
            display: inline-block;
        }
        li {
            display: inline;
        }
        :deep(.ant-breadcrumb-link) {
            display: inline;
        }
        span:last-child {
            pointer-events: none;
        }
    }

    .tabs {
        :deep(.ant-radio-group) {
            display: flex;
            text-align: center;
        }

        :deep(.ant-radio-button-wrapper) {
            flex: 1;

            &:first-child {
                border-top-left-radius: 40px;
                border-bottom-left-radius: 40px;
            }

            &:last-child {
                border-top-right-radius: 40px;
                border-bottom-right-radius: 40px;
            }
        }
    }

    .structures {
        flex: 1;
        overflow-y: auto;
        margin-right: -10px;
        :deep(.ant-spin-nested-loading) {
            height: 100%;
        }
        :deep(.ant-spin-container) {
            height: 100%;
        }

        .row {
            box-sizing: border-box;
            display: flex;
            align-items: center;
            padding: 0 8px;
            border-radius: 4px;
            margin-bottom: 6px;
            width: 100%;

            &:hover,
            &:active {
                background-color: #f6f6f6;
            }
        }

        .check {
            display: flex;
            align-items: center;
            padding: 6px 0;
            width: 100%;
            :deep(span:nth-of-type(2)) {
                flex: 1;
            }
        }

        .check-visible {
            pointer-events: none;

            :deep(.ant-radio),
            :deep(.ant-checkbox) {
                visibility: hidden;
            }
        }

        .cnt {
            flex: 1;
            max-width: 170px;
            margin-left: 8px;
        }

        :deep(.ant-radio + span),
        :deep(.ant-checkbox + span) {
            flex: 1;
            display: flex;
            align-items: center;
        }

        .sub {
            color: #999;
        }

        .more {
            font-size: 14px;
            color: var(--primary-color);
            line-height: 16px;
            padding-left: 12px;
            border-left: 1px solid @border-color-base;
            margin-left: auto;
            min-width: 50px;
            cursor: pointer;
            user-select: none;
        }
    }

    :deep(.ant-avatar) {
        font-size: 14px;
        background: var(--primary-color);
    }

    :deep(.ant-avatar-image) {
        background: transparent;
    }

    .selected-hd {
        display: flex;
        align-items: center;
        padding: 0 12px;

        .label {
            font-family:
                PingFangSC-Medium,
                PingFang SC;
        }

        .count {
            font-size: 12px;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            color: @heading-color;
            line-height: 17px;
            margin-left: 8px;
        }

        .btn-clear {
            color: var(--primary-color);
            margin-left: auto;
            cursor: pointer;
            user-select: none;
        }
    }

    .selected-bd {
        flex: 1;
        padding: 15px 12px 0;
        overflow-y: auto;
    }

    .selected-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .selected-item {
            display: flex;
            align-items: center;
            padding: 4px;
            background: #f6f6f6;
            border-radius: 4px;
        }

        .cnt {
            margin-left: 4px;
        }

        .sub {
            color: #999;
        }

        .icon-del {
            width: 14px;
            height: 15px;
            margin-left: 8px;
            cursor: pointer;
            user-select: none;
            font-size: 12px;
            background-image: url('/image/icon-del.png');
        }
    }
}

.ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all;
}

.data-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 90px;
    img {
        width: 201px;
        height: 117px;
    }
    p {
        font-size: 14px;
        font-family:
            PingFangSC-Regular,
            PingFang SC;
        font-weight: 400;
        color: #595959;
        line-height: 26px;
        text-align: center;
        margin-top: 17px;
    }
}
.single-table {
    :deep(.ant-checkbox-inner) {
        border-radius: 100%;
    }
    :deep(.ant-checkbox-checked) {
        &::after {
            border-radius: 100%;
        }
    }
    :deep(.ant-checkbox-wrapper) {
        border-radius: 100%;
    }
}
:deep(.ant-pagination) {
    flex-wrap: nowrap;
    margin-bottom: 0;
    display: none;
}
.ydpagination {
    display: block;
    margin-top:10px;
    text-align: right;
}