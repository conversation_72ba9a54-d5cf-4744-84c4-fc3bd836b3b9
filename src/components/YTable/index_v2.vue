<template>
    <div class="table_wrapper">
        <a-spin :spinning="spinLoad" class="table-spin">
            <slot name="handleItem" />
            <a-table
                :locale="{ emptyText: 'fasdfasdf' }"
                :rowKey="rowKeyId"
                showSorterTooltip
                :pagination="pagination"
                :columns="columns"
                :dataSource="dataSource"
                @change="handleTableChange"
                :scroll="
                    isScroll ? { x: `calc(${clientWidth} - 600px)` } : null
                "
                :class="{ antTableCellActive: !dataSource.length }"
                :row-selection="
                    rowSelection
                        ? {
                              selectedRowKeys: selectedRowKeys,
                              onChange: onSelectChange
                          }
                        : null
                "
            >
                <!--  onCheckboxProps: (record) => ({
                                  disabled: record.isEnable == 0
                })-->

                <template #customFilterIcon="{ column }">
                    <component
                        :is="column.customFilterIcon"
                        style="color: #000000d9"
                    ></component>
                </template>
                <template #headerCell="{ column }">
                    <template v-if="column.pathColor">
                        <span>
                            {{ column.title }}
                            <!-- <component
                                :is="'sliders-two-tone'"
                                :style="column.style? column.style:''"
                                :class="{pathColor: column.pathColor}"
                                @click="column.onFilter"
                            ></component>
                            {{column.filterIcon}}-->
                            <i
                                class="iconfont pathColor"
                                :class="column.filterIcon"
                                @click="column.onFilter"
                            ></i>
                        </span>
                    </template>
                </template>
                <template #bodyCell="{ column, text, record, index }">
                    <template v-if="slots.length">
                        <template v-for="(it, idex) in slots">
                            <slot
                                :key="idex"
                                :name="it"
                                v-if="column.dataIndex === it"
                                :itme="record"
                                :index="index"
                                >{{ text }}</slot
                            >
                        </template>
                    </template>
                    <span v-else> {{ text }}</span>
                    <!-- <a-tooltip placement="topLeft">
                        <template #title v-if="text?.length > 15">
                            {{ text }}
                        </template>
                        {{ text }}
                    </a-tooltip> -->
                </template>
                <template #emptyText>
                    <slot name="emptyText">
                        <a-empty
                            image="image/empty.png"
                            :image-style="{ width: '100%', height: '180px' }"
                        ></a-empty>
                    </slot>
                </template>
            </a-table>
        </a-spin>
    </div>
</template>

<script lang="ts">
import {
    computed,
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    ref,
    watch,
    watchEffect
} from 'vue'
import { useStore } from '@/store/index'
import { useRoute, useRouter } from 'vue-router'
window.clientWidths = ''
window.onresize = () => {
    window.clientWidths = `${document.body.clientWidth}px`
}
export default defineComponent({
    props: {
        totals: {
            type: Object,
            default: () => {
                return {
                    total: 0,
                    pageNo: 1,
                    pageSize: 10
                }
            }
        },
        rowKeyId: {
            type: String,
            default: 'id'
        },
        dataSource: {
            type: Array,
            default: () => []
        },
        columns: {
            type: Array,
            default: () => []
        },
        slots: {
            type: Array,
            default: () => []
        },
        rowSelection: {
            type: Boolean,
            default: true
        },
        isScroll: {
            type: Boolean,
            default: false
        },
        spinLoad: {
            type: Boolean,
            default: false
        }
    },
    emits: ['onSelectedRowKeys', 'onSelectedArry'],
    setup(props, { emit }) {
        const { proxy } = getCurrentInstance() as any
        const store = useStore()
        const route = useRoute()
        const router = useRouter()
        const clientWidth = ref<string>(`${document.body.clientWidth}px`)

        const pagination = ref<any>({
            hideOnSinglePage: true,
            total: 0,
            current: 1,
            showQuickJumper: true,
            showLessItems: true,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '30', '40'],
            showTotal: (total: any, range: any) => `共 ${total} 条`
        })

        const state = reactive({
            selectedRowKeys: [],
            loading: false
        })

        // 可选择
        const onSelectChange = (selectedRowKeys: any, data: any) => {
            state.selectedRowKeys = selectedRowKeys
            emit('onSelectedArry', selectedRowKeys, data)
        }

        const handleTableChange = (res: any, data: any) => {
            for (const i in data) res[i] = data[i]
            const { pageSize, current } = res
            emit('onSelectedRowKeys', res)
        }
        const handleSearch = (
            selectedKeys: any,
            confirm: any,
            dataIndex: any
        ) => {
            confirm()
            state.searchText = selectedKeys[0]
            state.searchedColumn = dataIndex
        }
        const hasSelected = computed(() => state.selectedRowKeys.length > 0)
        watchEffect(() => {
            pagination.value = {
                ...pagination.value,
                ...props.totals,
                current: props.totals.pageNo,
                hideOnSinglePage: props.totals.total < 11
            }
            // if (props.spinLoad) {
            //     state.selectedRowKeys = []
            //     emit('onSelectedArry', [])
            // }
        })

        watch(
            () => props.dataSource,
            (val: any, olVal: any) => {
                state.selectedRowKeys = []
                emit('onSelectedArry', [])
            }
        )
        return {
            pagination,
            clientWidth,
            hasSelected,
            handleSearch,
            onSelectChange,
            ...toRefs(state),
            handleTableChange
        }
    }
})
</script>

<style lang="less">
.table_info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 0;
    .table_title {
        font-weight: 500;
        font-size: 18px;
        color: #000;
    }
}
.table_hander {
    .marl10 {
        margin-left: 16px;
    }
}
.table_wrapper tr.ant-table-row-selected > td {
    background: #fafafa !important;
}
.antTableCellActive .ant-table-cell {
    border: none;
}
.pathColor {
    color: #00b781;
    width: 16px;
    height: 16px;
    position: absolute;
    top: 16px;
    right: 10px;
    font-size: 16px;
}
.table-spin {
    position: absolute;
    top: 50%;
    left: 50%;
}
.ant-table-expanded-row-fixed {
    width: 100% !important;
}
</style>
