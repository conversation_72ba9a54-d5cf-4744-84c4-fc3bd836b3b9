<template>
    <div class="table_wrapper">
        <a-spin :spinning="spinLoad" class="table-spin">
            <slot name="handleItem" />
            <a-table
                :bordered="isBordered"
                :locale="{ emptyText: '暂无数据' }"
                :row-key="rowKeyId"
                show-sorter-tooltip
                :pagination="pagination"
                :columns="columns"
                :data-source="dataSource"
                :scroll="
                    isScroll
                        ? {
                              x: `calc(${
                                  isSrollXW ? isSrollXW : clientWidth
                              } - 600px)`,
                          }
                        : isSrollY
                        ? { y: isSrollYH }
                        : null
                "
                :class="{
                    antTableCellActive: !dataSource.length,
                    table_width:
                        $route.name === 'schoolRoll' && sider_two_level.length,
                }"
                :row-selection="
                    rowSelection
                        ? {
                              getCheckboxProps:
                                  rowSelectionOptions?.getCheckboxProps,
                              selectedRowKeys: selectedRowKeys,
                              onChange: onSelectChange,
                              type: isRadio ? 'radio' : 'checkbox',
                          }
                        : null
                "
                @change="handleTableChange"
            >
                <!--  onCheckboxProps: (record) => ({   type: checkType
                                  disabled: record.isEnable == 0
                })-->

                <template #customFilterIcon="{ column }">
                    <component
                        :is="column.customFilterIcon"
                        style="color: #000000d9"
                    />
                </template>
                <template #headerCell="{ column, text, record, index }">
                    <template v-if="column.pathColor">
                        <span>
                            {{ column.title }}
                            <i
                                class="iconfont pathColor"
                                :class="column.filterIcon"
                                @click="column.onFilter"
                            />
                        </span>
                    </template>
                    <slot
                        name="headerCell"
                        :column="column"
                        :text="text"
                        :record="record"
                        :index="index"
                    />
                </template>
                <template #bodyCell="{ column, text, record, index }">
                    <slot
                        name="bodyCell"
                        :column="column"
                        :text="text"
                        :record="record"
                        :index="index"
                    />
                </template>
                <template
                    v-if="isExpandedRow"
                    #expandedRowRender="{ column, text, record, index }"
                >
                    <slot
                        name="expandedRowRender"
                        :column="column"
                        :text="text"
                        :record="record"
                        :index="index"
                    />
                </template>
                <template #emptyText>
                    <slot name="emptyText">
                        <a-empty
                            image="image/empty.png"
                            :image-style="{ width: '100%', height: '180px' }"
                        />
                    </slot>
                </template>
            </a-table>
        </a-spin>
    </div>
</template>

<script lang="ts">
import {
    computed,
    defineComponent,
    reactive,
    toRefs,
    ref,
    watch,
    watchEffect,
} from "vue";
export default defineComponent({
    props: {
        totals: {
            type: Object,
            default: () => {
                return {
                    total: 0,
                    pageNo: 1,
                    pageSize: 10,
                };
            },
        },
        rowKeyId: {
            type: String,
            default: "id",
        },
        dataSource: {
            type: Array,
            default: () => [],
        },
        columns: {
            type: Array,
            default: () => [],
        },
        // slots: {
        //     type: Array,
        //     default: () => []
        // },
        // 选择框
        rowSelection: {
            type: Boolean,
            default: true,
        },
        rowSelectionOptions: {
            type: Object,
            default: () => {},
        },
        isExpandedRow: {
            type: Boolean,
            default: false,
        },

        isScroll: {
            type: Boolean,
            default: false,
        },
        isSrollY: {
            type: Boolean,
            default: false,
        },
        isSrollYH: {
            type: Number,
            default: 240,
        },
        isSrollXW: {
            type: Number,
            default: 0,
        },

        spinLoad: {
            type: Boolean,
            default: false,
        },
        isRadio: {
            type: Boolean,
            default: false,
        },
        // checkType: {
        //     type: String,
        //     default: 'checkbox'
        // },
        isBordered: {
            type: Boolean,
            default: false,
        },
        selectedRowKey: {
            type: Array,
            default: () => [],
        },
    },
    emits: ["onSelectedRowKeys", "onSelectedArry"],
    setup(props, { emit }) {
        // const { proxy } = getCurrentInstance() as any
        // const store = useStore()
        // const route = useRoute()
        // const router = useRouter()
        const clientWidth = ref<string>(`${document.body.clientWidth}px`);

        const pagination = ref<any>({
            hideOnSinglePage: true,
            total: 0,
            current: 1,
            showQuickJumper: true,
            showLessItems: true,
            showSizeChanger: true,
            pageSizeOptions: ["10", "20", "30", "40", "100"],
            showTotal: (total: any, range: any) => `共 ${total} 条`,
        });

        const state = reactive({
            selectedRowKeys: [],
            loading: false,
        });

        // 可选择
        const onSelectChange = (selectedRowKeys: any, data: any) => {
            state.selectedRowKeys = selectedRowKeys;
            emit("onSelectedArry", selectedRowKeys, data);
        };

        const handleTableChange = (
            res: any,
            data: any,
            filters: any,
            sorter: any
        ) => {
            for (const i in data) res[i] = data[i];
            emit("onSelectedRowKeys", res, filters, sorter);
        };
        const handleSearch = (
            selectedKeys: any,
            confirm: any,
            dataIndex: any
        ) => {
            confirm();
            state.searchText = selectedKeys[0];
            state.searchedColumn = dataIndex;
        };
        const hasSelected = computed(() => state.selectedRowKeys.length > 0);
        watchEffect(() => {
            pagination.value = {
                ...pagination.value,
                ...props.totals,
                current: props.totals.pageNo,
                hideOnSinglePage: props.totals.total < 11,
            };
            // if (props.spinLoad) {
            //     state.selectedRowKeys = []
            //     emit('onSelectedArry', [])
            // }
        });

        watch(
            () => props.dataSource,
            (val: any, olVal: any) => {
                state.selectedRowKeys = [];
                emit("onSelectedArry", []);
            }
        );

        watch(
            () => props.selectedRowKey,
            (val: any, olVal: any) => {
                state.selectedRowKeys = toRaw(val);
            },{
                deep: true,
                immediate: true
            }
        );

        const sider_two_level = computed(() =>
            document.getElementsByClassName("sider_two_level")
        );

        return {
            sider_two_level,
            pagination,
            clientWidth,
            hasSelected,
            handleSearch,
            onSelectChange,
            ...toRefs(state),
            handleTableChange,
        };
    },
});
</script>

<style lang="less">
.table_wrapper {
    .table_width {
        width: calc(100vw - 324px);
    }

    tr.ant-table-row-selected > td {
        background: #fafafa !important;
    }

    .table_info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px 0;

        .table_title {
            font-weight: 500;
            font-size: 18px;
            color: #000;
        }
    }

    .table_hander {
        .marl10 {
            margin-left: 16px;
        }
    }

    .antTableCellActive .ant-table-cell {
        border: none;
    }

    .pathColor {
        color: #00b781;
        width: 16px;
        height: 16px;
        position: absolute;
        top: 16px;
        right: 10px;
        font-size: 16px;
    }

    .table-spin {
        position: absolute;
        top: 50%;
        left: 50%;
    }

    .ant-table-expanded-row-fixed {
        width: 100% !important;
    }
}
</style>
