<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-07-08 14:12:47
 * @LastEditors: 舒志建 <EMAIL>
 * @LastEditTime: 2023-07-13 13:57:16
-->
<template>
    <div class="census_box">
        <h2>当前考勤状态统计</h2>
        <div class="census">
            <i class="census_triangle"></i>
            <div class="census_item" style=" color: #00b781" @click="emit('emitFormQuery', item.value)" :key="idex">
                总人次 ：{{ list['total'] }}人
            </div>
            <div class="census_item" v-for="(item, idex) in statusList"
                :style="{ color: state.censusObj[item.value].color }" @click="emit('emitFormQuery', item.value)"
                :key="idex">
                {{ item.label }}：{{ list[state.censusObj[item.value].key] }}人
            </div>
        </div>
    </div>
</template>
<script setup>
import { reactive } from "vue"
const state = reactive({
    censusObj: {
        0: { key: 'normalNum', color: '#00b781' },
        1: { key: 'absenteeismNum', color: '#f5222d' },
        2: { key: 'beLateNum', color: '#fc941f' },
        3: { key: 'leaveEarlyNum', color: '#1ec1c3' },
        5: { key: 'leaveNum', color: '#717171' },
        6: { key: 'notSignInNum', color: '#595959' },
    },
})
const emit = defineEmits(['emitFormQuery'])
defineProps({
    list: {
        type: Object,
        default: () => { }
    },
    statusList: {
        type: Array,
        default: () => []
    }
})
</script>

<style lang="less" scoped>
.census_box {
    margin: 0px 0px 24px 0px;

    h2 {
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 25px;
    }

    .census {
        margin-top: 10px;
        // width: 100%;
        height: 72px;
        background: #ffffff;
        box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014,
            0 9px 28px 8px #0000000d;
        position: relative;
        justify-content: space-around;
        display: flex;
        align-items: center;

        .census_triangle {
            height: 15px;
            width: 15px;
            position: absolute;
            top: -10px;
            left: 20px;
            box-shadow: -2px -2px 5px #0000000f;
            transform: translateY(4.24264069px) rotate(45deg);
            background: #ffffff;
        }

        .census_item {
            // width: 325px;
            // border-right: 1px solid #ccc;
            cursor: pointer;
            font-size: 14px;
            text-align: center;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            line-height: 20px;
            flex: 1;

            &:not(:last-child) {
                border-right: 1px solid #ccc;
            }
        }
    }
}
</style>
