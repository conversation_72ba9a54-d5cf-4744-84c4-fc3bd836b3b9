<template>
    <a-modal class="mSelect" v-model:visible="modelState.openVisible" :destroyOnClose="true" :maskClosable="false"
        title="选择" width="850px" :bodyStyle="{ padding: '0' }" @cancel="onCancel" @ok="onOk">
        <div class="mSelect-wrap" v-if="modelState.openVisible">
            <div class="section" :class="{ active: !isShowSearch }">
                <!-- 搜索 -->
                <a-input-search v-if="isShowSearch" v-model:value.trim="state.name" placeholder="请输入搜索的内容"
                    @search="handleSearch" allow-clear />
                <div style="margin:20px" v-if="state.isSearchTable" :class="{ 'single-table': activeTab.single }">
                    <a-table rowKey="id" :scroll="{ y: 370 }" :pagination="pagination"
                        :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
                        :columns="columns" :data-source="modelState.searchTable">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.dataIndex === 'detClass'">
                                <Tooltip :title="tipTitle(record)"></Tooltip>
                            </template>
                            <template v-else-if="column.dataIndex === 'name'">
                                {{ record.name }}
                            </template>
                        </template>
                    </a-table>
                    <a-pagination class="ydpagination" :hideOnSinglePage="true" v-model:current="modelState.pages.pageNo"
                        v-model:pageSize="modelState.pages.pageSize" show-size-changer :total="modelState.pages.total"
                        @change="paginationChange" />
                </div>
                <div v-else class="select-wrap">
                    <!-- 分类 -->
                    <div class="tabs">
                        <a-radio-group v-model:value="activeTabIndex" button-style="solid" @change="onTabsChange">
                            <a-radio-button v-for="(item, index) in props.tabs" :key="item.id" :value="index">
                                {{ item.tab }}
                            </a-radio-button>
                        </a-radio-group>
                    </div>
                    <!-- 面包屑 -->
                    <a-breadcrumb>
                        <template #separator>
                            <a-avatar shape="square" :src="arrow" :size="20"></a-avatar>
                        </template>
                        <template v-for="(item, index) in state.breadcrumbs" :key="item[fieldNames.value]">
                            <a-breadcrumb-item href="" @click="handleBreadcrumb(item, index)">
                                {{ item[fieldNames.label] }}
                            </a-breadcrumb-item>
                        </template>
                    </a-breadcrumb>
                    <!-- 源数据 -->
                    <div class="structures" @scroll="handleScroll($event)">
                        <a-spin :spinning="modelState.spinning">
                            <template v-if="modelState.dataSource?.length">
                                <div class="row" mt-16 v-if="!activeTab.single">
                                    <a-checkbox :disabled="modelState.isaAuthority" class="check" :checked="checkAll"
                                        @change="onCheckAllChange">
                                        全选
                                    </a-checkbox>
                                </div>
                                <component :value="originCheckedList" style="width: 100%"
                                    :is="activeTab.single ? ARadio.Group : ACheckbox.Group">
                                    <div class="row tree" v-for="item in modelState.dataSource" :key="item.id">
                                        <component class="check" :class="{
                                            'check-visible': !isCheckVisible(item),
                                        }" :value="item[fieldNames.value]" :is="activeTab.single ? ARadio : ACheckbox"
                                            :disabled="item.disabled || modelState.isaAuthority" name="check"
                                            @change="onCheckChange($event, item)">
                                            <a-avatar shape="square" :src="(!isPerson(item) && structure) || ''" :size="36">
                                                <span v-if="isPerson(item)">
                                                    {{ item.studentName?.slice(-2) || item.name?.slice(-2) }}
                                                </span>
                                            </a-avatar>
                                            <div class="cnt">
                                                <div class="label ellipsis" :title="item[fieldNames.label]">
                                                    {{ item[fieldNames.label] }}
                                                </div>
                                            </div>
                                        </component>
                                        <div class="more" v-if="isShowMore(item)">
                                            <span @click="handleMore($event, item)">下级</span>
                                        </div>
                                    </div>
                                </component>
                            </template>
                            <div v-else class="data-empty">
                                <img src="/image/venues.png" />
                                <p>暂时没有找到相关结果</p>
                            </div>
                        </a-spin>
                    </div>
                </div>
            </div>
            <div class="section section-selected">
                <div class="selected-hd">
                    <div class="label">
                        已选
                        <span class="count">{{ targetCheckedList?.length || 0 }}{{ unit }}</span>
                    </div>
                    <div class="btn-clear" v-if="targetCheckedList.length" @click="handleClear">清空</div>
                </div>
                <div class="selected-bd">
                    <!-- 已选择数据 -->
                    <div class="selected-list" v-if="targetCheckedList?.length">
                        <div class="selected-item" v-for="(item, index) in targetCheckedList" :key="item[fieldNames.value]">
                            <a-avatar shape="square" :src="(!isPerson(item) && structure) || ''" :size="24">
                                <span v-if="isPerson(item)">
                                    {{ item.studentName?.slice(-2) || item.name?.slice(-2) }}
                                </span>
                            </a-avatar>
                            <div class="cnt">
                                <div class="label ellipsis" :title="item[fieldNames.label]">
                                    {{ item[fieldNames.label] }}
                                </div>
                            </div>

                            <div class="icon-del" @click="handleDelete(index)"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </a-modal>
</template>

<script setup>
// ------------- 在调用的页面设置 -------------
// const modelState = reactive({
//   openVisible: false, // 显示弹框
//   dataSource: [],// 左侧数据源
//   selectedData: [] // 已选中的数据
//   dept是否选部门、people是否人员、class班级(只能选择最后一个),all部门和人都可以选择
//   checkVisible: 'dept',
//   disableSelect: [], // 禁止选人的id
// })
// provide("modelState", () => modelState)
// provide('callbackFunction', () => ({
//     search, //搜索
//     toggleLevel, // 切换下一级或者面包屑
//     toggleTabs,  // 切换tabs
//     cancel,  // 取消
//     submit,  // 确定
// }))

//  ********** tabs定义 ***********
// [
//     {
//         tab: 'tab名称',   // tab名称
//         checked: true,    // 当前tab为聚焦状态(多个tab时一个选中，其余不选中)
//         id: TAB_ID_MAP.STUDENT,   // tab的唯一标识，可以自由定义
//         $ 用于区分是 人或者班级|宿舍 还是其他 （老接口，后端数据接口没有区分类型的判断）新版本接口使用personField
//         $ 推荐：使用personField
//         legacy_personKey: 'userId',
//         // $ 用于区分是 人或者班级|宿舍 还是其他 （新接口，后端数据接口带有区分类型的判断）
//         personField: { key: 'typeValue', value: ['student'] },
//         // 复选
//         single: true,  // true为单选  false为复选
//         // 搜索框配置
//         searchOption: {
//             show: true, // 是否展示搜索框 (默认为false)
//             displayMode: 'old',  // 展示的模式，'old'代表旧的， 'new'代表新的(table的形式展示 - 默认) DISPLAY_MODE
//         },
//     },
//     .....
// ]
//  ********** tabs定义 ***********

// ------------- 在调用的页面设置 -------------

import { watch } from 'vue'
import structure from '/image/icon-structure.png'
import arrow from '/image/icon-arrow.png'
import { Checkbox as ACheckbox, Radio as ARadio } from 'ant-design-vue'
// import YTable from "@/components/YTable/index.vue";

const modelState = inject('modelState')()
const callbackFunction = inject('callbackFunction')()
const pagination = ref({
    total: 0,
    current: 1,
    showQuickJumper: true,
    showLessItems: true,
    showSizeChanger: true,
    pageSizeOptions: ["10", "20", "30", "40", "100"],
    showTotal: (total, range) => `共 ${total} 条`,
});
const props = defineProps({
    // 用法看上面
    tabs: {
        type: Array,
        default: () => [],
    },
    // 已选择
    selected: {
        type: Array,
        default: () => [],
    },
    fieldNames: {
        type: Object,
        default: () => {
            return { label: 'name', value: 'id' }
        },
    },
})

// 选人组件类型
const SELECT_TYPE = {
    // 部门
    DEPT: 'dept',
    // 人
    PEOPLE: 'people',
    // 班级 （只选择最后一个）
    CLASS: 'class',
    // 所有
    ALL: 'all',
}

// 搜索组件展现模式
const DISPLAY_MODE = {
    NEW: 'new',
    OLD: 'old',
}

// 触发toggleLevel标识
const TRIGGER_MAP = {
    BREAD_CRUMBS: 'breadCrumbs',
    NEXT_MORE: 'nextMore',
}

const columns = [
    {
        title: '姓名',
        dataIndex: 'name',
    },
    {
        title: '部门/班级',
        dataIndex: 'detClass',
    },
]

const state = reactive({
    oldTargetCheckedList: [],
    selectedRowKeys: [],
    isSearchTable: false,
    name: '',
    spinning: false,
    // 面包屑
    breadcrumbs: [
        {
            name: props.tabs[0]?.tab,
            id: 0,
            children: [],
        },
    ],
    // 源数据
    dataSource: [],
})
// 滚动加载
const handleScroll = (event) => {
    // 滚动区域的高度 - 滚动条到顶部高度
    const isScroll = (event.target.scrollHeight - event.target.scrollTop) < 400
    if (modelState.isPpresent && isScroll) {
        modelState.isPpresent = false
        modelState.pages.pageNo++
        callbackFunction.onScroll()
    }
}
// 判断两数组字符串是否相同 数组字符串中存在相同时会自动去重 
function removeDuplicate(arr, attr) {
    if (attr) {
        const obj = {};
        return arr.reduce((cur, item) => {
            obj[item[attr]] ? '' : (obj[item[attr]] = true && item[attr] && cur.push(item));
            return cur;
        }, []);
    } else {
        return [...new Set(arr)];
    }
}
// 可选择
// 选择的表格数据
const onSelectChange = (ron, node) => {
    const { id: _type, single } = activeTab.value
    const ids = modelState.searchTable.map(item => item.id)
    const reservedList = targetCheckedList.value?.filter(item => !ids.includes(item.id)) || []
    const reservedKeys = reservedList.map(item => item.id)
    state.selectedRowKeys = [...reservedKeys, ...ron]
    const selectNode = node?.map(v => ({ ...v, _type })) || []
    targetCheckedList.value = [...reservedList, ...selectNode]
}

const tipTitle = computed(() => {
    return item => item.deptName || item.deptString || item.className || item.pName || '--'
})
// 查询人员分页
const paginationChange = (current, pageSize) => {
    modelState.pages.pageNo = current
    modelState.pages.pageSize = pageSize
    callbackFunction.search(activeTab.value.id, state.name)
}
// 当前选人业务
const activeTabIndex = ref(props.tabs.findIndex(item => item.checked) || 0)
const activeTab = computed(() => {
    return props.tabs[activeTabIndex.value]
})

// 判断是人还是其他 （有一个tab符合就显示人。 后续可增加条件拓展）
const legacy_personKeys = props.tabs.map(tab => tab.legacy_personKey).filter(Boolean)
const isPerson = computed(() => {
    return item => {
        if ([SELECT_TYPE.PEOPLE, SELECT_TYPE.ALL].includes(modelState.checkVisible)) {
            // $ tab有一个符合就算是人（切换tab,保持头像必须不变）
            const isNewRule = props.tabs.some(tab => !!tab.personField?.key)
            if (isNewRule) {
                return props.tabs.some(tab => tab.personField.value.includes(item[tab.personField?.key]))
            } else {
                let keys = legacy_personKeys.length ? legacy_personKeys : ['userId']
                return keys.find(key => item.hasOwnProperty(key))
            }
        }
        return false
    }
})

const isCheckVisible = computed(() => {
    return item => {
        if (modelState.checkVisible === SELECT_TYPE.DEPT) {
            // 选部门
            return true
        } else if (modelState.checkVisible === SELECT_TYPE.PEOPLE) {
            // 选人
            // $ 使用新规则还是旧规则
            const isNewRule = !!activeTab.value.personField?.key
            const value = activeTab.value.personField?.value || []
            const isPeople = value.includes(item[activeTab.value.personField?.key])
            return (!isNewRule && item.hasOwnProperty(activeTab.value.legacy_personKey)) || (isNewRule && isPeople)
        } else if (modelState.checkVisible === SELECT_TYPE.CLASS) {
            // 班级、宿舍
            const value = activeTab.value.personField?.value || []
            const isClass = value.includes(item[activeTab.value.personField?.key])
            return isClass
        } else if (modelState.checkVisible === SELECT_TYPE.ALL) {
            // 选人和选部门
            return true
        }
    }
})

// 单位
const unit = computed(() => {
    if (modelState.checkVisible === SELECT_TYPE.DEPT) {
        // 选部门
        return '部门'
    } else if (modelState.checkVisible === SELECT_TYPE.PEOPLE) {
        return '人'
    } else if (modelState.checkVisible === SELECT_TYPE.CLASS) {
        // 班级、宿舍
        return '项'
    } else if (modelState.checkVisible === SELECT_TYPE.ALL) {
        // 选人和选部门
        return '项'
    }
})

// 右侧选中
const targetCheckedList = ref([])

// watch 监听弹窗的开关 赋值targetCheckedList
// 如果modelState.openVisible的变化比props.selected的变化先发生那么watch的回调函数会先执行
// 此时targetCheckedList.value的值还是旧值，是不是
// 如果想 想确保watch的回调函数在props.selected变化后执行 watch就监听数组 2个参数
// 可以将props.selected也作为watch的第一个参数，这样watch就会先监听props.selected的变化，然后再监听modelState.openVisible的变化


// watchEffect 也可以

// watch(
//     () => props.selected,modelState.openVisible,
//     val => {
//         targetCheckedList.value = JSON.parse(JSON.stringify(props.selected))
//     }
// )

// watchEffect(() => {
//     targetCheckedList.value = JSON.parse(JSON.stringify(props.selected))
// })


watch(
    [() => modelState.openVisible, () => props.selected],
    ([openVisible, selected], [prevOpenVisible, prevSelected]) => {
        targetCheckedList.value = JSON.parse(JSON.stringify(selected))
    }
)


// 搜索
const handleSearch = () => {
    // 如果在请求数据时 则不让你点击
    if (modelState.spinning) return
    // $ 默认使用新的搜索展示框，如果指定旧的就用旧的
    const { searchOption = { displayMode: DISPLAY_MODE.NEW } } = activeTab.value
    const isOld = searchOption.displayMode === DISPLAY_MODE.OLD
    state.isSearchTable = !isOld && !!state.name
    // 旧的展示方式
    if (searchOption.displayMode === DISPLAY_MODE.OLD) {
        // 删除面包屑
        state.breadcrumbs.splice(1)
    }
    const tabId = activeTab.value.id
    callbackFunction.search(tabId, state.name)
    // 需要把选中的数据同步给table的select
    state.selectedRowKeys = targetCheckedList.value?.map(item => item.id)
}

// 楼层
let isShowSearch = computed(() => {
    const { legacy_personKey = '', searchOption = {} } = activeTab.value
    // $ 第一个条件是遗留问题
    return ['userId', 'studentCode'].includes(legacy_personKey) || !!searchOption?.show
})

// 面包屑点击
const handleBreadcrumb = (row = {}, index = 0) => {
    if (modelState.spinning) return
    state.breadcrumbs.splice(index + 1)
    state.name = ''
    const tabId = activeTab.value.id
    const options = {
        index,
        // 触发标识
        trigger: TRIGGER_MAP.BREAD_CRUMBS,
    }
    callbackFunction.toggleLevel(tabId, toRaw(row), options)
}

// tab切换
const onTabsChange = () => {
    state.name = ''
    // 初始化面包屑
    state.breadcrumbs = [
        {
            name: activeTab.value?.tab,
            id: activeTab.value?.id,
        },
    ]
    callbackFunction.toggleTabs(activeTab.value)
}
// 下级
const handleMore = (e, row) => {
    e.preventDefault()
    state.name = ''
    const isChecked = state.breadcrumbs.some(v => v.id === row.id)
    !isChecked && state.breadcrumbs.push(row)
    const tabId = activeTab.value.id
    const options = {
        index: state.breadcrumbs.length - 1,
        // 触发标识
        trigger: TRIGGER_MAP.NEXT_MORE,
    }
    callbackFunction.toggleLevel(tabId, toRaw(row), options)
}

// 左侧选中
const originCheckedList = computed(() => {
    return activeTab.value.single ? targetCheckedList.value[0]?.id : targetCheckedList.value.map(item => item.id)
})

// 全选
const checkAll = computed(() => {
    if (activeTab.value.single) {
        return false
    }
    let selectableList = []
    if (modelState.checkVisible === SELECT_TYPE.DEPT) {
        selectableList = modelState.dataSource
    } else if (modelState.checkVisible === SELECT_TYPE.PEOPLE) {
        const isNewRule = !!activeTab.value.personField?.key
        const value = activeTab.value.personField?.value || []
        // 获取到人的列表
        selectableList = modelState.dataSource.filter(
            item =>
                (!isNewRule && item.hasOwnProperty(activeTab.value.legacy_personKey)) ||
                (isNewRule && value.includes(item[activeTab.value.personField?.key])),
        )
    } else if (modelState.checkVisible === SELECT_TYPE.CLASS) {
        // 班级|宿舍
        const value = activeTab.value.personField?.value || []
        selectableList = modelState.dataSource.filter(item => value.includes(item[activeTab.value.personField.key]))
    } else if (modelState.checkVisible === SELECT_TYPE.ALL) {
        selectableList = modelState.dataSource
    }

    return !!selectableList.length && selectableList.every(item => originCheckedList.value.indexOf(item.id) > -1)
})

// 是否展示下一级
const isShowMore = computed(() => {
    return item => {
        if (modelState.checkVisible === SELECT_TYPE.PEOPLE) {
            // 人
            const isNewRule = !!activeTab.value.personField?.key
            const value = activeTab.value.personField?.value || []
            const isPeople = value.includes(item[activeTab.value.personField?.key])
            if ((!isNewRule && !item.hasOwnProperty(activeTab.value.legacy_personKey)) || (isNewRule && !isPeople)) {
                return true
            } else {
                return false
            }
        } else if ([SELECT_TYPE.CLASS, SELECT_TYPE.ALL].includes(modelState.checkVisible)) {

            // 班级|宿舍
            const value = activeTab.value.personField?.value || []
            if (
                value.includes('classesOrgrade')
            ) {
                return true
            } else {
                const isPerson = value.includes(item[activeTab.value.personField?.key])
                return !isPerson
            }
        }
        return item.children?.length
    }
})

// 全选事件
const onCheckAllChange = e => {

    let selectableList = []
    if (modelState.checkVisible === SELECT_TYPE.DEPT) {
        selectableList = modelState.dataSource
    } else if (modelState.checkVisible === SELECT_TYPE.ALL) {
        selectableList = modelState.dataSource
    } else if (modelState.checkVisible === SELECT_TYPE.PEOPLE) {
        // $ 使用新规则还是旧规则
        const isNewRule = !!activeTab.value.personField?.key
        const value = activeTab.value.personField?.value || []

        selectableList = modelState.dataSource.filter(item => {
            return (
                (!isNewRule && item.hasOwnProperty(activeTab.value.legacy_personKey)) ||
                (isNewRule && value.includes(item[activeTab.value.personField?.key]))
            )
        })
    } else if (modelState.checkVisible === SELECT_TYPE.CLASS) {
        const value = activeTab.value.personField?.value || []
        selectableList = modelState.dataSource.filter(item => value.includes(item[activeTab.value.personField?.key]))
    }

    //  $ 用于区分tab,记录类型
    const _type = activeTab.value.id

    if (e.target.checked) {
        const selected = targetCheckedList.value?.map(item => item[props.fieldNames.value])
        selectableList.forEach(item => {
            if (!selected.includes(item[props.fieldNames.value])) {
                targetCheckedList.value.push({ ...item, _type })
            }
        })
    } else {
        const ids = new Set(selectableList.map(item => item[props.fieldNames.value]))
        // 过滤在ids存在的
        targetCheckedList.value = targetCheckedList.value.filter(item => !ids.has(item[props.fieldNames.value]))
    }
}

// 单选
const onCheckChange = (e, row) => {
    //  $ 用于区分tab,记录类型
    const _type = activeTab.value.id
    if (activeTab.value.single) {
        // 单选
        targetCheckedList.value = e.target.checked ? [{ ...row, _type }] : []
    } else {
        // 复选
        if (e.target.checked) {
            targetCheckedList.value.push({ ...row, _type })
        } else {
            const index = targetCheckedList.value.findIndex(item => item[props.fieldNames.value] == row[props.fieldNames.value])
            ~index && targetCheckedList.value.splice(index, 1)
        }
    }
}

// 清空
const handleClear = () => {
    targetCheckedList.value = []
    state.selectedRowKeys = []
}

// 删除
const handleDelete = (index = 0) => {
    targetCheckedList.value?.splice(index, 1)
    state.selectedRowKeys.splice(index, 1)
}

// 取消
const onCancel = () => {
    state.name = ''
    state.isSearchTable = false
    handleClear()
    modelState.openVisible = false
    // $ 恢复第一层数据。新的接口是每一层都会请求的. 每次重新打开 以新状态展示
    // 恢复面包屑
    state.breadcrumbs = [state.breadcrumbs[0]]
    // 重置恢复选中的tab
    activeTabIndex.value = props.tabs.findIndex(item => item.checked) || 0
    callbackFunction.cancel?.()
}

// 确认
const onOk = () => {
    const newTargetChecked = JSON.parse(JSON.stringify(targetCheckedList.value))
    callbackFunction.submit(newTargetChecked)
    onCancel()
}

watch(
    () => props.tabs,
    val => {
        state.breadcrumbs[0].name = val[0].tab
    },
)
</script>

<style lang="less" scoped src="./index.less"></style>
