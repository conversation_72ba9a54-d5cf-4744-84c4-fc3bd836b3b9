<template>
    <div :style="state.announceShow ? { height: '46px' } : {}">
        <a-alert
            class="alert"
            type="warning"
            closable
            @close="onClose"
            v-if="state.announceShow"
        >
            <template #message>
                <div class="notice" v-if="state.announceDatas.length">
                    <div class="notice_content">
                        <div
                            class="alert_title notice_item"
                            v-for="(item, idx) in state.announceDatas"
                            :key="idx"
                        >
                            <span v-if="item.isEnterSchool">
                                {{ item.title }}
                                <a-button
                                    type="primary"
                                    size="small"
                                    @click="handlerAcademicRecords"
                                >
                                    {{ item.announcementContent }}
                                </a-button>
                            </span>
                            <span
                                v-else
                                class="scrollable-content"
                                :style="
                                    state.isScrollable
                                        ? {
                                              animationDuration: `${state.number}s`,
                                          }
                                        : {}
                                "
                            >
                                {{ item.title }}：
                                {{ item.announcementContent }}
                            </span>
                        </div>
                    </div>
                </div>
                <div v-else class="alert_title">
                    <span
                        id="alertcontent"
                        class="scrollable-content"
                        :style="
                            state.isScrollable
                                ? {
                                      animationDuration: `${state.number}s`,
                                  }
                                : {}
                        "
                    >
                        {{ state.announceData.title }}：
                        {{ state.announceData.announcementContent }}
                    </span>
                </div>
            </template>
        </a-alert>
        <a-modal
            v-model:visible="state.openModal"
            :footer="false"
            class="modal-style"
            :width="500"
            @cancel="onClose"
        >
            <div class="announce_class">
                <div class="bg"></div>
                <div class="box">
                    <div class="head">
                        <div class="title">{{ state.announceData.title }}</div>
                    </div>
                    <div class="content">
                        {{ state.announceData.announcementContent }}
                    </div>
                </div>
            </div>
        </a-modal>
    </div>
</template>
<script setup>
import { nextTick } from "vue";
import { Local } from "@/utils/storage";
import { useStore } from "vuex";
import { getEnableAnnouncement } from "@/api/user";
// import { locale } from "dayjs";
import { useRouter } from "vue-router";
const router = useRouter();
const store = useStore();
const emit = defineEmits(["editShow"]);

const state = reactive({
    announceData: {},
    announceDatas: [],
    number: 0,
    isScrollable: false,
    announceShow: false,
    openModal: false,
    time: null,
    numTop: 0,
    noticeContentTop: 0,
    noticeContentLen: 1,
});
const onClose = () => {
    Local.set("isShowAnnounce", false);
    Local.set("announceId", state.announceData.id);
    state.announceShow = false;
    state.openModal = false;
    clearInterval(state.time);
};

function checkAndApplyScroll() {
    const contentEl = document.getElementById("alertcontent");
    state.number = (contentEl.offsetWidth / window.innerWidth) * 18;
    if (contentEl.offsetWidth > window.innerWidth) {
        state.isScrollable = true;
    } else {
        state.isScrollable = false;
    }
}

function handlerAcademicRecords() {
    router.push({
        path: "/framework/schoolRoll",
        query: { isAcademicRecords: true },
    });
}

const tip = () => {
    state.announceDatas = [
        {
            // 这不要删
            isEnterSchool: true,
            title: "又到一年一度的升学时间啦，请确认是否升级",
            announcementContent: "立即升级",
        },
    ];
    if (state.announceData.id) {
        state.announceDatas = [...state.announceDatas, state.announceData];
    } else {
        state.announceShow = true;
    }
};
watch(
    () => state.announceData,
    (val) => {
        if (val) {
            const isShowAnnounce = Local.get("isShowAnnounce");
            const announceId = Local.get("announceId");
            if (val.id == announceId) {
                state.openModal = false;
                state.announceShow = false;
                if (isShowAnnounce == null) {
                    if (val.formType == 1) {
                        state.announceShow = true;
                        state.openModal = false;
                        nextTick(() => {
                            checkAndApplyScroll();
                        });
                    } else if (val.formType == 2) {
                        state.announceShow = false;
                        state.openModal = true;
                    } else {
                        state.openModal = false;
                        state.announceShow = false;
                    }
                }
            } else {
                if (val.formType == 1) {
                    state.announceShow = true;
                    state.openModal = false;
                    nextTick(() => {
                        checkAndApplyScroll();
                    });
                } else if (val.formType == 2) {
                    state.announceShow = false;
                    state.openModal = true;
                } else {
                    state.openModal = false;
                    state.announceShow = false;
                }
            }
        }
        // 先隐藏 后面要开启 不要删
        // if (checkUpgrade.value) {
        //     tip();
        // }
    }
);

async function getEnableByTypeFn() {
    //announceType 公告对象
    //  1.云平台, 2. IOS, 3.Android, 4. H5 ,5.一德后台

    await getEnableAnnouncement({ announceType: 1 }).then(({ data }) => {
        if (data?.title) {
            const { title, announcementContent, formType, id } = data;
            state.announceData = { title, announcementContent, formType, id };
        }
    });
}
// 判断是否升学
const checkUpgrade = computed(() => {
    return false;
    // return  store.state.user.checkUpgrade;
});

watch(
    () => checkUpgrade.value,
    (val) => {
        if (val) {
            tip();
        } else {
            state.announceDatas = [];
            clearInterval(state.time);
            if (!state.announceData.title) {
                state.openModal = false;
                state.announceShow = false;
            }
        }
    },
    {
        deep: true,
        immediate: true,
    }
);
watch(
    () => state.announceShow,
    (value) => {
        store.commit("base/setIsAnnounceTop", value);
    },
    {
        deep: true,
        immediate: true,
    }
);

// 监听是否升学
state.time = setInterval(() => {
    if (state.noticeContentLen < state.announceDatas.length) {
        state.noticeContentLen++;
        state.noticeContentTop = `${state.numTop - 30}px`;
    } else {
        state.noticeContentTop = 0;
        state.numTop = 0;
        state.noticeContentLen = 1;
    }
}, 5000);
onUnmounted(() => {
    clearInterval(state.time);
});
onMounted(async () => {
    await getEnableByTypeFn();
    setInterval(() => {
        getEnableByTypeFn();
    }, 8000);

    // await handlerGetUpgradeCheckUpgrade();
    store.dispatch("user/UpgradeCheckUpgrade");
});
</script>

<style lang="less" scoped>
.alert {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    z-index: 1000;
}
.alert_title {
    overflow: hidden;
    max-width: 94vw;
    white-space: nowrap;
    text-align: center;
}
.scrollable-content {
    overflow-x: hidden; /* 隐藏默认滚动条 */
    white-space: nowrap;
    display: inline-block;
    white-space: nowrap;
    animation-name: scroll-left;
    animation-duration: 0s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
}

@keyframes scroll-left {
    0% {
        transform: translateX(0%);
    }
    100% {
        transform: translateX(-100%);
    }
}
.modal-style {
    :deep(.ant-modal-header) {
        display: flex !important;
    }
}

.announce_class {
    height: 350px;
    width: 500px;
    background: url("/image/announceBg.png") no-repeat;
    background-size: contain;
    position: relative;
    .bg {
        position: absolute;
        right: 0px;
        top: 17px;
        background: url("/image/announceBg2.png") no-repeat;
        background-size: contain;
        width: 142px;
        height: 85px;
    }
    .box {
        position: absolute;
        left: 0px;
        top: 0px;
        padding: 16px 24px;
        width: 100%;
        .head {
            width: calc(100% - 20px);
            display: flex;
            min-height: 20px;
            justify-content: space-between;
            .title {
                width: 100%;
                text-align: center;
                font-weight: 600;
                font-size: 16px;
                color: rgba(0, 0, 0, 0.85);
                line-height: 22px;
                white-space: nowrap; /* 确保文本不换行 */
                overflow: hidden; /* 超出容器部分隐藏 */
                text-overflow: ellipsis;
            }
        }
        .content {
            margin-top: 10px;
            min-height: 240px;
            max-height: 240px;
            overflow: auto;
            background: #fff;
            padding: 14px 20px;
            font-weight: 400;
            font-size: 12px;
            color: #262626;
            line-height: 24px;
            box-shadow: inset 0px 1px 0px 0px rgba(255, 255, 255, 0.5);
            border-radius: 4px;
        }
    }
}
.notice {
    position: relative;
    overflow: hidden;
    height: 30px;
    line-height: 30px;
    // transition: transform 0.1s;
    // transform: rotateY(0deg);

    .notice_content {
        transition: transform 0.1s;
        transform: rotateY(0deg);
        position: absolute;
        left: 0;
        right: 0;
        top: v-bind("state.noticeContentTop");
        &.active {
            animation-name: scroll-top;
            animation-duration: 1s;
            animation-timing-function: linear;
            animation-iteration-count: infinite;
        }
    }
}
@keyframes scroll-top {
    0% {
        transform: rotateX(0deg);
    }

    50% {
        transform: rotateX(90deg);
    }

    100% {
        transform: rotateX(0deg);
    }
}
</style>
<style lang="less">
.modal-style {
    .ant-modal-body {
        padding: 0 !important;
    }
}
</style>
