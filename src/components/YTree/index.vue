<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-02-28 15:21:05
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-04-15 11:26:08
-->
<template>
    <a-tree
        class="y_tree"
        v-bind="$attrs"
        blockNode
        :selectable="true"
        :tree-data="treeData"
        :fieldNames="fieldNames"
    >
        <template #title="{ dataRef }">
            <slot name="title" :dataRef="dataRef">
                <i class="iconfont icon-yellow icon-a-Fill21" />
                {{ dataRef[fieldNames.title] || dataRef.name }}
            </slot>
            <slot name="handleItem" :handleItem="dataRef"></slot>
        </template>
    </a-tree>
    <slot name="handleFooter"></slot>
</template>
<script setup>
defineProps({
    treeData: {
        type: Array,
        default: () => [],
    },
    fieldNames: {
        type: Object,
        default: () => {
            return {
                children: "children",
                title: "showName",
                key: "id",
            };
        },
    },
});
</script>
<style lang="less">
.y_tree {
    // padding: 24px 16px;
    // border-right: 1px solid #d9d9d9;
    box-sizing: border-box;
    width: 100%;
    overflow: hidden;
    box-sizing: border-box;
    .handle_icon {
        float: right;
        transform: rotate(180deg);
        color: rgba(0, 0, 0, 0.65);
    }
}
.ant-tree-block-node {
    // margin-bottom: 10px;
}
// .ant-tree-switcher {
// width: 8px;
// }
// .ant-tree-node-content-wrapper .ant-tree-iconEle {
// width: 24px;
// }

.ant-tree .ant-tree-node-content-wrapper {
    // min-height: 36px;
    // line-height: 36px;
    // padding: 0 4px;
}
// .ant-tree .ant-tree-node-content-wrapper .ant-tree-iconEle {
// line-height: 36px;
// }
// .ant-tree-switcher {
// line-height: 36px;
// }

// .ant-tree.ant-tree-directory .ant-tree-treenode:hover::before {
// background-color: #dff7ec !important;
// }
// .ant-tree.ant-tree-directory .ant-tree-treenode-selected:hover::before,
// .ant-tree.ant-tree-directory .ant-tree-treenode-selected::before {
// background-color: #dff7ec !important;
// }
// .ant-tree.ant-tree-directory
// .ant-tree-treenode
// .ant-tree-node-content-wrapper.ant-tree-node-selected {
// color: @primary-color;
// .handle_icon {
// color: @primary-color;
// }
// }
// .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-switcher {
// color: rgba(0, 0, 0, 0.65);
// }

// .ant-tree-node-selected .icon-yellow {
// color: @primary-color;
// }
</style>
