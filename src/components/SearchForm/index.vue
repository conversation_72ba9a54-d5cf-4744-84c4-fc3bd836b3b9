<template>
    <a-form :model="formState" :labelCol="labelCol">
        <a-row :gutter="[24, 18]">
            <YCol
                v-for="item in formList"
                :key="item.value"
                v-bind="item.attrs || {}"
                :span="span(item)"
            >
                <a-form-item
                    :label="item.label"
                    v-bind="validateInfos[item.value]"
                    :labelCol="item.labelCol"
                    :wrapperCol="item.wrapperCol"
                >
                    <slot
                        :name="item.value"
                        v-bind="item"
                        v-if="item.type === 'slot'"
                    ></slot>
                    <template v-else>
                        <!-- 输入框 -->
                        <a-input
                            v-if="item.type === 'input'"
                            v-model:value="formState[item.value]"
                            :placeholder="`请输入${item.label}`"
                            v-bind="item.attrs || {}"
                            allowClear
                        />
                        <!-- 文本框 -->
                        <a-textarea
                            v-if="item.type === 'textarea'"
                            v-model:value="formState[item.value]"
                            :placeholder="`请输入${item.label}`"
                            v-bind="item.attrs || {}"
                            allowClear
                        />
                        <!-- 选择器 -->
                        <a-select
                            v-else-if="item.type === 'select'"
                            v-model:value="formState[item.value]"
                            :options="item.list"
                            :placeholder="`请选择${item.label}`"
                            v-bind="item.attrs || {}"
                            allowClear
                        ></a-select>
                        <!-- 日期选择器 -->
                        <a-date-picker
                            v-else-if="item.type === 'datePicker'"
                            v-model:value="formState[item.value]"
                            v-bind="item.attrs || {}"
                            allowClear
                        />
                        <!-- 日期选择区间 -->
                        <!-- -->
                        <RangePicker
                            v-else-if="item.type === 'rangePicker'"
                            v-model:startTime="formState[item.value[0]]"
                            v-model:endTime="formState[item.value[1]]"
                            v-bind="item.attrs || {}"
                        ></RangePicker>
                        <!-- 时间选择区间 -->
                        <a-time-picker
                            v-else-if="item.type === 'timePicker'"
                            v-model:value="formState[item.value]"
                            v-bind="item.attrs || {}"
                            allowClear
                        />
                    </template>
                </a-form-item>
            </YCol>
            <YCol v-if="showBtn" :span="btnSpan.span">
                <a-form-item>
                    <div style="display: flex; align-items: center">
                        <a-button
                            type="primary"
                            @click="submit"
                            style="margin-right: 10px"
                        >
                            <template #icon>
                                <SearchOutlined />
                            </template>
                            {{ okText }}
                        </a-button>
                        <a-button @click="reset">
                            <template #icon>
                                <ReloadOutlined />
                            </template>
                            {{ resetText }}
                        </a-button>
                    </div>
                </a-form-item>
            </YCol>
        </a-row>
    </a-form>
</template>
<script setup>
import { Form } from "ant-design-vue";
import { defineProps, defineEmits, watch, computed } from "vue";
import YCol from "@/components/YCol";
import RangePicker from "@/components/RangePicker/index.vue";

const useForm = Form.useForm;

const prop = defineProps({
    isBarCode: {
        type: Boolean,
        default: false,
    },
    layout: {
        type: String,
        default: "inline",
    },
    formState: {
        type: Object,
        default: () => {},
    },
    formList: {
        type: Array,
        default: [],
    },
    rules: {
        type: Object,
        default: () => {},
    },
    showBtn: {
        type: Boolean,
        default: true,
    },
    labelCol: {
        type: Object,
        default: {},
    },
    btnSpan: {
        type: Object,
        default: {},
    },
    okText: {
        type: String,
        default: "查询",
    },
    resetText: {
        type: String,
        default: "重置",
    },
});

const span = computed(() => {
    return (item) => {
        // $ 类型为rangePicker且没设置span，默认值给5
        if (!item.span && item.type === "rangePicker") {
            return 5;
        }
        return item.span;
    };
});

const rules = computed(() => {
    const rules = prop.formList.reduce((pre, cur) => {
        if (cur.rules) {
            pre[cur.value] = cur.rules;
        }
        return pre;
    }, {});
    return Object.assign(rules, prop.rules);
});

// 白名单 (不进行初始化的类型白名单)
const whitelist = ["slot", "rangePicker"];

watch(
    () => prop.formList,
    () => {
        prop.formList.forEach((i) => {
            if (!prop.formState[i.value] && !whitelist.includes(i.type)) {
                prop.formState[i.value] = null;
            }
        });
    },
    {
        immediate: true,
        deep: true,
    }
);

let { resetFields, validate, validateInfos } = useForm(prop.formState, rules);

const emit = defineEmits(["submit", "reset"]);

const submit = () => {
    return validate()
        .then(() => {
            emit("submit");
            return true;
        })
        .catch(() => {
            return false;
        });
};

const reset = () => {
    resetFields();
    emit("reset");
};

defineExpose({ submit, reset });
</script>
<style lang="less" scoped></style>
