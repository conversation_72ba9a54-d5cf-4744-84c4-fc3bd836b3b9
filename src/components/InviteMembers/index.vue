<!-- InviteMembers 邀请成员-->
<template>
    <a-modal
        v-model:visible="props.modalVisible"
        title="邀请成员"
        footer=""
        :width="408"
        class="invite_members"
        :bodyStyle="{ padding: '24px' }"
        @cancel="handleCancel"
        @ok="setModal1Visible(false)"
    >
        <div class="head">分享二维码邀请加入</div>
        <div class="content">
            <div class="codebox" ref="codebox">
                <div class="user_name">
                    {{ state.inviteForm.personName }} 邀请你加入
                </div>
                <div class="title">
                    {{ state.inviteForm.deptClassesName }}
                </div>
                <QRCodeGenerator
                    ref="qrcodeRef"
                    :value="state.inviteForm.url"
                />
            </div>
            <div class="btns">
                <a-button type="link" @click="downloadQRCode">
                    下载二维码
                </a-button>
                <a-button type="link" @click="handCopyLink">
                    复制链接
                </a-button>
            </div>
        </div>
        <div class="footer">
            <label class="lifespan"> 设置邀请有效期： </label>
            <a-select
                v-model:value="state.lifespan"
                style="width: 100%"
                placeholder="请选择有效期"
                @select="handleSelect"
            >
                <a-select-option
                    v-for="item in state.lifespans"
                    :key="item.key"
                    :value="item.key"
                >
                    {{ item.value }}
                </a-select-option>
            </a-select>
        </div>
    </a-modal>
</template>

<script setup>
import { reactive, watch, shallowRef } from 'vue'
import QRCodeGenerator from '@/components/QRCodeGenerator'
import { copyLink } from '@/utils/util'
import { generatorImage } from '@/views/appModel/schoolcalendar/utils/exportCanvas.js'
import {
    employeeInvite,
    studentInvite,
    employeeCutTime
} from '@/api/workers.js'
import { message } from 'ant-design-vue'
const codebox = shallowRef()
const qrcodeRef = shallowRef()
const props = defineProps({
    modalVisible: {
        type: Boolean,
        default: false
    },
    deptId: {
        type: String,
        default: 'false'
    },
    getUrl: {
        type: String,
        default: ''
    }
})
const emit = defineEmits(['update:modalVisible'])
const state = reactive({
    lifespan: 7,
    lifespans: [
        { key: 3, value: '3天' },
        { key: 7, value: '7天' },
        { key: 30, value: '30天' },
        { key: 90, value: '90天' }
    ],
    inviteForm: {
        id: '',
        type: 0,
        url: '',
        personName: '',
        deptClassesName: ''
    }
})
const handleSelect = (event) => {
    updateEmployeeCutTime()
}
const handleCancel = () => {
    emit('update:modalVisible', false)
}
const setModal1Visible = () => {
    handleCancel()
}

// 下载二维码
const downloadQRCode = async() => {
    codebox.value.style = 'box-shadow: 0px 0px 15px 0px #fff0'
    const name = `${
        state.inviteForm.personName +
        '邀请你加入' +
        state.inviteForm.deptClassesName +
        '.png'
    }`
    const base64 = await generatorImage(codebox.value, name, 'base64')
    const a = document.createElement('a')
    a.href = base64
    a.setAttribute('download', name)
    a.click()
    codebox.value.style = 'box-shadow: 0px 0px 15px 0px #d4f9ee'
}
// 复制链接
const handCopyLink = () => {
    copyLink(state.inviteForm.url)
    message.success('已复制链接')
}
// 邀请切换过期时间
const updateEmployeeCutTime = () => {
    const params = {
        id: props.deptId,
        day: state.lifespan
    }
    employeeCutTime(params)
}
const getEmployeeInvite = () => {
    // studentInvite
    const { deptId, getUrl } = props
    const params = {
        [getUrl]: deptId
    }
    let Api = employeeInvite
    // 学籍管理
    if (getUrl === 'classesId') {
        Api = studentInvite
    }
    Api(params).then(({ data }) => {
        const { url, type, personName, deptClassesName } = data
        state.inviteForm.url = url
        state.inviteForm.type = type
        state.inviteForm.personName = personName
        state.inviteForm.deptClassesName = deptClassesName
    })
}
watch(
    () => props.modalVisible,
    (val) => {
        if (val) {
            state.lifespan = 7
            getEmployeeInvite()
        }
    }
)
</script>

<style scoped lang="less">
.invite_members {
    .head {
        color: #000000a6;
    }
    .content {
        width: 266px;
        margin: 15px auto;
        .codebox {
            width: 100%;
            height: 302px;
            padding-top: 41px;
            background-image: url('/image/inviteBg.png');
            background-size: cover;
            text-align: center;
            border-radius: 6px;
            box-shadow: 0px 0px 15px 0px #d4f9ee;
            .user_name {
                color: @suggestive-color;
            }
            .title {
                padding: 0 10px;
                font-weight: 600;
            }
        }
        .btns {
            margin: 15px auto;
            text-align: center;
            .ant-btn-link {
                color: @primary-color;
            }
        }
    }
    .lifespan {
        color: @suggestive-color;
    }
}
</style>
