<!--
 * @Descripttion:出入校
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-03-22 10:44:28
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-03-29 15:58:24
-->
<template>
    <div style="padding: 16px 40px">
        <y-table :columns="columns" :loadData="getList">
            <template #handle> 123 </template>
            <template #title> title </template>

            <template #emptyText> emptyText vue </template>
            <!-- <template #bodyCell="{ column, text, record }">
                {{ column }}
            </template> -->
        </y-table>
    </div>
</template>

<script setup>
import YTable from '@/components/Table/table'
const columns = [
    {
        name: 'Name',
        dataIndex: 'name',
        key: 'name'
    },
    {
        title: 'Age',
        dataIndex: 'age',
        key: 'age',
        defaultSortOrder: 'descend',
        sorter: (a, b) => a.age - b.age
    },
    {
        title: 'Address',
        dataIndex: 'address',
        key: 'address',
        ellipsis: true,
        width: '300px',
        filters: [
            {
                text: 'London',
                value: 'London'
            },
            {
                text: 'New York',
                value: 'New York'
            }
        ],
        filterMultiple: false
    }
]

const dataSource = [
    {
        key: '1',
        name: '胡彦斌',
        age: 32,
        address:
            '西湖区湖底公园1号西湖区湖底公园1号西湖区湖底公园1号西湖区湖底公园1号西湖区湖底公园1号西湖区湖底公园1号西湖区湖底公园1号西湖区湖底公园1号西湖区湖底公园1号西湖区湖底公园1号西湖区湖底公园1号西湖区湖底公园1号西湖区湖底公园1号西湖区湖底公园1号西湖区湖底公园1号西湖区湖底公园1号西湖区湖底公园1号西湖区湖底公园1号西湖区湖底公园1号'
    },
    {
        key: '2',
        name: '胡彦祖',
        age: 42,
        address: '西湖区湖底公园1号'
    },
    {
        key: '3',
        name: '胡彦斌',
        age: 32,
        address: '西湖区湖底公园1号'
    },
    {
        key: '4',
        name: '胡彦祖',
        age: 42,
        address: '西湖区湖底公园1号'
    },
    {
        key: '5',
        name: '胡彦斌',
        age: 32,
        address: '西湖区湖底公园1号'
    },
    {
        key: '6',
        name: '胡彦祖',
        age: 42,
        address: '西湖区湖底公园1号'
    },
    {
        key: '7',
        name: '胡彦斌',
        age: 32,
        address: '西湖区湖底公园1号'
    },
    {
        key: '8',
        name: '胡彦祖',
        age: 42,
        address: '西湖区湖底公园1号'
    },
    {
        key: '9',
        name: '胡彦斌',
        age: 32,
        address: '西湖区湖底公园1号'
    },
    {
        key: '10',
        name: '胡彦祖',
        age: 42,
        address: '西湖区湖底公园1号'
    }
    // {
    //     key: '11',
    //     name: '胡彦斌',
    //     age: 32,
    //     address: '西湖区湖底公园1号'
    // },
    // {
    //     key: '12',
    //     name: '胡彦祖',
    //     age: 42,
    //     address: '西湖区湖底公园1号'
    // }
]

const getList = (p) => {
    return Promise.resolve({
        code: 0,
        data: {
            list: [...dataSource],
            // list: [],
            pageNo: 1,
            pageSize: 10,
            total: 12
        }
    })
}
</script>

<style lang="less" scoped></style>
