/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-03-28 14:07:35
 * @LastEditors: jingrou
 * @LastEditTime: 2022-07-21 16:22:15
 */
import {
    computed,
    defineComponent,
    onBeforeUnmount,
    onMounted,
    onUpdated,
    ref,
    Text,
    watch,
    watchEffect,
    reactive,
    toRefs,
    nextTick
} from 'vue'
import './style/index.less'
import tableProps from './tableTypes'

export default defineComponent({
    name: 'YTable',
    slots: ['handle', 'title'],
    emits: ['onSearch', 'onTableLoad'],
    props: tableProps(),
    setup(props, { slots, attrs, emit }) {
        const { columns, loadData } = props as { columns; loadData }

        // TODO 留着做自定义列
        const handleSlot = computed(() => slots.handle?.())

        const titleSlot = computed(() => slots.title?.())

        const state = reactive({
            loading: false,
            dataSource: [],
            columns: [...columns],
            parameter: {}
        })

        const pagination = reactive({
            total: 0,
            current: 1,
            pageSize: 10,
            showLessItems: true,
            hideOnSinglePage: true,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '30', '40'],
            showTotal: (total, range) => `共 ${total} 条`,
            ...(props.pagination || {})
        })

        const isExec = computed(() => {
            return props.loadData !== undefined
        })

        const getTableList = (firstPage?: boolean) => {
            if (!isExec.value) return
            state.loading = true

            // 缓存当前函数用于外部更新,firstPage为true表示从第一页
            loadData.update = getTableList

            if (firstPage === false) {
                pagination.current = 1
            }

            // TODO  state.parameter 留着处理排序，过滤时候逻辑 , 合并此参数
            const { filters, sorter }: any = state.parameter

            const parameter = {
                pageSize: pagination.pageSize,
                pageNo: pagination.current,
                ...filters,
                ...sorter
            }

            // 数据加载
            loadData(parameter)
                .then((res) => {
                    const { code, data } = res

                    if (Number.parseInt(code) === 0) {
                        const { list, pageNo, pageSize, total } = data || {}
                        // 为防止删除数据后导致页面当前页面数据长度为 0 ,自动翻页到上一页
                        if (list?.length === 0 && pageNo > 1) {
                            pagination.current--
                            getTableList()
                            return
                        }
                        state.dataSource = list
                        pagination.current = pageNo || 1
                        pagination.pageSize = pageSize || 10
                        pagination.total = total
                    } else {
                        // 错误处理
                    }
                    return 1
                })
                .finally(() => {
                    nextTick(() => {
                        state.loading = false
                    })
                })
        }
        const handleChange = ({ current, pageSize }, filters, sorter) => {
            pagination.current = current
            pagination.pageSize = pageSize
            state.parameter.sorter = sorter
            state.parameter.filters = filters
            getTableList()
        }
        onMounted(() => getTableList())

        const _slots: { title?: any; emptyText: any } = {
            emptyText: () => (
                <a-empty
                    image="/image/empty.png"
                    image-style={{ width: 'auto', height: '180px' }}
                />
            ),
            ...slots
        }
        delete _slots.title

        return () => {
            return (
                <div class="table_warp">
                    {(titleSlot.value || handleSlot.value) && (
                        <a-row
                            justify="space-between"
                            style="margin-bottom:16px;align-items: center;"
                        >
                            <a-col>{titleSlot.value}</a-col>
                            {handleSlot.value && (
                                <a-col>{handleSlot.value}</a-col>
                            )}
                        </a-row>
                    )}
                    <a-table
                        class={{
                            antTableCellActive: !state.dataSource?.length
                        }}
                        dataSource={state.dataSource}
                        {...attrs}
                        v-slots={_slots}
                        columns={state.columns}
                        pagination={
                            props.pagination === false ? false : pagination
                        }
                        loading={state.loading}
                        onChange={handleChange}
                    >
                    </a-table>
                </div>
            )
        }
    }
})
