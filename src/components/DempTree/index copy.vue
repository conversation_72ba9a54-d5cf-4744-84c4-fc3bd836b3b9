<!--
 * @Author: your name
 * @Date: 2022-01-10 10:51:56
 * @LastEditTime: 2022-03-01 10:18:18
 * @LastEditors: <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \cloud-system\src\components\DempTree\index.vue
-->
<template>
    <div>
        <a-tree
            v-model:expandedKeys="expandedKeys"
            v-model:selectedKeys="selectedKeys"
            :tree-data="treeData"
            showIcon
            checkStrictly
            :fieldNames="fieldNames"
            blockNode
            :selectable="true"
            @select="select"
        >
            <template #title="{ dataRef }">
                <slot name="treeIcon">
                    <i class="iconfont icon-yellow" :class="treeIcon"></i>
                </slot>
                <!-- <component :is="treeIcon" class="icon-yellow"></component> -->
                <a-tooltip>
                    <template
                        #title
                        v-if="aliasEndName(dataRef)?.length > 12"
                    >{{ aliasEndName(dataRef) }}</template>
                    {{ aliasEndName(dataRef) }}
                </a-tooltip>
                <slot name="handleItem" :handleItem="dataRef"></slot>
            </template>
        </a-tree>

        <a-empty
            v-if="!treeData.length"
            image="image/empty.png"
            :description="emptyTitle"
            :image-style="{ width: '100%', height: '180px', marginTop: '50%' }"
        ></a-empty>
        <slot name="handleFooter"></slot>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
export default defineComponent({
    props: {
        treeData: {
            type: Array,
            default: () => []
        },
        fieldNames: {
            type: Object,
            default: () => {
                return {
                    children: 'list',
                    title: 'name',
                    key: 'id'
                }
            }
        },
        treeIcon: {
            type: String,
            default: 'icon-a-Fill21'
        },
        defaultExpand: {
            type: String,
            default: ''
        },
        selectedKeys: {
            type: String,
            default: ''
        },
        emptyTitle: {
            type: String,
            default: '暂无数据'
        }
    },
    components: {},
    emitSelect: String,
    setup (props, { emit }) {
        const router = useRouter()
        const route = useRoute()
        const expandedKeys = ref<string[]>([])
        const selectedKeys = computed(() => {
            return [props.selectedKeys]
        })
        const treeIcon = ref<string>(props.treeIcon)
        const select = (selectedKeys: any, item: any) => {
            if (selectedKeys.length) {
                emit('emitSelect', selectedKeys[0], item.node)
                const { ancestors, type } = item.node
                router.push({
                    path: route.path,
                    query: {
                        ...route.query,
                        deptId: selectedKeys[0],
                        type,
                        ancestors
                    }
                })
            }
        }
        // 默认展开
        watch(
            () => props.defaultExpand,
            (val: any) => {
                expandedKeys.value = [val]
                selectedKeys.value = props.selectedKeys
                    ? [props.selectedKeys]
                    : [val]
            }
        )

        const aliasEndName = computed(() => {
            return (dataRef: any) => {
                let name = dataRef[props.fieldNames.title]
                if (dataRef.type == 3 && dataRef.yearName) {
                    name = dataRef[props.fieldNames.title] + dataRef.yearName
                }
                return name
            }
        })
        return {
            expandedKeys,
            selectedKeys,
            treeIcon,
            select,
            aliasEndName
        }
    }
})
</script>
<style lang="less">
.ant-tree-block-node {
    margin-bottom: 10px;
}
.ant-dropdown-menu-item:hover .ant-btn,
.ant-dropdown-menu-submenu-title:hover .ant-btn {
    background: transparent;
}
</style>
