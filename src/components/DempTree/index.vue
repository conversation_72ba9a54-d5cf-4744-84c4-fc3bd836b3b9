<template>
    <div>
        <!-- v-model:expandedKeys="expandedKeys" -->
        <slot name="handleFooter"></slot>
        <a-tree v-if="treeData.length" v-model:selectedKeys="selectedKeyss" :tree-data="treeData" showIcon
            :disabled="disabled" :checkable="checkable" blockNode :draggable="isDraggable" :fieldNames="fieldNames"
            :selectable="true" :defaultExpandAll="defaultExpandAll" @dragenter="onDragEnter" @drop="onDrop"
            @select="handlerSelect" v-bind="$attrs">
            <template #title="{ dataRef }">
                <slot name="treeIcon" :treeItem="{ id: dataRef.id, name: dataRef.name, item: dataRef }">
                    <i class="iconfont icon-yellow" :class="treeIcon"></i>
                </slot>
                <Tooltip :title="aliasEndName(dataRef)"></Tooltip>
                <slot name="handleItem" :key="dataRef.id" :handleItem="dataRef"></slot>
            </template>
        </a-tree>

        <a-empty v-if="!treeData.length && isShowEmpty" image="image/empty.png" :description="emptyTitle"
            :image-style="{ width: '100%', height: '180px', marginTop: '50%' }"></a-empty>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, toRaw, watch, computed, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getCurNodeParents } from "@/utils/util";

export default defineComponent({
    props: {
        isShowEmpty: {
            type: Boolean,
            default: true,
        },
        defaultExpandAll: {
            type: Boolean,
            default: true,
        },
        treeData: {
            type: Array,
            default: () => [],
        },
        fieldNames: {
            type: Object,
            default: () => {
                return {
                    children: "list",
                    title: "name",
                    key: "id",
                };
            },
        },
        isDraggable: {
            type: Boolean,
            default: false,
        },
        treeIcon: {
            type: String,
            default: "icon-a-Fill21",
        },
        defaultExpand: {
            type: Array,
            default: () => [],
        },
        selectedKeys: {
            type: Array,
            default: () => [],
        },
        emptyTitle: {
            type: String,
            default: "暂无数据",
        },
        checkable: {
            // 勾选框
            type: Boolean,
            default: false,
        },
        disabled: {
            // 禁用
            type: Boolean,
            default: false,
        },
    },
    components: {},
    setup(props, { emit }) {
        const router = useRouter();
        const route = useRoute();
        const expandedKeys = ref<[]>([]);
        const draggable = ref<boolean>(false);
        const titleObj = ref({});

        const selectedKeyss = computed(() => {
            return props.selectedKeys;
        });

        // const treeIcon = ref<string>(props.treeIcon)
        const handlerSelect = (selectedKeyss, item) => {
            if (draggable.value) return;
            if (selectedKeyss.length) {
                emit("emitSelect", selectedKeyss[0], item.node);
                const { ancestors, type, showName, name, rollValue } =
                    item.node;
                router.push({
                    path: route.path,
                    query: {
                        ...route.query,
                        deptId: selectedKeyss[0],
                        title: showName || name,
                        type,
                        ancestors,
                        rollValue,
                    },
                });
            }
        };
        const onDragEnter = (info) => {
            if (props.isDraggable) {
                draggable.value = true;
            }
        };
        // 可拖拽
        const onDrop = (info) => {
            if (props.isDraggable) {
                draggable.value = false;
                emit("emitDraggable", info);
            }
        };
        // // 默认展开
        // watch(
        //     () => props.defaultExpand,
        //     (val) => {
        //         expandedKeys.value = val
        //         selectedKeyss.value = props.selectedKeys
        //     }
        // )

        nextTick(() => {
            if (!props.defaultExpand.length) {
                const id =
                    props.selectedKeys[0] ||
                    (props.treeData[0] &&
                        props.treeData[0][props.fieldNames.key]);
                const { parentNodeIds } =
                    getCurNodeParents(toRaw(props.treeData), id) || {};
                expandedKeys.value = parentNodeIds
                    ? [...parentNodeIds, ...selectedKeyss.value]
                    : [...selectedKeyss.value];
            } else {
                expandedKeys.value = props.defaultExpand;
            }
        });

        const aliasEndName = computed(() => {
            return (dataRef) => {
                const name = dataRef[props.fieldNames.title] || dataRef.name;
                const names = dataRef.name;
                const Id = dataRef[props.fieldNames.key];
                titleObj.value[Id] = names;
                return name;
            };
        });

        return {
            expandedKeys,
            selectedKeyss,
            draggable,
            titleObj,
            handlerSelect,
            onDrop,
            onDragEnter,
            aliasEndName,
        };
    },
});
</script>
<style lang="less">
.ant-tree-block-node {
    margin-bottom: 18px;
}

.ant-dropdown-menu-item:hover .ant-btn,
.ant-dropdown-menu-submenu-title:hover .ant-btn {
    background: transparent;
}

.iconfont.icon-yellow {
    font-size: 12px;
}

.ant-tree-switcher-icon {
    font-size: 12px;
    vertical-align: middle;
}

.ant-tree-title {
    display: flex !important;
}
</style>
