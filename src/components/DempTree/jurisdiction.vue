<!--
 * @Author: your name
 * @Date: 2022-01-10 10:51:56
 * @LastEditTime: 2023-05-17 16:17:41
 * @LastEditors: yjx
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \云平台\cloud-system-2.0\src\components\DempTree\jurisdiction.vue
-->
<template>
    <div>
        <!-- 和全选有冲突 -->
        <!-- v-model:expandedKeys="expandedKeys" -->
        <slot name="handleFooter"></slot>
        <a-tree
            v-if="treeData.length"
            v-model:selectedKeys="selectedKeys"
            :tree-data="treeData"
            :fieldNames="fieldNames"
            :defaultExpandAll="expandAll"
            blockNode
            :selectable="true"
            @select="select"
        >
            <template #title="{ dataRef }">
                <!-- <team-outlined class="icon-yellow" /> -->
                <i
                    class="iconfont icon-a-Fill21 icon-yellow"
                    style="font-size: 12px"
                ></i>

                <a-tooltip>
                    <template #title v-if="aliasEndName(dataRef)">
                        {{ dataRef[fieldNames.title] }}
                    </template>
                    {{ dataRef[fieldNames.title] }}
                </a-tooltip>
                <slot name="handleItem" :handleItem="dataRef"></slot>
            </template>
        </a-tree>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
export default defineComponent({
    props: {
        treeData: {
            type: Array,
            default: () => []
        },
        expandAll: {
            type: Boolean,
            default: () => false
        },
        fieldNames: {
            type: Object,
            default: () => {
                return {
                    children: 'children',
                    title: 'name',
                    key: 'id'
                }
            }
        }
    },
    components: {},
    emitSelect: String,
    setup(props, { emit }) {
        const router = useRouter()
        const route = useRoute()
        const expandedKeys = ref<string[]>([])
        const selectedKeys = ref<string[]>([])
        const select = (selectedKeys: any, item: any) => {
            if (selectedKeys.length) {
                emit('emitSelect', selectedKeys[0], item.node)
                const { ancestors, type, isDefault, isSetBtn, codeValue } =
                    item.node
                router.push({
                    path: route.path,
                    query: {
                        ...route.query,
                        deptId: selectedKeys[0],
                        codeValue,
                        type,
                        isSetBtn,
                        isDefault,
                        ancestors
                    }
                })
            }
        }
        function findItemById(data, id) {
            for (let i = 0; i < data.length; i++) {
                if (data[i].id === id) {
                    return data[i]
                } else if (data[i].children) {
                    const result = findItemById(data[i].children, id)
                    if (result) {
                        return result
                    }
                }
            }
            return null
        }
        onMounted(() => {
            const { deptId }: any = route.query
            if (deptId) {
                selectedKeys.value = [deptId]
                const findItem = findItemById(props.treeData, deptId);
                emit(
                    'getTreeTypeId',
                    findItem
                )
            } else {
                // 如果路由上面没有deptId  默认选择第一项
                if (props.treeData && props.treeData.length > 0) {
                    props.treeData.some((item) => {
                        if (item.children && item.children.length) {
                            selectedKeys.value = [item.children[0].id]
                            emit(
                                'getTreeTypeId',
                                item.children[0]
                            )
                            return true
                        } else {
                            selectedKeys.value = [props.treeData[0].id]
                            emit(
                                'getTreeTypeId',
                                props.treeData[0]
                            )
                            return true
                        }
                    })
                }
            }
        })
        const aliasEndName = computed(() => {
            return (dataRef: any) => {
                let name = dataRef[props.fieldNames.title]
                if (dataRef.type == 3 && dataRef.yearName) {
                    name = dataRef[props.fieldNames.title] + dataRef.yearName
                }
                return dataRef[props.fieldNames.title].length > 12
            }
        })
        return {
            expandedKeys,
            selectedKeys,
            aliasEndName,
            select
        }
    }
})
</script>
<style lang="less">
.ant-tree-block-node {
    margin-bottom: 10px;
}

.ant-dropdown-menu-item:hover .ant-btn,
.ant-dropdown-menu-submenu-title:hover .ant-btn {
    background: transparent;
}

.ant-tree-indent-unit {
    width: 10px;
}
</style>
