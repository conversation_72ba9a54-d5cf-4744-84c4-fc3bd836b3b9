<template>
    <div>
        <div class="header">
            <a-select
                style="width: 50%"
                v-model:value="state.searchValue"
                show-search
                placeholder="请输入地址搜索"
                :default-active-first-option="false"
                :show-arrow="false"
                :filter-option="false"
                :not-found-content="null"
                :options="state.searchResult"
                @search="handleSearch"
                @change="handleChange"
                :field-names="{ label: 'name', value: 'id' }"
            >
                <template #option="{ label, name, address }">
                    {{ name }}
                    <span style="font-size: 12px; color: #333"
                        >- {{ address }}
                    </span>
                </template>
            </a-select>

            <a-select
                v-model:value="state.currentRange"
                style="margin-left: 12px; width: 30%"
                @change="handleRangeChange"
            >
                <a-select-option
                    :value="item"
                    v-for="(item, index) in state.rangeList"
                    :key="index"
                    >{{ item }}米</a-select-option
                >
            </a-select>
        </div>

        <div
            id="__myMap"
            :style="{
                width: options.style.width,
                height: options.style.height
            }"
            class="map"
        ></div>
    </div>
</template>

<script setup>
import { reactive, onMounted, nextTick, toRaw, computed } from 'vue'

const props = defineProps({
    options: {
        type: Object,
        default: () => {
            return {
                id: '__myMap',
                // 定位方式  0 地址定位 1经纬度定位
                locationType: 0,
                zoom: 16,
                // 经度
                longitude: 0,
                // 纬度
                latitude: 0,
                // 地址
                address: '',
                // 地图模式
                viewMode: '3D',
                pitch: 30,
                // 缩略图
                showOverView: true,
                // 工具条
                showToolBar: true,
                // 卫星图
                showMapType: true,
                // 定位控件
                showGeolocation: true,
                // ICON
                positionIcon:
                    'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
                style: {
                    padding: 0,
                    height: '380px',
                    width: '740px',
                    margin: '0px auto 0 auto'
                }
            }
        }
    }
})

const emit = defineEmits(['getAddress'])

const state = reactive({
    key: '41a32b85bc37cf2fe1b0ed049874f5ce',
    map: null,
    marker: null,
    circle: null,

    rangeList: [100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 2000, 3000],
    currentRange: 300,
    currentAddress: {},
    //
    searchValue: null,
    searchResult: []
})

function handleRangeChange(v) {
    state.currentRange = v
    if (state.marker) {
        renderCircle()
        state.map.setFitView()

        emit('getAddress', {
            address: state.currentAddress,
            range: state.currentRange
        })
    }
}

// 搜索
async function handleSearch(val) {
    state.searchValue = val
    const placeSearch = new AMap.PlaceSearch({
        city: '全国'
    })
    const pois = await getPois(placeSearch, val)
    state.searchResult = pois
}

// 搜索结果改变
function handleChange(val) {
    state.currentAddress = state.searchResult.find((i) => i.id == val)
    if (state.currentAddress) {
        updateAddress()
        renderCircle()
        state.map.setFitView()

        emit('getAddress', {
            address: state.currentAddress,
            range: state.currentRange
        })
    }
}

// 更新地图定位
function updateAddress() {
    const { lng, lat } = state.currentAddress.location
    state.marker && state.map.remove(state.marker)
    state.marker = new window.AMap.Marker({
        icon: props.options.positionIcon,
        position: [lng, lat]
        // draggable: true,
        // cursor: 'move',
    })
    state.map.add(state.marker)
}

// 画圈
function renderCircle() {
    const { lng, lat } = state.currentAddress?.location
    state.circle && state.map.remove(state.circle)
    state.circle = new window.AMap.Circle({
        center: [lng, lat], // 圆心位置
        radius: state.currentRange, // 半径
        strokeColor: '#00b781', // 线颜色
        strokeOpacity: 0.7, // 线透明度
        strokeWeight: 2, // 线宽
        fillColor: '#1791fc', // 填充色
        fillOpacity: 0.5 // 填充透明度
    })
    state.map.add(state.circle)
}

// 获取搜索结果
function getPois(placeSearch, address) {
    return new Promise((resolve, reject) => {
        placeSearch.search(address, (status, result) => {
            // 搜索成功时，result即是对应的匹配数据
            if (result.info === 'OK') {
                const pois = result.poiList.pois
                resolve(pois)
            } else {
                reject()
            }
        })
    })
}

function init() {
    const {
        id,
        viewMode,
        zoom,
        pitch,
        positionIcon,
        showOverView,
        showToolBar,
        showMapType,
        showGeolocation,
        showScale,
        longitude,
        latitude,
        address,
        locationType
    } = toRaw(props.options)

    window.onmaploaded = () => {
        const params = {
            // https://lbs.amap.com/api/javascript-api/reference/map
            zoom, // 初始化地图层级
            zooms: [3, 18], // 设置地图级别范围
            // center: [longitude, latitude], //经纬度没有传入定在当前城市
            pitch, // 地图俯仰角度，有效范围 0 度- 83 度
            viewMode, // 地图模式,
            resizeEnable: true, // 地图初始化加载定位到当前城市,
            // mapStyle: 'amap://styles/whitesmoke',//设置地图的显示样式
            lang: 'zh_cn' // 设置地图语言类型
        }

        const map = new window.AMap.Map(id, params)
        state.map = map

        AMap.plugin(
            [
                'AMap.ToolBar',
                'AMap.Scale',
                'AMap.HawkEye',
                'AMap.MapType',
                'AMap.Geolocation',
                'AMap.AutoComplete',
                'AMap.PlaceSearch',
                'AMap.Circle'
                // "AMap.InfoWindow"
            ],
            async() => {
                // 搜索
                // let autoComplete = new AMap.AutoComplete({
                //     city: '全国'
                // })

                // 创建 infoWindow 实例  信息窗体
                // var infoWindow = new AMap.InfoWindow({
                // 	isCustom: true,  //使用自定义窗体
                // 	content: "<div style='color:red;padding:20px;background:#fff;'>高德软件有限公司</div>",  //传入 dom 对象，或者 html 字符串
                // 	offset: new AMap.Pixel(16, -50)
                // });
                // // 信息窗体
                // map.addControl(infoWindow);
                // var position = new AMap.LngLat(longitude, latitude);
                // infoWindow.open(map, position);

                if (showScale) {
                    // 比例尺
                    map.addControl(new AMap.Scale())
                }

                if (showToolBar) {
                    // 工具条控件
                    map.addControl(
                        new AMap.ToolBar({
                            position: {
                                top: '130px',
                                right: '20px'
                            }
                        })
                    )
                }
                if (showOverView) {
                    // 显示地图的缩略图
                    map.addControl(new AMap.HawkEye({ isOpen: true }))
                }
                if (showMapType) {
                    // 在图面添加类别切换控件，实现默认图层与卫星图、实施交通图层之间切换的控制
                    map.addControl(new AMap.MapType())
                }
                if (showGeolocation) {
                    // 在图面添加定位控件，用来获取和展示用户主机所在的经纬度位置
                    map.addControl(new AMap.Geolocation())
                }
                let point = []
                if (locationType == 0 && address) {
                    if (address) {
                        const placeSearch = new AMap.PlaceSearch({
                            city: '全国'
                        })
                        const pois = await getPois(placeSearch, address)
                        point = pois
                    }
                } else {
                    if (longitude && latitude) {
                        point = [Number(longitude), Number(latitude)]
                    }
                }

                // updateAddress()

                if (locationType == 0 && address) {
                    map.setFitView()
                }
                if (longitude && latitude) {
                    map.setFitView()
                }
            }
        )
    }
    const url = `https://webapi.amap.com/maps?v=2.0&key=${state.key}&callback=onmaploaded`
    const jsapi = document.createElement('script')
    jsapi.charset = 'utf-8'
    jsapi.src = url
    document.head.appendChild(jsapi)
}

onMounted(() => {
    window._AMapSecurityConfig = {
        serviceHost: 'https://cloud.yyide.com/_AMapService/api'
    }
    nextTick(() => {
        init()
    })
})
</script>

<style scoped>
.map {
    position: relative;
}
.header {
    margin-bottom: 12px;
}
</style>
