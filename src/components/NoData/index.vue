<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-04-09 14:35:02
 * @LastEditors: jingrou
 * @LastEditTime: 2022-04-09 17:48:23
-->
<template>
    <div>
        <div class="no_data">
            <a-empty
                :image="image"
                :image-style="{
                    height: '180px'
                }"
            >
                <template #description>
                    <slot name="description">
                        <span> {{ description }} </span>
                    </slot>
                </template>
            </a-empty>
        </div>
    </div>
</template>

<script setup>
defineProps({
    description: {
        type: String,
        default: '暂无数据'
    },
    image: {
        type: String,
        default: '/image/empty.png'
    }
})
</script>

<style lang="less" scoped>
.no_data {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0%;
    left: 0%;
}
</style>
