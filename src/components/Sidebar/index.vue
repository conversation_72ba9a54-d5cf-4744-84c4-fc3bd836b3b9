<template>
    <div>
        <div v-for="item in list" :key="item.id">
            <div
                class="schoolYear"
                v-for="yearItem in item.yearList"
                :key="yearItem.id"
            >
                <div>
                    <img
                        :src="item.img"
                        style="color: #ffca28; margin-right: 4px"
                    />
                    <span style="color: rgba(0, 0, 0, 0.85); font-size: 14px">{{
                        yearItem.title
                    }}</span>
                </div>

                <div class="operation">
                    <a-popover placement="bottomRight" trigger="click">
                        <template #content>
                            <div
                                style="width: 60px; text-align: right"
                                @click="addEdityear(true)"
                            >
                                编辑
                            </div>
                            <div
                                style="
                                    width: 60px;
                                    text-align: right;
                                    color: #f5222d;
                                "
                            >
                                删除
                            </div>
                        </template>
                        <more-outlined style="cursor: pointer" />
                    </a-popover>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
const props = defineProps({
    list: {
        type: Array,
        default: () => [],
    },
});
</script>

<style lang="less">
.schoolYear {
    width: 252px;
    padding: 12px 18px;
    height: 36px;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.schoolYear:hover {
    background: #f6f6f6;
}
</style>
