<template>
    <a-drawer
        :width="width"
        :closable="false"
        v-model:visible="drawerOpen"
        :maskClosable="false"
        class="YDrawer"
        :destroyOnClose="true"
        :style="rootStyle"
        v-bind="$attrs"
    >
        <div class="drawer_title">
            <ArrowLeftOutlined
                v-if="showArrowLeftOutlined"
                @click="close"
                :style="{ color: '#00B781', fontSize: '16px' }"
            />
            <span class="ml-10">{{ title }}</span>
        </div>
        <slot></slot>
        <template #footer>
            <slot name="footer"></slot>
        </template>
    </a-drawer>
</template>
<script setup>
defineOptions({
    inheritAttrs: false,
});

const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    mask: {
        type: Boolean,
        default: false,
    },
    title: {
        type: String,
        default: "",
    },
    width: {
        type: Number,
        default: 0,
    },
    rootStyle: {
        type: Object,
        default: () => {
            return {
                height: "calc(100vh - 60px)",
                top: "60px",
            };
        },
    },
    showArrowLeftOutlined: {
        type: Boolean,
        default: true,
    },
});

const width = computed({
    get: () => {
        if (props.width) {
            return `calc(100% - ${props.width}px)`;
        }
        return `calc(100% - 272px)`;
    },
});

const emit = defineEmits(["update:visible"]);

let drawerOpen = ref(false);
watch(
    () => props.visible,
    (val) => {
        drawerOpen.value = val;
    },
    {
        immediate: true,
        deep: true,
    }
);

const close = () => {
    drawerOpen.value = false;
    emit("update:visible", drawerOpen.value);
};
</script>
<style lang="less">
.YDrawer .ant-drawer-content-wrapper {
    border-top-left-radius: 12px;
    .ant-drawer-content {
        border-top-left-radius: 12px;
    }
    box-shadow: none;
    .drawer_title {
        padding-bottom: 20px;
        padding-left: 24px;
        margin-top: -4px;
        margin-left: -24px;
        margin-right: -24px;
        font-size: 16px;
        color: #000;
        font-weight: 600;
        border-bottom: 1px solid #d9d9d9;
        .ml-10 {
            margin-left: 10px;
        }
    }
}
</style>
