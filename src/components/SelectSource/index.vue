<!--
 * @Descripttion: 选人 选部门 选班
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-28 13:33:41
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-03-18 19:14:36
 * const params = {modalVisible: false, selectSourceObj:{}}
 * store.commit('selectSource/GET_USER_INFOS', params)  //开启选人框
 * callbackParameter  // 确定提交
 *  isParent: false, //truen:  添加家长
 * selectSourceParams: {
     selectedPoolSelectedSet: [],//回显的数据
 *  optionalClass:{key: '', value: 2}  // 指定可选级 对象 key: 类型,字符(例如： 班级：type) ,value:值， 数组(例如： 班级：[1, 2, 3])
 *  singleChoice  // 是否单选  单选 :true ,多选 ：false
 *  checkPeople: boolean,//查人 true  查部门、班级 false
 *  treeData_teacher: [],//老师 数组（tree 数据）
 *  treeData_student:[],//学生 数组（tree 数据）
 *  ... //更多
 *  tabsTexts: [ 数组（多个tab 切换按钮）
 *  { tab: '老师', key: 'teacher' },
 *  { tab: '学生', key: 'student' }
 *   ]
 * }
-->
<template>
    <a-modal
        class="source-modal"
        width="1000px"
        v-model:visible="modalVisible"
        :maskClosable="false"
        :confirm-loading="modalConfirmLoading"
        @cancel="handerCancel"
        @ok="handerSourceSubmit"
    >
        <!-- v-model:activeKey="activeKey" -->
        <a-tabs
            class="source-modal-top"
            size="large"
            :class="{
                'source-modal-tops': selectSourceParams.tabsTexts.length > 1,
                'source-modal-reset': selectSourceParams.tabsTexts.length < 2
            }"
            :type="selectSourceParams.tabsTexts.length > 1 ? 'card' : 'line'"
            @tabClick="handerTabClick"
        >
            <a-tab-pane
                v-for="itme in selectSourceParams.tabsTexts"
                :key="itme"
                :tab="itme.tab"
            />
        </a-tabs>
        <a-row class="source-modal-content">
            <a-col class="source-modal-col" :span="16">
                <!-- 搜索框 -->
                <div class="source-modal-search">
                    <a-input-search
                        v-model:value="sourceSearch"
                        placeholder="请输入关键字"
                        enter-button
                        @search="onSourceSearch"
                    />
                    <a-button
                        v-if="sourceSearch"
                        class="clear-search"
                        type="link"
                        @click="handerClearSearch"
                        >取消
                    </a-button>
                </div>
                <!-- 搜索后的table表格 -->
                <a-table
                    :rowKey="(item: any) => item"
                    v-if="!isSourceSearch"
                    :columns="tableColumns"
                    :dataSource="tableDataSource"
                    :row-selection="{
                        selectedRowKeys: selectedRowKeys,
                        onChange: onSelectChange,
                        type:
                            selectSourceParams.isNum == 1
                                ? 'radio'
                                : 'checkbox '
                    }"
                />
                <a-row v-else class="source-modal-col-row">
                    <a-col
                        class="source-modal-col-row-col"
                        style="padding: 0"
                        :span="12"
                    >
                        <!-- 左侧的tree树 -->
                        <a-tree
                            v-model:expandedKeys="
                                selectSourceParams.defaultExpand
                            "
                            v-model:selectedKeys="selectedKeys"
                            :tree-data="
                                selectSourceParams[`treeData_${activeKey}`]
                            "
                            blockNode
                            :fieldNames="fieldNames"
                            :selectable="true"
                            @select="clickSelect"
                        >
                            <template #title="{ dataRef }">
                                <team-outlined class="icon-yellow" />
                                {{
                                    activeKey == "student"
                                        ? dataRef.name || dataRef.showName
                                        : dataRef.name
                                }}
                            </template>
                        </a-tree>
                        <a-empty
                            v-if="
                                !selectSourceParams[`treeData_${activeKey}`]
                                    .length
                            "
                            image="/image/empty.png"
                            :image-style="{
                                marginTop: '114px',
                                height: '120px'
                            }"
                        ></a-empty>
                    </a-col>

                    <!-- 中间的被选池 -->
                    <a-col
                        :span="12"
                        class="source-modal-col-row-col"
                        style="padding: 0; border: none"
                    >
                        <p
                            class="check-all"
                            style="padding: 6px 15px"
                            v-if="selectSourceParams.singleChoice"
                        >
                            全选
                            <span class="check-all-num">
                                共{{
                                    selectedPool[`selestList${activeKey}`]
                                        .length
                                }}
                                {{
                                    selectSourceParams.checkPeople ? "人" : "组"
                                }}
                            </span>
                        </p>
                        <a-checkbox
                            v-else
                            class="check-all"
                            v-model:checked="
                                selectedPool[`selestcheckAll${activeKey}`]
                            "
                            :disabled="
                                selectSourceParams.isNum ||
                                !selectedPool[`selestList${activeKey}`].length
                            "
                            :indeterminate="
                                selectedPool[`selestIndeterminate${activeKey}`]
                            "
                            @change="onCheckAllChange"
                        >
                            全选
                            <span class="check-all-num">
                                共{{
                                    selectedPool[`selestList${activeKey}`]
                                        .length
                                }}{{
                                    selectSourceParams.checkPeople ? "人" : "组"
                                }}
                            </span>
                        </a-checkbox>
                        <a-checkbox-group
                            v-model:value="
                                selectedPool[`selestChecked${activeKey}`]
                            "
                            @change="handerChangeItem"
                        >
                            <div
                                class="customize-checkbox-group"
                                v-for="item in selectedPool[
                                    `selestList${activeKey}`
                                ]"
                                :key="item.id"
                                @click="clickCheckWarp(item)"
                            >
                                <team-outlined class="icon-yellow" />
                                <a-checkbox
                                    :value="item"
                                    :disabled="
                                        selectSourceParams.optionalClass &&
                                        JSON.stringify(
                                            selectSourceParams.optionalClass
                                        ) !== '{}'
                                            ? selectSourceParams.optionalClass.value.includes(
                                                  item[
                                                      selectSourceParams
                                                          .optionalClass.key
                                                  ]
                                              )
                                            : false
                                    "
                                >
                                    {{
                                        activeKey == "student"
                                            ? item.showName || item.name
                                            : item.name
                                    }}
                                </a-checkbox>
                            </div>
                        </a-checkbox-group>

                        <a-empty
                            v-if="
                                !selectedPool[`selestList${activeKey}`].length
                            "
                            image="/image//empty.png"
                            :image-style="{
                                marginTop: '86px',
                                height: '120px'
                            }"
                        ></a-empty>
                    </a-col>
                </a-row>
            </a-col>

            <!-- 右侧的已选池 -->
            <a-col :span="8" class="source-modal-ovh">
                <a-checkbox
                    class="check-all select-check-all"
                    v-model:checked="selectCheckAll"
                    :disabled="!selectedPool.selectedPoolSelectedSet.length"
                    :indeterminate="selectIndeterminate"
                    @change="onCheckSelectAllChange"
                >
                    已选
                    <span class="check-all-num">
                        共{{ selectedPool.selectedPoolSelectedSet.length
                        }}{{ selectSourceParams.checkPeople ? "人" : "组" }}
                    </span>
                    <a-button class="clear-all" type="link">清空</a-button>
                </a-checkbox>
                <a-checkbox-group v-model:value="selectedDataSet">
                    <div
                        class="customize-checkbox-group customize-checkbox-select-group"
                        v-for="item in selectedPool.selectedPoolSelectedSet"
                        :key="
                            selectSourceParams.isParent
                                ? item.studentId
                                : item.id
                        "
                    >
                        <team-outlined class="icon-yellow" />
                        <a-checkbox :value="item">
                            {{
                                activeKey == "student"
                                    ? item.showName || item.name
                                    : item.name
                            }}
                        </a-checkbox>
                        <close-outlined
                            class="close-outlined"
                            @click="
                                handerSelectedDel(
                                    selectSourceParams.isParent
                                        ? item.studentId
                                        : item.id
                                )
                            "
                        />
                    </div>
                </a-checkbox-group>
                <a-empty
                    v-if="!selectedPool.selectedPoolSelectedSet.length"
                    image="/image/empty.png"
                    :image-style="{ marginTop: '152px', height: '120px' }"
                ></a-empty>
            </a-col>
        </a-row>
    </a-modal>
</template>

<script lang="ts">
import { useStore } from "@/store/index"
import api from "@/api"
import {
    ref,
    toRefs,
    computed,
    reactive,
    defineComponent,
    getCurrentInstance
} from "vue"

export default defineComponent({
    name: "SelectSource",
    props: {
        modalConfirmLoading: {
            type: Boolean,
            default: false
        },
        isJob: {
            type: Boolean,
            default: false
        }
    },
    emits: ["callbackParameter"],
    components: {},
    setup(props, { emit }) {
        const { proxy } = getCurrentInstance() as any
        const store = useStore()
        // 是否显示模态框
        // const modalVisible = ref<boolean>(false)
        const expandedKeys = ref<string[]>(["1458257624041546325"])
        const selectedKeys = ref<string[]>([])
        const olSelectedPoolSelectedSet = ref<string[]>([])
        const selectedItmes = ref<object>({})
        // const personnelAssembly = ref([])
        const activeKey = ref<string>("teacher")
        const fieldNames = ref({
            children: "children",
            title: "name",
            key: "id"
        })
        const state = reactive({
            sourceSearch: "",
            // table
            tableDataSource: [],
            selectedRowKeys: [],
            tableColumns: [
                {
                    title: "姓名",
                    dataIndex: "name"
                },
                {
                    title: activeKey.value === "teacher" ? "部门" : "班级",
                    dataIndex: "showName"
                }
            ],
            // 中间被选池
            selectedPool: {
                // 已选池 tab1、tab2选中集合
                selectedPoolSelectedSet: []
            },
            // 是否搜索
            isSourceSearch: true,
            // 已选池
            selectCheckAll: false,
            selectIndeterminate: false,
            selectedDataSet: []
        })
        // 获取教职工列表
        const getFacultyList = (name: any) => {
            const { emp_status_yes_id } = store.state.selectSource.dictionary
            const params = {
                pageNo: 1,
                pageSize: 100,
                [state.sourceSearch !== "" ? "name" : "deptId"]: name
            }
            props.isJob && (params.statusList = emp_status_yes_id)
            return new Promise((resolve, reject) => {
                proxy.$defHttp
                    .post(api.facultyList, params)
                    .then((res: any) => {
                        const { data } = res
                        resolve(data.list)
                    })
            })
        }
        // 获取学生家长列表
        const getStudentList = (name: any, newData?: any) => {
            const params = {
                [state.sourceSearch ? "name" : "classesId"]: name
            }
            return new Promise((resolve, reject) => {
                proxy.$defHttp
                    .post(api.studentlistSearch, params)
                    .then((res: any) => {
                        const { data } = res
                        resolve(data)
                        newData &&
                            data.forEach((v: any) => {
                                v.children = v.elterns
                                newData.push(v)
                            })
                    })
            })
        }
        // 模态框 确定
        const handerSourceSubmit = () => {
            if (proxy.selectedPool.selectedPoolSelectedSet.length) {
                // proxy.selectedPool.selectedPoolSelectedSet // 要提交的人
                // store.commit(
                // 	'selectSource/CALLBACK_PARAMETER',
                // 	proxy.selectedPool.selectedPoolSelectedSet
                // ) //开启选人

                emit(
                    "callbackParameter",
                    state.selectedPool.selectedPoolSelectedSet
                )
                handerCancel()
            }
        }
        // 点击遮罩层或右上角叉或取消按钮
        const handerCancel = () => {
            // 已选池清空所有的数据
            onCheckSelectAllChange()
            // proxy.selectSourceParams.tabsTexts.forEach((v:any) => {
            //   proxy.selectedPool[`selestcheckAll${v.key}`] = false
            //   proxy.selectedPool[`selestIndeterminate${v.key}`] = false
            //   proxy.selectedPool[`selestList${v.key}`] = []
            // });
            // activeKey.value = 'teacher'
            state.sourceSearch = ""
            state.isSourceSearch = true
            const params = {
                modalVisible: false
            }
            store.commit("selectSource/GET_USER_INFOS", params)
        }
        // tree 点击事件
        const clickSelect = async(selectedIds: any, selectedItme: any) => {
            selectedKeys.value = [selectedItme.node.id]
            selectedItmes.value = selectedItme.node
            let departmentPeopleGather = selectedItme.node.children
            if (
                !proxy.selectSourceParams.checkPeople &&
                !departmentPeopleGather.length
            ) {
                return
            }
            // 如果是checkPeople true 则选人
            if (proxy.selectSourceParams.checkPeople) {
                // activeKey: teacher 老师， 2  学生
                if (activeKey.value === "teacher") {
                    departmentPeopleGather = await getFacultyList(
                        selectedIds[0]
                    )
                } else {
                    if (proxy.selectSourceParams.isParent) {
                        departmentPeopleGather = []
                        if (
                            selectedItme.node.type == 4 &&
                            !selectedItme.node.children.length
                        ) {
                            await getStudentList(
                                selectedIds[0],
                                selectedItme.node.children
                            )
                        }
                        !selectedItme.node.type &&
                            (departmentPeopleGather =
                                selectedItme.node.children)
                    } else {
                        departmentPeopleGather = []
                        if (selectedItme.node.type == 4) {
                            departmentPeopleGather = await getStudentList(
                                selectedIds[0]
                            )
                        } else {
                            return
                        }
                    }
                }
            }
            // 切换tab 时 将数据 分发到各自的数组中
            proxy.selectedPool[`selestList${activeKey.value}`] =
                departmentPeopleGather

            dataEcho(
                departmentPeopleGather,
                proxy.selectedPool.selectedPoolSelectedSet
            )
        }
        // 中间 被选池所有的数据
        const onCheckAllChange = (e: any) => {
            const isAll = proxy.selectedPool[`selestcheckAll${activeKey.value}`]
            const targetCheckedList = isAll
                ? proxy.selectedPool[`selestList${activeKey.value}`]
                : []
            proxy.selectedPool[`selestChecked${activeKey.value}`] =
                targetCheckedList
            proxy.selectedPool.selectedPoolSelectedSet = targetCheckedList
            proxy.selectedPool[`selestIndeterminate${activeKey.value}`] = false
        }
        // 已选池清空所有的数据
        const onCheckSelectAllChange = () => {
            // 删除 tabel 已选中的数据
            if (!proxy.isSourceSearch) {
                proxy.selectedPool.selectedPoolSelectedSet = []
                state.selectedRowKeys = []
                return
            }
            proxy.selectSourceParams.tabsTexts.forEach((v: any) => {
                proxy.selectedPool[`selestChecked${v.key}`] = []
            })
            proxy.selectedPool.selectedPoolSelectedSet = []
            proxy.selectedPool[`selestIndeterminate${activeKey.value}`] = false
            proxy.selectedPool[`selestcheckAll${activeKey.value}`] = false
        }

        const clickCheckWarp = ({ id }) => {
            const newData = []
            state.selectedPool.selectedPoolSelectedSet.forEach((v: any) => {
                v.id !== id && newData.push(v)
            })
            state.selectedPool.selectedPoolSelectedSet = newData
        }
        //  单个选择
        const handerChangeItem = (val: any, node: any) => {
            // 单选状态
            if (proxy.selectSourceParams.singleChoice) {
                val = val.length ? [val[val.length - 1]] : val
                proxy.selectedPool.selectedPoolSelectedSet = []
                proxy.selectedPool[`selestChecked${activeKey.value}`] = val
            } else {
                // 全选中的半选状态
                proxy.selectedPool[`selestIndeterminate${activeKey.value}`] =
                    !!val.length &&
                    val.length <
                        proxy.selectedPool[`selestList${activeKey.value}`]
                            .length
                // 是否全选中
                proxy.selectedPool[`selestcheckAll${activeKey.value}`] =
                    val.length ===
                    proxy.selectedPool[`selestList${activeKey.value}`].length
            }
            // 已选池中   老师 、 学生的混选集合
            const personnelAssembly = []
            proxy.selectSourceParams.tabsTexts.forEach((v: any) => {
                personnelAssembly.push(
                    ...state.selectedPool.selectedPoolSelectedSet,
                    ...state.selectedPool[`selestChecked${v.key}`]
                )
            })
            const newPersonnelAssembly = arrayDeduplication(
                personnelAssembly,
                "id"
                // proxy.selectSourceParams.isParent ? 'studentId' : 'id'
            )
            // 判断限制的个数
            if (
                proxy.selectSourceParams.checkPeople &&
                proxy.selectSourceParams.isNum &&
                newPersonnelAssembly.length > proxy.selectSourceParams.isNum
            ) {
                const newData = []
                newPersonnelAssembly.forEach((j: any, i: number) => {
                    if (i < proxy.selectSourceParams.isNum) newData.push(j)
                })
                state.selectedPool.selectedPoolSelectedSet = newData
                state.selectedPool[`selestChecked${activeKey.value}`] = newData
                proxy.$message.warning(
                    `最多只能选${proxy.selectSourceParams.isNum}个`
                )
                return
            }
            state.selectedPool.selectedPoolSelectedSet = newPersonnelAssembly
        }
        // 数组去重
        const arrayDeduplication = (data: any, id: String) => {
            const obj = {}
            const newData = []
            data.forEach((v: any) => {
                if (!obj[v[id]]) {
                    obj[v[id]] = v
                    newData.push(v)
                }
            })
            return newData
        }
        // 已选池单个删除的数据
        const handerSelectedDel = (val: any) => {
            const newData1: any[] = []
            const newData2: any[] = []
            // 删除 tabel 已选中的数据
            if (!proxy.isSourceSearch) {
                proxy.selectedPool.selectedPoolSelectedSet.forEach((v: any) => {
                    v.id !== val && newData1.push(v)
                })
                state.selectedRowKeys = newData1
                proxy.selectedPool.selectedPoolSelectedSet = newData1
                return
            }
            proxy.selectedPool[`selestChecked${activeKey.value}`].forEach(
                (v: any) => {
                    v.id !== val && newData1.push(v)
                }
            )
            proxy.selectedPool[`selestIndeterminate${activeKey.value}`] = true
            proxy.selectedPool[`selestcheckAll${activeKey.value}`] = false
            // 删除tab选中中的对象
            proxy.selectedPool[`selestChecked${activeKey.value}`] = newData1
            // 删除已选集合选中中的对象
            proxy.selectedPool.selectedPoolSelectedSet = [
                ...newData1,
                ...newData2
            ]
            state.selectedPool[`selestIndeterminate${activeKey.value}`] =
                state.selectedPool.selectedPoolSelectedSet.length > 0
        }
        // 搜索
        const onSourceSearch = async(e: any) => {
            const data = proxy.selectSourceParams[`treeData_${activeKey.value}`]
            const newData: any[] = []
            state.tableDataSource = []
            // 查选人
            if (proxy.selectSourceParams.checkPeople) {
                state.isSourceSearch = !e
                if (!selectedItmes.value.id) {
                    const item =
                        proxy.selectSourceParams[
                            `treeData_${activeKey.value}`
                        ][0]
                    selectedItmes.value = item
                }
                const personnels: any[] = []
                if (activeKey.value === "teacher") {
                    // 查老师
                    state.tableDataSource = await getFacultyList(
                        state.sourceSearch
                    )
                } else {
                    // 查学生
                    state.tableDataSource = await getStudentList(
                        state.sourceSearch
                    )
                }
                // personnels.forEach((v: any) => {
                //     const iswhether = v.name.includes(e)
                //     // v.showName = selectedItmes.value.name
                //     iswhether && state.tableDataSource.push(v)
                // })
            } else {
                recursiveQuery(data, e, newData)
                // 查部门、查班级
                proxy.selectedPool[`selestList${activeKey.value}`] = newData
            }
            dataEcho(
                state.tableDataSource,
                proxy.selectedPool.selectedPoolSelectedSet
            )
        }

        // 递归查询
        const recursiveQuery = (data: any[], name: string, newData: any[]) => {
            data.forEach((v: any) => {
                const isName = v.name.includes(name)
                if (isName) {
                    const newObj = JSON.parse(JSON.stringify(v))
                    delete newObj.children
                    newData.push(newObj)
                    // newData.push(v)
                }
                if (v.children.length) {
                    recursiveQuery(v.children, name, newData)
                }
            })
        }
        // 数据回显
        const dataEcho = (data: any, olData: any) => {
            const newData = []
            // olData: 已选框的数据   data：
            const id = "id"
            // const id = proxy.selectSourceParams.isParent ? 'studentId' : 'id'
            data &&
                data.length &&
                olData.length &&
                data.forEach((v: any) => {
                    const isCheced = olData.some((key: any) => {
                        if (key[id] == v[id]) {
                            key = v
                            return v
                        }
                    })
                    isCheced && newData.push(v)
                })
            if (newData.length) {
                proxy.selectedPool[`selestIndeterminate${activeKey.value}`] =
                    newData.length !== data.length
                proxy.selectedPool[`selestcheckAll${activeKey.value}`] =
                    newData.length == data.length
            } else {
                proxy.selectedPool[`selestcheckAll${activeKey.value}`] = false
                proxy.selectedPool[`selestIndeterminate${activeKey.value}`] =
                    false
            }
            if (!state.isSourceSearch) {
                // 搜索选人
                state.selectedRowKeys = newData
            } else {
                proxy.selectedPool[`selestChecked${activeKey.value}`] = newData
            }
        }

        // 清空搜索框的内容
        const handerClearSearch = () => {
            state.sourceSearch = ""
            state.isSourceSearch = true
            if (proxy.selectSourceParams.checkPeople) {
                state.tableDataSource = []
            } else {
                proxy.selectedPool[`selestList${activeKey.value}`] = []
            }
        }
        // tabs 切换
        const handerTabClick = (item: any) => {
            state.sourceSearch = ""
            state.tableDataSource = []
            state.selectedRowKeys = []
            activeKey.value = item.key
            fieldNames.value.title =
                item.key === "student" ? "showName" : "name"
            state.tableColumns[1].title =
                item.key === "student" ? "班级" : "部门"
            if (item.states) {
                // 选部门
                proxy.selectSourceParams.checkPeople = false
            } else {
                // 选人
                proxy.selectSourceParams.checkPeople = true
            }
        }
        // table
        const onSelectChange = (selectedRowKeys: any) => {
            state.selectedRowKeys = selectedRowKeys
            // proxy.selectedPool.selectedPoolSelectedSet = [
            //     ...state.selectedPool[`selestChecked${activeKey.value}`]
            // ]
            state.selectedPool[`selestChecked${activeKey.value}`] =
                selectedRowKeys
            const personnelAssembly = []
            proxy.selectSourceParams.tabsTexts.forEach((v: any) => {
                personnelAssembly.push(
                    // ...proxy.selectedPool.selectedPoolSelectedSet,
                    ...state.selectedPool[`selestChecked${v.key}`]
                )
            })
            proxy.selectedPool.selectedPoolSelectedSet = arrayDeduplication(
                personnelAssembly,
                "id"
            )
        }
        // computed
        const modalVisible = computed(() => {
            // 回显
            state.selectedPool.selectedPoolSelectedSet =
                store.state.selectSource.selectSourceObj.selectedPoolSelectedSet
            return store.state.selectSource.modalVisible
        })
        const selectSourceParams = computed(() => {
            const { tabsTexts } = store.state.selectSource.selectSourceObj
            if (tabsTexts && tabsTexts.length) {
                activeKey.value = tabsTexts[0].key
                tabsTexts.map((v: any) => {
                    // tab1被选数据
                    proxy.selectedPool[`selestList${v.key}`] = []
                    // 已选中数据
                    proxy.selectedPool[`selestChecked${v.key}`] = []
                    // 是否全选
                    proxy.selectedPool[`selestcheckAll${v.key}`] = false
                    // 是否半选
                    proxy.selectedPool[`selestIndeterminate${v.key}`] = false
                })
            }
            return store.state.selectSource.selectSourceObj
        })
        return {
            activeKey,
            fieldNames,
            modalVisible,
            handerCancel,
            selectedItmes,
            selectedKeys,
            handerTabClick,
            expandedKeys,
            clickSelect,
            onSourceSearch,
            onCheckAllChange,
            handerClearSearch,
            handerChangeItem,
            selectSourceParams,
            handerSourceSubmit,
            handerSelectedDel,
            onSelectChange,
            ...toRefs(state),
            onCheckSelectAllChange,
            olSelectedPoolSelectedSet,
            clickCheckWarp
        }
    }
})
</script>

<style lang="less">
.source-modal {
    .ant-modal-body {
        padding: 0 !important;

        .source-modal-top {
            padding: 0 20px;
            border-bottom: 1px solid @border-color-base;
        }

        .source-modal-reset {
            .ant-tabs-tab-btn {
                color: @text-color;
            }

            .ant-tabs-ink-bar {
                width: 0 !important;
            }
        }

        .source-modal-tops {
            padding: 12px 16px 0;
        }

        .ant-tabs-nav {
            margin: 0;

            &::before {
                border: none;
            }
        }

        .source-modal-content {
            max-height: 600px;

            .source-modal-search {
                padding: 0 20px 20px;
                border-bottom: 1px solid @border-color-base;
                display: flex;

                .clear-search {
                    color: @primary-color;
                }
            }

            // tree 被选池样式
            .source-modal-col-row-col,
            .source-modal-col {
                padding-top: 20px;
                border-right: 1px solid @border-color-base;
            }

            .source-modal-col-row-col {
                height: 500px;
                overflow: hidden auto;
            }

            // 中间 被选池样式
            .source-modal-col-row {
                padding-left: 20px;
            }

            .ant-radio-group,
            .ant-radio-wrapper,
            .ant-checkbox-group,
            .ant-checkbox-wrapper {
                display: block;
            }

            .ant-radio-wrapper,
            .ant-checkbox-wrapper {
                padding: 6px 10px 0;
                margin: 0;
                overflow: hidden;

                .ant-radio,
                .ant-checkbox {
                    float: right;
                }
            }

            span.ant-radio + * {
                vertical-align: super;
            }

            .check-all {
                font-weight: 800;

                .check-all-num {
                    font-size: 12px;
                    font-weight: normal;
                }
            }

            .customize-checkbox-group {
                position: relative;
                padding-left: 16px;

                .anticon-team {
                    position: absolute;
                    top: 10px;
                }

                &:hover {
                    background: #f6f6f6ff;
                }

                .ellipsis,
                .ant-checkbox + span {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    display: inline-block;
                    max-width: 260px;
                }
            }

            // 已选池样式
            .source-modal-ovh {
                height: 574px;
                padding: 14px 20px 0;
                overflow: hidden auto;

                .ant-checkbox + span {
                    max-width: 240px;
                }

                // 清空
                .select-check-all {
                    position: relative;
                    padding: 0;

                    .clear-all {
                        color: @primary-color;
                        padding: 0;
                        position: absolute;
                        top: -6px;
                        right: 0;
                    }

                    .ant-checkbox {
                        width: 34px;
                    }
                }

                .ant-checkbox-inner,
                .ant-checkbox-input {
                    opacity: 0;
                }

                .customize-checkbox-select-group {
                    position: relative;

                    &:hover .close-outlined {
                        display: block;
                    }

                    .close-outlined {
                        cursor: pointer;
                        color: red;
                        display: none;
                        position: absolute;
                        top: 10px;
                        right: 10px;
                    }

                    .ant-checkbox {
                        display: none;
                    }
                }
            }
        }

        // table
        .ant-table-cell {
            padding: 0;
            vertical-align: middle;
            text-align: center;
        }
    }

    .ant-table-row-expand-icon {
        display: none;
    }

    .ant-table-thead {
        height: 40px;
    }
}
</style>
