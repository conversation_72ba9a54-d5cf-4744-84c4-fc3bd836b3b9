<!--
 * @Descripttion: 选人 选部门 选班
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-28 13:33:41
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-02-27 18:18:11
 * const params = {modalVisible: false, selectSourceObj:{}}
 * store.commit('selectSource/GET_USER_INFOS', params)  //开启选人框
 * callbackParameter  // 确定提交
 *  isParent: false, //truen:  添加家长
 * selectSourceParams: {
 *  optionalClass:{key: '', value: 2}  // 指定可选级 对象 key: 类型,字符(例如： 班级：type) ,value:值， 数组(例如： 班级：[1, 2, 3])
 *  singleChoice  // 是否单选  单选 :true ,多选 ：false
 *  checkPeople: boolean,//查人 true  查部门、班级 false
 *  treeData_teacher: [],//老师 数组（tree 数据）
 *  treeData_student:[],//学生 数组（tree 数据）
 *  ... //更多
 *  tabsTexts: [ 数组（多个tab 切换按钮）
 *  { tab: '老师', key: 'teacher' },
 *  { tab: '学生', key: 'student' }
 *   ]
 * }
-->
<template>
    <a-modal
        class="source-modal"
        width="1000px"
        v-model:visible="modalVisible"
        :maskClosable="false"
        :confirm-loading="modalConfirmLoading"
        @cancel="handerCancel"
        @ok="handerSourceSubmit"
    >
        {{ activeKey.key }}
        <a-tabs
            class="source-modal-top"
            v-model:activeKey="activeKey.key"
            size="large"
            :class="{
                'source-modal-tops': selectSourceParams.tabsTexts.length > 1,
                'source-modal-reset': selectSourceParams.tabsTexts.length < 2
            }"
            :type="selectSourceParams.tabsTexts.length > 1 ? 'card' : 'line'"
            @change="handerTabPane"
        >
            <a-tab-pane v-for="itme in selectSourceParams.tabsTexts" :key="itme" :tab="itme.tab" />
        </a-tabs>
        <a-row class="source-modal-content">
            <a-col class="source-modal-col" :span="16">
                <!-- 搜索框 -->
                <div class="source-modal-search">
                    <a-input-search
                        v-model:value="sourceSearch"
                        placeholder="请输入关键字"
                        enter-button
                        @search="onSourceSearch"
                    />
                    <a-button
                        v-if="sourceSearch"
                        class="clear-search"
                        type="link"
                        @click="handerClearSearch"
                    >取消</a-button>
                </div>
                <!-- 搜索后的table表格 -->
                <a-table
                    :rowKey="(item: any) => item"
                    v-if="!isSourceSearch"
                    :columns="tableColumns"
                    :dataSource="tableDataSource"
                    :row-selection="{
                        selectedRowKeys: selectedRowKeys,
                        onChange: onSelectChange
                    }"
                />
                <a-row v-else class="source-modal-col-row">
                    <a-col class="source-modal-col-row-col" style="padding: 0" :span="12">
                        <!-- 左侧的tree树 -->
                        <a-tree
                            v-model:expandedKeys="
                                selectSourceParams.defaultExpand
                            "
                            v-model:selectedKeys="selectedKeys"
                            :tree-data="
                                selectSourceParams[`treeData_${activeKey.key}`]
                            "
                            blockNode
                            :fieldNames="fieldNames"
                            :selectable="true"
                            @select="clickSelect"
                        >
                            <template #title="{ dataRef }">
                                <team-outlined class="icon-yellow" />
                                {{ dataRef.name }}
                            </template>
                        </a-tree>
                        <a-empty
                            v-if="
                                !selectSourceParams[`treeData_${activeKey.key}`]
                                    .length
                            "
                            image="/image//empty.png"
                            :image-style="{
                                marginTop: '114px',
                                height: '120px'
                            }"
                        ></a-empty>
                    </a-col>

                    <!-- 中间的被选池 -->
                    <a-col
                        :span="12"
                        class="source-modal-col-row-col"
                        style="padding: 0; border: none"
                    >
                        <p
                            class="check-all"
                            style="padding: 6px 15px"
                            v-if="selectSourceParams.singleChoice"
                        >
                            全选
                            <span class="check-all-num">
                                共{{
                                    selectedPool[`selestList${activeKey.key}`]
                                        .length
                                }}
                                {{
                                    selectSourceParams.checkPeople ? '人' : '组'
                                }}
                            </span>
                        </p>
                        <a-checkbox
                            v-else
                            class="check-all"
                            v-model:checked="
                                selectedPool[`selestcheckAll${activeKey.key}`]
                            "
                            :disabled="
                                isNum ||
                                !selectedPool[`selestList${activeKey.key}`].length
                            "
                            :indeterminate="
                                selectedPool[`selestIndeterminate${activeKey.key}`]
                            "
                            @change="onCheckAllChange"
                        >
                            全选
                            <span class="check-all-num">
                                共{{
                                    selectedPool[`selestList${activeKey.key}`]
                                        .length
                                }}{{ selectSourceParams.checkPeople ? '人' : '组' }}
                            </span>
                        </a-checkbox>
                        <a-checkbox-group
                            v-model:value="selectedPool[`selestChecked${activeKey.key}`]"
                            @change="handerChangeItem"
                        >
                            <div
                                class="customize-checkbox-group"
                                v-for="item in selectedPool[`selestList${activeKey.key}`]"
                                :key="item.id"
                            >
                                <team-outlined class="icon-yellow" />
                                <a-checkbox
                                    :value="item"
                                    :disabled="
                                        selectSourceParams.optionalClass &&
                                            JSON.stringify(
                                                selectSourceParams.optionalClass
                                            ) !== '{}'
                                            ? selectSourceParams.optionalClass.value.includes(
                                                item[
                                                selectSourceParams
                                                    .optionalClass.key
                                                ]
                                            )
                                            : false
                                    "
                                >{{ item.name }}</a-checkbox>
                            </div>
                        </a-checkbox-group>

                        <a-empty
                            v-if="
                                !selectedPool[`selestList${activeKey.key}`].length
                            "
                            image="/image//empty.png"
                            :image-style="{
                                marginTop: '86px',
                                height: '120px'
                            }"
                        ></a-empty>
                    </a-col>
                </a-row>
            </a-col>

            <!-- 右侧的已选池 -->
            <a-col :span="8" class="source-modal-ovh">
                <a-checkbox
                    class="check-all select-check-all"
                    v-model:checked="selectCheckAll"
                    :disabled="!selectedPool.selectedPoolSelectedSet.length"
                    :indeterminate="selectIndeterminate"
                    @change="onCheckSelectAllChange"
                >
                    已选
                    <span class="check-all-num">
                        共{{
                            selectedPool.selectedPoolSelectedSet.length
                        }}{{
    selectSourceParams.checkPeople ? '人' : '组'
}}
                    </span>
                    <a-button class="clear-all" type="link">清空</a-button>
                </a-checkbox>
                <a-checkbox-group v-model:value="selectedDataSet">
                    <div
                        class="customize-checkbox-group customize-checkbox-select-group"
                        v-for="item in selectedPool.selectedPoolSelectedSet"
                        :key="
                            selectSourceParams.isParent
                                ? item.studentId
                                : item.id
                        "
                    >
                        <team-outlined class="icon-yellow" />
                        <a-checkbox :value="item">{{ item.name }}</a-checkbox>
                        <close-outlined
                            class="close-outlined"
                            @click="
                                handerSelectedDel(
                                    selectSourceParams.isParent
                                        ? item.studentId
                                        : item.id
                                )
                            "
                        />
                    </div>
                </a-checkbox-group>
                <a-empty
                    v-if="!selectedPool.selectedPoolSelectedSet.length"
                    image="/image/empty.png"
                    :image-style="{ marginTop: '152px', height: '120px' }"
                ></a-empty>
            </a-col>
        </a-row>
    </a-modal>
</template>

<script lang='ts'>
import type { TreeProps } from 'ant-design-vue'
import { useStore } from '@/store/index'
import { useRoute, useRouter } from 'vue-router'

import api from '@/api'
import {
    ref,
    watch,
    toRefs,
    computed,
    reactive,
    defineComponent,
    getCurrentInstance
} from 'vue'

export default defineComponent({
    name: 'SelectSource',
    props: {
        modalConfirmLoading: {
            type: Boolean,
            default: false
        },
        isNum: {
            type: Number,
            default: 0
        }
    },
    emits: ['callbackParameter'],
    components: {},
    setup (props, { emit }) {
        const { proxy } = getCurrentInstance() as any
        const store = useStore()
        const route = useRoute()
        // 是否显示模态框
        // const modalVisible = ref<boolean>(false)
        const expandedKeys = ref<string[]>(['1458257624041546325'])
        const selectedKeys = ref<string[]>([])
        const olSelectedPoolSelectedSet = ref<string[]>([])
        const selectedItmes = ref<object>({})
        // const personnelAssembly = ref([])
        const activeKey = ref({ tab: '教职工', key: 'teacher' })
        // const activeKey = ref<string>('teacher')
        const fieldNames: TreeProps['fieldNames'] = {
            children: 'children',
            title: 'name',
            key: 'id'
        }

        const state = reactive({
            sourceSearch: '',
            // table
            tableDataSource: [],
            selectedRowKeys: [],
            tableColumns: [
                {
                    title: '姓名',
                    dataIndex: 'name'
                },
                {
                    title: activeKey.value.key == 'teacher' ? '部门' : '班级',
                    dataIndex: 'showName'
                }
            ],
            // 中间被选池
            selectedPool: {
                // 已选池 tab1、tab2选中集合
                selectedPoolSelectedSet: []
            },
            // 是否搜索
            isSourceSearch: true,
            // 已选池
            selectCheckAll: false,
            selectIndeterminate: false,
            selectedDataSet: []
        })
        // 获取教职工列表
        const getFacultyList = (deptId: any) => {
            const params = { pageNo: 1, pageSize: 100, deptId }
            return new Promise((resolve, reject) => {
                proxy.$defHttp
                    .post(api.facultyList, params)
                    .then((res: any) => {
                        const { code, data } = res
                        resolve(data.list)
                    })
                    .catch((err: any) => {
                        console.log('error', err)
                    })
            })
        }
        const handerTabPane = (itme: any) => {
        }
        // 获取学生家长列表
        const getStudentList = (classesId: any, newData?: any) => {
            return new Promise((resolve, reject) => {
                proxy.$defHttp
                    .post(api.studentlistSearch, { classesId })
                    .then((res: any) => {
                        const { code, data } = res
                        resolve(data)
                        newData && data.forEach((v: any) => {
                            v.children = v.elterns
                            newData.push(v)
                        })
                    })
                    .catch((err: any) => {
                        console.log('error', err)
                    })
            })
        }
        // 模态框 确定
        const handerSourceSubmit = () => {
            if (proxy.selectedPool.selectedPoolSelectedSet.length) {
                proxy.selectedPool.selectedPoolSelectedSet // 要提交的人
                emit(
                    'callbackParameter',
                    state.selectedPool.selectedPoolSelectedSet
                )
                handerCancel()
            }
        }
        // 点击遮罩层或右上角叉或取消按钮
        const handerCancel = () => {
            // 已选池清空所有的数据
            onCheckSelectAllChange()
            // proxy.selectSourceParams.tabsTexts.forEach((v:any) => {
            //   proxy.selectedPool[`selestcheckAll${v.key}`] = false
            //   proxy.selectedPool[`selestIndeterminate${v.key}`] = false
            //   proxy.selectedPool[`selestList${v.key}`] = []
            // });
            const params = {
                modalVisible: false
            }
            store.commit('selectSource/GET_USER_INFOS', params)
        }
        // tree 点击事件
        const clickSelect = async (selectedIds: any, selectedItme: any) => {
            proxy.selectedItmes = selectedItme.node
            let departmentPeopleGather = selectedItme.node.children
            if (
                !proxy.selectSourceParams.checkPeople &&
                !departmentPeopleGather.length
            ) { return }
            // 如果是checkPeople true 则选人
            // activeKey ==

            if (proxy.selectSourceParams.checkPeople) {
                // activeKey: teacher 老师， 2  学生
                if (activeKey.value.key == 'teacher') {
                    departmentPeopleGather = await getFacultyList(selectedIds[0])
                } else {
                    if (proxy.selectSourceParams.isParent) {
                        departmentPeopleGather = []
                        if (
                            selectedItme.node.type == 4 &&
                            !selectedItme.node.children.length
                        ) {
                            await getStudentList(
                                selectedIds[0],
                                selectedItme.node.children
                            )
                        }
                        !selectedItme.node.type &&
                            (departmentPeopleGather = selectedItme.node.children)
                    } else {
                        departmentPeopleGather = []
                        if (
                            selectedItme.node.type == 4 &&
                            !selectedItme.node.children.length
                        ) {
                            departmentPeopleGather = await getStudentList(selectedIds[0])
                        } else { return }
                    }
                }
            }
            // 切换tab 时 将数据 分发到各自的数组中
            proxy.selectedPool[`selestList${activeKey.value.key}`] =
                departmentPeopleGather
            dataEcho(
                proxy.selectedPool[`selestList${activeKey.value.key}`],
                proxy.selectedPool.selectedPoolSelectedSet
            )
        }
        // 中间 被选池所有的数据
        const onCheckAllChange = (e: any) => {
            const isAll = proxy.selectedPool[`selestcheckAll${activeKey.value.key}`]
            const targetCheckedList = isAll
                ? proxy.selectedPool[`selestList${activeKey.value.key}`]
                : []
            proxy.selectedPool[`selestChecked${activeKey.value.key}`] =
                targetCheckedList
            proxy.selectedPool.selectedPoolSelectedSet = targetCheckedList
            proxy.selectedPool[`selestIndeterminate${activeKey.value.key}`] = false
        }
        // 已选池清空所有的数据
        const onCheckSelectAllChange = () => {
            // 删除 tabel 已选中的数据
            if (!proxy.isSourceSearch) {
                proxy.selectedPool.selectedPoolSelectedSet = []
                proxy.selectedRowKeys = []
                return
            }
            proxy.selectSourceParams.tabsTexts.forEach((v: any) => {
                proxy.selectedPool[`selestChecked${v.key}`] = []
            })
            state.selectedPool.selectedPoolSelectedSet = []
            proxy.selectedPool[`selestIndeterminate${activeKey.value.key}`] = false
            proxy.selectedPool[`selestcheckAll${activeKey.value.key}`] = false
        }
        //  单个选择
        const handerChangeItem = (val: any, node: any) => {
            // 单选状态
            // proxy.selectedPool[`selestChecked${activeKey.value.key}`]
            if (props.isNum && val.length > props.isNum) {
                const newData = []
                proxy.selectedPool[`selestChecked${activeKey.value.key}`].forEach(
                    (element, idx) => {
                        if (idx > 2) return
                        newData.push(element)
                    }
                )
                proxy.selectedPool[`selestChecked${activeKey.value.key}`] = newData
                return
            }
            if (proxy.selectSourceParams.singleChoice) {
                val = val.length ? [val[val.length - 1]] : val
                proxy.selectedPool.selectedPoolSelectedSet = []
                proxy.selectedPool[`selestChecked${activeKey.value.key}`] = val
            } else {
                // 全选中的半选状态
                proxy.selectedPool[`selestIndeterminate${activeKey.value.key}`] =
                    !!val.length &&
                    val.length <
                    proxy.selectedPool[`selestList${activeKey.value.key}`]
                        .length
                // 是否全选中
                proxy.selectedPool[`selestcheckAll${activeKey.value.key}`] =
                    val.length ===
                    proxy.selectedPool[`selestList${activeKey.value.key}`].length
            }
            // 已选池中   老师 、 学生的混选集合

            const personnelAssembly = []
            proxy.selectSourceParams.tabsTexts.forEach((v: any) => {
                personnelAssembly.push(
                    ...state.selectedPool.selectedPoolSelectedSet,
                    ...state.selectedPool[`selestChecked${v.key}`]
                )
            })

            proxy.selectedPool.selectedPoolSelectedSet = arrayDeduplication(
                personnelAssembly,
                proxy.selectSourceParams.isParent ? 'studentId' : 'id'
            )
            // proxy.selectedPool.selectedPoolSelectedSet = arrayDeduplication(
            //     olSelectedPoolSelectedSet.value,
            //     proxy.selectSourceParams.isParent ? 'studentId' : 'id'
            // )
            // 测试
            // proxy.selectedPool.selectedPoolSelectedSet = personnelAssembly
        }
        // 数组去重
        const arrayDeduplication = (data: any, id: String) => {
            const obj = {}
            const newData = []
            data.forEach((v: any) => {
                if (!obj[v[id]]) {
                    obj[v[id]] = v
                    newData.push(v)
                }
            })
            return newData
        }
        // 已选池单个删除的数据
        const handerSelectedDel = (val: any) => {
            const newData1: any[] = []
            const newData2: any[] = []
            // 删除 tabel 已选中的数据
            if (!proxy.isSourceSearch) {
                proxy.selectedPool.selectedPoolSelectedSet.forEach((v: any) => {
                    v.id !== val && newData1.push(v)
                })
                proxy.selectedRowKeys = newData1
                proxy.selectedPool.selectedPoolSelectedSet = newData1
                return
            }
            proxy.selectedPool[`selestChecked${activeKey.value.key}`].forEach(
                (v: any) => {
                    v.id !== val && newData1.push(v)
                }
            )
            proxy.selectedPool[`selestIndeterminate${activeKey.value.key}`] = true
            proxy.selectedPool[`selestcheckAll${activeKey.value.key}`] = false
            // 删除tab选中中的对象
            proxy.selectedPool[`selestChecked${activeKey.value.key}`] = newData1
            // 删除已选集合选中中的对象
            proxy.selectedPool.selectedPoolSelectedSet = [
                ...newData1,
                ...newData2
            ]
            state.selectedPool[`selestIndeterminate${activeKey.value.key}`] = state.selectedPool.selectedPoolSelectedSet.length > 0
        }
        // 搜索
        const onSourceSearch = async (e: any) => {
            const data = proxy.selectSourceParams[`treeData_${activeKey.value.key}`]
            const newData: any[] = []
            // 查选人
            if (proxy.selectSourceParams.checkPeople) {
                state.isSourceSearch = !e
                if (!proxy.selectedItmes.id) {
                    const item =
                        proxy.selectSourceParams[
                            `treeData_${activeKey.value.key}`
                        ][0]
                    proxy.selectedItmes = item
                }
                let personnels: any[] = []
                if (activeKey.value.key === 'teacher') {
                    // 查老师
                    personnels = await getFacultyList(proxy.selectedItmes.id)
                    proxy.tableColumns[1].title = '部门'
                } else {
                    // 查学生
                    personnels = await getStudentList(proxy.selectedItmes.id)
                    proxy.tableColumns[1].title = '班级'
                }
                personnels.forEach((v: any) => {
                    const iswhether = v.name.includes(e)
                    v.showName = proxy.selectedItmes.name
                    iswhether && proxy.tableDataSource.push(v)
                })
            } else {
                recursiveQuery(data, e, newData)
                // 查部门、查班级
                proxy.selectedPool[`selestList${activeKey.value.key}`] = newData
            }
        }

        // 递归查询
        const recursiveQuery = (data: any[], name: string, newData: any[]) => {
            data.forEach((v: any) => {
                const isName = v.name.includes(name)
                if (isName) {
                    const newObj = JSON.parse(JSON.stringify(v))
                    delete newObj.children
                    newData.push(newObj)
                    // newData.push(v)
                }
                if (v.children.length) {
                    recursiveQuery(v.children, name, newData)
                }
            })
        }
        // 数据回显
        const dataEcho = (data: any, olData: any) => {
            const newData = []
            // olData: 已选框的数据   data：
            const id = proxy.selectSourceParams.isParent ? 'studentId' : 'id'
            data.length &&
                olData.length &&
                data.forEach((v: any) => {
                    const isCheced = olData.some((key: any) => key[id] == v[id])
                    isCheced && newData.push(v)
                })
            if (newData.length) {
                proxy.selectedPool[`selestIndeterminate${activeKey.value.key}`] =
                    newData.length !== data.length
                proxy.selectedPool[`selestcheckAll${activeKey.value.key}`] =
                    newData.length == data.length
            } else {
                proxy.selectedPool[`selestcheckAll${activeKey.value.key}`] = false
                proxy.selectedPool[`selestIndeterminate${activeKey.value.key}`] =
                    false
            }
            proxy.selectedPool[`selestChecked${activeKey.value.key}`] = newData
        }
        // 清空搜索框的内容
        const handerClearSearch = () => {
            state.sourceSearch = ''
            state.isSourceSearch = true
            if (proxy.selectSourceParams.checkPeople) {
                proxy.tableDataSource = []
            } else {
                proxy.selectedPool[`selestList${activeKey.value.key}`] = []
            }
        }
        // table
        const onSelectChange = (selectedRowKeys: any) => {
            state.selectedRowKeys = selectedRowKeys
            proxy.selectedPool.selectedPoolSelectedSet = selectedRowKeys
        }
        // computed
        const modalVisible = computed(() => {
            // const select = [{
            //     id: "1496785294207246337",
            //     joinDate: "2009-01-08",
            //     name: "罗哈哈-开除12",
            //     phone: "17811111118",
            //     status: 9
            // }, {
            //     id: "1496785289257967618",
            //     jobNumber: "",
            //     joinDate: "2009-01-05",
            //     name: "罗哈哈-调出1",
            //     phone: "17811111115"
            // }, {
            //     icCardNo: "232323",
            //     id: "1496766243027369986",
            //     idCard: "3241312312321312312",
            //     idType: 1,
            //     isChild: 0,
            //     isFlow: 1,
            //     name: "发电房"
            // }]

            // 回显
            // state.selectedPool.selectedPoolSelectedSet = select
            return store.state.selectSource.modalVisible
        })
        const selectSourceParams = computed(() => {
            const { tabsTexts } = store.state.selectSource.selectSourceObj
            if (tabsTexts && tabsTexts.length) {
                activeKey.value.key = tabsTexts[0].key
                tabsTexts.map((v: any) => {
                    // tab1被选数据
                    proxy.selectedPool[`selestList${v.key}`] = []
                    // 已选中数据
                    proxy.selectedPool[`selestChecked${v.key}`] = []
                    // 是否全选
                    proxy.selectedPool[`selestcheckAll${v.key}`] = false
                    // 是否半选
                    proxy.selectedPool[`selestIndeterminate${v.key}`] = false
                })
            }
            return store.state.selectSource.selectSourceObj
        })
        return {
            activeKey,
            fieldNames,
            modalVisible,
            handerCancel,
            selectedItmes,
            selectedKeys,
            handerTabPane,
            expandedKeys,
            clickSelect,
            onSourceSearch,
            onCheckAllChange,
            handerClearSearch,
            handerChangeItem,
            selectSourceParams,
            handerSourceSubmit,
            handerSelectedDel,
            onSelectChange,
            ...toRefs(state),
            onCheckSelectAllChange,
            olSelectedPoolSelectedSet
        }
    }
})
</script>

<style lang="less">
.source-modal {
    .ant-modal-body {
        padding: 0 !important;
        .source-modal-top {
            padding: 0 20px;
            border-bottom: 1px solid @border-color-base;
        }
        .source-modal-reset {
            .ant-tabs-tab-btn {
                color: @text-color;
            }
            .ant-tabs-ink-bar {
                width: 0 !important;
            }
        }
        .source-modal-tops {
            padding: 12px 16px 0;
        }
        .ant-tabs-nav {
            margin: 0;
            &::before {
                border: none;
            }
        }
        .source-modal-content {
            max-height: 600px;
            .source-modal-search {
                padding: 0 20px 20px;
                border-bottom: 1px solid @border-color-base;
                display: flex;
                .clear-search {
                    color: @primary-color;
                }
            }
            // tree 被选池样式
            .source-modal-col-row-col,
            .source-modal-col {
                padding-top: 20px;
                border-right: 1px solid @border-color-base;
            }
            .source-modal-col-row-col {
                height: 500px;
                overflow: hidden auto;
            }

            // 中间 被选池样式
            .source-modal-col-row {
                padding-left: 20px;
            }
            .ant-radio-group,
            .ant-radio-wrapper,
            .ant-checkbox-group,
            .ant-checkbox-wrapper {
                display: block;
            }
            .ant-radio-wrapper,
            .ant-checkbox-wrapper {
                padding: 6px 10px 0;
                margin: 0;
                overflow: hidden;
                .ant-radio,
                .ant-checkbox {
                    float: right;
                    margin-top: 4px;
                }
            }
            span.ant-radio + * {
                vertical-align: super;
            }
            .check-all {
                font-weight: 800;
                .check-all-num {
                    font-size: 12px;
                    font-weight: normal;
                }
            }
            .customize-checkbox-group {
                position: relative;
                padding-left: 16px;
                .anticon-team {
                    position: absolute;
                    top: 8px;
                }
                &:hover {
                    background: #f6f6f6ff;
                }
                .ellipsis,
                .ant-checkbox + span {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    display: inline-block;
                    max-width: 260px;
                }
            }
            // 已选池样式
            .source-modal-ovh {
                height: 574px;
                padding: 14px 20px 0;
                overflow: hidden auto;
                .ant-checkbox + span {
                    max-width: 240px;
                }
                // 清空
                .select-check-all {
                    position: relative;
                    padding: 0;
                    .clear-all {
                        color: @primary-color;
                        padding: 0;
                        position: absolute;
                        top: -6px;
                        right: 0;
                    }
                    .ant-checkbox {
                        width: 34px;
                    }
                }
                .ant-checkbox-inner,
                .ant-checkbox-input {
                    opacity: 0;
                }
                .customize-checkbox-select-group {
                    position: relative;
                    &:hover .close-outlined {
                        display: block;
                    }
                    .close-outlined {
                        cursor: pointer;
                        color: red;
                        display: none;
                        position: absolute;
                        top: 10px;
                        right: 10px;
                    }
                    .ant-checkbox {
                        display: none;
                    }
                }
            }
        }
        // table
        .ant-table-cell {
            padding: 0;
            vertical-align: middle;
            text-align: center;
        }
    }
    .ant-table-row-expand-icon {
        display: none;
    }
}
</style>
