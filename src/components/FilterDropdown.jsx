/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-02-17 09:17:57
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-05-20 11:27:45
 */
// 封装的filterDropdown函数，
export const getColumnCheckboxGroupFilterDropdown = (data) => {
    const { filters, selectedKeys, setSelectedKeys, confirm } = data
    const radioGroupStyle = {
        display: 'flex',
        color: 'red',
        padding: '16px 10px',
        flexDirection: 'column',
        // alignItems: 'center'
        overflow: 'hidden auto',
        maxHeight: '222px'
    }
    const radioStyle = {
        padding: '5px 0',
        marginRight: 0
    }
    const onChange = (e) => {
        setSelectedKeys([e.target.value])
        confirm()
        // clearFilters()
    }
    return (
        <div className="filter-content">
            <a-radio-group
                className="filter-group"
                value={selectedKeys}
                style={radioGroupStyle}
                onChange={onChange}
            >
                {filters?.map((v) => (
                    <a-radio value={v.value} style={radioStyle} key={v.value}>
                        {v.text}
                    </a-radio>
                ))}
            </a-radio-group>
        </div>
    )
}
