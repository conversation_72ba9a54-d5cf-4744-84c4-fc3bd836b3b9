/*
 * @Descripttion: 上传到oss
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-08-20 16:34:19
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-07-15 13:59:40
 */

import uploadFile from './ydutils'

export const getUploadFileOSSpath = (file, config, progressCallback) => {
    return new Promise((resolve, reject) => {
        uploadFile(file, config, progressCallback)
            .then((url) => {
                resolve({ code: 0, url })
            })
    })
}
