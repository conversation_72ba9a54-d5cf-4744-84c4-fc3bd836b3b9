/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-04-21 15:12:28
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-04-22 15:07:50
 */
import { useStore } from "vuex";
import { ls } from "@/utils/util";
import config from "@/config/config.js";
const { ACCESS_TOKEN } = config;
const token = ls.get(ACCESS_TOKEN);

const store = useStore();

class WebSocketClient {
    constructor(config) {
        const defautConfig = {
            url: import.meta.env.VITE_BASE_API_SOCKET,
            port: "3000",
            protocol: "wss",
            timeInterval: 30 * 1000,
        };
        /* eslint-disable */
        const finalConfig = { ...defautConfig, ...config };
        /* eslint-disable */
        this.ws = {};
        this.port = finalConfig.port;
        this.url = finalConfig.url;
        this.protocol = finalConfig.protocol;
        this.handle = null;
        this.timeInterval = finalConfig.timeInterval;
    }
    init() {
        this.ws = new WebSocket(`${this.url}`, [token]);
        this.ws.onopen = this.onOpen;
        this.ws.onmessage = (msg) => this.onMessage(msg);
        this.ws.onclose = () => this.onClose();
        this.ws.onerror = () => this.onError();
    }
    send(msg) {
        this.ws.send(msg);
    }
    onOpen() {
        this.send(
            JSON.stringify({
                event: "auth",
                msg: token,
            })
        );
    }
    onMessage(event) {
        if (this.isShow) {
            return;
        }
        let obj = JSON.parse(event.data);
        /* eslint-disable */
        switch (obj.event) {
            case "noauth":
                console.log("webSocket鉴权失败");
                break;
            case "otu":
                console.log("xxx退出系统");
                break;
            case "heartbeat":
                this.checkServer();
                this.send(
                    JSON.stringify({
                        event: "heartbeat",
                        message: "pong",
                    })
                );
                break;
            default:
                store.dispatch(obj.event, obj);
                break;
        }
        /* eslint-disable */
    }
    onClose() {
        // console.log("webSocket已关闭");
        this.ws.close();
    }
    onError() {
        console.log(`webSocket连接失败, 正在重新连接...`);
        setTimeout(() => {
            this.init();
        }, 2000);
    }
    // 断线重连
    checkServer() {
        const _this = this;
        this.handle = setTimeout(() => {
            _this.onClose();
            setTimeout(() => {
                _this.init();
            }, 1000);
        }, this.timeInterval + 1000);
    }
}
export default WebSocketClient;
