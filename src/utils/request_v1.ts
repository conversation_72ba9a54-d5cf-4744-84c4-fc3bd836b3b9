/*
 * @Descripttion: 网络请求配置
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-30 15:06:28
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-05-06 14:49:55
 */
import axios from 'axios'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import qs from 'qs'

import { ls } from '@/utils/util'
import config from '@/config/config.js'
const { ACCESS_TOKEN, ACCESS_REFRESHTOKEN } = config

const router = useRouter()

// 正在进行中的请求列表
const reqList = []

class Http {
    constructor(options = {}) {
        // 任务列表
        this.taskList = []
        // 缓存列表
        this.cachMap = new Map()
    }

    // 添加任务
    addTask(config, cancelToken) {
        this.taskList.push({
            original: `${config.url}&${config.method}`,
            cancelToken
        })
    }

    // 删除任务
    deleteTask(config, start, cancelToken) {
        let cancel = false
        for (const i in this.taskList) {
            if (
                this.taskList[i].original == `${config.url}&${config.method}`
            ) {
                this.taskList[i].cancelToken('取消请求')
                this.taskList.splice(i, 1)
                cancel = true
                break
            }
            if (!cancel && start) {
                this.deleteCach(config, cancelToken)
            }
        }
    }

    /** 创建key */
    createKey(config) {
        let str = ''
        config.url && (str += config.url)
        if (config.method) {
            str += ',method:' + config.method
            if (config.method === 'get') {
                str += ',data:' + qs.stringify(config.params) + ''
            } else {
                str += ',data:' + config.data
            }
        }
        return str
    }

    /** 删除缓存 */
    deleteCach(config, cancelToken) {
        const cachMap = this.cachMap
        const key = this.createKey(config)
        const now = new Date().getTime()
        const cach = cachMap.get(key) || {}
        if (cach && cach.expirationTime && now <= cach.deadline && cach.data) {
            cach.cach = true
            cancelToken(cach.data)
        }
    }

    /** 新增缓存 */
    addCach(config, cancel) {
        const key = this.createKey(config)
        const expirationTime = config.headers.expirationTime || 0
        if (expirationTime) {
            this.cachMap.set(key, {
                expirationTime,
                deadline: new Date().getTime() + expirationTime,
                data: '',
                cancel
            })
        }
    }

    /** 更新缓存 */
    updateCach(res) {
        if (!res || !res.config) {
            return false
        }
        const key = this.createKey(res.config)
        const oldVal = this.cachMap.get(key)
        if (!oldVal) {
            return false
        }
        this.cachMap.set(key, {
            expirationTime: oldVal.expirationTime,
            deadline: oldVal.deadline,
            data: res
        })
    }

    // 错误处理
    handleError() { }

    // 消息提示
    tipMessage() { }

    // 重新登录
    toLogin() { }

    // 取消请求
    cancel() { }

    // 二次重发
    secondaryRepeat() { }
}

const http = new Http()

const service = axios.create({
    baseURL: import.meta.env.VITE_BASE_API as string,
    timeout: 50000,
    // headers: { 'Content-Type': 'application/json' },
    // headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
})

//
const instance = axios.create({
    baseURL: import.meta.env.VITE_BASE_API as string,
    timeout: 5000,
})

const toLogin = () => {
    localStorage.clear();
    sessionStorage.clear()
    window.location.reload()
    setTimeout(() => {
        router.replace({
            path: '/login',
            query: {
                redirect: router.currentRoute?.fullPath
            }
        })
    }, 100)
}

function debounce(func, wait = 1000) {
    let timer = null
    return function () {
        const context: any = this
        const args = arguments

        if (timer) clearTimeout(timer)

        timer = setTimeout(() => {
            func.apply(context, args)
        }, wait)
    }
}

const tip = (msg: string) => {
    message.error(msg)
}

const errorHandle = async (status: number, err: any) => {
    /* eslint-disable */
    // 状态码判断
    switch (status) {
        // 401: 未授权 或未登录状态，跳转登录页 或者重新获取token，二次重发
        case 401:
            tip(`${err?.data?.msg || err}`)
            toLogin()
            break
        // 403 token过期
        // 清除token并跳转登录页
        case 403:
            // eslint-disable-next-line indent
            debounce(tip(`${err.data.msg}`))
            ls.remove(ACCESS_TOKEN)
            setTimeout(() => {
                toLogin()
            }, 1000)
            break
        // 404请求不存在
        case 404:
            debounce(tip('请求的资源不存在 404'))
            // 404页面
            break
        case 500:
            debounce(tip('系统异常，请稍后再试'))
            // 404页面
            break
        default:
            debounce(tip(err.message || err.data?.message || err))
    }
    /* eslint-disable */
}

// 添加请求拦截器
service.interceptors.request.use(
    (config) => {
        const token = ls.get(ACCESS_TOKEN)
        if (token) {
            config.headers.common['Authorization'] = `Bearer ${token}`
        }
        config.headers.expirationTime = ''
        config.cancelToken = new axios.CancelToken((c) => {
            // 删除任务
            http.deleteTask(config, true, c)
            // // 添加任务
            http.addTask(config, c)
            // // 添加缓存
            http.addCach(config, c)
        })
        return config
    },
    (error) => Promise.reject(error)
)

// 添加响应拦截器
service.interceptors.response.use(
    (res) => {
        http.deleteTask(res.config, false)
        http.updateCach(res)

        if (res.status === 200) {
            if (res.data.code === 0) {
                return Promise.resolve(res.data)
            } else if (res.request.responseType == 'arraybuffer') {
                return Promise.resolve(res.data)
            } else {
                errorHandle(res.data.code, res.data.message)
            }
            return Promise.reject(res)
        } else {
            return Promise.reject(res)
        }
    },
    (error) => {
        const { response } = error
        if (response) {
            // 请求已发出，但是不在2xx的范围
            errorHandle(response.status, response)
        } else {
            message.error(error.message || '网络超时')
        }
    }
)

export default service
