import axios from "axios";
import { message } from "ant-design-vue";
import router from "@/router";

import { ls, debounce } from "@/utils/util";
import config from "@/config/config.js";

import CancelToken from "./cancelToken";

const { ACCESS_TOKEN, ACCESS_REFRESHTOKEN, ACCESS_DATA } = config;

const service = axios.create({
    baseURL: import.meta.env.VITE_BASE_API as string,
    timeout: 100000,
    // headers: { 'Content-Type': 'application/json' },
    // headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
});

//
const instance = axios.create({
    baseURL: import.meta.env.VITE_BASE_API as string,
    timeout: 5000,
});

const toLogin = () => {
    localStorage.clear();
    sessionStorage.clear();
    window.location.reload();
    setTimeout(() => {
        router.replace({
            path: "/login",
        });
    }, 100);
};
const tip = debounce((msg: string) => {
    message.error(msg);
}, 1000);

const errorHandle = async (status: number, err: any, errorInfo = {}) => {
    /* eslint-disable */
    // 状态码判断
    switch (status) {
        // 401: 未授权 或未登录状态，跳转登录页 或者重新获取token，二次重发
        case 401:
            tip(`${err?.data?.msg || err?.data?.message || err}`);
            setTimeout(() => {
                toLogin();
            }, 2000);
            break;
        // 403 token过期
        // 清除token并跳转登录页
        case 403:
            // eslint-disable-next-line indent
            CancelToken.clearPending();
            tip(`${err?.data?.msg || err?.data?.message || err}`);
            ls.remove(ACCESS_TOKEN);
            setTimeout(() => {
                toLogin();
            }, 2000);
            break;
        case 450:
            // 使用权限已到期
            CancelToken.clearPending();
            tip(err.message || err.data?.message || err);
            ls.remove(ACCESS_TOKEN);
            setTimeout(() => {
                toLogin();
            }, 2000);
            break;
        // 404请求不存在
        case 404:
            tip("请求的资源不存在 404");
            // 404页面
            break;
        case 500:
            tip(err.message || err.data?.message || "服务器繁忙");
            // 404页面
            break;
        default:
            tip(err.message || err.data?.message || err);
    }
    /* eslint-disable */
};

let isRemovePending = false;

// 添加请求拦截器
service.interceptors.request.use(
    (config: any) => {
        let token = ls.get(ACCESS_TOKEN);
        // token = '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
        if (token) {
            if (token.indexOf("Bearer") == -1) {
                config.headers.common["Authorization"] = `Bearer ${token}`;
            } else {
                config.headers.common["Authorization"] = token;
            }
        }
        // 这里的判断用于处理白名单不参与取消请求
        // if (!CancelToken.whiteRequest.includes(`${config.url}`)) {
        //     // 请求开始前，检查一下是否已经有该请求了，有则取消掉该请求

        //
        //     // 把当前请求添加进去
        CancelToken.addPending(config);
        if (isRemovePending) {
            CancelToken.clearPending();
            isRemovePending = false;
        }
        // }
        return config;
    },
    (error) => Promise.reject(error)
);

// 需要跳转到登录页的code
const TO_LOGIN_CODE = [
    1001001001, 1002002012, 1002002013, 1002002014, 1002002024, 1002003009,
];

// 添加响应拦截器
service.interceptors.response.use(
    (res) => {
        if (res.status === 200) {
            // 拦截流文件请求
            if (
                res.request.responseType == "arraybuffer" ||
                res.request.responseType == "blob"
            ) {
                console.log("resresresresres", res);

                // return Promise.resolve(res);
                return Promise.resolve(res.data);
            }

            if (res.data.code === 0) {
                return Promise.resolve(res.data);
            } else if (TO_LOGIN_CODE.includes(res.data.code)) {
                if (!isRemovePending) {
                    tip(res.data?.message);
                    setTimeout(() => {
                        ls.remove(ACCESS_TOKEN);
                        ls.remove(ACCESS_REFRESHTOKEN);
                        ls.remove(ACCESS_DATA);
                        ls.remove("dictionary");
                        ls.remove("columnsCheckedList");
                        window.localStorage.removeItem(`USERNAME`);
                        toLogin();
                    }, 2000);
                }
                isRemovePending = true;
            } else {
                errorHandle(res.data.code, res);
            }
            return Promise.reject(res);
        } else {
            return Promise.reject(res || {});
        }
    },
    (error) => {
        const { response } = error;
        // CancelToken.removePending(error.config || {})
        if (axios.isCancel(error)) {
            console.error("已取消重复的请求", error.message);
        } else {
            if (response) {
                // 请求已发出，但是不在2xx的范围
                errorHandle(response.status, response);
            } else {
                message.error(error.message || "网络超时");
            }
        }
        return Promise.reject(error || {});
    }
);

const download = (
    url,
    data,
    name,
    fn,
    type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
) => {
    return service({
        url,
        method: "post",
        data,
        responseType: "arraybuffer",
    }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type,
            })
        );
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", `${name}`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        fn && fn();
        setTimeout(() => window.URL.revokeObjectURL(url), 1000);
    });
};

export { download };

export default service;
