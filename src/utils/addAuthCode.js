const initPerms = (arr) => {
    let list = []
    const setPerms = arr => {
        arr?.forEach(i => {
            if (i.perms) {
                list.push(i.perms)
            }
            setPerms(i.btnList)
            setPerms(i.children)
            setPerms(i.tabList)
        })
    }
    setPerms(arr)
    let pers = localStorage.getItem('btnPerms')
    try {
        pers = JSON.parse(pers) || []
    } catch (error) {
        pers = []
    }
    pers = [...pers, ...list]
    localStorage.setItem('btnPerms', JSON.stringify(pers))

}

export default initPerms