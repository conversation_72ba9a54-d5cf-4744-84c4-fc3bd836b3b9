/*
 * @Descripttion: 全局utils
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-02-18 11:03:37
 * @LastEditors: 舒志建 <EMAIL>
 * @LastEditTime: 2023-06-25 15:36:17
 */
import Secure<PERSON> from "secure-ls";
import dayjs from "dayjs";
import config from "@/config/config";
import { Modal } from "ant-design-vue";
import { createVNode } from "vue";
const { encryptionSecret } = config;

export const ls = new SecureLS({ encodingType: "aes", encryptionSecret });

export function debounce(fn, delay) {
    let timer = null;
    return function () {
        const context = this;
        const args = arguments;
        if (timer) {
            clearTimeout(timer);
        }
        timer = setTimeout(() => {
            fn.apply(context, args);
        }, delay);
    };
}

// 树形结构默认字段
const treeDefaultFieldNames = {
    children: "children",
    key: "id",
    parent: "pid",
};

/**
 * @description: 获取当前传入节点得所有父节点
 * @param {Array} tree tree数组
 * @param {string} id tree数组
 * @param {Object} options 配置对象
 * @param {Object} children tree结构得children字段 - 默认children
 * @param {Object} key tree结构得唯一标识 - 默认id
 * @param {Object} parent tree结构父级标识 - 默认pid
 * @return {Object} 返回所有父节点,及id
 */
export function getCurNodeParents(
    tree: [],
    id: string | number,
    options: object = {}
) {
    if (!Array.isArray(tree)) {
        throw new Error(
            "The first parameter of getcurnodeparts requires an array"
        );
    }
    if (!id) {
        return;
    }
    const _options = Object.assign(treeDefaultFieldNames, options);
    const parent: Array<string | number> = [];
    const node: Array<{}> = [];
    const forFn = function (arr: [], id) {
        for (let i = 0; i < arr.length; i++) {
            const item = arr[i];
            if (item[_options.key] === id) {
                // TODO: 当前业务顶级pid为 0，顶级时则需要使用唯一标识
                parent.push(
                    item[_options.parent] === 0
                        ? item[_options.key]
                        : item[_options.parent]
                );
                node.push(item);
                forFn(tree, item[_options.parent]);
                break;
            } else {
                if (item[_options.children]) {
                    forFn(item[_options.children], id);
                }
            }
        }
    };
    forFn(tree, id);
    return {
        parentNodes: node,
        parentNodeIds: parent,
    };
}

/**
 * @description: 获取tree第一层最底层父节点
 * @param {Array} tree tree数组
 * @param {Object} options 配置对象
 * @param {Object} children tree结构得children字段 - 默认children
 * @param {Object} key tree结构得唯一标识 - 默认id
 * @param {Object} parent tree结构父级标识 - 默认pid
 * @return {Object} 返回第一层最底层所有父节点,及id
 */
export function getTreeFirstfloorNodeParents(tree: [], options: object = {}) {
    if (!Array.isArray(tree)) {
        throw new Error(
            "The first parameter of getTreeFirstfloorNodeParents requires an array"
        );
    }
    const _options = Object.assign(treeDefaultFieldNames, options);
    const result: Array<{}> = [];
    const forFn = (arr) => {
        // eslint-disable-next-line no-unreachable-loop
        for (let i = 0; i < arr.length; i++) {
            const item = arr[i];
            if (item[_options.children].length) {
                forFn(item[_options.children]);
                break;
            } else {
                result.push(item);
                break;
            }
        }
    };
    forFn(tree);
    return {
        ...(getCurNodeParents(
            tree,
            result[0] && result[0][_options.key],
            _options
        ) || {}),
    };
}

/**
 * @description: 扁平化tree
 * @param {*} tree tree
 * @param {*} childrenField 子级key
 * @return {Array} []
 */
export function flatTree(data = [], children = "children") {
    if (!(data && data.length)) return [];
    return data.reduce((acc: [], cur: {}) => {
        acc.push(cur);
        if (cur[children] && cur[children].length) {
            flatTree(cur[children]);
        }
        return acc;
    }, []);
}

function addDate(date, n) {
    date.setDate(date.getDate() + n);
    return date;
}

function formatsDate(date) {
    const year = date.getFullYear();
    const month = makeup(date.getMonth() + 1) + "-";
    const day = makeup(date.getDate());
    const week = [
        "周 日",
        "周 一",
        "周 二",
        "周 三",
        "周 四",
        "周 五",
        "周 六",
    ][date.getDay()];
    return {
        specific_date: year + "-" + month + day,
        date: month + day,
        week,
        isToday: new Date().getDay() === date.getDay(),
        week_num: date.getDay(),
    };
}

/**
 * @description: 获取一周的集合
 * @param {data} date new Date()
 * @return {array} []
 */
export function getWeekList(date = new Date()) {
    const week = !date.getDay() ? 6 : date.getDay() - 1;
    date = addDate(date, week * -1);
    // this.currentFirstDate = new Date(date);
    const arr = [];
    for (let i = 0; i < 7; i++) {
        arr.push(formatsDate(i === 0 ? date : addDate(date, 1)));
    }
    return arr;
}

/**
 * @description: 补零
 * @param {number} numer 需要补齐的数字
 * @return {string} "00"
 */
export function makeup(num) {
    return num < 10 ? (num = `0${num}`) : num;
}

export function getURLQueryParams() {
    // 先获取参数字符串
    const url = window.location.href;
    const index = url.indexOf("?");
    const paramsUrl = url.slice(index + 1);
    // name=zs&age=18let
    const paramsArr = paramsUrl.split("&");
    // ['name=zs','age=18']
    const params = {};
    for (const item of paramsArr) {
        const temp = item.split("=");
        params[temp[0]] = temp[1];
    }
    return params;
}

/**
 * @description 二次确认弹框
 */
export function modalConfirm(tit: string, con: string, antIcon: any) {
    return new Promise((resolve) => {
        Modal.confirm({
            title: tit,
            icon: createVNode(antIcon),
            content: con,
            centered: true,
            onOk() {
                resolve(true);
            },
            onCancel() {
                resolve(false);
            },
        });
    });
}

/**
 * @name 文件压缩
 * @description
 * 1、将文件转img对象
 * 2、获取文件宽高比例
 * 3、自定义等比例缩放宽高属性，这里我用的是固定800宽度，高度是等比例缩放
 * 4、canvas重新绘制图片
 * 5、canvas转二进制对象转文件对象，返回
 * @returns { File } 文件
 */
export const imgCompress = async (file) => {
    // 将文件转img对象
    const img = await fileToImg(file);
    return new Promise((resolve, reject) => {
        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");
        // 获取文件宽高比例
        const { width: originWidth, height: originHeight } = img;
        // 自定义等比例缩放宽高属性，这里我用的是固定800宽度，高度是等比例缩放
        const scale = +(originWidth / originHeight).toFixed(2); // 比例取小数点后两位)
        const targetWidth = 800; // 固定宽
        const targetHeight = Math.round(800 / scale); // 等比例缩放高
        canvas.width = targetWidth;
        canvas.height = targetHeight;
        context.clearRect(0, 0, targetWidth, targetHeight);
        // canvas重新绘制图片
        context.drawImage(img, 0, 0, targetWidth, targetHeight);
        // canvas转二进制对象转文件对象，返回
        const type = "image/png";
        canvas.toBlob((blob) => {
            const f = new File([blob], file.name, {
                type,
                lastModified: file.lastModified,
            });
            resolve(f);
        }, type);
    });
};
// file转换成img对象
const fileToImg = (file) => {
    return new Promise((resolve, reject) => {
        const img = new Image();
        const reader = new FileReader();
        reader.onload = function (e) {
            img.src = e.target.result;
        };
        reader.onerror = function (e) {
            reject(e);
        };
        reader.readAsDataURL(file);
        img.onload = function () {
            resolve(img);
        };
        img.onerror = function (e) {
            reject(e);
        };
    });
};

/**
 * @name 禁止选历史时间
 * @description
 */
const range = (start, end) => {
    const result = [];
    for (let i = start; i < end; i++) {
        result.push(i);
    }
    return result;
};
// 年月日
export const disabledDate = (current) => {
    const subtractDays = dayjs()
        .subtract(1, "day")
        .format("YYYY-MM-DD HH:mm:ss");

    return current && current < dayjs(subtractDays).endOf("day");
};
// 时分秒
export const disabledRangeTime = (item) => {
    const current = dayjs().date();
    let hour = dayjs().hour();
    const minute = dayjs().minute();
    let disabledHours = null;
    let disabledMinutes = null;
    disabledHours = () => range(-1, hour).splice(1, 20);
    disabledMinutes = () => range(-1, minute).splice(1, minute);

    if (item) {
        const date = dayjs(item).date();
        if (date > current) {
            hour = dayjs().hour();
            disabledHours = "";
            disabledMinutes = "";
        } else {
            if (dayjs(item).hour() > hour) {
                hour = dayjs().hour();
                disabledMinutes = "";
                disabledSecond = "";
            } else {
                disabledMinutes = () => range(-1, minute).splice(1, minute);
            }
        }
    }
    return {
        disabledHours,
        disabledMinutes,
    };
};

/**
 * @description: 复制链接
 * @return {string}
 */
export function copyLink(text: any) {
    const textarea = document.createElement("textarea"); // 构建textarea
    textarea.value = text; // 设置内容
    document.body.appendChild(textarea); // 添加临时实例
    textarea.select(); // 选择实例内容
    document.execCommand("Copy"); // 执行复制
    document.body.removeChild(textarea); // 删除临时实例
}

/**
 * @description: 下载canvas 节点和图片名
 * @canvas dome 节点
 * @filename 取名为文件名
 */
export function downloadIamge(canvas, filename) {
    const downloadUrl = canvas.toDataURL("image/png");
    const a = document.createElement("a");
    a.href = downloadUrl;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
}

/**
 * 数字转成汉字
 * @params num === 要转换的数字
 * @return 汉字
 * */
export function toChinesNum(num) {
    let changeNum = [
        "零",
        "一",
        "二",
        "三",
        "四",
        "五",
        "六",
        "七",
        "八",
        "九",
    ];
    let unit = ["", "十", "百", "千", "万"];
    num = parseInt(num);
    let getWan = (temp) => {
        let strArr = temp.toString().split("").reverse();
        let newNum = "";
        let newArr = [];
        strArr.forEach((item, index) => {
            newArr.unshift(
                item === "0" ? changeNum[item] : changeNum[item] + unit[index]
            );
        });
        let numArr = [];
        newArr.forEach((m, n) => {
            if (m !== "零") numArr.push(n);
        });
        if (newArr.length > 1) {
            newArr.forEach((m, n) => {
                if (newArr[newArr.length - 1] === "零") {
                    if (n <= numArr[numArr.length - 1]) {
                        newNum += m;
                    }
                } else {
                    newNum += m;
                }
            });
        } else {
            newNum = newArr[0];
        }

        return newNum;
    };
    let overWan = Math.floor(num / 10000);
    let noWan = num % 10000;
    if (noWan.toString().length < 4) {
        noWan = "0" + noWan;
    }
    return overWan ? getWan(overWan) + "万" + getWan(noWan) : getWan(num);
}

/**
 * 当在第页都删除后  pageSize还在当前页数的问题
 * @params pagination: 分页对象
 * @params checkedList: 选中要删除的数量 0
 * @params callback: 回调方法
 * */
export function deletePageSize(pagination, checkedList, callback) {
    const { total, pageNo, pageSize } = pagination;
    // 当删除的数量等于当前页的数量时 页数减一

    // total: 总数量 - （（当前页数 * 每页数量） - 每页数量） 等于列表数据数量时 页数减一（说明删除当前页的最后一条数据时）
    if (total - (pageNo * pageSize - pageSize) == checkedList) {
        pagination.pageNo = pageNo > 1 ? pageNo - 1 : 1;
    }
    callback();
}
// 密码正则特殊字符
export const regularSpecialCharacters =
    /^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{}|;':",.\/<>?])[A-Za-z\d!@#$%^&*()_+\-=\[\]{}|;':",.\/<>?]{8,20}$/;
