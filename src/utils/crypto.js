/*
 * @Author: 加密解密
 * @Date: 2021-09-17 19:02:51
 * @LastEditTime: 2023-03-20 16:29:31
 * @LastEditors: jingrou
 * @Description: In User Settings Edit
 * @FilePath: \cloud1\src\utils\cryptoJs.js
 */
import cryptoJs from "crypto-js";
import { Base64 } from "js-base64";

// 密钥
const _key = "xiaoniu#888#!";
//  偏移量
const _iv = "20210916";

// 3DES 加密
export const encryptByDES = (dataStr, key, iv) => {
    const keyHex = cryptoJs.enc.Utf8.parse(key);
    const encrypted = cryptoJs.TripleDES.encrypt(dataStr, keyHex, {
        mode: cryptoJs.mode.CBC,
        padding: cryptoJs.pad.Pkcs7,
        iv: cryptoJs.enc.Utf8.parse(iv),
    });
    return encrypted.toString();
};

// 3DES 解密
export const decryptByDES = (ciphertext, key, iv) => {
    const keyHex = cryptoJs.enc.Utf8.parse(key);
    const decrypted = cryptoJs.TripleDES.decrypt(
        {
            ciphertext: cryptoJs.enc.Base64.parse(ciphertext),
        },
        keyHex,
        {
            mode: cryptoJs.mode.CBC,
            padding: cryptoJs.pad.Pkcs7,
            iv: cryptoJs.enc.Utf8.parse(iv),
        }
    );
    return decrypted.toString(cryptoJs.enc.Utf8);
};

export const xiaoniuApplyUrlParameter = () => {
    const params = {
        username: "18555237570",
        password: "991015",
        now: new Date().getTime(),
    };
    return encryptByDES(JSON.stringify(params), _key, _iv);
};

export function random_string(len) {
    len = len || 32;
    const chars = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
    const maxPos = chars.length;
    let pwd = "";
    for (let i = 0; i < len; i++) {
        pwd += chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
}

// Base64加密对url安全
function base64_encode_url_safe(string) {
    return Base64.encode(string)
        .replace("+", "-")
        .replace("/", "_")
        .replace("=", "");
}

// Base64解密对url安全
function base64_decode_url_safe(string) {
    let base64 = string.replace("-", "+").replace("_", "/");
    const mod = base64.length % 4;
    if (mod > 0) base64 += "====".substring(mod);
    return Base64.decode(base64);
}

export const libraryApplyUrlParameter = (params) => {
    const iv = random_string(8);
    const key = "rw3n9SQCRG9LikBrjNKLleCn";
    let str = encryptByDES(JSON.stringify(params), key, iv);
    str = str.replace(/\+/g, "-");
    str = str.replace(/\//g, "_");
    str = str.replace(/\=/g, "");
    return {
        str,
        iv,
    };
};

// AES解密方法
export function decryptAES(word, key) {
    //  解密
    var key = cryptoJs.enc.Utf8.parse(key); // 秘钥
    // var iv = cryptoJs.enc.Utf8.parse(iv);
    const decrypt = cryptoJs.AES.decrypt(word, key, {
        // iv: iv,
        mode: cryptoJs.mode.ECB,
        padding: cryptoJs.pad.Pkcs7,
    });
    const decryptedStr = decrypt.toString(cryptoJs.enc.Utf8);
    return decryptedStr;
}
// 解码
export function decodeFromBase64(base64Str) {
    const decodedData = atob(base64Str);
    const decoder = new TextDecoder("utf-8");
    base64Str = decoder.decode(
        new Uint8Array([...decodedData].map((c) => c.charCodeAt(0)))
    );
    if (base64Str) {
        base64Str = JSON.parse(base64Str);
    }
    return base64Str;
}
// const params = {
//   username: '18555237570',
//   password: '991015',
//   now: new Date().getTime()
// }

// let str = encryptByDES(JSON.stringify(params), _key, _iv)
