/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-30 18:37:24
 * @LastEditors: 舒志建 <EMAIL>
 * @LastEditTime: 2023-03-07 10:01:34
 */
/**
 * 手机号码
 * @param val 当前值字符串
 * @returns 返回 true: 手机号码正确
 */
export function verifyPhone(val: string) {
    // false: 手机号码不正确
    // if (!/^((12[0-9])|(13[0-9])|(14[5])|(15([0-3]|[5-9]))|(18[0,5-9]))\d{8}$/.test(val)) return false;
    if (!/^[1][3-9][0-9]{9}$/.test(val)) return false
    // true: 手机号码正确
    else return true
}

/**
 * 国内电话号码
 * @param val 当前值字符串
 * @returns 返回 true: 国内电话号码正确
 */
export function verifyTelPhone(val: string) {
    // false: 国内电话号码不正确
    // true: 国内电话号码正确
    return /^0\d{2,3}-?\d{7,8}$/.test(val)
}

/**
 * 手机号码座机号
 * @param val 当前值字符串
 * @returns 返回 true: 手机号码正确
 */
export function verifyTelPhones(val: string) {
    const phone = /^[1][3-9][0-9]{9}$/
    // 座机号
    const tel = /^0\d{2,3}-?\d{7,8}$/
    // true: 号码正确 false: 号码不正确
    return phone.test(val) || tel.test(val)
}
/**
 * 邮箱
 * @param val 当前值字符串
 * @returns 返回 true: 邮箱正确
 */
export function verifyEmail(val: string) {
    // false: 邮箱不正确 // true: 邮箱正确
    const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/
    return reg.test(val)
}

/**
 * 对象深克隆
 * @param obj 源对象
 * @returns 克隆后的对象
 */
export function deepClone(obj: any) {
    let newObj: any
    try {
        newObj = obj.push ? [] : {}
    } catch (error) {
        newObj = {}
    }
    for (const attr in obj) {
        if (typeof obj[attr] === 'object') {
            newObj[attr] = deepClone(obj[attr])
        } else {
            newObj[attr] = obj[attr]
        }
    }
    return newObj
}

/**
 * 去掉中文及空格
 * @param val 当前值字符串
 * @returns 返回处理后的字符串
 */
export function verifyCnAndSpace(val: string) {
    // 匹配中文与空格
    let v = val.replace(/[\u4e00-\u9fa5\s]+/g, '')
    // 匹配空格
    v = v.replace(/(^\s*)|(\s*$)/g, '')
    // 特殊字符
    v = v.replace(/[~'！!@#￥$%^&*()-+_=:]/g, '')
    // 返回结果
    return v
}

/**
 * 字符长度
 * @param value input框的值
 * @param maxLength 需求最大限制的字符
 * @param minLength 需求最小限制的字符
 * @returns 克隆后的对象
 */
export function checkField(value: string, minLength: number, maxLength: number) {
    const newvalue = value.replace(/[^\x00-\xff]/g, '**')
    const length = newvalue.length
    // 当填写的字节数小于设置的字节数
    if (length * 1 < minLength * 1 || length * 1 > maxLength * 1) {
        return
    }
    const limitDate = newvalue.substring(0, maxLength)
    let count = 0
    let limitvalue = ''
    for (let i = 0; i < limitDate.length; i++) {
        const flat = limitDate.substring(i, 1)
        if (flat == '*') count++
    }
    let size = 0
    const istar = newvalue.substring(maxLength * 1 - 1, 1) // 校验点是否为“×”
    // if 基点是×; 判断在基点内有×为偶数还是奇数
    if (count % 2 === 0) {
        // 当为偶数时
        size = count / 2 + (maxLength * 1 - count)
        limitvalue = value.substring(0, size)
    } else {
        // 当为奇数时
        size = (count - 1) / 2 + (maxLength * 1 - count)
        limitvalue = value.substring(0, size)
    }
    return limitvalue
}

/**
 * 回显表单
 * @param newData 后端返回的新对象数据
 * @param olData 前端的旧对象数据
 * @returns olData
 */
export function echoForm(newData: any, olData: any) {
    for (const i in newData) {
        olData[i] = newData[i]
    }
    return olData
}

/**
 * 身份证号
 * @param val 当前值字符串
 * @returns 返回 true: 身份证号正确
 */
export function verifyIdCard(val: string) {
    // false: 身份证号不正确
    return /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))((0[1-9])|([1-2][0-9])|30|31)\d{3}(\d|X)$/i.test(
        val
    );
}
