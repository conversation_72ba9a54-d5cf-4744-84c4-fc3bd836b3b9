import axios from "axios";
import { message } from "ant-design-vue";
import { ls, debounce } from "@/utils/util";
import config from "@/config/config.js";
import CancelToken from "./cancelToken";

const { ACCESS_TOKEN, ACCESS_REFRESHTOKEN, ACCESS_DATA } = config;
const service = axios.create({
    baseURL: import.meta.env.VITE_BASE_API as string,
    timeout: 100000,
});

const toLogin = () => {
    localStorage.clear();
    sessionStorage.clear();
};
const tip = debounce((msg: string) => {
    message.error(msg);
}, 1000);

const errorHandle = async (status: number, err: any, errorInfo = {}) => {
    /* eslint-disable */
    // 状态码判断
    switch (status) {
        // 401: 未授权 或未登录状态，跳转登录页 或者重新获取token，二次重发
        case 401:
            tip(`${err?.data?.error_description || err?.data?.message || err}`);
            setTimeout(() => {
                toLogin();
            }, 2000);
            break;
        // 403 token过期
        // 清除token并跳转登录页
        case 403:
            // eslint-disable-next-line indent
            CancelToken.clearPending();
            tip(`${err?.data?.error_description || err?.data?.message || err}`);
            ls.remove(ACCESS_TOKEN);
            setTimeout(() => {
                toLogin();
            }, 2000);
            break;
        case 450:
            // 使用权限已到期
            CancelToken.clearPending();
            tip(err.message || err?.data?.error_description || err.data?.message || err);
            ls.remove(ACCESS_TOKEN);
            setTimeout(() => {
                toLogin();
            }, 2000);
            break;
        // 404请求不存在
        case 404:
            tip("请求的资源不存在 404");
            // 404页面
            break;
        case 500:
            tip("系统异常，请稍后再试");
            // 404页面
            break;
        default:
            tip(err.message || err?.data?.error_description || err.data?.message || err);
    }
    /* eslint-disable */
};

let isRemovePending = false;

// 添加请求拦截器
service.interceptors.request.use(
    (config: any) => {
        let token = ls.get(ACCESS_TOKEN);
        if (token) {
            if (token.indexOf("Bearer") == -1) {
                config.headers.common["Authorization"] = `Bearer ${token}`;
            } else {
                config.headers.common["Authorization"] = token;
            }
        }
        CancelToken.addPending(config);
        if (isRemovePending) {
            CancelToken.clearPending();
            isRemovePending = false;
        }
        // }
        return config;
    },
    (error) => Promise.reject(error)
);

// 添加响应拦截器
service.interceptors.response.use(
    (res) => {
        if (res.status === 200) {
            if (res.data.code === 0) {
                return Promise.resolve(res.data);
            } else if (
                [1002002013, 1002002024, 1001001001].includes(res.data.code)
            ) {
                if (!isRemovePending) {
                    tip(res.data?.message);
                    setTimeout(() => {
                        ls.remove(ACCESS_TOKEN)
                        ls.remove(ACCESS_REFRESHTOKEN)
                        ls.remove(ACCESS_DATA)
                        ls.remove('dictionary')
                        ls.remove('columnsCheckedList')
                        window.localStorage.removeItem(`USERNAME`)
                        toLogin()
                    }, 2000)
                }
                isRemovePending = true;
            } else {
                errorHandle(res.data.code, res);
            }
            return Promise.reject(res);
        } else {
            return Promise.reject(res || {});
        }
    },
    (error) => {
        const { response } = error;
        if (axios.isCancel(error)) {
            console.error("已取消重复的请求", error.message);
        } else {
            if (response) {
                // 请求已发出，但是不在2xx的范围
                errorHandle(response.status, response);
            } else {
                message.error(error.message || "网络超时");
            }
        }
        return Promise.reject(error || {});
    }
);

export default service;
