/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-10-12 17:35:02
 * @LastEditors: jingrou
 * @LastEditTime: 2022-10-12 17:36:05
 */
import SecureLS from 'secure-ls'
/**
 * @description: 设置缓存压缩加密
 * @return 返回Storage处理函数集合
 */
export function useStorage(
    options = {
        encodingType: 'aes',
        encryptionSecret: 'yide-monitoring-system-manage'
    }
) {
    const ls = new SecureLS(options)

    return {
        getItem: (key: string) => ls.get(key),
        setItem: (key: string, value: any) => ls.set(key, value),
        removeItem: (key: string) => ls.remove(key),
        removeAll: () => ls.removeAll()
    }
}
