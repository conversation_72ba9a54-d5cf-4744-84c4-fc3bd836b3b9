/*
 * @Descripttion:数据格式
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-06-25 15:15:35
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-05-19 18:47:21
 */
import html2canvas from 'html2canvas'

export const replaceBr = (value) => {
    return value ? value.replace(/\n|\r\n/g, '<br/>') : null
}
export const getTagStyleAttribute = (tab = 'div') => {
    const divTag = document.createElement('div')
    const inline = divTag.style
    const cssArr = Object.keys(inline)
    return cssArr
}

export const hasStyleAttributePX = (obj) => {
    const cssArr = getTagStyleAttribute()
    const suffixArr = [
        'top',
        'left',
        'right',
        'bottom',
        'width',
        'height',
        'x',
        'y',
        'fontSize',
        'paddingTop',
        'paddingLeft',
        'paddingRight',
        'textIndent'
    ]
    const result = {
        // transform: "none",
    }
    for (const k in obj) {
        if (suffixArr.includes(k)) {
            if (typeof obj[k] === 'number') {
                result[k] = obj[k] + 'px'
                continue
            } else {
                result[k] = obj[k]
            }
        }
        if (k === 'textShadow') {
            const textShadow = []
            obj.textShadow.forEach((num) => {
                if (typeof num === 'number') {
                    textShadow.push(num / 2 + 'px')
                } else {
                    textShadow.push(num)
                }
            })
            result.textShadow = textShadow.join(' ')
            continue
        }
        if (k === 'borderRadius') {
            const borderRadius = []
            obj.borderRadius.forEach((num) => {
                if (typeof num === 'number') {
                    borderRadius.push(num / 2 + 'px')
                } else {
                    borderRadius.push(num)
                }
            })
            result.borderRadius = borderRadius.join(' ')
            continue
        }
        if (k === 'border') {
            const border = []
            obj.border.forEach((num) => {
                if (typeof num === 'number') {
                    border.push(num / 2 + 'px')
                } else {
                    border.push(num)
                }
            })
            result.border = border.join(' ')
            continue
        }
        if (cssArr.indexOf(k) !== -1) {
            result[k] = obj[k]
        }
    }
    return result
}

export const hasStyleAttributeRem = (obj) => {
    const cssArr = getTagStyleAttribute()
    const suffixArr = [
        'top',
        'left',
        'right',
        'bottom',
        'width',
        'height',
        'x',
        'y',
        'fontSize',
        'paddingTop',
        'paddingLeft',
        'paddingRight',
        'textIndent'
    ]
    const html = document.querySelector('html')
    const htmlSize = Number.parseFloat(html.style.fontSize)
    const result = {
        // transform: "none",
    }
    for (const k in obj) {
        if (suffixArr.includes(k)) {
            if (typeof obj[k] === 'number') {
                result[k] = obj[k] / htmlSize / 2 + 'rem'
                continue
            } else {
                result[k] = obj[k]
            }
        }
        if (k === 'textShadow') {
            const textShadow = []
            obj.textShadow.forEach((num) => {
                if (typeof num === 'number') {
                    textShadow.push(num / htmlSize / 2 + 'rem')
                } else {
                    textShadow.push(num)
                }
            })
            result.textShadow = textShadow.join(' ')
            continue
        }
        if (k === 'borderRadius') {
            const borderRadius = []
            obj.borderRadius.forEach((num) => {
                if (typeof num === 'number') {
                    borderRadius.push(num / htmlSize / 2 + 'rem')
                } else {
                    borderRadius.push(num)
                }
            })
            result.borderRadius = borderRadius.join(' ')
            continue
        }
        if (k === 'border') {
            const border = []
            obj.border.forEach((num) => {
                if (typeof num === 'number') {
                    border.push(num / htmlSize / 2 + 'rem')
                } else {
                    border.push(num)
                }
            })
            result.border = border.join(' ')
            continue
        }
        if (cssArr.indexOf(k) !== -1) {
            result[k] = obj[k]
        }
    }
    return result
}

export const dataURLtoFile = (dataurl, filename) => {
    const arr = dataurl.split(',')
    const mime = arr[0].match(/:(.*?);/)[1]
    const bstr = atob(arr[1])
    let n = bstr.length
    const u8arr = new Uint8Array(n)
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
    }
    return new File([u8arr], filename, { type: mime })
}
// 下载图片
export const generatorImage = (targetDom, name, fileType = 'file') => {
    return new Promise((resolve, reject) => {
        window.scrollTo(0, 0)
        html2canvas(targetDom, {
            logging: true,
            scale: 1,
            allowTaint: false,
            useCORS: true,
            height: targetDom?.offsetHeight || 0,
            width: targetDom?.offsetWidth || 0,
            windowWidth: document.body.scrollWidth,
            windowHeight: document.body.scrollHeight,
            dpi: window.devicePixelRatio,
            backgroundColor: null
        })
            .then((canvas) => {
                const base64 = canvas.toDataURL('image/png', 1)
                const file = dataURLtoFile(base64, name)
                resolve(fileType === 'file' ? file : base64)
            })
            .catch((err) => {
                reject(err)
            })
    })
}
