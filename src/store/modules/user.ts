/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-02-17 21:45:02
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-08-31 20:20:51
 */
import { ls } from "@/utils/util";
import config from "@/config/config";
import { getUpgradeCheckUpgrade } from "@/api/user";

const { ACCESS_TOKEN, ACCESS_REFRESHTOKEN, ACCESS_DATA } = config;
interface stateInterface {
    userInfo: object;
    currentSchool: object;
    accessToken: string;
    refreshToken: string;
}

// 用户模块
export default {
    namespaced: true,
    state: {
        userInfo: {},
        currentSchool: {
            schoolLogo: "image/uploadImg.png",
        },
        accessToken: "",
        refreshToken: "",
        announceShow: false,
        checkUpgrade: false,
    },
    mutations: {
        // 修改用户信息
        setUser(state: stateInterface, user: object) {
            state.userInfo = user;
        },
        //
        setCurrentSchool(state: stateInterface, currentSchool: object) {
            state.currentSchool = currentSchool;
        },
        setToken(
            state: stateInterface,
            { accessToken, refreshToken }: stateInterface
        ) {
            state.accessToken = accessToken;
            state.refreshToken = refreshToken;
            ls.set(ACCESS_TOKEN, accessToken);
            ls.set(ACCESS_REFRESHTOKEN, refreshToken);
        },
        removeToken(state: stateInterface) {
            state.accessToken = "";
            state.refreshToken = "";
        },
        removerUserLoginInfo(state: stateInterface) {
            state.userInfo = {};
            state.currentSchool = {};
            state.accessToken = "";
            state.refreshToken = "";
            ls.remove(ACCESS_TOKEN);
            ls.remove(ACCESS_REFRESHTOKEN);
            ls.remove(ACCESS_DATA);
        },
        CHECK_UPGRADE(state, data) {
            state.checkUpgrade = data;
        },
    },

    actions: {
        UpgradeCheckUpgrade({ commit }) {
            return new Promise((resolve, reject) => {
                getUpgradeCheckUpgrade()
                    .then(({ data }) => {
                        commit("CHECK_UPGRADE", data);
                        resolve(data);
                    })
                    .catch((error) => {
                        reject(error);
                    })
                    .finally(() => {
                        // commit('CHECK_UPGRADE', false)
                    });
            });
        },
    },
};
