/*
 * @Descripttion: 学籍管理 schoolRollManagement
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-31 10:19:01
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-03-26 15:50:32
 */
import { Session } from '@/utils/storage'
// 此处加上 `.ts` 后缀报错，具体原因不详
import api from '@/api'
import * as defHttp from '@/api/defHttp'

const studentManagementModule = {
    namespaced: true,
    state: {
        studentObj: {
            studentNumber: 0,
            teacherNumber: 0,
            list: [],
            leaders: [],
            teachers: [],
            total: 0
        }
    },
    mutations: {
        STUDENT_LIST_INFO(state: any, paylod: any) {
            state.studentObj.teachers = []
            const { studentNumber, teacherNumber, studentPageListVO, leaders, teachers }: any = paylod
            if (studentPageListVO) {
                state.studentObj = {
                    studentNumber,
                    teacherNumber,
                    leaders,
                    teachers,
                    list: studentPageListVO.list,
                    total: studentPageListVO.total
                }
            } else {
                state.studentObj = {
                    studentNumber: paylod.length,
                    teacherNumber: 0,
                    leaders: [],
                    teachers,
                    list: paylod,
                    total: paylod.length
                }
            }
        }
    },
    actions: {
        // 获取学生列表数据
        async getStudentListInfo({ commit }: any, paylod: object) {
            return new Promise((resolve, reject) => {
                defHttp.post(api.pageStudent, paylod).then((res: any) => {
                    const { code, data } = res
                    resolve(res)
                    !code && commit('STUDENT_LIST_INFO', data)
                }).catch((error) => {
                    reject(error)
                })
            })
        },
        // 获取学生详情列表数据
        async getStudentListDetailsiInfo({ commit }: any, paylod: object) {
            return new Promise((resolve, reject) => {
                defHttp.get(api.studentDetails, paylod).then((res: any) => {
                    const { code, data } = res
                    data.parents = data.elterns
                    !code && commit('STUDENT_LIST_INFO', [data])
                })
            })
        }
    }
}

export default studentManagementModule
