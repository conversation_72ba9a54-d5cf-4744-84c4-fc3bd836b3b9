import {
    siteDeviceList,
    getCloudBuildList
} from "@/api/site.js";
import axios from "@/utils/request";
interface State {
    [key: string]: any
}

// 智慧宿舍
export default {
    namespaced: true,
    state: {
        // 区分某些功能相同但模块不同的接口-智慧宿舍：4
        moduleType: 4,
        // 当前选中宿舍楼栋
        activeDorm: {},
        // 宿舍列表
        dorms: [
            // {
            //     buildingId: '1585460558453301249',
            //     name: "宿舍A",
            //     checked: true
            // }
        ],
        // 宿舍入住状态
        checkInStatus: [
            { label: '未入住', value: 1 },
            { label: '已入住', value: 2 },
            { label: '已退宿', value: 3 },
        ],
        // 考勤类型
        attendanceTypes: [
            { label: '循环考勤', value: 0 },
            { label: '单次考勤', value: 1 },
        ],
        // 考勤状态
        attendanceStatus: [
            { label: '正常', value: 0, color: '#00B781' },
            { label: '未归寝', value: 1, color: '#F5222D' },
            { label: '晚归', value: 2, color: '#FC941F' },
            { label: '请假', value: 5, color: '#3896EA' },
            { label: '未打卡', value: 6, color: '#5534F0' },
        ],
        // 楼栋列表
        buildings: [],
        // 场地类型
        siteTypes: [],
        // 设备列表
        devices: [],
        // 寝室床位设置方式
        bedSetTypes: [
            { label: '按每层设置', value: 1 },
            { label: '按整栋设置', value: 2 },
        ],
        // 通行方向
        directions: [
            { label: '出', value: 1, color: '#00B781', },
            { label: '入', value: 2, color: '#FAAD14 ', },
        ],
        // 通行方式
        accessTypes: [
            { label: '人脸', value: 1 },
            { label: '刷卡', value: 2 },
        ],
        // 寝室状态
        roomStatus: [
            { label: '满员', value: 2, color: '#FAAD14' },
            { label: '未满', value: 1, color: '#ffffff' },
            { label: '空房', value: 0, color: '#00B781' },
            // { label: '未分房', value: 3, color: '#D9D9D9' },
        ],
        // 调动方式
        transferTypes: [
            { label: '换寝', value: 1 },
            { label: '退寝', value: 2 },
        ],
        // 性别
        genders: [
            { label: '男生', value: 1 },
            { label: '女生', value: 0 },
        ]
    },
    mutations: {
        setActiveDorm(state: State, dorm: {}) {
            state.activeDorm = dorm || {}
        },
        setDorms(state: State, dorms: []) {
            state.dorms = dorms || []
        },
        setBuildings(state: State, buildings: []) {
            state.buildings = buildings || []
        },
        setSiteTypes(state: State, siteTypes: []) {
            state.siteTypes = siteTypes || []
        },
        setDevices(state: State, devices: []) {
            state.devices = devices || []
        },
    },
    actions: {
        // 宿舍列表
        async getDorms({ commit, state }: any) {
            return axios
                .post("/cloud/dormitory/buildingManagement/dormitoryBuilding")
                .then((res) => {
                    commit('setActiveDorm', res.data?.length ? res.data[0] : {})
                    commit('setDorms', res.data)
                    return res
                });
        },
        // 建筑列表
        async getBuildings({ commit, state }: any, params = {}) {
            return getCloudBuildList(params)
                .then((res) => {
                    commit('setBuildings', res.data)
                    return res
                })
        },
        // 场景列表
        async getSiteTypes({ commit, state }: any, params = {}) {
            return axios
                .post("/cloud/site/siteTypePage", params)
                .then((res) => {
                    commit("setSiteTypes", res.data?.list)
                    return res
                });
        },
        // 设备列表
        async getDevices({ commit, state }: any) {
            return siteDeviceList()
                .then((res) => {
                    commit('setDevices', res.data)
                    return res
                })
        },
    }
}
