/* eslint-disable */
import { getqueryClassMaterList } from '@/api/3.0.js'
import { Local } from '@/utils/storage.ts'
const cutClass = {
    namespaced: true,
    state: {
        director_classId: '',
        isSwitchclass: false,
        classInfo: {},
        classesObj: {}
    },
    mutations: {
        // 存储id
        UPDATECLASSID(state, id) {
            state.director_classId = id
        },
        SETSHOW(state, is) {
            state.isSwitchclass = is
        },
        UPDATEINFO(state, data) {
            state.classInfo = data
        },
        CLASSES_OBJ(state, data = {}) {
            state.classesObj = data
        }
    },
    actions: {
        async reqqueryClassMaterList({ commit }) {
            new Promise(async (resolve, reject) => {
                const { data } = await getqueryClassMaterList()
                // 没有id 就算了
                if (!data.length) return
                commit('UPDATEINFO', data)
                const director_classId = Local.get('director_classId')
                // 如果已经缓存了班主任的班级id 那就算了,优先用缓存内的
                if (director_classId) return
                commit('UPDATECLASSID', data[0]?.id) // 默认储存第一个班级id
                Local.set('director_classId', data[0]?.id) // 默认储存第一个班级id
                resolve(data)
            })
        },
    }
}
export default cutClass
