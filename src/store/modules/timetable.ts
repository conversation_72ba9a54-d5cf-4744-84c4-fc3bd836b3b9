/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-06-30 23:47:29
 * @LastEditors: yjx
 * @LastEditTime: 2023-09-05 16:40:14
 */
import api from '@/api'
import * as defHttp from '@/api/defHttp'

const timetableModule = {
    namespaced: true,
    state: {
        timetableData: {
            weekTotal: 0, // 总周数
            thisWeek: 1,
            thisWeekStartDate: '',
            thisWeekEndDate: '',
            semesterYear: '',
            semesterYearName: '',
            sectionList: {
                earlySelfStudyList: [], // 早自习数据列表
                morningList: [], // 上午数据列表
                afternoonList: [], // 下午数据列表
                nightList: [], // 晚上数据列表
                lateSelfStudyList: [] // 晚自习数据列表
            }
        },
        // 课程
        sectionData: [
            { name: '早自习', key: 'earlySelfStudyList', weeks: {}, sectionType: 1 },
            { name: '上午', key: 'morningList', weeks: {}, sectionType: 2 },
            { name: '下午', key: 'afternoonList', weeks: {}, sectionType: 3 },
            { name: '晚上', key: 'nightList', weeks: {}, sectionType: 4 },
            { name: '晚自习', key: 'lateSelfStudyList', weeks: {}, sectionType: 5 }
        ],
        allSectionData: {},
        weekList: {},
        isTimetable: 0

    },
    mutations: {
        TIMETABLE_WEEKTIME(state, paylod) {
            state.timetableData = paylod
            state.weekList = paylod.weekList
            state.allSectionData = {}
            state.isTimetable = paylod.timetableList.length
            paylod.timetableList.map((v) => {
                const idx = v.sectionType - 1 // 取时段
                // 时段 + 星期 + 节次
                if (!state.allSectionData[`${state.sectionData[idx].key}_${v.sectionTypeNum}_${v.week}`]) {
                    state.allSectionData[`${state.sectionData[idx].key}_${v.sectionTypeNum}_${v.week}`] = v
                }
            })
        }
    },
    actions: {
        // 获取每周课表
        async getTimetableWeekTime({ commit }, paylod: object) {
            return new Promise((resolve, reject) => {
                defHttp.post(api.timetableWeekTime, paylod).then((res) => {
                    const { code, data } = res
                    resolve(data)
                    !code && commit('TIMETABLE_WEEKTIME', data)
                }).catch((err: any) => reject(err))
            })
        }

    }
}

export default timetableModule
