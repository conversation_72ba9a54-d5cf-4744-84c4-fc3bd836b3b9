/*
 * @Descripttion: 页面初始化数据
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @LastEditors: yjx
 * @LastEditTime: 2023-09-04 14:17:55
 */
/* eslint-disable */
import {
    getDepartmentTree,
    getSchoolRollTree,
    getCustomMethod,
    getSchoolRollBrand,
} from "@/api/faceLibrary.js";

import {
    getNewRouters,
    getJsonCommentKey,
    listNoSingleApp,
    checkUserLogin,
} from "@/api/user";
import WebSocketClient from "@/utils/webSocket";
import api from "@/api/index.ts";
import ViewCom from "@/router/view.vue";
import modules from "@/store/modules/modules.js";
const pMeteMap = {};

import {
    getApplyGroupList,
} from "@/api/apply";


const hasIntelligenceCode = ref(false)

let aiRouter = {
    "appCode": "",
    "btnList": [],
    "children": [],
    "code": "",
    "component": "chatAi",
    "createTime": "",
    "customParams": "",
    "directoryList": [],
    "filePath": "../../views/chatAi/index.vue",
    "icon": "icon-xiaoyiyun",
    "id": "1913040299720634370",
    "isCache": null,
    "isDefault": false,
    "isFrame": true,
    "isHideInMenu": null,
    "isVisible": true,
    "jumpList": [],
    "link": "http://192.168.3.158:8038/#/home",
    "name": "小壹AI智能",
    "path": "/chatAi",
    "perms": "",
    "pid": "0",
    "redirect": "/",
    "schoolType": 0,
    "sort": 8,
    "status": 0,
    "tabList": [],
    "type": "C"
}
async function getApplyGroupListFn() {
    await getApplyGroupList()
        .then(({ data }) => {
            hasIntelligenceCode.value = data.some(item => item.apps && item.apps.some(appItem => appItem.code == 'Intelligence'));
        })
}


function flatTree(data = [], treeMap = [], depth = 0): Array<object> {
    if (!(data && data.length)) return [];
    depth++;
    return data.reduce((acc, cur: unknown) => {
        cur.depth = depth;
        acc.push(cur);
        if (cur.children && cur.children.length) {
            flatTree(cur.children, treeMap, depth);
        }
        return acc;
    }, treeMap);
}
const btnList: any = [];
function getBtnList(item: any) {
    item.forEach((i: any) => {
        btnList.push(i.path);
        if (i.children && i.children.length > 0) {
            getBtnList(i.children);
        }
        if (i.btnList && i.btnList.length > 0) {
            getBtnList(i.btnList);
        }
    });
    localStorage.setItem(
        "btnList",
        JSON.stringify(Array.from(new Set(btnList)))
    );
}
function hasPermission(route, permission: []) {
    const flatTreeArr: Array<{ path: string; btnList: []; code: string }> =
        flatTree(permission) as [];
    if (route.meta) {
        let flag = false;
        // ———————————————————————————— 白名单：提交代码 developWhiteList 必须为空
        // 默认的 不要修改
        let defaultWhiteList = [
            "dashboard",
            "layoutManage",
            "componentLibrary",
            "userCenter",
            "noauth401",
            "401",
            "siteEquipment",
            "siteBooking",
            "siteBook",
            "siteIdleList",
            "moralEducationEvaluation",
            // 'moralEducationEvaluationSet',
            "moralEducationEvaluationedit",
            // 'moralEducationEvaluationclassInfo',
            "moralEducationEvaluationactivity",
            "activityDetails",
            "activityDetailsCom",
            "schoolDormSystem",
            "schoolLibrarySystem",
            "schoolAttendanceSystem",
            "sAttendanceInAndOuts",
            "sAttendanceEvents",
            "sAttendanceCourses",
            "sAttendanceStatisticss",
            "sAttendanceDetailss",
            "chatAi"
        ];
        //  临时开发时 上线记得删掉为空！！
        const developWhiteList = [];
        if (
            import.meta.env.MODE === "development" ||
            import.meta.env.MODE === "uat"
        ) {
            defaultWhiteList = defaultWhiteList.concat(developWhiteList);
        }
        for (let i = 0, len = flatTreeArr.length; i < len; i++) {
            flag = route.name === flatTreeArr[i].path;

            // 信息发布先特殊处理
            if (flatTreeArr[i].path === "notice") {
                defaultWhiteList = defaultWhiteList.concat([
                    "createInformation",
                    "horVertDetails",
                    "template",
                    "announcement",
                ]);
            }
            if (flatTreeArr[i].path) {
            }
            // 白名单跳过
            if (defaultWhiteList.includes(route.name)) {
                return true;
            }

            if (flag) {
                try {
                    let actionList: Array<{}> = [];

                    if (flatTreeArr[i].btnList) {
                        actionList = flatTreeArr[i].btnList.map(
                            (i: {
                                path: string;
                                name: string;
                                isRule?: boolean;
                            }) => {
                                return {
                                    label: i.name,
                                    value: i.path,
                                };
                            }
                        );
                    }
                    // 子页面的按钮同名问题 作此处理（ value: v.path + '_' + i.path）
                    if (flatTreeArr[i]?.children.length) {
                        let newActionList = [];
                        flatTreeArr[i]?.children.forEach((v) => {
                            newActionList = v.btnList.map(
                                (i: {
                                    path: string;
                                    name: string;
                                    isRule?: boolean;
                                }) => {
                                    return {
                                        label: i.name,
                                        value: v.path + "_" + i.path,
                                    };
                                }
                            );
                            actionList = actionList.concat(newActionList);
                        });
                    }
                    // 以下先不处理，后期加上
                    // isCache:  是否缓存页面：
                    // isVisible: 是否菜单显示：
                    // 路由是否显示在菜单
                    // route.hideInMenu = flatTreeArr[i].isVisible
                    // 子路由是否显示在菜单
                    // route.hideChildrenInMenu =  flatTreeArr[i].isVisible
                    route.meta.btnList = actionList;
                    route.meta.code = flatTreeArr[i].code;
                    route.meta.isRule = pMeteMap[flatTreeArr[i].code]?.isRule;

                    // route.meta.icon = flatTreeArr[i].icon || route.meta.icon
                    // route.meta.title = flatTreeArr[i].name || route.meta.title
                } catch (error) {
                    console.error(error);
                }
                return true;
            }
        }
        return false;
    }
    return false;
}
const objs = {};
function generator(routerMap: [], backEndRouter: []): [] {
    const accessedRouters = routerMap.filter(
        (route: { children: []; name: string; redirect?: string }) => {
            if (hasPermission(route, backEndRouter)) {
                objs[route.name] = route;
                if (route.children && route.children.length) {
                    route.children = generator(route.children, backEndRouter);
                    // 白名单 => 不需要redirect的路由 'managementModule'
                    const whiteListNoRedirect = ["dashboard"];
                    if (!whiteListNoRedirect.includes(route.name)) {
                        route.redirect = route.children[0]?.path;
                    }
                    // TODO 特殊处理，当前端有子页面，后端数据无返回时页面显示空白找不到子路由时
                    if (!route.children.length) {
                        route.children = [
                            {
                                path: "/404",
                                name: "notFound",
                                component: () =>
                                    import("@/views/error/401.vue"),
                                meta: {
                                    title: "NotFound",
                                },
                            },
                        ];
                        route.redirect = "/404";
                    }
                }
                return true;
            }
            return false;
        }
    );
    return accessedRouters as [];
}
const regRoute = (arr: any) => {
    let list: any = [];
    arr?.forEach((i: any) => {
        if (!(i.type == "C" || i.type == "A" || i.type == "J")) return;
        const btnList = i.btnList.map((v) => {
            return {
                label: v.name,
                value: v.perms,
            };
        });
        let item: any = {
            path: i.path,
            name: i.component,
            meta: {
                btnList,
                title: i.name,
                icon: i.icon,
                tabList: i.tabList,
                customParams: i.customParams,
            },
            hideInMenu: i.type != "C",
            redirect: "",
        };
        if (i.path !== "/dashboard") {
            item.redirect = i.children.length ? i.children[0].path : "";
        }
        if (i.path.indexOf("/appModel") !== -1) {
            const paths = i.path.split("/");
            item.meta.code = paths[2];
        }
        if (i.children.length > 0) {
            item.children = regRoute(i.children);
        }
        if (i.filePath) {
            item.component = modules[i.filePath] || ViewCom;
        } else {
            item.component = ViewCom;
        }
        list.push(item);
    });
    return list;
};
const pathMatch: object = {
    path: "/:path(.*)*",
    redirect: "/404",
    hideInMenu: true,
};
export default {
    namespaced: true,
    state: () => {
        return {
            isAnnounceTop: false,
            allRouteObjs: objs,
            commentJsonKeys: {},
            departmentTree: [],
            departmentSelectedKeys: [],
            schoolRollTree: [],
            schoolRollSelectedKeys: [],
            permission: [],
            allJurisdiction: [],
            userRoleList: [],
            ws: null,
            userInfo: null,
            permissionList: [],
            integratedSiderTwoLevels: [],
        };
    },
    getters: {
        departmentTree(state: any) {
            return state.departmentTree;
        },
        schoolRollTree(state: any) {
            return state.schoolRollTree;
        },
    },
    mutations: {
        // 获取组织架构tree
        setDepartmentTree(state: any, list: any) {
            state.departmentSelectedKeys = [list[0]?.id];
            state.departmentTree = list;
        },
        // 获取学籍tree
        setSchoolRollTree(state: any, list: any) {
            state.schoolRollSelectedKeys = [list[0]?.id];
            state.schoolRollTree = list;
        },
        setPermission(state: any, list: []) {
            state.permission = list;
        },
        // 所有权限
        ALL_JURISDICTION(state: any, list: []) {
            state.allJurisdiction = list;
        },
        PERMISSION_LIST(state: any, list: []) {
            state.permissionList = list;
        },
        setUserInfo(state: any, info: {}) {
            state.userInfo = info;
        },
        setUserRoleList(state: any, paylod: []) {
            state.userRoleList = paylod;
        },
        COMMENT_JSON_KEYS(state: any, paylod) {
            state.commentJsonKeys = paylod;
        },
        INTEGRATED_INTOSY_STEM(state: any, paylod) {
            state.integratedSiderTwoLevels = paylod;
        },
        setIsAnnounceTop(state: any, data) {
            state.isAnnounceTop = data;
        },
    },
    actions: {
        // 组织架构
        async getDepartmentTree({ commit, state }: any, code: string) {
            return new Promise(async (relove, reject) => {
                let result: { data: [] } = { data: [] };
                try {
                    result = await getDepartmentTree(code);
                    commit("setDepartmentTree", result.data || []);
                    relove(result);
                } catch (error) {
                    reject(error);
                }
            });
        },
        // 学籍
        async getSchoolRollTree({ commit, state }: any, code: string) {
            return new Promise(async (relove, reject) => {
                let result: { data: [] } = { data: [] };
                try {
                    result = await getSchoolRollTree(code);
                    commit("setSchoolRollTree", result.data);
                    relove(result);
                } catch (error) {
                    reject(error);
                }
            });
        },
        //  Platform code
        getUserRouters({ commit, dispatch }: any, params: any) {
            return new Promise(async (relove, reject) => {
                const { schoolId, Platform } = params;
                // 登录进来后再次请求下用户校验接口
                await checkUserLogin({ schoolId });
                getNewRouters({ Platform: "cloud" })
                    .then(async (res) => {
                        await getApplyGroupListFn()
                        if (hasIntelligenceCode.value) {
                            res.data.push(aiRouter)
                        }
                        const result = regRoute(res.data);
                        commit("setPermission", result);
                        dispatch(
                            "cutClass/reqqueryClassMaterList",
                            {},
                            { root: true }
                        );
                        commit("setUserInfo", res.data.userInfo);
                        relove(result);
                    })
                    .catch((res) => {
                        console.log(res);
                    });
            });
        },

        // 场地班牌
        getCustomMethod({ commit }: any, paylod) {
            return new Promise(async (relove, reject) => {
                let result: { data: [] } = { data: [] };
                try {
                    result = await getCustomMethod(
                        api.buildingMachine,
                        "post",
                        paylod
                    );
                } catch (error) {
                    console.error(error);
                }
                relove(result);
            });
        },

        // 班级班牌
        getSchoolRollBrand({ commit }: any, paylod) {
            return new Promise(async (relove, reject) => {
                let result: { data: [] } = { data: [] };
                try {
                    result = await getSchoolRollBrand(
                        api.schoolRollBrand,
                        "post",
                        paylod
                    );
                } catch (error) {
                    console.error(error);
                }
                relove(result);
            });
        },

        initWebSocket(state, config = {}) {
            state.ws = new WebSocketClient(config);
            state.ws.init();
        },
        async getCommentKeys({ commit }: any, schoolId) {
            const params = {
                schoolId,
                commentKey: [
                    "INIT_ACT_APPROVE",
                    "INIT_ACT_LEAVE",
                    "INIT_IDENTITY_LIST",
                    "INIT_STUDENT_IMPORT_EXPORT_LIST",
                    "INIT_IMPORT_EXPORT_LIST",
                    "SCHOOL_TYPE",
                    "INIT_SELECT",
                    "INIT_SCHOOL",
                    "INIT_STUDENT_TABLE_LIST",
                    "INIT_OPERATION_LIST",
                    "INIT_ROLL_LIST",
                ],
            };
            const { data } = await getJsonCommentKey(params);
            commit("COMMENT_JSON_KEYS", data || {});
        },
        // 集成到系统 （二级菜单）
        async integratedIntoSystem({ commit, state }, systemAppId) {
            const { data } = await listNoSingleApp({ systemAppId });
            commit("INTEGRATED_INTOSY_STEM", data);
        },
    },
};
