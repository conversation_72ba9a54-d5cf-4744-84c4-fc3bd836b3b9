/*
 * @Descripttion:  classScheduleModule
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-31 10:19:01
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-03-08 21:11:33
 */
import { Session } from '@/utils/storage'
// 此处加上 `.ts` 后缀报错，具体原因不详
import { getExist } from '@/api/classSchedule.js'

const classScheduleModule = {
    namespaced: true,
    state: {
        TimetableExistData: {},
        type: 2,
        isExists: false,
        oneClickClear: 0,
        fieldTrees: [],
        classTrees: [], // 班级
        defaultExpand: '',
        selectedKeys: ''
    },
    mutations: {
        TIMETABLE_EXIST(state: any, paylod: any) {
            state.TimetableExistData = paylod
            if (paylod.buildings.length) {
                state.fieldTrees = paylod.buildings
                state.defaultExpand = paylod.buildings[0].id
                state.selectedKeys = paylod.buildings[0].children[0].id
            }
            state.isExists = paylod.isExists
        }
    },
    actions: {
        // 获取场地列表
        getTimetableExist({ commit }: any, paylod: any) {
            return new Promise((resolve, reject) => {
                getExist(paylod).then((res: any) => {
                    const { code, data } = res
                    !code && commit('TIMETABLE_EXIST', data)
                    resolve(res)
                }).catch((error) => {
                    reject(error)
                })
            })
        }
    }
}

export default classScheduleModule
