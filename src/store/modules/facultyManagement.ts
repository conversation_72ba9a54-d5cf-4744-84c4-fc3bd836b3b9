/*
 * @Descripttion: 教职工管理 facultyManagement
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-31 10:19:01
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-04-24 17:44:02
 */
// 此处加上 `.ts` 后缀报错，具体原因不详
import api from '@/api'
import * as defHttp from '@/api/defHttp'

const facultyManagementModule = {
    namespaced: true,
    state: {
        facultyData: {
            list: [],
            total: 0
        },
        isCheckAll: false
    },
    mutations: {
        FACULTY_LIST_INFOS(state: any, paylod: any) {
            state.facultyData.list = paylod.list
            state.facultyData.total = paylod.total
        }

    },
    actions: {
        // 获取教职工列表数据
        async getFacultyListInfos({ commit }: any, paylod: any) {
            return new Promise((resolve, reject) => {
                defHttp.post(api.facultyList, paylod).then((res: any) => {
                    const { code, data } = res
                    !code && commit('FACULTY_LIST_INFOS', data)
                    resolve(res)
                }).catch((error) => {
                    reject(error)
                })
            })
        }

    }
}

export default facultyManagementModule
