/*
 * @Descripttion:日程 fullcalendarModule
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-03-14 15:17:24
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-04-11 14:35:02
 */

// 此处加上 `.ts` 后缀报错，具体原因不详
import { scheduleLabel, scheduleLCreateLabelBatch, scheduleList } from '@/api/fullcalendar.js'

const fullcalendarModule = {
    namespaced: true,
    state: {
        timeDayjs: '',
        scheduleLabelList: [],
        isShowSchedule: true,
        scheduleData: {
            timeShow: 0,
            historyShow: 1,
            scheduleList: [],
            waitingSchedule: [],
            historySchedule: []
        },
        // 日程查询表单数据
        queryCalendarForm: {
            labelId: null,
            type: null,
            status: null,
            name: '',
            startTime: '',
            endTime: ''
        },
        scheduleDetail: {
            visible: false,
            isShowDetails: false,
            startStrTime: '',
            title: '新建日程',
            id: ''
        }
    },
    mutations: {
        SCHEDULE_LABEL(state, paylod) {
            state.scheduleLabelList = []
            state.scheduleLabelList = paylod
        },
        SCHEDULE_LIST(state, paylod) {
            state.scheduleData = paylod
        },
        SCHEDULE_DETAIL(state, paylod) {
            state.scheduleDetail = paylod
        }
    },
    actions: {
        // 获取标签列表
        getScheduleLabel({ commit }, paylod) {
            return new Promise((resolve, reject) => {
                scheduleLabel(paylod).then((res) => {
                    const { data } = res
                    resolve(data)
                    commit('SCHEDULE_LABEL', data)
                }).catch((error) => {
                    reject(error)
                })
            })
        },
        // 添加标签
        createScheduleLabel({ commit }, paylod) {
            return new Promise((resolve, reject) => {
                scheduleLCreateLabelBatch(paylod).then((res) => {
                    resolve(res)
                }).catch((error) => {
                    reject(error)
                })
            })
        },
        // 日程列表
        getScheduleList({ commit }, paylod) {
            return new Promise((resolve, reject) => {
                scheduleList(paylod).then((res) => {
                    const { data } = res
                    commit('SCHEDULE_LIST', data)
                    resolve(res)
                }).catch((error) => {
                    reject(error)
                })
            })
        }
    }
}

export default fullcalendarModule
