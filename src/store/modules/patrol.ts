// 巡查

const patrol = {
    namespaced: true,
    state: {
        formsActive: 0,
        formItems: []
    },
    mutations: {
        // 修改状态
        UPDATEFORMSACTIVE(state, paylod) {
            state.formsActive = paylod
        },
        // 修改表单属性
        UPDATEFORMPROPERTIES(state, paylod) {
            const { type, idx, params } = paylod
            // 刪除
            if (type === 'delete') {
                state.formItems.splice(idx, 1)
            } else {
                state.formItems = params
            }
        }
    },
    actions: {}
}
export default patrol
