/*
 * @Descripttion:  functionalAuthorityModule
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-03-14 15:17:24
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-04-22 17:21:31
 */

// 此处加上 `.ts` 后缀报错，具体原因不详
const functionalAuthorityModule = {
    namespaced: true,
    state: {
        fatherSonCollection: {}
    },
    mutations: {
        // 添加
        FATHER_SON_COLLECTION(state, paylod) {
            const { id, it } = paylod
            // if (!state.fatherSonCollection[id] || !state.fatherSonCollection[id].length) {
            //     state.fatherSonCollection[id] = [it]
            // } else {
            
                const findI = state.fatherSonCollection[id].some(
                    (v) => v.id !== it.id
                )
                findI && state.fatherSonCollection[id].push(it)
            // }
        },
        // 删除对象
        DEl_FATHER_SON_COLLECTION(state, paylod) {
            const { pid, id } = paylod
            if (state.fatherSonCollection[pid]?.length) {
                const idx = state.fatherSonCollection[pid].findIndex(
                    (v) => v.id === id
                )
                state.fatherSonCollection[pid].splice(idx, 1)
            }
        }
    },
    actions: {

    }
}

export default functionalAuthorityModule
