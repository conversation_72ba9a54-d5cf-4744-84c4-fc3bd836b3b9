/*
 * @Descripttion: 信息发布 noticeModule
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-31 10:19:01
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-04-14 11:22:03
 */
import {
    getInformationList,
    getInformationListAll,
    getInformationListReceive,
    getSchoolList,
    getInformationClassList
} from '@/api/notice'
const noticeModule = {
    namespaced: true,
    state: {
        isInquires: '',
        identifier: '',
        // 我发送的
        announcementData: {
            list: [],
            pageNo: 1,
            pageSize: 10,
            total: 0
        },
        // 我收到的
        announcementReceiveData: {
            list: [],
            pageNo: 1,
            pageSize: 10,
            total: 0
        }
    },
    mutations: {
        ANNOUNCEMENT_TYPE(state, paylod) {
            state.identifier = paylod
        },
        INFORMATION_LIST(state, paylod) {
            state.announcementData = paylod
        },

        INFORMATION_LIST_RECEIVE(state, paylod) {
            state.announcementReceiveData = paylod
        },
        ISINQUIRES_MUTATIONS(state, paylod) {
            state.isInquires = paylod
        }

    },
    actions: {
        // 获取我发布列表
        getInformationList({ commit }, paylod) {
            return new Promise((resolve, reject) => {
                getInformationList(paylod).then((res) => {
                    const { data } = res
                    commit('INFORMATION_LIST', data)
                    resolve(res)
                }).catch((error) => {
                    reject(error)
                })
            })
        },
        // 获取学校通知列表
        getSchoolList({ commit }, paylod) {
            return new Promise((resolve, reject) => {
                getSchoolList(paylod).then((res) => {
                    const { data } = res
                    commit('INFORMATION_LIST', data)
                    resolve(res)
                }).catch((error) => {
                    reject(error)
                })
            })
        },
        // 获取班级通知列表
        getInformationClassList({ commit }, paylod) {
            return new Promise((resolve, reject) => {
                getInformationClassList(paylod).then((res) => {
                    const { data } = res
                    commit('INFORMATION_LIST', data)
                    resolve(res)
                }).catch((error) => {
                    reject(error)
                })
            })
        },
        // 获取所有信息列表
        getInformationListAll({ commit }, paylod) {
            return new Promise((resolve, reject) => {
                getInformationListAll(paylod).then((res) => {
                    const { data } = res
                    commit('INFORMATION_LIST', data)
                    resolve(res)
                }).catch((error) => {
                    reject(error)
                })
            })
        },

        // 获取我收到列表
        getInformationListReceive({ commit }, paylod) {
            getInformationListReceive(paylod).then((res) => {
                const { data } = res
                commit('INFORMATION_LIST_RECEIVE', data)
            })
        }

    }
}

export default noticeModule
