/*
 * @Descripttion: selectSource
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-30 15:10:06
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-04-14 10:26:11
 */
import { Session, Local } from '@/utils/storage'
// 此处加上 `.ts` 后缀报错，具体原因不详
import api from '@/api'
import * as defHttp from '@/api/defHttp'
const getdictionary = Local.get('dictionary')
// roll_status_no 学籍不在校状态 roll_status所有状态 roll_status_yes 在校状态
// preparation_category // 编制类别
// marital_type // 婚姻状况
// health_type // 健康状况
// highest_education // 学历
// political_status // 政治面貌
// registered_residence_type // 户口类别
// postal_code // 港澳台侨代码
// teacher_type//教职工类别
// id_type//证件类型
// nationnation//职称
// emp_status // 入职状态
// emp_status_yes  //在任
// emp_status_no  //不在任
// blood_type//血型
// nation//民族
// gender  用户性别
// relations  家庭关系
// political_status 政治面貌
// accommodation 住宿方式
// roll_status 学籍状态
// health_status // 健康状况
// blood_type//血型
// household_type // 户口类别
// nation//民族
// id_type//证件类型
// country 国籍
// school_nature  学校性质
// school_type  学校类型
// school_area  学校区域
// school_quality  学校质量
// report_type  上报类型
// student_education_status, // 学历
// student_academic_system_status, // 学制
// student_second_degree_status //  第二学位
const paramsDictionary = [
    'face_user_type',
    'face_sync_status',
    'roll_status_no',
    'roll_status_yes',
    'roll_status',
    'emp_status',
    'emp_status_no',
    'emp_status_yes',
    'preparation_category',
    'marital_type',
    'health_type',
    'highest_education',
    'registered_residence_type',
    'postal_code',
    'teacher_type',
    'id_type',
    'nationnation',
    'blood_type',
    'nation',
    'country',
    'relations',
    'political_status',
    'accommodation',
    'roll_status',
    'id_type',
    'nation',
    'household_type',
    'blood_type',
    'health_status',
    'gender',
    'school_nature',
    'school_type',
    'school_area',
    'school_quality',
    'report_type',
    'student_education_status', // 学历
    'student_academic_system_status', // 学制
    'student_second_degree_status', //  第二学位
    'dormitory_room_type'
]

const selectSourceModule = {
    namespaced: true,
    state: {
        modalVisible: false,
        selectedPoolSelectedSet: [],
        selectSourceObj: {
            isParent: false, // 是否添加家长
            treeData_teacher: [], // 老师
            treeData_student: [], // 学生
            defaultExpand: [], // 默认展开
            isNum: 0, // 限制选中数量
            selectedPoolSelectedSet: [], // 回显的数据
            checkPeople: false,
            singleChoice: false,
            optionalClass: {},
            tabsTexts: [
                { tab: '老师', key: 'teacher' },
                { tab: '学生', key: 'student' }
            ]
        },
        studentStatusTree: [],
        isTabel: false,
        dictionary: {
            ...getdictionary
        }
    },
    mutations: {
        // 开启选人模块  修改状态
        GET_USER_INFOS(state: any, data: any) {
            state.modalVisible = data.modalVisible
            if (data.selectSourceObj) {
                state.selectSourceObj = {
                    ...state.selectSourceObj,
                    ...data.selectSourceObj
                }
            }
        },
        // 已选中的数据
        CALLBACK_PARAMETER(state: any, data: any) {
            state.selectedPoolSelectedSet = data
        },
        STUDENT_STATUS_TREE(state: any, data: any) {
            state.selectSourceObj.treeData_student = data
            state.studentStatusTree = data
        },
        // 字典集合
        DICTIONARY_COLLECTION(state: any, data: any) {
            const { dictionary } = state
            data.forEach((v: any) => {
                dictionary[v.type] = v.list // 字典集合
                dictionary[`${v.type}_filters`] = [] // table  columns filters
                dictionary[`${v.type}_id`] = [] // 字典指定id
                dictionary[`${v.type}_filters`] = v.list.map((j: any) => {
                    dictionary[`${v.type}${j.value}`] = j.label // 字典指定text
                    dictionary[`${v.type}_id`].push(j.value)
                    return {
                        text: j.label,
                        value: j.value
                    }
                })
            })
            Local.set('dictionary', dictionary)
        }
    },
    actions: {
        // 组织架构 tree
        async setOrganizationTree({ commit }: any, paylod: object) {
            return new Promise((resolve, reject) => {
                defHttp.get(api.admineptDoList, paylod).then((res: any) => {
                    const { code, data } = res
                    resolve(data)
                    !code && commit('GET_USER_INFOS', data)
                })
            })
        },
        // 学籍 tree
        async setStudentStatusTree({ commit }: any, paylod: object) {
            return new Promise((resolve, reject) => {
                defHttp
                    .get(api.schoolRoll, paylod)
                    .then((res: any) => {
                        const { code, data } = res
                        resolve(data)
                        !code && commit('STUDENT_STATUS_TREE', data)
                    })
                    .catch((error: any) => {
                        reject(error)
                    })
            })
        },
        // 学籍v1 tree
        async setStudentStatusTreev1({ commit }: any, paylod: object) {
            return new Promise((resolve, reject) => {
                defHttp
                    .get(api.schoolRollv1, paylod)
                    .then((res: any) => {
                        const { code, data } = res
                        resolve(data)
                        !code && commit('STUDENT_STATUS_TREE', data)
                    })
                    .catch((error: any) => {
                        reject(error)
                    })
            })
        },
        // 获取数据字典 select
        async getDataDictionary({ commit }: any, paylod: any) {
            return new Promise((resolve, reject) => {
                defHttp
                    .post(api.dataDictionary, paramsDictionary)
                    .then((res: any) => {
                        const { code, data } = res
                        !code && commit('DICTIONARY_COLLECTION', data)
                        resolve(data)
                    })
                    .catch((error: any) => {
                        reject(error)
                    })
            })
        }
    }
}

export default selectSourceModule
