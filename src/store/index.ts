/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-30 15:06:28
 * @LastEditors: jingrou
 * @LastEditTime: 2022-07-04 13:55:01
 */
import { createStore, useStore as baseUseStore } from 'vuex'
import createPersistedState from 'vuex-persistedstate'
import { ls } from '@/utils/util'
import config from '@/config/config'
const { ACCESS_DATA } = config

// Vite supports importing multiple modules from the file system using the special import.meta.glob function
// see https://cn.vitejs.dev/guide/features.html#glob-import
const modulesFiles = import.meta.globEager('./modules/*.ts')
const pathList: string[] = []
for (const path in modulesFiles) {
    pathList.push(path)
}
const modules = pathList.reduce(
    (modules: { [x: string]: any }, modulePath: string) => {
        const moduleName = modulePath.replace(/^\.\/modules\/(.*)\.\w+$/, '$1')
        const value = modulesFiles[modulePath]
        modules[moduleName] = value?.default || ''
        return modules
    },
    {}
)

export const store = createStore({
    modules,
    plugins: [
        createPersistedState({
            storage: {
                getItem: (key) => ls.get(key),
                setItem: (key, value) => ls.set(key, value),
                removeItem: (key) => ls.remove(key)
            },
            key: ACCESS_DATA, // 存储名字

            paths: ['user'] // 指定模块
        })
    ]
})

export function useStore() {
    // eslint-disable-line no-unused-vars
    return baseUseStore()
}
