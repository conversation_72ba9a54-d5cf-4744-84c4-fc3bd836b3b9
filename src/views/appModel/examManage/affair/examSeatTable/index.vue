<!-- 考场座位表 -->
<!-- 考场座位表 -->
<!-- 考场座位表 -->
<template>
    <div class="pageMain">
        <div class="headCon">
            <a-radio-group v-model:value="seatType" button-style="solid">
                <a-radio-button value="room">考场</a-radio-button>
                <a-radio-button value="class">考场组</a-radio-button>
            </a-radio-group>
        </div>
        <div class="comType">
            <div class="room" v-if="seatType === 'room'">
                <div class="head">
                    <a-form layout="inline">
                        <a-form-item label="考场:">
                            <a-select
                                :getPopupContainer="
                                    (triggerNode) => triggerNode.parentNode
                                "
                                style="width: 360px"
                                allowClear
                                @change="changeSite"
                                v-model:value="state.siteId"
                                placeholder="请选择"
                                :options="state.siteOptions"
                                :showArrow="true"
                                :field-names="{
                                    label: 'name',
                                    value: 'id',
                                }"
                            >
                            </a-select>
                        </a-form-item>
                    </a-form>
                    <div>
                        <a-tooltip overlayClassName="poperLayTable" placement="bottomRight">
                            <template #title>
                                <div class="seatBox">
                                    <div class="titleBox">
                                        <div>
                                            考场容量：<span>{{
                                                state.examPeopleNum
                                            }}</span>
                                        </div>
                                        <div>
                                            考场布局：<span
                                                >{{ state.siterow }} *
                                                {{ state.sitecell }}</span
                                            >
                                        </div>
                                        <div>
                                            编排方式：<span>{{
                                               arrangeObj[state.arrangeType] || "-"}}</span>
                                        </div>
                                    </div>

                                    <div class="container">
                                        <!-- {{state.siteArr}} -->
                                        <div
                                            v-for="(
                                                row, index
                                            ) in state.siteArr"
                                            :key="index"
                                            class="row"
                                        >
                                            <!-- <div  class="seat" v-for="seat in row" :key="seat.seat">{{ seat.seat }}</div> -->
                                            <!-- <div v-else class="seat" v-for="seat in row.reverse()" :key="seat.seat">{{ seat.seat }}</div> -->
                                            <div
                                                class="seat"
                                                v-for="item in row"
                                                :key="item.seat"
                                            >
                                            <div style="padding-right: 15px;"><img src="/image/siteIcon.png" alt=""></div>
                                            <div style="padding-right: 15px;">{{ item.seat }}</div>
                                            <div>{{ item.personName }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            <span v-show="state.siteId" class="lookImg">
                                <eye-outlined />查看座位图</span
                            >
                        </a-tooltip>
                        <a-button @click="roomEx">导出</a-button>
                    </div>
                </div>
                <div>
                    <Tables
                        :spinning="state.roomSpinning"
                        :paginations="state.roomPaginations"
                        :columns="roomColumns"
                        :dataSource="state.roomSource"
                        :bordered="false"
                        @change="roomTableChange"
                    >
                        <template #bodyCell="{ column, record }"> </template>
                    </Tables>
                </div>
            </div>
            <div class="classBox" v-if="seatType === 'class'">
                <div class="head">
                    <a-form layout="inline">
                        <a-form-item label="考试人员:">
                            <a-select
                                :getPopupContainer="
                                    (triggerNode) => triggerNode.parentNode
                                "
                                style="width: 360px"
                                allowClear
                                @change="changeGrade"
                                v-model:value="state.groupId"
                                placeholder="请选择"
                                :options="state.groupOptions"
                                :showArrow="true"
                                :field-names="{
                                    label: 'objectName',
                                    value: 'id',
                                }"
                            >
                            </a-select>
                        </a-form-item>
                        <!-- <a-form-item label="班级:">
                            <a-select
                            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                            style="width: 360px"
                            allowClear
                            @change="changeClass"
                            v-model:value="state.classesId"
                            placeholder="请选择"
                            :options="state.classesOptions"
                            :showArrow="true"
                            :field-names="{
                                label: 'classesName',
                                value: 'classesId'
                            }"
                        >
                        </a-select>
                        </a-form-item> -->
                    </a-form>
                    <div>
                        <!-- <span class="lookImg"> <eye-outlined />查看座位图</span> -->
                        <a-button @click="classEx">导出</a-button>
                    </div>
                </div>
                <div>
                    <Tables
                        :spinning="state.classSpinning"
                        :paginations="state.classPaginations"
                        :columns="classColumns"
                        :dataSource="state.classSource"
                        :bordered="false"
                        @change="classTableChange"
                    >
                        <template #bodyCell="{ column, record }"> </template>
                    </Tables>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import Tables from "@/components/Tables";
import { reactive, onMounted, ref } from "vue";
import {
    getExamSiteList,
    getExamClassesList,
    getExamSeatingPage,
    exportExamSeating,
    getExamGradeList,
    viewExamSeat,
} from "@/api/exam.js";
import { useRoute, useRouter } from "vue-router";
const router = useRouter();
const route = useRoute();
const roomColumns = [
    {
        title: "序号",
        key: "id",
        customRender: ({ index }) => {
            return `${index + 1}`;
        }, // 显示每一行的序号
        // fixed: 'left'
    },
    {
        title: "考场",
        key: "siteName",
        dataIndex: "siteName",
    },
    {
        title: "姓名",
        key: "personName",
        dataIndex: "personName",
    },
    {
        title: "班级",
        key: "classesName",
        dataIndex: "classesName",
    },
    {
        title: "准考证号",
        key: "examineNumber",
        dataIndex: "examineNumber",
    },
    {
        title: "座位号",
        key: "seat",
        dataIndex: "seat",
    },
];
const classColumns = [
    {
        title: "序号",
        key: "id",
        customRender: ({ index }) => {
            return `${index + 1}`;
        }, // 显示每一行的序号
        // fixed: 'left'
    },
    {
        title: "考场",
        key: "siteName",
        dataIndex: "siteName",
    },
    {
        title: "姓名",
        key: "personName",
        dataIndex: "personName",
    },
    {
        title: "班级",
        key: "classesName",
        dataIndex: "classesName",
    },
    {
        title: "准考证号",
        key: "examineNumber",
        dataIndex: "examineNumber",
    },
    {
        title: "座位号",
        key: "seat",
        dataIndex: "seat",
    },
];

const arrangeObj = {
    1: "顺序编排",
    2: "S型编排",
};


const seatType = ref("room");
const state = reactive({
    roomSpinning: false,
    classSpinning: false,
    siteOptions: [],
    classesOptions: [],
    tableSource: [],
    roomPaginations: {
        pageNo: 1, // 页码
        pageSize: 10, // 条数
    },
    classPaginations: {
        pageNo: 1, // 页码
        pageSize: 10, // 条数
        total: 0,
    },
    siteArr: [],
});

// 获取考场
const reqExamSiteList = async () => {
    const { data } = await getExamSiteList({ examinationId: route.query.id });
    state.siteOptions = data;
};

// 按考场获取考场座位分页
const reqExamSeatingPage = async () => {
    state.roomSpinning = true;
    const seatObj = {
        ...state.roomPaginations,
        examinationId: route.query.id,
        siteId: state.siteId,
    };
    try {
        const { data } = await getExamSeatingPage(seatObj);
        state.roomSource = data.list;
        state.roomPaginations.total = data.total;
        state.roomSpinning = false;
    } catch (error) {
        state.roomSpinning = false;
    }
};

// 按班级获取考场座位分页
const reqclassSeatingPage = async () => {
    state.classSpinning = true;
    const seatObj = {
        ...state.classPaginations,
        examinationId: route.query.id,
        groupId: state.groupId,
        // classesId: state.classesId
    };
    try {
        const { data } = await getExamSeatingPage(seatObj);
        state.classSource = data.list;
        state.classPaginations.total = data.total;
        state.classSpinning = false;
    } catch (error) {
        state.classSpinning = false;
    }
};


function processSiteArr(arr) {
    let maxLen = 0;
    // 找到最长的item数组长度
    for (let i = 0; i < arr.length; i++) {
        if (arr[i].length > maxLen) {
            maxLen = arr[i].length;
        }
    }
    // 对每个item数组进行补充
    for (let i = 0; i < arr.length; i++) {
        while (arr[i].length < maxLen) {
            arr[i].push({
                "personName": "-",
                "seat": "-"
            });
        }
    }
    return arr;
}


const reqviewExamSeat = () => {
    viewExamSeat({
        examId: route.query.id,
        siteId: state.siteId,
    }).then((res) => {
        state.siteArr = processSiteArr(res.data.seatList);
        state.examPeopleNum = res.data.examPeopleNum;
        state.siterow = res.data.row;
        state.sitecell = res.data.cell;
        state.arrangeType = res.data.arrangeType;
    });
};

// 修改考场触发事件
const changeSite = () => {
    state.roomPaginations.pageNo = 1;
    reqExamSeatingPage();

    // 除了筛选考场 还需要 查询座位图接口
    if (state.siteId) {
        reqviewExamSeat();
    }
};

// 切换分页的操作
const roomTableChange = (data) => {
    state.roomPaginations = data;
    reqExamSeatingPage();
};

// 切换分页的操作
const classTableChange = (data) => {
    state.classPaginations = data;
    reqclassSeatingPage();
};

const roomEx = async () => {
    const roomObjEx = {
        examinationId: route.query.id,
        siteId: state.siteId,
    };
    await exportExamSeating(roomObjEx).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            })
        );
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "考场-考试座位表.xlsx");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
};
// 调接口获取年级
const reqGradeTree = async () => {
    const { data } = await getExamGradeList({ id: route.query.id });
    state.groupOptions = data;
};
// 调接口获取班级
const reqExamGradeList = async () => {
    const classObj = {
        examinationId: route.query.id,
        groupId: state.groupId,
    };
    const { data } = await getExamClassesList(classObj);
    state.classesOptions = data;
};

const changeGrade = () => {
    reqclassSeatingPage();
};
// 切换年级 请求班级接口
// const changeGrade = async(val) => {
//     state.classPaginations.pageNo = 1
//     state.classesId = ''
//     state.classesOptions = []
//     if (val) {
//         await reqExamGradeList()
//     }
//     await reqclassSeatingPage()
// }

// 切换班级 请求分页
const changeClass = async () => {
    state.classPaginations.pageNo = 1;
    await reqclassSeatingPage();
};

const classEx = async () => {
    const roomObjEx = {
        examinationId: route.query.id,
        groupId: state.groupId,
        classesId: state.classesId,
    };
    await exportExamSeating(roomObjEx).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            })
        );
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "班级-考试座位表.xlsx");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
};
onMounted(() => {
    reqExamSiteList();
    reqExamSeatingPage();
    reqGradeTree();
    reqclassSeatingPage();
});
</script>

<style lang="less" scoped>
.headCon {
    padding-bottom: 24px;
}
.pageMain {
    padding: 24px 16px 16px 16px;
}
.head {
    padding-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.lookImg {
    cursor: pointer;
    padding-right: 16px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
}

.titleBox {
    display: flex;
    padding-bottom: 20px;
    padding-top: 8px;
    align-items: center;
    justify-content: space-between;
}

.seatBox {
    max-height: 600px;
    overflow: auto;
}

.container {
    display: flex;
    flex-wrap: wrap;
    // width: 900px;
    // overflow-x: scroll;
}
.row {
    display: flex;
    width: 100%;
}

.seat {
    display: flex;
    align-items:center;
    // justify-content: space-between;
     width: 150px;
     padding-bottom: 24px;
    // text-align: center;
}

</style>

<style>
.poperLayTable{
    font-size: 12px;
    max-width: min-content;
    overflow: auto;
}
</style>
