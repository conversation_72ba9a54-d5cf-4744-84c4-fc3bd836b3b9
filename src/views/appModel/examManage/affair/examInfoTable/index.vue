<template>
    <div class="infoPage">
        <div class="exBtn">
            <a-button @click="derive">导出</a-button>
        </div>
        <div>
            <Tables
                :spinning="state.spinning"
                :paginations="state.paginations"
                :columns="infoColumns"
                :dataSource="state.tableSource"
                :bordered="false"
                @change="handleTableChange"
            >
                <template #bodyCell="{ column, record }">
                </template>
            </Tables>
        </div>
    </div>
</template>

<script setup>
import { reactive, onMounted, ref, inject } from 'vue'
import Tables from '@/components/Tables'
import { examSitePage, exportExamSite } from '@/api/exam.js'
import { useRoute, useRouter } from 'vue-router'
const router = useRouter()
const route = useRoute()
const examinationId = inject('examinationId')
const infoColumns = [
    {
        title: '考场名称',
        key: 'name',
        dataIndex: 'name'
    },
    {
        title: '考场地点',
        key: 'buildingName',
        dataIndex: 'buildingName'
    },
    {
        title: '考场教室',
        key: 'siteName',
        dataIndex: 'siteName'
    },
    {
        title: '场地容量（人）',
        key: 'sitePeopleNum',
        dataIndex: 'sitePeopleNum'
    },
    {
        title: '考场容量（人）',
        key: 'examPeopleNum',
        dataIndex: 'examPeopleNum'
    },
    {
        title: '考场规格',
        key: 'specification',
        dataIndex: 'specification'
    }
]
const state = reactive({
    spinning: false,
    tableSource: [],
    paginations: {
        pageNo: 1, // 页码
        pageSize: 10, // 条数
        total: 0
    }
})
// 获取考场信息分页
const reqExamSitePage = async() => {
    const obj = {
        ...state.paginations,
        examinationId: route.query.id
    }
    const { data } = await examSitePage(obj)
    state.tableSource = data.list
    state.paginations.total = data.total
}
/* 分页事件 */
const handleTableChange = (data) => {
    state.paginations = data
    reqExamSitePage()
}
// 导出excel表
const derive = async() => {
    await exportExamSite({ examinationId: route.query.id }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })
        )
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '考场信息表.xlsx')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    })
}

onMounted(() => {
    reqExamSitePage()
})
</script>

<style lang="less" scoped>
.infoPage {
    padding: 16px;
    .exBtn {
        padding-bottom: 16px;
        display: flex;
        justify-content: flex-end;
    }
}
</style>
