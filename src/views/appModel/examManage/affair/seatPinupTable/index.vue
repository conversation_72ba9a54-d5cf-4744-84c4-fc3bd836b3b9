<!-- 考场座位贴 -->
<!-- 考场座位贴 -->
<!-- 考场座位贴 -->
<template>
    <div class="pageMain">
        <div>
            <div class="head">
                <a-form layout="inline">
                    <a-form-item label="考场:">
                        <a-select
                        :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                            style="width: 360px"
                            @change="changeSite"
                            v-model:value="state.siteId"
                            placeholder="请选择"
                            :options="state.siteOptions"
                            :showArrow="true"
                            :field-names="{
                                label: 'name',
                                value: 'id'
                            }"
                        >
                        </a-select>
                    </a-form-item>
                </a-form>
                <div>
                    <a-button @click="exSiteTable">导出分场座位表</a-button>
                    <a-button class="allBtn" @click="allexSiteTable"
                        >导出考场总座位表</a-button
                    >
                </div>
            </div>
        </div>
        <div>
            <div class="subInfo">
                <div>
                    考试地点：<span class="subText">{{
                        state.buildingName || '-'
                    }}</span>
                </div>
                <div>
                    考试教室：<span class="subText">{{
                        state.siteName || '-'
                    }}</span>
                </div>
                <div>
                    考试容量：<span class="subText">{{
                        state.examPeopleNum || '-'
                    }}</span>
                </div>
            </div>
        </div>
        <div>
        <div class="paste" v-if="state.personVOList.length > 0">
            <div v-if="state.showed">
                <a-row :gutter="[24, 32]" type="flex" justify="flex-start" :wrap="true">
                    <a-col
                        v-for="item in state.personVOList"
                        :key="item.id"
                    >
                    <div class="siteBox">
                                    <div class="seat">
                                        <div>{{ item.seat || "-" }}</div>
                                    </div>
                                    <div class="info">
                                        <div class="infoBox">
                                            <div class="nameBox">
                                                姓名：<span>{{
                                                    item.personName || "-"
                                                }}</span>
                                            </div>
                                            <div class="examineNumberBox">
                                                准考证号：<span>{{
                                                    item.examineNumber || "-"
                                                }}</span>
                                            </div>
                                            <!-- <div class="classesNameBox">
                                                班级：<span>{{
                                                    item.classesName || "-"
                                                }}</span>
                                            </div> -->
                                        </div>
                                    </div>
                                </div>
                    </a-col>
                </a-row>
            </div>
            <div v-else>
                <a-row :gutter="[24, 32]" type="flex" justify="flex-start" :wrap="true">
                    <a-col
                        v-for="item in state.personVOList"
                        :key="item.id"
                    >
                        <div class="siteBoxs">
                            <div class="seat">
                                <div>{{ item.seat || '-' }}</div>
                            </div>
                            <div class="info">
                                <div class="infoBox">
                                    <div>
                                        姓名：<span>{{
                                            item.personName || '-'
                                        }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a-col>
                </a-row>
            </div>
        </div>
        <a-empty
        v-else
        image="image/empty.png"
        :image-style="{ width: '100%', height: '180px' }"
    >
        <template #description>
            <span>暂无数据</span>
        </template>
    </a-empty>
    </div>
    </div>
</template>

<script setup>
import { reactive, onMounted, ref } from 'vue'
import {
    getExamSiteList,
    examSeatStickers,
    exportExamSeatStickers
} from '@/api/exam.js'
import { useRoute, useRouter } from 'vue-router'
const router = useRouter()
const route = useRoute()
const state = reactive({
    siteSource: [],
    personVOList: []
})

// 请求考场座位帖
const reqExamSeatStickers = async () => {
    if (!state.siteId) return
    const seatObj = {
        examinationId: route.query.id,
        siteId: state.siteId
    }
    const { data } = await examSeatStickers(seatObj)
    state.personVOList = data.personVOList
    // 是否展示准考证号
    state.showed = data.showed
    state.buildingName = data.buildingName
    state.siteName = data.siteName
    state.examPeopleNum = data.examPeopleNum
}
// 获取考场
const reqExamSiteList = async() => {
    const { data } = await getExamSiteList({ examinationId: route.query.id })
    state.siteOptions = data
    state.siteId = data[0]?.id
}

// 更改考场 请求座位贴
const changeSite = () => {
    if (!state.siteId) {
        state.personVOList = []
    } else {
        reqExamSeatStickers()
    }
}

//  导出
const exSiteTable = async() => {
    const siteObj = {
        examinationId: route.query.id,
        siteId: state.siteId
    }
    await exportExamSeatStickers(siteObj).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })
        )
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '分场座位表.xlsx')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    })
}

//  全部导出
const allexSiteTable = async() => {
    await exportExamSeatStickers({ examinationId: route.query.id }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })
        )
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '考场总座位表.xlsx')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    })
}
onMounted(async() => {
    await reqExamSiteList()
    await reqExamSeatStickers()
})
</script>

<style lang="less" scoped>
.pageMain {
    padding: 16px;
}
.head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16px;
}
.allBtn {
    margin-left: 12px;
}
.paste {
    padding-top: 16px;
}
.subInfo {
    color: rgba(0, 0, 0, 0.65);
    display: flex;
    font-size: 14px;
    padding: 16px;
    background: #f6f6f6;
    border-radius: 5px;
    .subText {
        color: rgba(0, 0, 0, 0.85);
        padding-right: 42px;
    }
}
.siteBoxs {
    border: 1px dashed #595959;
    width: 304px;
    height: 80px;
    display: flex;
    border-radius: 10px;
    .seat {
        width: 76px;
        height: 80px;
        font-size: 30px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.65);
        border-right: 1px dashed #595959;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .info {
        flex: 1;
        padding: 10px 20px 10px 20px;
        .infoBox {
            height: 100%;
            display: flex;
            align-items: center;
            .bor {
                border: 1px solid #d9d9d9;
            }
        }
    }
}

.siteBox {
    cursor: pointer;
    border: 1px dashed #595959;
    width: 304px;
    display: flex;
    border-radius: 10px;
    .seat {
        border-radius: 10px 0px 0px 10px;
        width: 76px;
        // height: 80px;
        font-size: 30px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.65);
        background: #f6f6f6;
        border-right: 1px dashed #595959;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .info {
        flex: 1;
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
        padding: 10px 20px 10px 20px;
        display: table;
        .infoBox {
            height: 100%;
            display: table-cell;
            vertical-align: middle;
            // display: flex;
            // justify-content: space-between;
            // flex-direction: column;
            // .bor {
            //     border: 1px solid #d9d9d9;
            // }
        }
    }
}


.nameBox {
    padding-bottom: 8px;
    border-bottom: 1px solid #d9d9d9;
}

.examineNumberBox{
    // padding-bottom: 8px;
    padding-top: 8px;
    // border-bottom: 1px solid #d9d9d9;
}

.classesNameBox{
    padding-top: 8px;
}


:deep(.ant-col-5) {
    flex: 0 0 20%;
    max-width: 20%;
}
</style>
