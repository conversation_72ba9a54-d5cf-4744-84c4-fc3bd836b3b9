<!-- 监考安排表 -->
<!-- 监考安排表 -->
<!-- 监考安排表 -->
<template>
    <div class="mainPae">
        <div class="body">
            <div class="search">
                <a-form layout="inline">
                    <a-form-item label="考试人员:">
                        <a-select
                        :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                            style="width: 360px"
                            @change="changeClass"
                            v-model:value="state.groupId"
                            placeholder="请选择"
                            :options="state.groupListOptions"
                            :showArrow="true"
                            :field-names="{
                                label: 'objectName',
                                value: 'id'
                            }"
                        >
                        </a-select>
                    </a-form-item>
                </a-form>
            </div>
            <div class="subBoxTime">
                <div class="exbtn">
                    <a-button @click="exSub">导出</a-button>
                </div>
                <a-table
                    :columns="dayColumns"
                    :bordered="true"
                    :data-source="state.tableSource"
                    :pagination="false"
                >
                <template #bodyCell="{ column, text, record, index }">
                <!-- <template v-if="column.dataIndex === 'chiefText'">
                    {{record.chiefText}}
                </template> -->
            </template>
                </a-table>
            </div>
            <div class="siteBox">
                <div class="exbtn">
                    <a-button @click="exSite">导出</a-button>
                </div>
                <a-table
                    :columns="siteColumns"
                    :bordered="false"
                    :data-source="state.siteSource"
                    :pagination="false"
                >
                </a-table>

                <!-- <Tables
                    :spinning="state.siteSpin"
                    :paginations="state.paginations"
                    :columns="siteColumns"
                    :dataSource="state.siteSource"
                    :bordered="false"
                    @change="handleTableChange"
                >
                    <template #bodyCell="{ column, record, text }">
                        <template v-if="column.dataIndex === 'invigilatorText'">
                        <div v-html ="text"></div>
                    </template>
                    </template>
                </Tables> -->
            </div>
        </div>
    </div>
</template>

<script setup>
import { reactive, onMounted, ref, computed } from 'vue'
// import Tables from '@/components/Tables'
import {
    getExamGradeList,
    getSubjectInvigilatorList,
    siteInvigilatorPage,
    exportSubjectInvigilator,
    exportSiteInvigilator
} from '@/api/exam.js'
import { useRoute, useRouter } from 'vue-router'
const router = useRouter()
const route = useRoute()

const dayColumns = [
    {
        title: '日期',
        key: 'dateText',
        dataIndex: 'dateText'
    },
    {
        title: '时间',
        key: 'timeText',
        dataIndex: 'timeText'
    },
    {
        title: '考试年级',
        key: 'gradeName',
        dataIndex: 'gradeName'
    },
    {
        title: '考试班级',
        key: 'classesName',
        dataIndex: 'classesName'
    },
    {
        title: '科目',
        key: 'subjectName',
        dataIndex: 'subjectName'
    },
    {
        title: '主考员',
        key: 'chiefText',
        dataIndex: 'chiefText',
        customCell: (_, index) => {
            if (index === 0 ) {
                return { rowSpan: state.tableSource.length }
            }
            return { rowSpan: 0 }
        },
    },
    {
        title: '巡考员',
        key: 'inspectorText',
        dataIndex: 'inspectorText',
        customCell: (_, index) => {
            if (index === 0 ) {
                return { rowSpan: state.tableSource.length }
            }
            return { rowSpan: 0 }
        },
    },


]

const siteColumns = ref([
    {
        title: '考场名称',
        key: 'name',
        dataIndex: 'name'
    },
    {
        title: '考场容量（人）',
        key: 'examPeopleNum',
        dataIndex: 'examPeopleNum'
    },
    {
        title: '考场布局',
        key: 'row',
        dataIndex: 'row'
    },
    {
        title: '所属楼栋',
        key: 'buildingName',
        dataIndex: 'buildingName'
    },
    {
        title: '场地名称',
        key: 'siteName',
        dataIndex: 'siteName'
    },
    {
        title: '场地类型',
        key: 'siteTypeName',
        dataIndex: 'siteTypeName'
    },
    {
        title: '楼层',
        key: 'floor',
        dataIndex: 'floor'
    },
    {
        title: '房间号',
        key: 'roomNum',
        dataIndex: 'roomNum'
    },
    {
        title: '考试人员',
        key: 'groupName',
        dataIndex: 'groupName'
    },
])


const oldsiteColumns = [
    {
        title: '考场名称',
        key: 'name',
        dataIndex: 'name'
    },
    {
        title: '考场容量（人）',
        key: 'examPeopleNum',
        dataIndex: 'examPeopleNum'
    },
    {
        title: '考场布局',
        key: 'row',
        dataIndex: 'row'
    },
    {
        title: '所属楼栋',
        key: 'buildingName',
        dataIndex: 'buildingName'
    },
    {
        title: '场地名称',
        key: 'siteName',
        dataIndex: 'siteName'
    },
    {
        title: '场地类型',
        key: 'siteTypeName',
        dataIndex: 'siteTypeName'
    },
    {
        title: '楼层',
        key: 'floor',
        dataIndex: 'floor'
    },
    {
        title: '房间号',
        key: 'roomNum',
        dataIndex: 'roomNum'
    },
    {
        title: '考试人员',
        key: 'groupName',
        dataIndex: 'groupName'
    },
]



const state = reactive({
    siteSource: [],
    siteSpin: false,
    tableSource: [],
    paginations: {
        pageNo: 1, // 页码
        pageSize: 1000, // 条数
        total: 0
    },
    groupListOptions: [],
})

// 调接口获取年级树
const reqGradeTree = async() => {
    const { data } = await getExamGradeList({ id: route.query.id })
    state.groupListOptions = data
    state.groupId = data[0]?.id
}

// 调接口获取科目监考老师列表
const reqInvigilatorList = async() => {
    const subObj = {
        examinationId: route.query.id,
        groupId: state.groupId
    }
    try {
        const { data } = await getSubjectInvigilatorList(subObj)
        state.tableSource = data
    } catch (error) {
        state.tableSource = []
    }
}

function addPersonNamesProperties(arr) {
    for (let i = 0; i < arr.length; i++) {
        const personNames = arr[i].personNames;
        if (personNames.length > 0) {
            for (let j = 0; j < personNames.length; j++) {
                arr[i][`personNames${j + 1}`] = personNames[j];
            }
        }
    }
    return arr;
}


// 场地监考老师分页接口
const reqsitePage = async() => {
    const siteObj = {
        examinationId: route.query.id,
        ...state.paginations,
        groupId: state.groupId
    }
    const { data } = await siteInvigilatorPage(siteObj)

    // 这里又要处理一下 动态的监考员了
    if (data.list[0]?.personNames?.length) {
        const personNamecolumns = data.list[0]?.personNames.map((item, index) => {
            return {
                title: `监考员${index + 1}`,
                dataIndex: `personNames${index + 1}`,
                key: `personNames${index + 1}`,
            }
        })
        siteColumns.value = [...oldsiteColumns, ...personNamecolumns];
        state.siteSource = addPersonNamesProperties(data.list)
    } else {
        state.siteSource = data.list
    }
}


// 处理表头倒过来的逻辑
// const getHeaders = computed(() => {
//     return state.tableSource.reduce(
//         (pre, cur, index) => pre.concat(`value${index}`),
//         ['title']
//     )
// })
// const getValues = computed(() => {
//     return state.subColumns.map((item) => {
//         return state.tableSource.reduce(
//             (pre, cur, index) =>
//                 Object.assign(pre, { ['value' + index]: cur[item.dataIndex] }),
//             { title: item.title }
//         )
//     })
// })

/* 分页事件 */
const handleTableChange = (data) => {
    state.paginations = data
    reqsitePage()
}

// 年级变更事件
const changeClass = () => {
    reqInvigilatorList()
    reqsitePage()
}

// 导出
const exSub = async() => {
    await exportSubjectInvigilator({ id: route.query.id }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })
        )
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '科目监考老师表.xlsx')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    })
}

const exSite = async() => {
    await exportSiteInvigilator({ id: route.query.id }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })
        )
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '场地监考老师表.xlsx')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    })
}
onMounted(async() => {
    await reqGradeTree()
    await reqInvigilatorList()
    await reqsitePage()
})
</script>

<style lang="less" scoped>
.mainPae {
    padding: 16px;
}
.exbtn {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-bottom: 16px;
}
.subBoxTime {
    padding-bottom: 24px;
}

.subBoxTime :deep(table > tbody > tr > td:nth-child(6)){
    vertical-align: middle;
}
.subBoxTime :deep(table > tbody > tr > td:nth-child(7)){
    vertical-align: middle;
}

</style>
