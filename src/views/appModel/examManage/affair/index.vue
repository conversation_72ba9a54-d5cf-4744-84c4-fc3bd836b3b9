
<template>
    <a-tabs
        v-model:activeKey="state.activeKey"
        @change="handleChangeTabs"
        v-bind="$attrs"
    >
        <template #leftExtra v-if="showBack">
            <slot name="leftExtra">
                <a-button type="text" class="tabs_back" @click="back">
                    <i class="iconfont icon-xingzhuangjiehe19"></i>
                </a-button>
            </slot>
        </template>
        <a-tab-pane
            :key="item.path"
            :tab="item.meta?.title"
            v-for="item in state.tabsList"
        >
        </a-tab-pane>
    </a-tabs>
    <router-view />
</template>

<script setup>
import { reactive, onMounted, toRaw, computed } from "vue"
import { useRouter, useRoute } from "vue-router"

const props = defineProps({
    backRout: {
        type: String,
        default: ""
    },
    showBack: {
        type: Boolean,
        default: true
    },
    tabsList: {
        type: Array,
        default: () => []
    }
})
const state = reactive({
    activeKey: "/appModel/examManage/examManage/supervisionTable",
    tabsList: []
})
const router = useRouter()
const route = useRoute()

// 进入考务管理页面的返回按钮
// 这里也要区分一下是从哪里进入的
// 如果是从任教管理 考试 进入的也应该返回任教管理
const back = () => {
    router.push({
            path: '/appModel/examManage/list'
        })
}
const handleChangeTabs = (v) => {
    router.replace({
        path: v,
        query: { ...route.query }
    })
    state.activeKey = v
}
onMounted(() => {
    const curpath = route.path
    const len = curpath.lastIndexOf("/")
    const ppath = curpath.substring(0, len)
    const allrouter = router.getRoutes().filter((item) => item.path === ppath)
    const allrouterSosn = allrouter[0]?.children || []

    if (props.tabsList.length && allrouterSosn.length) {
        props.tabsList.forEach((j) => {
            allrouterSosn.forEach((v) => {
                if (v.path.includes(j.key)) {
                    state.tabsList.push({
                        ...j,
                        ...v
                    })
                }
            })
        })
    } else {
        state.tabsList = allrouterSosn
    }

    router.push({
        path: route.path,
        query: {
            ...route.query
        }
    })
    state.activeKey = route.path
})

const showBackStyle = computed(() => {
    return props.showBack ? "0px" : "24px"
})
</script>

<style lang="less" scoped>
:deep(.ant-tabs-nav) {
    padding-left: v-bind(showBackStyle);
    margin-bottom: 0;
    border-bottom: 1px solid @border-color-base;
    line-height: 33px;
}

.faceLibrary_warp_pageitem {
    height: calc(100% - 57px);
}
</style>
