<template>
  <div class="create_warp">
      <div class="header">
          <a-button type="text" class="tabs_back" @click="goBack">
              <i class="iconfont icon-xingzhuangjiehe19" />
          </a-button>
          <span>{{ `${route.query.name}签到表` }}</span>
      </div>
      <div class="body">
          <div class="search">
              <a-form layout="inline">
                  <a-form-item label="考试人员:">
                      <a-select
                          :getPopupContainer="
                              (triggerNode) => triggerNode.parentNode
                          "
                          style="width: 360px"
                          allowClear
                          @change="changeClass"
                          v-model:value="state.pagination.groupId"
                          placeholder="请选择"
                          :options="state.groupListOptions"
                          :showArrow="true"
                          :field-names="{
                              label: 'objectName',
                              value: 'id'
                          }"
                      >
                      </a-select>
                  </a-form-item>
                  <a-form-item label="考试科目">
                      <a-select
                          :getPopupContainer="
                              (triggerNode) => triggerNode.parentNode
                          "
                          placeholder="请选择"
                          :allowClear="true"
                          v-model:value="state.pagination.subjectId"
                          style="width: 360px"
                          :options="state.subOptions"
                          :showArrow="true"
                          @change="changePage"
                          :field-names="{
                              label: 'subjectName',
                              value: 'id'
                          }"
                      ></a-select>
                  </a-form-item>
              </a-form>
              <a-button @click="exportSign">导出</a-button>
          </div>
          <div class="byDay">
              <a-spin :spinning="state.spinning">
                  <YTable
                      :totals="state.pagination"
                      :columns="signColumns"
                      :dataSource="state.signDataSource"
                      :slots="['operation']"
                      :rowSelection="false"
                      @onSelectedRowKeys="handerSelectedRowKeys"
                  >
                      <template #bodyCell="{ column, record, index, text }">
                          <template v-if="column.dataIndex === 'signInType'">
                              <a-badge
                                  :color="
                                      renderStatus(record.signInType).color
                                  "
                                  :status="`success`"
                                  :text="renderStatus(record.signInType).text"
                              />
                          </template>
                          <template
                              v-if="column.dataIndex === 'signInTimeText'"
                          >
                              <div>{{ record.signInTimeText || '-' }}</div>
                          </template>
                      </template>
                  </YTable>
              </a-spin>
          </div>
      </div>
  </div>
</template>

<script setup>
import { reactive, onMounted, ref, computed } from 'vue'
import YTable from '@/components/YTable/index.vue'
import {
  getExamGradeList,
  getsubjectList,
  examSignInPage,
  getExamSiteList,
  exportExamSignIn
} from '@/api/exam.js'
import { getColumnCheckboxGroupFilterDropdown } from '@/components/FilterDropdown.jsx'
import { useRoute, useRouter } from 'vue-router'
const router = useRouter()
const route = useRoute()
let filtersArr = []
const signColumns = ref([
  {
      title: '姓名',
      dataIndex: 'personName'
  },
  {
      title: '考场',
      dataIndex: 'siteName',
      filters: [
          {
              text: '全部',
              value: ''
          }
      ],
      filterDropdown: (vnode) => {
          if (!vnode.selectedKeys.length) {
              vnode.selectedKeys = ''
          } else {
              vnode.selectedKeys = vnode.selectedKeys[0]
          }
          return getColumnCheckboxGroupFilterDropdown(vnode)
      },
      customFilterDropdown: true,
      customFilterIcon: 'caret-right-outlined'
  },
  {
      title: '班级名称',
      dataIndex: 'classesName'
  },
  {
      title: '考试科目',
      dataIndex: 'subjectName'
  },
  {
      title: '实际签到时间',
      dataIndex: 'signInTimeText'
  },
  {
      title: '签到状态',
      dataIndex: 'signInType',
      filters: [
          {
              text: '全部',
              value: ''
          },
          {
              text: '已签到',
              value: '1'
          },
          {
              text: '未签到',
              value: '2'
          }
      ],
      filterDropdown: (vnode) => {
          if (!vnode.selectedKeys.length) {
              vnode.selectedKeys = ''
          } else {
              vnode.selectedKeys = vnode.selectedKeys[0]
          }
          return getColumnCheckboxGroupFilterDropdown(vnode)
      },
      customFilterDropdown: true,
      customFilterIcon: 'caret-right-outlined'
  }
])

// 响应式数据
const state = reactive({
  signDataSource: [],
  groupListOptions: [],
  subOptions: [],
  spinning: false,
  pagination: {
      pageNo: 1,
      pageSize: 10,
      total: 0,
      examinationId: route.query.id,
      siteId: '',
      signInType: ''
  }
})

// 调接口获取年级树
const reqGradeTree = async() => {
  const { data } = await getExamGradeList({ id: route.query.id })
  state.groupListOptions = data
}
// 调接口科目列表
const reqSubjectList = async() => {
  const obj = {
      examinationId: route.query.id,
      groupId: state.pagination.groupId
  }
  const { data } = await getsubjectList(obj)
  state.subOptions = data
}

// 切换年级了需要请求科目考场数据
const changeClass = async(val) => {
  state.pagination.pageNo = 1
  // 如果切换了年级数据需要重新请求科目数据
  state.subOptions = []
  state.pagination.subjectId = ''
  if (val) {
      await reqSubjectList()
  }
  await reqSignPage()
  await getAllExamRoom()
}
// 获取签到表分页
const reqSignPage = async() => {
  const { data } = await examSignInPage(state.pagination)
  state.signDataSource = data.list
  state.pagination.total = data.total
}
const changePage = async() => {
  state.pagination.pageNo = 1
  // 请求分页
  reqSignPage()
}
// 返回上一页
const goBack = () => {
    router.push({
          path: '/appModel/examManage/list'
      })
}

// 切换分页
const handerSelectedRowKeys = (res) => {
  const { current, pageSize, pageNo, siteName, signInType } = res
  state.pagination.pageNo = current
  state.pagination.pageSize = pageSize
  state.pagination.siteId = siteName ? siteName[0] : ''
  state.pagination.signInType = signInType ? signInType[0] : ''
  reqSignPage(state.pagination)
}

// 获取所有考场
const getAllExamRoom = async() => {
  signColumns.value[1].filters = []
  const roomObj = {
      examinationId: route.query.id,
      groupId: state.pagination.groupId
  }
  const { data } = await getExamSiteList(roomObj)
  filtersArr = data.map((item) => {
      return {
          text: item.name,
          value: item.id
      }
  })
  if (!filtersArr.length) {
      signColumns.value[1].filters = [
          {
              text: '全部',
              value: ''
          }
      ]
  } else {
      signColumns.value[1].filters.unshift({
          text: '全部',
          value: ''
      })
      signColumns.value[1].filters.push(...filtersArr)
  }
}

const exportSign = async() => {
  const exObj = {
      examinationId: route.query.id,
      signInType: state.pagination.signInType,
      subjectId: state.pagination.subjectId,
      groupId: state.pagination.groupId,
      siteId: state.pagination.siteId
  }
  await exportExamSignIn(exObj).then((blob) => {
      const url = window.URL.createObjectURL(
          new Blob([blob], {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          })
      )
      const link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', '签到表.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
  })
}

const renderStatus = computed(() => {
  const [open, close] = [
      {
          text: '已签到',
          color: '#00B781'
      },
      {
          text: '未签到',
          color: '#BFBFBF'
      }
  ]
  return (status) => (status === 1 ? open : close)
})

onMounted(() => {
  reqGradeTree()
  reqSignPage()
  getAllExamRoom()
})
</script>

<style lang="less" scoped>
.create_warp {
  background: #fff;
  // transform: translateY(-58px);
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  z-index: 99;
  position: relative;
  .header {
      height: 57px;
      line-height: 57px;
      border-bottom: 2px solid #f0f0f0;
      z-index: 99;
  }
  .body {
      flex: 1;
      overflow-y: scroll;
      padding: 24px 16px;
      .search {
          padding-bottom: 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;
      }
  }

  .footer {
      border-top: 1px solid #f0f0f0;
      padding: 16px 0;
  }
}
</style>
