<!-- 霸屏预览的模块 -->
<template>
    <div>
        <a-modal
            :keyboard="false"
            @cancel="cancel"
            :maskClosable="false"
            v-model:visible="modalOpen"
            title="霸屏预览"
            :footer="null"
            width="950px"
            :body-style="{ height: '730px', overflow: 'hidden auto' }"
        >
            <div class="previewBody">
                <div class="preview_type">
                    <a-radio-group
                        v-model:value="state.direction"
                        button-style="solid"
                    >
                        <a-radio-button :value="0">横版</a-radio-button>
                        <a-radio-button :value="1">竖版</a-radio-button>
                    </a-radio-group>
                    <div>
                        logo显示：
                        <a-switch
                        :disabled="disabled"
                            v-model:checked="dataOrigin.settingInfo.showLogo"
                        />
                    </div>
                </div>

                <div
                    class="textBox_item"
                    :style="{
                        width: containerWidth,
                        background: containerBackground,
                    }"
                >
                    <div class="schoolName" :style="{
                            justifyContent: dataOrigin.settingInfo.typeSetting,
                        }"  v-if="dataOrigin.settingInfo.showLogo">
                        <div class="school_icon">
                            <a-avatar
                                :src="icon"
                            />
                        </div>
                        <div class="school_name">{{schoolName || '学校名称'}}</div>
                    </div>
                    <div class="titleList" v-if="customType === 1">
                        <div
                            v-for="(item, index) in dataOrigin?.contentArr"
                            class="title_item_box"
                            :style="{
                                justifyContent:
                                    dataOrigin.settingInfo.typeSetting,
                            }"
                        >
                            <div
                                class="title_item"
                                :style="{
                                    fontSize: item.fontSize * 0.46 + 'px',
                                    fontWeight: item.isBold ? 'bold' : 'normal',
                                    lineHeight: item.rowPitch,
                                }"
                            >
                                {{ item.text }}
                            </div>
                        </div>
                    </div>
                    <div class="imgList" v-if="customType === 3">
                        <a-carousel autoplay>
                            <div v-for="(item, index) in dataOrigin.imgArr">
                                <img class="imgItem" :src="item.url" />
                            </div>
                        </a-carousel>
                    </div>
                    <div class="videoList" v-if="customType === 4">
                        <div v-for="(item, index) in dataOrigin?.contentArr">
                            <video controls :src="item.url"></video>
                        </div>
                    </div>
                </div>
            </div>
            <div class="previewFooter">
                <div class="backBox">
                    <div>选择背景样式：</div>
                    <a-radio-group
                    :disabled="disabled"
                        v-model:value="dataOrigin.settingInfo.background"
                        name="radioGroup"
                        class="bg_item"
                        :key="index"
                        v-for="(item, index) in state.backgroundList"
                    >
                        <a-radio :value="item.url">
                            <img :src="item.url" alt="背景图"
                        /></a-radio>
                    </a-radio-group>
                </div>
                <!-- 排版 -->
                <div class="typeSetting">
                    <div class="typeSetting_text">排版：</div>
                    <a-radio-group
                    :disabled="disabled"
                        v-model:value="dataOrigin.settingInfo.typeSetting"
                    >
                        <a-radio-button value="left">靠左</a-radio-button>
                        <a-radio-button value="center">居中</a-radio-button>
                        <a-radio-button value="right">靠右</a-radio-button>
                    </a-radio-group>
                </div>
            </div>
        </a-modal>
    </div>
</template>

<script setup name="fsPreview">
import { reactive, onMounted, ref } from "vue";
import { useStore } from "vuex";
const store = useStore();
const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    customType: {
        type: Number,
        default: 1,
    },
    // 传进来的item的数据源
    dataOrigin: {
        type: Object,
        default: () => {},
    },
    disabled: {
        type: Boolean,
        default: false,
    }
});
let modalOpen = ref(props.visible);
const icon = computed(() => {
    return store.state.user.currentSchool?.schoolLogo;
});

const schoolName = computed(() => {
    return store.state.user.currentSchool?.schoolName;
});

const { dataOrigin } = toRefs(props);

const state = reactive({
    direction: 0,
    backgroundList: [
        {
            backgroundColor:
                "linear-gradient( 225deg, #E1FFEC 0%, #C9EEFF 100%)",
            url: "https://cloudcdn.yyide.com/background/fullscreenbg7.png",
        },
        {
            backgroundColor:
                "linear-gradient( 225deg, #F2E1FF 0%, #C9F5FF 100%)",
            url: "https://cloudcdn.yyide.com/background/fullscreenbg8.png",
        },
        {
            backgroundColor:
                "linear-gradient( 225deg, #FFFCE1 0%, #FFD3C9 100%)",
            url: "https://cloudcdn.yyide.com/background/fullscreenbg9.png",
        },
        {
            backgroundColor:
                "linear-gradient( 225deg, #FFE1F9 0%, #C9FFDB 100%)",
            url: "https://cloudcdn.yyide.com/background/fullscreenbg10.png",
        },
        {
            backgroundColor:
                "linear-gradient( 225deg, #FDFFE1 0%, #C9FFE7 100%)",
            url: "https://cloudcdn.yyide.com/background/fullscreenbg11.png",
        },
        {
            backgroundColor:
                "linear-gradient( 225deg, #EAFFE1 0%, #FFECC9 100%)",
            url: "https://cloudcdn.yyide.com/background/fullscreenbg12.png",
        },
    ],
});

const emit = defineEmits(["update:visible"]);
watch(
    () => props.visible,
    (val) => {
        modalOpen.value = val;
    },
    {
        immediate: true,
        deep: true,
    }
);
watch(modalOpen, (newVal) => {
    emit("update:visible", newVal);
});

const cancel = () => {
    modalOpen.value = false;
};

const containerWidth = computed(() => {
    return state.direction === 0 ? "880px" : "278px";
});

const containerBackground = computed(() => {
    return state.backgroundList.find(
        (item) => item.url === props.dataOrigin.settingInfo.background
    ).backgroundColor;
});

defineExpose({
    state,
});
</script>

<style lang="less" scoped>
.preview_type {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 16px;
}

.textBox_item {
    position: relative;
    margin: 0 auto;
    height: 495px;
    padding: 24px;
    overflow-y: auto;
    // background-repeat: no-repeat;
    // overflow: hidden;
    display: flex;
    flex-direction: column;
    .schoolName {
        margin-bottom: 24px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        // position: absolute;
        // left: 0px;
        // right: 0px;
        // z-index: 9999;
        .school_icon {
            padding-right: 8px;
            // width: 30px;
            // height: 30px;
            // border-radius: 50%;
        }
        .school_name {
            font-weight: 600;
            font-size: 18px;
            color: #000000;
        }
    }
}

.previewFooter {
    padding-top: 24px;
}

.bg_item {
    width: 100px;
    height: 56px;
    margin-right: 16px;
    :deep(.ant-radio-wrapper) {
        display: block;
        .ant-radio {
            position: absolute;
            right: 4px;
            top: 4px;
        }
        span.ant-radio + * {
            padding-right: 0px;
            padding-left: 0px;
        }
    }
    img {
        width: 100%;
        height: 100%;
    }
}

.backBox {
    display: flex;
}

.typeSetting {
    display: flex;
    padding-top: 16px;
    :deep(.ant-radio-button-wrapper) {
        margin-right: 16px;
        border-radius: unset;
    }
}

.typeSetting_text {
    width: 100px;
    text-align: right;
}

.titleList {
    flex: 1;
    display: flex;

    flex-direction: column;
    justify-content: center;
    .title_item_box {
        display: flex;
        align-items: center;
    }
}
.imgList {
    height: 415px;
    .imgItem {
        object-fit: cover;
        width: 100%;
        height: 100%;
    }
    .ant-carousel :deep(.slick-slide) {
        height: 400px;
        overflow: hidden;
    }
}
.videoList {
    width: 100%;
    height: 415px;
    video {
        width: 100%;
        height: 400px;
        overflow: hidden;
    }
}
</style>
