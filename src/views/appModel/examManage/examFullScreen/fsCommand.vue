
<template>
  <div class="fsCommand">
    <div class="tabBox">
      <arrow-left-outlined
        style="
          font-size: 20px;
          color: #00b781;
          margin-right: 12px;
          cursor: pointer;
        "
        @click="showList"
      />
      <span class="tabtitle">{{pageTitle}}</span>
    </div>
    <a-row>
      <a-col :span="18" :offset="3">
        <div class="contentBox">
          <!-- 这是考试霸屏的表单 -->
          <div v-if="state.modeType === 3">
            <a-form
              ref="customFormRef"
              :model="state.customForm"
              layout="vertical"
            >
            <a-form-item label="优先级：" name="level">
                <a-select
                :disabled="forbidden"
                  :allowClear="true"
                  v-model:value="state.customForm.level"
                  placeholder="如同时启用霸屏，请设置优先级，不可重复选择"
                  :options="state.priorityOpt"
                >
                </a-select>
              </a-form-item>
              <a-form-item label="自定义类型：" name="type" :rules="[{ message: '请选择',required: true}]">
                <a-select
                :disabled="forbidden"
                  @change="changeType"
                  v-model:value="state.customForm.type"
                  placeholder="请选择"
                  :options="customOpt"
                >
                </a-select>
              </a-form-item>
              <div
                v-for="(item, index) in state.customForm.groups"
                :key="index"
                class="conArr"
              >
                <div>
                  <a-form-item
                    :label="comlabel(inx)"
                    v-for="(itm, inx) in item.contentArr"
                    :key="inx"
                    :rules="[{ message: '请选择',required: true}]"
                  >
                    <div class="titleBox">
                      <div class="textBox">
                        <a-input
                        :disabled="forbidden"
                          v-model:value="itm.text"
                          show-count
                          :maxlength="100"
                          placeholder="请输入"
                        />
                      </div>
                      <div class="wordSize">
                        <div>字号：</div>
                        <a-input-number :disabled="forbidden" style="width:150px" v-model:value="itm.fontSize" :min="12" :step="1" :precision="0" :controls="false">
                          <template #addonBefore>
                            <minus-outlined class="jianIcon" @click="itm.fontSize = itm.fontSize - 1" />
                          </template>
                          <template #addonAfter>
                            <plus-outlined class="jiaIcon" @click="itm.fontSize = itm.fontSize + 1" />
                          </template>
                        </a-input-number>
                      </div>
                      <div>
                        <a-button
                        :style="{ fontWeight: itm.isBold ? 'bold' : 'normal',background: itm.isBold ? '#00B781' : '#ffffff',color: itm.isBold ? '#ffffff' : '#00B781'}"
                          :disabled="forbidden"
                          type="primary"
                          ghost
                          @click="itm.isBold = !itm.isBold"
                          >  加 粗</a-button
                        >
                      </div>
                      <div class="lineHeight">
                        <div>行距：</div>
                        <a-select :disabled="forbidden" v-model:value="itm.rowPitch" style="width:100px" :options="rowPitchOpt"></a-select>
                      </div>
                      <div>
                        <a-button
                          v-show="inx !== 0"
                          danger
                          :disabled="forbidden"
                          @click="delContent(index, inx)"
                          >删除</a-button
                        >
                        <a-button
                          v-show="inx === 0"
                          type="primary"
                          :disabled="forbidden"
                          @click="addContent(index)"
                          >添加</a-button
                        >
                      </div>
                    </div>
                  </a-form-item>
                </div>
                <a-form-item label="选择设备：" :name="['groups', index, 'devices']" :rules="[{ message: '请选择',required: true}]">
                  <div>
                      <a-radio-group
                      :disabled="forbidden"
                      @change="(e) => deviceTypeChange(e, item)"
                    v-model:value="item.deviceType"
                    name="radioGroup"
                  >
                    <a-radio :value="1">按场地</a-radio>
                    <a-radio :value="2">按设备</a-radio>
                  </a-radio-group>
                  <div style="display: flex;align-items: center; padding-top:16px">
                    <a-tree-select
                  @dropdownVisibleChange="(open)=>dropdownVisibleChange(open,item)"
                  :disabled="forbidden"
                    :labelInValue="true"
                    style="flex: 1"
                    tree-checkable
                    multiple
                    v-model:value="item.devices"
                    placeholder="请选择"
                    allow-clear
                    :tree-data="state.signOptions"
                    :fieldNames="fieldNames"
                  >
                    <template #title="title">
                      <home-outlined class="icon-yellow" />
                      {{ title.showName }}
                    </template>
                  </a-tree-select>
                  <div class="previewText" @click="previewItme(item)">预览</div>
                  </div>
                  </div>
                </a-form-item>
                <div class="delItem" v-if="index !== 0">
                  <span  v-show="!forbidden" class="delItem_text" @click="delItem(index)">删除</span>
                </div>
              </div>
              <div class="addGroupBox">
                <a-button type="primary" :disabled="forbidden" @click="addGroup">添加</a-button>
                <span  v-show="!forbidden" @click="copyLastItem" class="copyCon"
                  >复制上一条内容</span
                >
              </div>
              <a-form-item label="轮播时间：" name="duration" :rules="[{ message: '请选择',required: true}]">
                <a-select
                :disabled="forbidden"
                  :allowClear="true"
                  placeholder="请选择轮播时长"
                  v-model:value="state.customForm.duration"
                >
                  <a-select-option :value="10">10 s/张</a-select-option>
                  <a-select-option :value="20">20 s/张</a-select-option>
                  <a-select-option :value="30">30 s/张</a-select-option>
                  <a-select-option :value="40">40 s/张</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="展示时间：" name="times" :rules="[{ message: '请选择',required: true}]">
                <a-range-picker
                :disabled="forbidden"
                style="width:100%"
                  valueFormat="YYYY-MM-DD HH:mm:ss"
                  format="YYYY-MM-DD HH:mm:ss"
                  show-time
                  v-model:value="state.customForm.times"
                ></a-range-picker>
              </a-form-item>
            </a-form>
          </div>
        </div>
      </a-col>
    </a-row>
    <div class="footerBox" v-show="!forbidden">
      <a-button @click="showList">取消</a-button>
      <a-button @click="saveBtn(1)" style="margin-left: 12px">保存</a-button>
      <a-button @click="saveBtn(2)" type="primary" style="margin-left: 12px">发布并启用</a-button>
    </div>
  </div>
      <!-- 霸屏预览的组件 -->
      <FsPreview ref="fsPreviewRef" v-model:visible="state.showPreview" :dataOrigin="state.dataOrigin" :customType ="state.customForm.type" :disabled="forbidden"></FsPreview>
</template>

<script setup name="fsCommand">
import { reactive, onMounted, ref, computed } from 'vue';
import { getBindingSiteTreeeV2, getModeCreate, getModeinfo, getModeupdate, getLevelList } from '@/api/classSign';
import { message, Modal } from 'ant-design-vue';
import { customOpt, customObj, fieldNames, rowPitchOpt, checkTextValues, checkUrlValues, checkImgValues, typeActions } from "./config.js";
import FsPreview from "./fsPreview.vue";
import dayjs from "dayjs";
const props = defineProps({
fullScreenID: String,
pattern: String
})

const emit = defineEmits(['showList']);
const showList = () => {
  emit('showList', true);
};

const pageTitle = computed(() => {
  const labelInfo = {
      'add': "新增霸屏",
      'edit': "编辑霸屏",
      'info': "霸屏详情",
  };
  return labelInfo[props.pattern];
});


const forbidden = computed(() => {
  return props.pattern === "info"
});

const fsPreviewRef = ref()
const customFormRef = ref()
let brandIdArr = []
let siteBrandIdArr = []
const state = ref({
  priorityOpt: [],
  dataOrigin: {},
  showPreview: false,
  modeType: 3,
  setForm: {},
  customForm: {
    type: 1,
    groups: [
      {
        contentArr: [
          {
            text: '',
            url: '',
            fontSize: 16,
            isBold: false,
            rowPitch: 1,
          },
        ],
        settingInfo: {
          showLogo: false,
          typeSetting: "center",
          background: "https://cloudcdn.yyide.com/background/fullscreenbg7.png",
        },
        imgArr: [],
        type: '',
        deviceType: 1,
      },
    ],
  },
  signOptions: [],
});


// 切换类型重置数据
const changeType = () => {
  state.value.customForm.groups = [
  {
        contentArr: [
          {
            text: '',
            url: '',
            fontSize: 16,
            isBold: false,
            rowPitch: 1,
          },
        ],
        settingInfo: {
          showLogo: false,
          typeSetting: "center",
          background: "https://cloudcdn.yyide.com/background/fullscreenbg7.png",
        },
        imgArr: [],
        type: '',
        deviceType: 1,
      },
  ]
}

const comlabel = computed(() => {
  return (index) => {
    if (state.value.customForm.type === 1) {
      return '标题' + (index + 1) + '：';
    } else if (state.value.customForm.type === 2) {
      return '链接' + (index + 1)  + '：';
    } else if (state.value.customForm.type === 3) {
      return '图片：' ;
    } else if (state.value.customForm.type === 4) {
      return '视频：';
    }
  };
});


// 添加组
const addGroup = () => {
  state.value.customForm.groups.push({
    contentArr: [
      {
        text: '',
        url: '',
        fontSize: 16,
        isBold: false,
        rowPitch: 1,
      },
      ],
      imgArr: [],
      type: '',
      deviceType: 1,
      settingInfo: {
          showLogo: false,
          typeSetting: "center",
          background: "https://cloudcdn.yyide.com/background/fullscreenbg7.png",
        }
  });
};


// 删除组
const delItem = (index) => {
  state.value.customForm.groups.splice(index, 1);
};


const addContent = (index) => {
  state.value.customForm.groups[index].contentArr.push({
    text: '',
    url: '',
    fontSize: 16,
    isBold: false,
    rowPitch: 1,
  });
};


const delContent = (index, inx) => {
  state.value.customForm.groups[index].contentArr.splice(inx, 1);
};



// 复制上一条数据
const copyLastItem = () => {
  if (state.value.customForm.groups.length > 0) {
    const copyArr = JSON.parse(JSON.stringify(state.value.customForm.groups));
    const lastItem = copyArr[copyArr.length - 1];
    const newItem = { ...lastItem };
    state.value.customForm.groups.push(newItem);
  }
};

// 保存接口
// 自定义霸屏
const saveBtn = (status) => {
  // 表单校验一下
customFormRef.value.validate().then(() => {
  const action = typeActions[state.value.customForm.type];
  if (action.check(state.value.customForm.groups)) {
    console.log('通过');
  } else {
    message.error(action.errorMessage);
    return;
  }
  let api = props.fullScreenID ? getModeupdate : getModeCreate;
  let reqdata = props.fullScreenID ? { id: props.fullScreenID } : {};
   reqdata = {
          ...reqdata,
          title: state.value.customForm.title || state.value.customForm.groups[0].contentArr[0].text || `${customObj[state.value.customForm.type]}: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`,
          status: status,
          modeType: state.value.modeType,
          level: state.value.customForm.level,
          type: state.value.customForm.type,
          duration: state.value.customForm.duration,
          startDate: state.value.customForm.times[0],
          endDate: state.value.customForm.times[1],
          groups: state.value.customForm.groups.map((item) => {
              const { deviceType, ...rest } = item
              return {
                  deviceType,
                  devices: item.devices.map((it) => {
                      return {
                          businessId: it.value,
                          businessName: it.label,
                      };
                  }),
                  type: state.value.customForm.type,
                  content: state.value.customForm.type === 3 ? JSON.stringify(item.imgArr) : JSON.stringify(item.contentArr),
                  settingInfo: JSON.stringify(item.settingInfo)
              };
          }),
      };
  api(reqdata).then(() => {
      message.success('操作成功！');
      showList();
    });
  })
};

// 获取场地
function processTreeData(data) {
  return data.filter(item => {
      if (item.type === 4) {
          return false;
      }
      if (item.children && item.children.length > 0) {
          item.children = processTreeData(item.children);
      }
      return true;
  });
}

// 获取绑定场地的班牌
const getSignSite = async () => {
// 竖版横版都获取
  const obj = { direction: '' };
  let { data } = await getBindingSiteTreeeV2(obj);
  brandIdArr = JSON.parse(JSON.stringify(data))
  siteBrandIdArr = JSON.parse(JSON.stringify(data))
  // 默认是按场地
  state.value.signOptions = processTreeData(siteBrandIdArr);
};

const reqgetModeinfo = () => {
  getModeinfo({ id: props.fullScreenID }).then(({ data}) => {
      state.value.modeType = data.modeType
      state.value.customForm.level = data.level
      state.value.customForm.duration = data.duration
      state.value.customForm.times = [data.startDate, data.endDate]
      state.value.customForm.type = data.type
      const groupsArr = data.groups.map((item) => {
          return {
              ...item,
              contentArr: JSON.parse(item.content),
            imgArr: state.value.customForm.type === 3 ? JSON.parse(item.content) : [],
            settingInfo:JSON.parse(item.settingInfo),
              devices: item.devices.map((it) => {
                  return {
                      value: it.businessId,
                      label: it.businessName
                  };
              }),
          }
      })
      state.value.customForm.groups = groupsArr
      console.log('state.value.customForm.groups', state.value.customForm.groups)
  })
}


// 设备类型切换
const deviceTypeChange = (e,item) => {
   item.devices = []
}

// 展开下拉列表
const dropdownVisibleChange = (open,item) => {
  console.log('open', open)
  if (item.deviceType === 2) {
    // 选设备就不处理接口数据
    state.value.signOptions = brandIdArr
    console.log('brandIdArr', brandIdArr)
    console.log('选设备', state.value.signOptions)
  } else {
    // 选场地就把接口数据 type===4 的全部去掉
    state.value.signOptions = processTreeData(siteBrandIdArr);
    console.log('选场地',   state.value.signOptions)
  }
}


// 预览霸屏
const previewItme = (item) => {
  console.log('item', item)
  state.value.dataOrigin = item
  state.value.showPreview = true;
}

  // 发送一个请求来获取优先级能不能使用
  const reqLevelList = () => {
  getLevelList().then((res) => {
     state.value.priorityOpt= res.data.map((item) => {
      return {
        value: item.level,
        label: item.level,
        disabled: item.disabled,
      }
    })
  })
}


onMounted(() => {
reqLevelList()
  getSignSite();
  // 如果有id进来,查get接口
    if (props.fullScreenID) {
      reqgetModeinfo()
    }
})
</script>

<style lang="less" scoped>
.fsCommand {
  // padding: 20px 16px;
  transform: translateY(-57px);
  // background: #fff;
  // min-height: calc(100vh - 57px);
  .tabBox {
    padding: 0px 16px;
    background: #fff;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ccc;
    height: 57px;
  }
  .contentBox {
    padding: 20px;
    .pattern {
      padding-bottom: 20px;
    }
  }
}

.titleBox {
  display: flex;
  align-items: center;
  .textBox{
      flex: 1;
  }
  .wordSize {
    display: flex;
    align-items: center;
    padding: 0 16px;
    :deep(.ant-input-number){
      input {
        text-align: center;
      }
    }
  }
  .lineHeight{
      display: flex;
      align-items: center;
      padding: 0 16px;
  }
}

.conArr {
  border-bottom: 1px dashed #ccc;
  margin-bottom: 24px;
}

.footerBox {
  text-align: center;
}
.addGroupBox {
  padding-bottom: 20px;
}

// .overstriking {
//   color: #00b781;
//   border-color: #00b781;
//   background: transparent;
//   user-select: none;
//   touch-action: manipulation;
//   height: 32px;
//   padding: 4px 15px;
//   font-size: 14px;
//   border-radius: 4px;
//   cursor: pointer;
//   font-weight: 400;
//   // text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
//   // box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
//   outline: 0;
//   border: 1px solid #00b781;
// }

.copyCon {
  padding-left: 12px;
  color: #00b781;
  cursor: pointer;
}

.tabtitle {
  font-size: 16px;
  font-weight: 500;
}
.httpUrlBox {
  display: flex;
}

.delItem {
  padding-bottom: 20px;
  cursor: pointer;
  .delItem_text {
    color: #f5222d;
  }
}
.image-container {
  width: 100%;
  height: 100%;
  position: relative;
  &:hover:before {
    content: '';
    position: absolute;
    background: #000000;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    border-radius: 4px;
    opacity: 0.6;
    z-index: 1;
  }
  &:hover:after {
    content: '更改';
    position: absolute;
    left: calc(50% - 20px);
    top: calc(50% - 10px);
    display: inline-block;
    box-sizing: border-box;
    width: 40px;
    height: 20px;
    background: #000000;
    opacity: 0.6;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 400;
    color: #ffffff;
    line-height: 14px;
    z-index: 2;
    transform: scale(0.8);
  }
}
.avatar-uploader {
  width: 100%;
  height: 100%;
}

.avatar-uploader {
  :deep(.ant-upload-select-picture-card) {
    width: 100% !important;
    height: 100% !important;
  }
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
.tip_text {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.imgBox {
  width: 160px;
  height: 90px;
  margin-bottom: 20px;
}

.videoBox{
  width: 320px;
  height: 180px;
  margin-bottom: 20px;
}
.showImgList {
  max-width: 600px;
  .imgItem {
      position: relative;
      .delimgIcon {
      font-size: 18px;
      background: #fff;
      border-radius: 50%;
      position: absolute;
      top: 3px;
      right: 35px;
      color: #f5222d;
      cursor: pointer;
  }
  }
}

.video_item {
  width: 100% ;
  height: 100%;
  background: rgb(0, 0, 0);
  position: relative;

  video {
      width: 100% ;
      height: 100%;
  }

  .delvideoIcon{
      background: #fff;
      font-size: 18px;
      border-radius: 50%;
      position: absolute;
      top: 0px;
      right: 0px;
      color: #f5222d;
      cursor: pointer;
  }
}
.previewText {
  padding-left:12px;
  color: #00b781;
  cursor: pointer;
}

.jiaIcon,.jianIcon{
  cursor: pointer;
}
</style>
