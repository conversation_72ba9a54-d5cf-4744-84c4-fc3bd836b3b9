<!-- 考试霸屏 -->
<template>
    <div class="fullScreen" v-if="state.homepage">
        <div class="searchBox">
            <a-form :model="searchForm" autocomplete="off">
                <a-row :gutter="[25, 16]">
                    <a-col :span="4">
                        <a-form-item label="标题：" name="title">
                            <a-input
                                v-model:value.trim="searchForm.title"
                                placeholder="请输入"
                                allowClear
                            ></a-input>
                        </a-form-item>
                    </a-col>
                    <a-col :span="5">
                        <a-form-item label="霸屏状态：" name="status">
                            <a-select
                                v-model:value="searchForm.status"
                                placeholder="请选择"
                                :options="statusOpt"
                            ></a-select>
                        </a-form-item>
                    </a-col>
                    <!-- <a-col :span="5">
                        <a-form-item label="霸屏类型：" name="modeType">
                            <a-select
                                v-model:value="searchForm.modeType"
                                placeholder="请选择"
                                :options="modeTypeOpt"
                            ></a-select>
                        </a-form-item>
                    </a-col> -->
                    <a-col :span="6">
                        <a-form-item label="展示时间：" name="time">
                            <RangePicker
                                v-model:startTime="searchForm.startDate"
                                v-model:endTime="searchForm.endDate"
                            ></RangePicker>
                        </a-form-item>
                    </a-col>
                    <a-col>
                        <a-button
                            type="primary"
                            style="margin-right: 10px"
                            @click="queryBtn"
                        >
                            <template #icon>
                                <SearchOutlined />
                            </template>
                            查询
                        </a-button>
                        <a-button @click="reset">
                            <template #icon>
                                <reload-outlined />
                            </template>
                            重置
                        </a-button>
                    </a-col>
                </a-row>
            </a-form>
        </div>
        <div class="actionBar_box">
            <a-button @click="added" type="primary" style="margin-right: 12px">
                <template #icon> <plus-outlined /> </template>新增
            </a-button>
            <a-button
                :disabled="!state.selectedRowKeys.length"
                @click="opened"
                style="margin-right: 12px"
                >开启</a-button
            >
            <a-button :disabled="!state.selectedRowKeys.length" @click="stoped"
                >停止</a-button
            >
            <a-button
                :disabled="!state.selectedRowKeys.length"
                @click="batchDel"
                style="margin-left: 12px"
                >批量删除</a-button
            >
        </div>
        <div class="tableBox">
            <Tables
                :isRadio="false"
                :rowSelection="true"
                :spinning="state.spinning"
                :paginations="state.paginations"
                :columns="columns"
                :dataSource="state.tableSource"
                :bordered="false"
                @selectChange="selectChangeSite"
                :selectedRowKeys="state.selectedRowKeys"
                @change="handleTableChange"
            >
                <template #headerCell="{ column }"> </template>
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'title'">
                        <span  class="showTruncation" :title="record.title">{{ record.title }}</span>
                    </template>
                    <template v-if="column.dataIndex === 'brandNames'">
                        <span  class="showTruncation" :title="record.brandNames">{{ record.brandNames }}</span>
                    </template>
                    <template v-if="column.dataIndex === 'startDate'">
                        {{ record.startDate }} - {{ record.endDate }}
                    </template>
                    <template v-if="column.dataIndex === 'modeType'">
                        {{ getStatusObj(record.modeType) }}
                    </template>
                    <template v-if="column.dataIndex === 'status'">
                        <a-badge :color="getfullStatusObj(record.status).color" :text="getfullStatusObj(record.status).text" />
                    </template>
                    <template v-if="column.dataIndex === 'operation'">
                        <a
                            class="btn-link-color operation_btn"
                            @click="details(record)"
                            >详情</a
                        >
                        <a
                            class="btn-link-color operation_btn"
                            @click="compile(record)"
                            >编辑</a
                        >
                        <a
                            class="btn-link-color operation_btn"
                            v-show="record.status === 2"
                            style="color: #f5222d"
                            @click="forbidden(record)"
                            >禁用</a
                        >
                    </template>
                </template>
            </Tables>
        </div>
    </div>
    <FsCommand
        v-else
        @showList="handel"
        :fullScreenID="state.fullScreenID"
        :pattern="state.pattern"
    ></FsCommand>
</template>

<script setup name="examFullScreen">
import { reactive, onMounted, ref } from "vue";
import { message, Modal } from "ant-design-vue";
import Tables from "@/components/Tables";
import FsCommand from "./fsCommand.vue";
import RangePicker from "@/components/RangePicker/index.vue";
import {
    getModePage,
    getModeDelete,
    getModeStatusUpdate,
} from "@/api/classSign";
import { statusOpt, modeTypeOpt, columns, statusFormat } from "./config.js";

function getStatusObj(key) {
    return statusFormat[key] || statusFormat.default;
}

const statusObj = {
    1: {
        text: '已停止',
        color: '#F5222D',
    },
    2: {
        text: '已开启',
        color: '#00B781',
    },
    3: {
        text: '已过期',
        color: '#BFBFBF',
    },
    default: {
        text: '-',
        color: '',
    }, // 默认值存储在 default 属性中
}

function getfullStatusObj(key) {
    return statusObj[key] || statusObj.default
}

const state = reactive({
    fullScreenID: "",
    homepage: true,
    spinning: false,
    paginations: {
        pageNo: 1, // 页码
        pageSize: 10, // 条数
        total: 0,
    },
    tableSource: [],
    selectedRowKeys: [],
});

const searchForm = ref({
    status: null,
    modeType: 3,
});

const queryBtn = () => {
    state.paginations.pageNo = 1;
    reqModePage();
};

// 重置
const reset = () => {
    state.paginations.pageNo = 1;
    state.paginations.pageSize = 10;
    searchForm.value = {
        status: null,
        modeType: 3,
    };
    reqModePage();
};

const reqModePage = () => {
    state.spinning = true;
    getModePage({
        ...state.paginations,
        ...searchForm.value,
    })
        .then((res) => {
            state.tableSource = res.data.list;
            state.paginations.total = res.data.total;
            state.spinning = false;
        })
        .catch((e) => {
            console.log("catch", e);
        })
        .finally(() => {
            state.spinning = false;
        });
};

// 新增
const added = () => {
    state.homepage = false;
    state.fullScreenID = "";
    state.pattern = "add";
};

// table 翻页
const handleTableChange = (data) => {
    state.paginations = data;
    reqModePage();
};

// table多选
const selectChangeSite = (data) => {
    state.selectedRowKeys = data.map((item) => item.id);
};

// 开启
const opened = () => {
    Modal.confirm({
        title: "提示",
        type: "warning",
        content: "确定开启霸屏?",
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        onOk() {
            getModeStatusUpdate({
                status: 2,
                ids: state.selectedRowKeys,
            })
                .then((res) => {
                    reqModePage();
                    state.selectedRowKeys = [];
                })
                .catch((e) => {
                    console.log("catch", e);
                });
        },
        onCancel() {
            message.warning("已取消!");
        },
    });
};

// 停止
const stoped = () => {
    Modal.confirm({
        title: "提示",
        type: "warning",
        content: "确定停止霸屏?",
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        onOk() {
            getModeStatusUpdate({
                status: 1,
                ids: state.selectedRowKeys,
            })
                .then((res) => {
                    reqModePage();
                    state.selectedRowKeys = [];
                })
                .catch((e) => {
                    console.log("catch", e);
                });
        },
        onCancel() {
            message.warning("已取消!");
        },
    });
};
// 批量删除
const batchDel = () => {
    Modal.confirm({
        title: "删除",
        type: "warning",
        content: "确定删除霸屏?",
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        onOk() {
            getModeDelete({
                ids: state.selectedRowKeys,
            })
                .then((res) => {
                    reqModePage();
                    state.selectedRowKeys = [];
                })
                .catch((e) => {
                    console.log("catch", e);
                });
        },
        onCancel() {
            message.warning("已取消!");
        },
    });
};

const handel = (val) => {
    console.log("val", val);
    state.homepage = val;
    reqModePage();
};

// 点击查看详情
const details = (data) => {
    state.homepage = false;
    state.fullScreenID = data.id;
    state.pattern = "info";
};

// 点击编辑
const compile = (data) => {
    state.homepage = false;
    state.fullScreenID = data.id;
    state.pattern = "edit";
};


const forbidden =(data)=>{
    Modal.confirm({
        title: "提示",
        type: "warning",
        content: "确定停止霸屏?",
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        onOk() {
            getModeStatusUpdate({
                status: 1,
                ids: [data.id],
            })
                .then((res) => {
                    reqModePage();
                })
                .catch((e) => {
                    console.log("catch", e);
                });
        },
        onCancel() {
            message.warning("已取消!");
        },
    });
}

onMounted(() => {
    reqModePage();


});
</script>

<style lang="less" scoped>
.fullScreen {
    padding: 20px 16px;
}

.actionBar_box {
    padding-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: end;
}
.operation_btn {
    margin-right: 16px;
}
.showTruncation {
    display: inline-block; /* 将span转换为块级元素，以便设置宽度 */
    width: 100%;
    white-space: nowrap; /* 防止文字换行 */
    overflow: hidden; /* 隐藏超出部分的文字 */
    text-overflow: ellipsis; /* 显示省略号 */
}
</style>
