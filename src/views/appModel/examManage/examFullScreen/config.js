export const statusOpt = [
    {
        value: null,
        label: "全部",
    },
    {
        value: 1,
        label: "已停止",
    },
    {
        value: 2,
        label: "已开启",
    },
    {
        value: 3,
        label: "已过期",
    },
];

export const modeTypeOpt = [
    {
        value: null,
        label: "全部",
    },
    {
        value: 1,
        label: "上课霸屏",
    },
    {
        value: 2,
        label: "信息发布霸屏",
    },
    {
        value: 3,
        label: "考试霸屏",
    },
    {
        value: 4,
        label: "自定义霸屏",
    },
];


export const columns = [
    {
        title: "序号",
        key: "id",
        width: "100px",
        customRender: ({ index }) => {
            return `${index + 1}`;
        }, // 显示每一行的序号
        fixed: "left",
    },
    {
        title: "标题",
        dataIndex: "title",
        key: "title",
        ellipsis: true,
    
    },
    {
        title: "班牌名称",
        dataIndex: "brandNames",
        ellipsis: true,
    },
    {
        title: "霸屏类型",
        dataIndex: "modeType",
        width: "150px",
    },
    {
        title: "启止时间",
        dataIndex: "startDate",
        width: "350px",
    },
    {
        title: "霸屏状态",
        dataIndex: "status",
        width: "100px",
    },

    {
        title: "操作",
        dataIndex: "operation",
        width: '200px',
    },
];

export const priorityOpt = [
    {
        value: 1,
        label: 1,
    },
    {
        value: 2,
        label: 2,
    },
    {
        value: 3,
        label: 3,
    },
    {
        value: 4,
        label: 4,
    },
    {
        value: 5,
        label: 5,
    },
    {
        value: 6,
        label: 6,
    },
    {
        value: 7,
        label: 7,
    },
    {
        value: 8,
        label: 8,
    },
    {
        value: 9,
        label: 9,
    },
];


export const customObj = {
    1: "文字",
    2: "链接",
    3: "图片",
    4: "视频",
}

export const customOpt = [
    {
        value: 1,
        label: '文字',
    },
    // {
    //     value: 2,
    //     label: '链接',
    // },
    // {
    //     value: 3,
    //     label: '图片',
    // },
    // {
    //     value: 4,
    //     label: '视频',
    // },
];

export const rowPitchOpt = [
    {
        value: 1,
        label: '1倍',
    },
    {
        value: 2,
        label: '2倍',
    },
    {
        value: 3,
        label: '3倍',
    },
    {
        value: 4,
        label: '4倍',
    },
    {
        value: 5,
        label: '5倍',
    },
    {
        value: 6,
        label: '6倍',
    },
    {
        value: 7,
        label: '7倍',
    },
    {
        value: 8,
        label: '8倍',
    },
    {
        value: 9,
        label: '9倍',
    },
    {
        value: 10,
        label: '10倍',
    },
]

export const fieldNames = {
    label: 'showName',
    value: 'id',
    children: 'children',
  };

  export const statusFormat = {
    1: "上课霸屏",
    2: "信息发布霸屏",
    3: "考试霸屏",
    4: "自定义霸屏",
    default: "-",
};

export function checkTextValues(arr) {
    return arr.every(obj => obj.contentArr.every(content => content.text !== ""));
}

export function checkUrlValues(arr) {
    return arr.every(obj => obj.contentArr.every(content => content.url !== ""));
}

export function checkImgValues(arr) {
    return arr.every(item => item.imgArr.length > 0 && item.imgArr.every(img => img.url));
}


export const typeActions = {
    1: {
  check: checkTextValues,
  errorMessage: '霸屏的标题内容不能为空'
},
2: {
  check: checkUrlValues,
  errorMessage: '霸屏的链接地址不能为空'
},
3: {
  check: checkImgValues,
  errorMessage: '霸屏的图片不能为空'
},
4: {
  check: checkUrlValues,
  errorMessage: '霸屏的视频不能为空'
}
};