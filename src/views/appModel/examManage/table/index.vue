<template>
    <a-spin :spinning="spinning">
        <a-table
            :columns="columns"
            :data-source="dataSource"
            :align="'center'"
            :bordered="bordered"
            :pagination="state.paginationAttr"
            :position="positionSort"
            @change="change"
            :scroll="scrolls"
            :row-key="rowKeyId"
            :row-selection="
                rowSelection
                    ? {
                          selectedRowKeys: state.selectedRowKeys,
                          onChange: onSelectChange,
                          type: isRadio ? 'radio' : 'checkbox'
                      }
                    : null
            "
        >
            <template #headerCell="{ column, text, record, index }">
                <slot
                    name="headerCell"
                    :column="column"
                    :text="text"
                    :record="record"
                    :index="index"
                />
            </template>
            <template #bodyCell="{ column, text, record, index }">
                <slot
                    name="bodyCell"
                    :column="column"
                    :text="text"
                    :record="record"
                    :index="index"
                />
            </template>
            <template #emptyText>
                <slot name="emptyText">
                    <a-empty
                        image="/image/empty.png"
                        :image-style="{ width: '100%', height: '180px' }"
                    />
                </slot>
            </template>
        </a-table>
    </a-spin>
</template>

<script setup>
import { reactive, watch, shallowRef, onMounted } from 'vue'
const props = defineProps({
    // hander 数据
    columns: {
        type: Array,
        default: () => []
    },
    // boby 数据
    dataSource: {
        type: Array,
        default: () => []
    },
    rowKeyId: {
        type: [Function, String],
        default: 'id'
    },
    //  勾选框单选:true 多选 false
    isRadio: {
        type: Boolean,
        default: false
    },
    //  勾选框
    rowSelection: {
        type: Boolean,
        default: false
    },
    selectedRowKeys: {
        type: Array,
        default: () => []
    },
    // 分页条数
    paginations: {
        type: Object,
        default: () => {
            return {
                pageNo: 1, // 页码
                pageSize: 1000, // 条数
                total: 0
            }
        }
    },
    // loing 加载
    spinning: {
        type: Boolean,
        default: false
    },
    // 是否显示边框
    bordered: {
        type: Boolean,
        default: true
    },
    scrolls: {
        type: Object,
        default: () => {}
    }
})

const state = reactive({
    selectedRowKeys: props.selectedRowKeys || [],
    paginationAttr: {
        total: 0,
        current: 1,
        pageSize: 10,
        hideOnSinglePage: true,
        showLessItems: true,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '30', '40'],
        ...props.paginations,
        showTotal: (total) => `共 ${total}  条`
    }
})
watch(
    () => props.paginations,
    ({ pageNo, pageSize, total }) => {
        state.paginationAttr.current = pageNo
        state.paginationAttr.pageSize = pageSize
        state.paginationAttr.total = total
        state.paginationAttr.hideOnSinglePage = total < 11
    }
)
watch(
    () => props.selectedRowKeys,
    (val) => {
        state.selectedRowKeys = val
    }
)

const positionSort = shallowRef(['bottomLeft'])
const emit = defineEmits(['change', 'selectChange'])
// 分页事件
const change = (data) => {
    const { current, pageSize, total } = data
    emit('change', { pageNo: current, pageSize, total })
}

const onSelectChange = (selectedRowKeys, data) => {
    state.selectedRowKeys = selectedRowKeys
    emit('selectChange', selectedRowKeys, data)
}
</script>
