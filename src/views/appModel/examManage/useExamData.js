/**
 * @annotation: 全局状态显示
 */
import { reactive } from 'vue'


const examinationId = ref('')// 考试id


const createType = ref(1)  // 1是大型考试 2是小型考试


// 基本信息数据
const formState = reactive(
    {
        "signIn": false,
        "showed": true
    }
)

// 重置数据
const resetState = () => {
    for (const key in formState) {
        delete formState[key]
    }
    // 默认数据
    formState.signIn = false
    formState.showed = true
}
export function useExamData() {
    return {
        formState,
        examinationId, 
        createType,
        resetState
    }
}
