<!-- eslint-disable -->
<!-- 考场管理 -->
<!-- 考场管理 -->
<!-- 考场管理 -->
<template>
    <div class="examRoom">
        <div class="queryBox">
            <a-form layout="inline">
                <a-form-item
                label="考场名称：">
                    <a-input
                        v-model:value="state.examName"
                        placeholder="请输入"
                        style="width: 206px"
                    />
                </a-form-item>
                <a-form-item label="场地名称：">
                    <a-input
                        v-model:value="state.siteName"
                        placeholder="请输入"
                        style="width: 206px"
                    />
                </a-form-item>
                <a-form-item>
                    <a-button
                        type="primary"
                        style="margin-right: 12px"
                        @click="queryStatistics"
                    >
                        <template #icon>
                            <SearchOutlined style="margin-right: 4px" />
                        </template>
                        查询
                    </a-button>
                    <a-button @click="resetStatistics">
                        <template #icon>
                            <redo-outlined style="margin-right: 4px" />
                        </template>
                        重置
                    </a-button>
                </a-form-item>
            </a-form>
            <div>
                <a-button
                    style="margin-right: 12px"
                    @click="addExam"
                    type="primary"
                    ><template #icon>
                        <plus-outlined style="margin-right: 8px" /> </template
                    >新增考场</a-button
                >
                <a-button
                    style="margin-right: 12px"
                    @click="amend"
                    :disabled="!state.selectedRowKeys.length"
                    >批量修改</a-button
                >
                <a-button
                    type="primary"
                    :disabled="!state.selectedRowKeys.length"
                    danger
                    ghost
                    @click="deletesiteManage"
                    >删除</a-button
                >
            </div>
        </div>
        <div class="roomTable">
            <Tables
                :isRadio="false"
                :rowSelection="true"
                :spinning="state.spinning"
                :paginations="state.paginations"
                :columns="columns"
                :dataSource="state.tableSource"
                :bordered="false"
                @selectChange="selectChangeSite"
                :selectedRowKeys="state.selectedRowKeys"
                @change="handleTableChange"
            >
                <template #headerCell="{ column }">
                    <template v-if="column.dataIndex === 'row'">
                        <div>
                            考场布局
                            <span style="font-size: 12px">（行X列）</span>
                        </div>
                    </template>
                </template>
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'row'">
                        <a-input-number
                            :min="1"
                            @blur="handleBlur(record)"
                            :precision="0"
                            :controls="false"
                            v-model:value="record.row"
                        />行
                        <a-input-number
                            :min="1"
                            @blur="handleBlur(record)"
                            :precision="0"
                            :controls="false"
                            v-model:value="record.cell"
                        />列
                    </template>
                    <template v-if="column.dataIndex === 'examPeopleNum'">
                       <span></span>
                        <a-input-number
                            :min="1"
                            :disabled="!record.row || !record.cell"
                            @blur="handleBlur(record)"
                            :precision="0"
                            :controls="false"
                            v-model:value="record.examPeopleNum"
                        />
                    </template>
                </template>
            </Tables>
        </div>
        <!-- 新增考场的modal  -->
        <a-modal
            :body-style="{ maxHeight: '600px', overflow: 'hidden auto' }"
            :confirm-loading="state.confirmLoading"
            :maskClosable="false"
            centered
            v-model:visible="state.sitePageFormVisible"
            title="新增考场"
            @cancel="addSiteCancel"
            @ok="addsiteHandleOk"
            width="900px"
            ok-text="确认"
            cancel-text="取消"
        >
            <div class="addInquire">
                <a-form layout="inline">
                    <a-form-item label="楼栋：">
                        <a-select
                            :getPopupContainer="
                                (triggerNode) => triggerNode.parentNode
                            "
                            style="width: 240px"
                            v-model:value="state.buildingId"
                            placeholder="请选择"
                            :options="state.buildList"
                            :showArrow="true"
                            :allowClear="true"
                            :field-names="{
                                label: 'name',
                                value: 'id',
                            }"
                        >
                        </a-select>
                    </a-form-item>
                    <a-form-item label="场地类型：">
                        <a-select
                            :getPopupContainer="
                                (triggerNode) => triggerNode.parentNode
                            "
                            style="width: 240px"
                            v-model:value="state.siteTypeId"
                            placeholder="请选择"
                            :options="state.siteTypeList"
                            :showArrow="true"
                            :allowClear="true"
                            :field-names="{
                                label: 'name',
                                value: 'id',
                            }"
                        >
                        </a-select>
                    </a-form-item>
                    <a-form-item>
                        <a-button
                            type="primary"
                            style="margin-right: 12px"
                            @click="querysitePage"
                        >
                            <template #icon>
                                <SearchOutlined style="margin-right: 4px" />
                            </template>
                            查询
                        </a-button>
                        <a-button @click="resetSitePage">
                            <template #icon>
                                <redo-outlined style="margin-right: 4px" />
                            </template>
                            重置
                        </a-button>
                    </a-form-item>
                </a-form>
            </div>
            <a-table
                rowKey="id"
                :row-selection="{
                    selectedRowKeys: state.siteSelectedRowKeys,
                    onChange: onSiteSelectChange,
                }"
                :dataSource="state.sitePageSource"
                :pagination="false"
                :columns="siteColumns"
            >
            </a-table>
        </a-modal>
        <!-- 批量修改的modal -->
        <a-modal
            :confirm-loading="state.confirmLoading"
            :body-style="{ maxHeight: '600px', overflow: 'hidden auto' }"
            :maskClosable="false"
            centered
            v-model:visible="state.modification"
            title="批量修改"
            @cancel="cancelForm"
            @ok="handleOk"
            width="1000px"
            ok-text="确认"
            cancel-text="取消"
        >
            <a-table
                size="middle"
                :dataSource="state.choiceExam"
                :columns="batchColumns"
                :pagination="false"
            >
                <template #headerCell="{ column }">
                    <template v-if="column.dataIndex === 'row'">
                        <div>
                            考场布局
                            <span style="font-size: 12px">（行X列）</span>
                        </div>
                    </template>
                </template>
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'row'">
                        {{ record.row }}*{{ record.cell }}
                    </template>
                </template>
            </a-table>
            <div class="batchForm">
                <a-form
                    layout="vertical"
                    :model="state.upobj"
                    name="upobj"
                    ref="upobjRef"
                >
                    <a-form-item
                        label="考场容量："
                        name="examPeopleNum"
                        :rules="[{ required: true, message: '请输入考场容量' }]"
                    >
                        <a-input-number
                            :min="1"
                            style="width: 220px"
                            :controls="false"
                            placeholder="请输入"
                            :precision="0"
                            v-model:value="state.upobj.examPeopleNum"
                        />

                        <span class="numTips"
                            >考场容量需小于等于行X列的总数</span
                        >
                    </a-form-item>
                    <a-form-item
                        label="考场布局："
                        name="row"
                        :rules="[{ required: true, message: '请输入考场布局' }]"
                    >
                        <a-input-number
                            :min="1"
                            placeholder="请输入"
                            :precision="0"
                            :controls="false"
                            v-model:value="state.upobj.row"
                        /><span class="numTips">行</span>
                        <a-form-item-rest
                            ><a-input-number
                                :min="1"
                                placeholder="请输入"
                                :precision="0"
                                :controls="false"
                                v-model:value="
                                    state.upobj.cell
                                " /></a-form-item-rest
                        ><span class="numTips">列</span>
                    </a-form-item>
                </a-form>
            </div>
        </a-modal>
    </div>
</template>

<script setup name="examRoom">
/* eslint-disable */
import { reactive, onMounted, ref, createVNode } from "vue";
import Tables from "@/components/Tables";
// import Tables from "../table/index.vue";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { Modal, message } from "ant-design-vue";
import {
    getCloudBuildList,
    siteTypeList,
    sitePage,
    createExamSiteManage,
    getexamSiteManagePage,
    batchUpdateExamSiteManage,
    deleteExamSiteManage,
} from "@/api/exam.js";

// 数据
const state = reactive({
    selectedRowKeys: [],
    choiceExam: [],
    upobj: {},
    buildList: [],
    siteTypeList: [],
    siteSelectedRowKeys: [],
    tableSource: [],
    sitePageFormVisible: false,
    modification: false,
    paginations: {
        pageNo: 1, // 页码
        pageSize: 10, // 条数
        total: 0,
    },
});

const upobjRef = ref(null);

// 考场的table 表头数据
const columns = [
    {
        title: "序号",
        key: "index",
        dataIndex: "index",
        customRender: ({ text, record, index }) => `${index + 1}`, // 显示每一行的序号
    },
    {
        title: "考场名称",
        key: "name",
        dataIndex: "name",
    },
    {
        title: "场地名称",
        key: "siteName",
        dataIndex: "siteName",
    },
    {
        title: "场地类型",
        key: "siteTypeName",
        dataIndex: "siteTypeName",
    },
    {
        title: "归属楼栋",
        key: "buildingName",
        dataIndex: "buildingName",
    },
    {
        title: "楼层",
        key: "floor",
        dataIndex: "floor",
    },
    {
        title: "房间号",
        key: "roomNum",
        dataIndex: "roomNum",
    },
    {
        title: "容纳人数",
        key: "sitePeopleNum",
        dataIndex: "sitePeopleNum",
    },
    {
        title: "考场布局",
        key: "row",
        dataIndex: "row",
    },
    {
        title: "考场容量",
        key: "examPeopleNum",
        dataIndex: "examPeopleNum",
    },
];

const siteColumns = [
    {
        title: "场地名称",
        key: "name",
        dataIndex: "name",
    },
    {
        title: "场地类型",
        key: "siteTypeName",
        dataIndex: "siteTypeName",
    },
    {
        title: "所属楼栋",
        key: "passsceneName",
        dataIndex: "passsceneName",
    },
    {
        title: "楼层",
        key: "floor",
        dataIndex: "floor",
    },
    {
        title: "房间号",
        key: "roomNum",
        dataIndex: "roomNum",
    },
    {
        title: "容纳人数",
        key: "peppleNum",
        dataIndex: "peppleNum",
    },
];

const batchColumns = [
    {
        title: "考场名称",
        key: "name",
        dataIndex: "name",
    },
    {
        title: "场地名称",
        key: "siteName",
        dataIndex: "siteName",
    },
    {
        title: "场地类型",
        key: "siteTypeName",
        dataIndex: "siteTypeName",
    },
    {
        title: "所属楼栋",
        key: "buildingName",
        dataIndex: "buildingName",
    },
    {
        title: "楼层",
        key: "floor",
        dataIndex: "floor",
    },
    {
        title: "房间号",
        key: "roomNum",
        dataIndex: "roomNum",
    },
    {
        title: "容纳人数",
        key: "sitePeopleNum",
        dataIndex: "sitePeopleNum",
    },
    {
        title: "考场布局",
        key: "row",
        dataIndex: "row",
    },
];

const addExam = () => {
    querysitePage();
    state.sitePageFormVisible = true;
};

// 删除
const deletesiteManage = () => {
    deleteExamSiteManage({
        ids: state.selectedRowKeys,
    }).then(() => {
        message.success("成功");
        state.selectedRowKeys = [];
        reqexamSiteManagePage();
    });
};

const amend = () => {
    state.modification = true;
};

// 增加考场确认
const addsiteHandleOk = () => {
    if (!state.siteSelectedRowKeys.length) return;
    Modal.confirm({
        title: "提示",
        icon: createVNode(ExclamationCircleOutlined),
        type: "warning",
        content:
            "新增考场后请至列表页继续设置【考场容量】【考场布局】，若未设置将导致在考场安排中无法使用该考场。",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        onOk: () => {
            createExamSiteManage({
                siteIds: state.siteSelectedRowKeys,
            }).then((res) => {
                message.success("新增成功！");
                reqexamSiteManagePage();
                state.siteSelectedRowKeys = [];
                state.sitePageFormVisible = false;
            });
        },
        onCancel() {
            message.warning("已取消!");
        },
    });
};

// 获取学校下面的建筑列表
const reqCloudBuildList = async () => {
    const { data } = await getCloudBuildList();
    state.buildList = data[0].children;
};

// 获取场地类型的列表
const reqSiteTypeList = async () => {
    const { data } = await siteTypeList();
    state.siteTypeList = data;
};

// 通过场地建筑id 和 场地类型id 查询 场地列表
const querysitePage = () => {
    sitePage({
        buildingId: state.buildingId,
        siteTypeId: state.siteTypeId,
        pageNo: 1,
        pageSize: 9999, // 不分页了 不分页了
    })
        .then((result) => {
            const { data } = result;
            state.sitePageSource = data.list;
        })
        .catch((err) => {
            console.log("err", err);
        });
};

const resetSitePage = () => {
    state.buildingId = null;
    state.siteTypeId = null;
    querysitePage();
};

// 勾选的场地的table
const onSiteSelectChange = (selectedRowKeys) => {
    state.siteSelectedRowKeys = selectedRowKeys;
};

// 考场管理分页的接口
const reqexamSiteManagePage = async () => {
    const obj = {
        ...state.paginations,
        name: state.examName,
        siteName: state.siteName,
    };
    const { data } = await getexamSiteManagePage(obj);
    state.tableSource = data.list;
    state.paginations.total = data.total;
};

// 查询
const queryStatistics = () => {
    state.paginations.pageNo = 1;
    reqexamSiteManagePage();
};

// 重置
const resetStatistics = () => {
    state.paginations.pageNo = 1;
    state.paginations.pageSize = 10;
    state.examName = "";
    state.siteName = "";
    reqexamSiteManagePage();
};

// table的分页操作
const handleTableChange = (data) => {
    state.paginations = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
    };
    reqexamSiteManagePage();
};

const selectChangeSite = (data) => {
    state.choiceExam = data;
    state.selectedRowKeys = data.map((item) => item.id);
};

function addNonNullValues(obj) {
    let list = [];
    let newObj = {};
    if (obj.cell) {
        newObj.cell = obj.cell;
        const examPeopleNum = obj.cell * (obj.row || 0);
        if (examPeopleNum) {
            newObj.examPeopleNum = examPeopleNum;
        }
    }
    if (obj.row) {
        newObj.row = obj.row;
        const examPeopleNums = obj.cell * (obj.row || 0);
        if (examPeopleNums) {
            newObj.examPeopleNum = examPeopleNums;
        }
    }
    if (obj.examPeopleNum) {
        const result = checkMultiplication(
            obj.row,
            obj.cell,
            obj.examPeopleNum
        );

        if (result) {
            newObj.examPeopleNum = obj.examPeopleNum;
        } else {
            message.warning("考场容量需小于等于行X列的总数");
            newObj.examPeopleNum = obj.cell * obj.row;
        }
    }
    if (Object.keys(newObj).length > 0) {
        newObj.id = obj.id;
        list.push(newObj);
    }
    return list;
}

// 给这些考场设置 容量 行列
const handleBlur = (data) => {
    let list = addNonNullValues(data);

    if (list.length > 0) {
        batchUpdateExamSiteManage({
            list,
        }).then((res) => {
            reqexamSiteManagePage()
        });
    }
};

const addSiteCancel = () => {
    state.siteSelectedRowKeys = [];
    state.sitePageFormVisible = false;
};

function checkMultiplication(field1, field2, field3) {
    const num1 = field1 ? field1 : 0;
    const num2 = field2 ? field2 : 0;
    return num1 * num2 >= field3;
}

// 确认批量修改
const handleOk = () => {
    upobjRef.value.validate().then(() => {
        const result = checkMultiplication(
            state.upobj.row,
            state.upobj.cell,
            state.upobj.examPeopleNum
        );
        if (result) {
            const list = state.choiceExam.map((item) => {
                return {
                    id: item.id,
                    ...state.upobj,
                };
            });
            batchUpdateExamSiteManage({
                list,
            }).then((res) => {
                state.selectedRowKeys = [];
                message.success("修改成功！");
                reqexamSiteManagePage();
                cancelForm();
                // state.modification = false;
            });
        } else {
            message.warning("考场容量需小于等于行X列的总数");
        }
    });
};

const cancelForm = () => {
    upobjRef.value.resetFields();
    state.upobj.cell = null;
    state.modification = false;
};

onMounted(() => {
    reqexamSiteManagePage();
    reqCloudBuildList();
    reqSiteTypeList();
});
</script>

<style lang="less" scoped>
.examRoom {
    padding: 16px;
}
.queryBox {
    display: flex;
    justify-content: space-between;
    padding-bottom: 16px;
}
.addInquire {
    padding-bottom: 60px;
}

.batchForm {
    padding-top: 24px;
}
.numTips {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    padding-left: 8px;
    padding-right: 8px;
}
</style>
