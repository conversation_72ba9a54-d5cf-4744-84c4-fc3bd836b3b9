<template>
    <div class="create_warp">
        <div class="header">
            <div style="display: inline-block">
                <a-button type="text" class="tabs_back" @click="goBack">
                    <i class="iconfont icon-xingzhuangjiehe19" />
                </a-button>
                <span>返回</span>
            </div>

            <!-- 这里是步骤条啊 -->
            <div class="stepsBox">
                <div :class="{
                    stepsBox_item: true,
                    activeBorder: state.activateIndex === index,
                }" @click="stepItem(index, item)" v-for="(item, index) in state.stepList" :key="item.id">
                    <div :class="{
                        circle: true,
                        circleActivate: state.activateIndex === index,
                    }">
                        <span :class="{
                            textActivate: true,
                            ytextActivate: state.activateIndex === index,
                        }">{{ item.id }}</span>
                    </div>
                    <div :class="{
                        textActivate: true,
                        ytextActivate: state.activateIndex === index,
                    }">
                        {{ item.name }}
                    </div>
                </div>
            </div>
        </div>

        <!-- 考试基本信息页面 都在这里切换 -->
        <div class="bodyArea">
            <component ref="componentIdRef" :is="componentId"></component>
        </div>
        <div class="footer" v-if="!isExamTable">
            <a-button class="planBtns" @click="lastStep" v-if="!isInformation">上一步</a-button>
            <a-button v-if="!isSmallPublish" class="planBtn" type="primary" @click="nextStep">下一步</a-button>
            <a-button v-if="isPublish && !isDisabled" class="planBtn" type="primary" @click="publishOk">发布</a-button>
            <a-button v-if="isSmallPublish" class="planBtn" type="primary" @click="publishOk">小型考试发布</a-button>
        </div>
        <div class="footer" v-else>
            <a-button class="planBtns" type="primary" @click="finish">完成</a-button>
        </div>
    </div>
</template>

<script setup>
import { reactive, ref, toRaw, computed } from "vue";
import { message, Modal } from "ant-design-vue";
import { publishExam } from "@/api/exam.js";

// 步骤1考试计划
import Examplan from "../comp/examPlan.vue";

// 步骤2考场编排
import ExamArrange from "../comp/examArrange.vue";

// 步骤3 监考安排
import Invigilate from "../comp/invigilate.vue";

// 步骤4 考场表
import ExamTable from "../comp/examTable.vue";

import { useRoute, useRouter } from "vue-router";
import { useExamData } from "../useExamData.js";
const { examinationId, createType } = useExamData();

const router = useRouter();
const route = useRoute();

const componentId = shallowRef(Examplan);
// 计算属性判断是否为查看页面 禁用所有表单选项
const isDisabled = computed(() => route.query.type === "look");
// 组件列表
const componentList = {
    Examplan: Examplan,
    ExamArrange: ExamArrange,
    Invigilate: Invigilate,
    ExamTable: ExamTable,
};

// component子组件ref 实例
const componentIdRef = ref();

const isPublish = computed(
    () => componentIdRef.value?.state.activityName === "invigilate"
);


const isSmallPublish = computed(
    () => componentIdRef.value?.state.activityName === "seatArrange" && createType.value === 2
);


const isExamTable = computed(
    () => componentIdRef.value?.state.activityName === "examTable"
);

const isInformation = computed(
    () => componentIdRef.value?.state.activityName === "information"
);

const state = reactive({
    activateIndex: 0,
    iscomp: "info",
    subFormVisible: false,
    stepList: [
        {
            id: "1",
            value: "Examplan",
            name: "考试计划",
        },
        {
            id: "2",
            value: "ExamArrange",
            name: "考场编排",
        },
        {
            id: "3",
            value: "Invigilate",
            name: "监考安排",
        },
        {
            id: "4",
            value: "ExamTable",
            name: "考场表",
        },
    ],
});

// 下一步按钮下一步按钮下一步按钮
const nextStep = async () => {
    // 从这里开始逻辑有些复杂了
    // 先判断当前页面是不是考试计划的基本信息如果是则去掉子组件的方法去发送请求新增考试基本信息
    // 下一步显示的组件切换等逻辑全在这里面
    switch (componentIdRef.value.state.activityName) {
        case "information":
            componentIdRef.value.addExamPlanInformation();
            break;
        case "schedule":
            const consequence =
                await componentIdRef.value.addExamPlanSchedule();
            if (consequence.data) {
                // 去考场编排
                state.activateIndex += 1;
                componentId.value = componentList["ExamArrange"];
            }
            break;
        case "roomArrange":
            componentIdRef.value.goNextSeat();
            break;
        case "seatArrange":
            const verify = await componentIdRef.value.verifyType();
            if (verify.data) {
                state.activateIndex += 1;
                componentId.value = componentList["Invigilate"];
            }
            break;
        case "invigilate":
            state.activateIndex += 1;
            componentId.value = componentList["ExamTable"];
            break;
        default:
            break;
    }

};

const finish = () => {
    router.push({ path: "/appModel/examManage/list" });
};

const publishOk = () => {
    Modal.confirm({
        title: "提示",
        type: "warning",
        content: "发布考试后，生成考场表将不能编辑，确认发布考试？",
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        onOk() {
            publishExam({
                id: examinationId.value,
            }).then((res) => {
                router.push({ path: "/appModel/examManage/list" });
            });
        },
        onCancel() {
            // message.warning("已取消!");
        },
    });
};

// 上一步上一步上一步上一步上一步

const lastStep = () => {
    switch (componentIdRef.value.state.activityName) {
        case "schedule":
            componentIdRef.value.goLastStep();
            break;
        case "roomArrange":
            state.activateIndex -= 1;
            componentId.value = componentList["Examplan"];
            break;
        case "seatArrange":
            componentIdRef.value.goLastStep();
            break;
        case "invigilate":
            state.activateIndex -= 1;
            componentId.value = componentList["ExamArrange"];
            break;
        default:
            break;
    }
};

// 切换步骤条事件
// const stepItem = (index, data) => {
//     state.activateIndex = index;
//     componentId.value = componentList[data.value];
// };


const goBack = () => {
    router.push({
        path: "/appModel/examManage/list",
    });
};

onMounted(() => {
    if (createType.value === 1) {
        state.stepList = [
            {
                id: "1",
                value: "Examplan",
                name: "考试计划",
            },
            {
                id: "2",
                value: "ExamArrange",
                name: "考场编排",
            },
            {
                id: "3",
                value: "Invigilate",
                name: "监考安排",
            },
            {
                id: "4",
                value: "ExamTable",
                name: "考场表",
            },
        ];
    }
    if (createType.value === 2) {
        state.stepList = [
            {
                id: "1",
                value: "Examplan",
                name: "考试计划",
            },

            {
                id: "2",
                value: "ExamArrange",
                name: "考场编排",
            },
        ];
    }
});
</script>

<style lang="less" scoped>
.create_warp {
    overflow: auto;
    background: #9e9e9e;
    background: #fff;
    transform: translateY(-58px);
    height: calc(100vh - 74px);
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    // position: relative;

    .edit_rule {
        color: @primary-color;
        z-index: 1;

        &:hover {
            background: none;
        }
    }

    .header {
        display: flex;
        height: 57px;
        line-height: 57px;
        border-bottom: 2px solid #f0f0f0;
    }

    .bodyArea {
        // height: 1500px;
        flex: 1;
        // height: calc(100vh -110px);
        // overflow-y: scroll;
        padding: 24px 16px;

        .tips {
            color: rgba(0, 0, 0, 0.45);
        }
    }

    .footer {
        text-align: center;
        justify-content: center;
        display: flex;
        border-top: 1px solid #f0f0f0;
        padding: 16px 16px 16px 280px;
    }

    // .tabs_back__icon {
    //     color: @primary-color;
    // }
}

.circle {
    height: 22px;
    width: 22px;
    line-height: 20px;
    border-radius: 11px;
    border: 1px solid rgba(0, 0, 0, 0.45);
    vertical-align: middle;
    text-align: center;
    margin-right: 8px;
}

.circleActivate {
    border: 1px solid rgba(0, 0, 0, 0.85) !important;
}

.textActivate {
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.45);
}

.ytextActivate {
    color: rgba(0, 0, 0, 0.85) !important;
}

.activeBorder {
    border-bottom: 2px solid #00b781;
}

.stepsBox {
    justify-content: center;
    align-items: center;
    flex: 1;
    display: flex;

    .stepsBox_item {
        display: inline-flex;
        align-items: center;
        margin-right: 60px;
        // cursor: pointer;
    }
}

.planBtn {
    margin-left: 12px;
}

.planBtns {
    margin-left: -300px;
}
</style>
