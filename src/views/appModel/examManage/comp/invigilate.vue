<!-- eslint-disable -->
<template>
    <div class="invigilate">
        <div class="invigilate_title">
            当前编排考试: <span>{{ formState.name }}</span>
        </div>
        <div class="personnel">考试人员：</div>
        <div class="personnel_card">
            <div
                :class="{
                    card_item: true,
                    activity_card_item: state.cardIndex === index,
                }"
                v-for="(item, index) in state.groups"
                @click="getExamCrewDetail(item, index)"
            >
                <div class="name_box">
                    <div>{{ item.gradeName }}</div>
                    <div class="interval">|</div>
                    <div>{{ item.classesName }}</div>
                </div>
            </div>
        </div>
        <div class="roomNum">
            考场数：<span>{{ state.siteCount }}</span>
        </div>
        <div>
            <a-form layout="vertical" :model="personnel">
                <div class="formBox">
                    <a-form-item
                        style="padding-right: 8px"
                        label="考场监考人数："
                        :rules="[
                            {
                                message: '请输入考场监考人数',
                                required: true,
                            },
                        ]"
                    >
                        <a-input-number
                            style="width: 270px"
                            placeholder="请输入"
                            :min="1"
                            :max="3"
                            :controls="false"
                            :precision="0"
                            v-model:value="personnel.personNum"
                        />
                        <div class="tipNum">
                            最大填写数量为3，最小填写数量为1
                        </div>
                    </a-form-item>
                    <a-form-item
                        style="padding-right: 8px"
                        label="监考员："
                        :rules="[
                            {
                                message: '请输入考场监考人数',
                                required: true,
                            },
                        ]"
                    >
                        <a-input
                            style="width: 270px"
                            @click="showSelectOrigin('personIds')"
                            readonly="readonly"
                            v-model:value="personnel.person"
                            placeholder="请选择"
                        />
                        <div class="tipNum">
                            已选择
                            {{
                                personnel.person
                                    ? personnel.person?.split("、").length
                                    : 0
                            }}
                            人
                        </div>
                    </a-form-item>
                    <a-form-item label="主监考员：" style="padding-right: 8px">
                        <a-input
                            @click="showSelectOrigin('mainPersonIds')"
                            style="width: 270px"
                            v-model:value="personnel.mainPerson"
                            placeholder="请选择"
                        />
                    </a-form-item>
                    <a-form-item label="巡考员：">
                        <a-input
                            @click="showSelectOrigin('subPersonIds')"
                            style="width: 270px"
                            v-model:value="personnel.subPerson"
                            placeholder="请选择"
                        />
                    </a-form-item>
                </div>
            </a-form>
        </div>

        <div class="makeBtn">
            <a-button
                type="primary"
                :disabled="isDisabled"
                @click="setupPersonnel"
                >编排</a-button
            >
        </div>
        <div class="detailsTitle">安排详情</div>
        <div>
            <div>
                <a-form
                    class="enquiry-form"
                    layout="vertical"
                    ref="enquiryFormRef"
                    :model="state.subStates"
                >
                    <div class="formBox">
                        <a-form-item
                            label="考试日期："
                            style="padding-right: 8px"
                        >
                            <a-range-picker
                                v-model:value="state.subStates.times"
                                separator="至"
                                format="YYYY-MM-DD"
                                valueFormat="YYYY-MM-DD"
                            />
                        </a-form-item>
                        <a-form-item label="科目：" style="padding-right: 8px">
                            <a-select
                                style="width: 270px"
                                v-model:value="state.subStates.subjectId"
                                placeholder="请选择"
                                :options="state.subjectOptions"
                                :showArrow="true"
                                :field-names="{
                                    label: 'name',
                                    value: 'id',
                                }"
                            >
                            </a-select>
                        </a-form-item>
                        <a-form-item
                            label="监考员："
                            style="padding-right: 8px"
                        >
                            <a-input
                                style="width: 270px"
                                v-model:value="state.subStates.personName"
                                placeholder="请输入"
                            />
                        </a-form-item>
                        <a-form-item label=" ">
                            <a-button
                                type="primary"
                                @click="inquireSite"
                                style="margin-right: 8px"
                            >
                                <template #icon>
                                    <SearchOutlined style="margin-right: 4px" />
                                </template>
                                查询
                            </a-button>
                            <a-button @click="resetSite">
                                <template #icon>
                                    <redo-outlined style="margin-right: 4px" />
                                </template>
                                重置
                            </a-button>
                        </a-form-item>
                    </div>
                </a-form>
            </div>
            <div>
                <Tables
                    :spinning="state.spinning"
                    :paginations="state.paginations"
                    :columns="columns"
                    :dataSource="state.tableSource"
                    :bordered="false"
                    @change="handleTableChange"
                >
                    <template #bodyCell="{ column, record, index, text }">
                        <template v-if="column.dataIndex === 'startTime'">
                            {{ record.startTime }} - {{ record.endTime }}
                        </template>
                    </template>
                </Tables>
            </div>
        </div>
    </div>

    <!-- 选人组件 -->
    <ModelSelects :tabs="state.tabs" :selected="state.checkedList" />
</template>

<script setup name="Invigilate">
/* eslint-disable */
import Tables from "@/components/Tables";
import { reactive, onMounted, ref, createVNode } from "vue";
import ModelSelects from "@/components/ModelSelects/index.vue";
import { useExamData } from "../useExamData.js";
import { debounce, ls } from "@/utils/util";
import { message, Modal } from "ant-design-vue";
import personnelTint from "./personnelTint.vue";
const { formState, resetState, examinationId } = useExamData();
import { useStore } from "@/store/index";
const store = useStore();
import { useRoute, useRouter } from "vue-router";

import {
    getExamPersonCountInfo,
    pageExamGroupAdmin,
    updateExamGroupAdmin,
    getDistributeExamSiteDetail,
    getSubList,
} from "@/api/exam.js";
import {
    getDepartmentTree,
    getDepartmentPersonnel,
} from "@/api/faceLibrary.js";
const route = useRoute();
const router = useRouter();

// 计算属性判断是否为查看页面 禁用所有表单选项
const isDisabled = computed(() => route.query.type === "look");

const columns = ref([
    {
        title: "序号",
        dataIndex: "index",
        key: "index",
        customRender: ({ text, record, index }) => `${index + 1}`, // 显示每一行的序号
    },
    {
        title: "考试日期",
        dataIndex: "examDate",
        key: "examDate",
    },
    {
        title: "时间段",
        dataIndex: "startTime",
        key: "startTime",
    },
    {
        title: "考场名称",
        dataIndex: "siteName",
        key: "siteName",
    },
    {
        title: "考试年级",
        dataIndex: "objectName",
        key: "objectName",
    },
    {
        title: "科目",
        dataIndex: "subjectName",
        key: "subjectName",
    },
]);

const oldcolumns = [
    {
        title: "序号",
        dataIndex: "index",
        key: "index",
        customRender: ({ text, record, index }) => `${index + 1}`, // 显示每一行的序号
    },
    {
        title: "考试日期",
        dataIndex: "examDate",
        key: "examDate",
    },
    {
        title: "时间段",
        dataIndex: "startTime",
        key: "startTime",
    },
    {
        title: "考场名称",
        dataIndex: "siteName",
        key: "siteName",
    },
    {
        title: "考试年级",
        dataIndex: "objectName",
        key: "objectName",
    },
    {
        title: "科目",
        dataIndex: "subjectName",
        key: "subjectName",
    },
];

const state = reactive({
    subStates: {
        times: [],
    },
    activityName: "invigilate",
    paginations: {
        pageNo: 1, // 页码
        pageSize: 10, // 条数
        total: 0,
    },
    cardIndex: 0,
    groups: [],
    tabs: [
        {
            tab: "教职工",
            id: 1,
            legacy_personKey: "userId",
            single: false, // 通通可以多选
            checked: true, // 直接默认
        },
    ],
});

// 页面加载的时候把科目信息也直接获取吧
const reqSubList = () => {
    getSubList().then((res) => {
        state.subjectOptions = res.data || [];
    });
};

// 查组的详情数据
const reqDistributeExamSiteDetail = (examId, groupId) => {
    getDistributeExamSiteDetail({
        examId: examId,
        groupId: groupId,
    }).then((res) => {
        const { data } = res;
        state.siteCount = data.siteCount; // 考场数量

        personnel.personNum = data.groupAdmin.personNum; // 考场数量
        state.person = data.groupAdmin.persons.map((item) => {
            return {
                id: item.personId,
                name: item.personName,
            };
        });
        personnel.person = data.groupAdmin.persons
            .map((item) => item.personName)
            .join("、"); // 监考员

        state.mainPerson = data.groupAdmin.mainPersons.map((item) => {
            return {
                id: item.personId,
                name: item.personName,
            };
        });
        personnel.mainPerson = data.groupAdmin.mainPersons
            .map((item) => item.personName)
            .join("、"); // 主监考员

        state.subPerson = data.groupAdmin.subPersons.map((item) => {
            return {
                id: item.personId,
                name: item.personName,
            };
        });
        personnel.subPerson = data.groupAdmin.subPersons
            .map((item) => item.personName)
            .join("、"); // 主监考员

        // state.arrangeType = data.arrangeType;
        // state.siteList = data.siteList; // 考试人数
        // state.totalCount = data.totalCount; // 考试人数
        // state.settingCount = data.settingCount; // 已设置
    });
};

const personnel = reactive({});

function addPersonNamesProperties(arr) {
    for (let i = 0; i < arr.length; i++) {
        const personNames = arr[i].personNames;
        if (personNames.length > 0) {
            for (let j = 0; j < personNames.length; j++) {
                arr[i][`personNames${j + 1}`] = personNames[j];
            }
        }
    }
    return arr;
}

const reqAlllist = () => {
    pageExamGroupAdmin({
        pageNo: state.paginations.pageNo,
        pageSize: state.paginations.pageSize,
        examId: examinationId.value,
        groupId: state.targetGroupId,
        ...state.subStates,
        startDate: state.subStates.times?.length
            ? state.subStates.times[0]
            : "",
        endDate: state.subStates.times?.length ? state.subStates.times[1] : "",
    }).then((res) => {
        const { data } = res;
        if (data.list.length) {
            // 到这一部分需要优先处理出 监考员的动态长度
            const personNamecolumns = data.list[0]?.personNames.map(
                (item, index) => {
                    return {
                        title: `监考员${index + 1}`,
                        dataIndex: `personNames${index + 1}`,
                        key: `personNames${index + 1}`,
                    };
                }
            );

            // state.tableSource = addPersonNamesProperties(data.list)
            columns.value = [...oldcolumns, ...personNamecolumns];
            // state.tableSource =data.list;
            state.paginations.total = data.total;
            state.tableSource = addPersonNamesProperties(data.list);
        } else {
            state.tableSource = [];
            state.paginations.total = 0;
        }
    });
    reqDistributeExamSiteDetail(examinationId.value, state.targetGroupId);
};

const getExamCrewDetail = (item, index) => {
    state.cardIndex = index;
    state.targetGroupId = item.id;
    resetSite();
};

// 获取考场组的详情数据
const reqExamPersonCountInfo = () => {
    getExamPersonCountInfo({
        id: examinationId.value,
    }).then((res) => {
        const { data } = res;
        state.totalPersonCount = data.totalPersonCount;
        state.groups = data.groups;
        const firstgroup = data.groups[0];
        getExamCrewDetail(firstgroup, 0);
    });
};

const querys = computed(() => route.query);
const codes = querys.value.codes || ls.get("codes");

// 获取部门 学籍
const getSchoolAggregate = async () => {
    // 部门
    await getDepartmentTree("traffic").then(({ data }) => {
        stateDataSource.value = data;
    });
};

// ----------------- start 选人框逻辑 ---------------
// 教职工 数据
let stateDataSource = shallowRef([]);

const modelState = reactive({
    spinning: false,
    isaAuthority: false, // 用于是否有权限控制选框是否可选
    openVisible: false, // 显示弹框
    oldDataSource: [], // 左侧数据源 用于返回赋值数据
    dataSource: [], // 左侧数据源
    personnelsParams: {}, // 调选人接口要传的参数
    personnels: [], // 人员数据源
    selectedData: [], // 已选中的数据
    checkVisible: "people", // dept 是否选部门, people 是否人员
    searchTable: [], // 选人搜索 table 中显示
    globalID: "", // 最顶成id
    currentDepartID: "", // 当前部门id
    sourchName: "", // sourchName 搜索名称
    isPpresent: false, // 如有人员数据 则为true(用于滚动加载人员数据)
    pages: {
        //分页器
        pageNo: 1,
        pageSize: 999,
        total: 0,
        size: "small",
    },
});
// 设置选人数据源
function setDataSource(data) {
    modelState.dataSource = data || [];
}

// 判断身份有权限选部门
function isaAuthorityFn(data) {
    let authorityNum = 0;
    if (data.length) {
        data.forEach((v) => {
            if (v.isaAuthority) authorityNum++;
        });
        modelState.isaAuthority = authorityNum == !data.length;
    }
}

const { roll_status_yes_id = [], emp_status_yes_id = [] } =
    store.state.selectSource.dictionary;

// 获取人员接口
function getUserPageInfo(callback) {
    const { currentDepartID, pages, sourchName } = modelState;
    let params = {
        name: sourchName,
        deptId: currentDepartID,
        statusList: emp_status_yes_id,
        code: codes || "",
        ...pages,
    };
    let Api = getDepartmentPersonnel;
    Api(params)
        .then(({ data }) => {
            const { list, total, pageNo, pageSize } = data;
            modelState.pages.pageNo = pageNo;
            modelState.pages.pageSize = pageSize;
            modelState.pages.total = total;
            modelState.isPpresent = pageNo * pageSize < total;
            callback(list);
        })
        .finally(() => {
            modelState.spinning = false;
        });
}

// 滚动加载数据
const onScroll = debounce(() => {
    const callback = (data) => {
        modelState.dataSource = modelState.dataSource?.concat(data);
    };
    getUserPageInfo(callback);
}, 1500);

// 查找教职工
function searchSelect(tabId, name) {
    if (name) {
        // 选人组件 - 首次聚焦教职工
        searchStaff(name);
    } else {
        // name为空时，不发送请求，恢复最原始数据
        setDataSource(stateDataSource.value);
    }
}

// 搜索人员
function searchStaff(name) {
    modelState.spinning = true;
    modelState.sourchName = name;
    modelState.currentDepartID = modelState.globalID;
    const callback = (data) => {
        modelState.searchTable = data;
    };
    getUserPageInfo(callback);
}

// 点击下一步查找教职工数据
function getStaffPage(item) {
    const { id, children, type } = item;
    modelState.spinning = true;
    modelState.currentDepartID = item.id;
    const callback = (data) => {
        let childrens = children || [];
        modelState.dataSource = childrens?.concat(data);
    };

    getUserPageInfo(callback);

    // 特需处理
    // 如果是学籍查找学生 只有在班级中查学生
    // if ((state.toggleTabsType == 2, type == 4)) {
    //     getUserPageInfo(callback);
    // } else {
    //     modelState.dataSource = children;
    //     modelState.spinning = false;
    // }
    // // 查老师
    // if (state.toggleTabsType == 1) {
    //     getUserPageInfo(callback);
    // }
}

// 请求下一级数据
function toggleLevel(tabId, item = {}, options) {
    const { index, trigger } = options;
    const firstLevel = !index;
    // 重置分页及搜索
    modelState.isPpresent = false;
    modelState.sourchName = "";
    modelState.pages.pageNo = 1;
    modelState.pages.pageSize = 999;
    modelState.pages.total = 0;
    if (firstLevel) {
        let newDataSource = stateDataSource.value;
        isaAuthorityFn(newDataSource);
        // 第一层数据，恢复原本数据
        modelState.dataSource = newDataSource;
    } else {
        modelState.isaAuthority = false;

        getStaffPage(item);
    }
}

// 取消
function cancels() {
    // state.breadCrumb = []
}

// 提交
function submit(checked) {
    switch (state.personnelType) {
        case "personIds":
            // 确定第一步 掏上点名字做回显用
            personnel.person = checked.map((item) => item.name).join("、"); // 监考员
            // 掏上点id给接口发请求用吧
            personnel.personIds = checked.map((item) => item.id);
            // 也给他留一个对象回显呗 还能咋
            state.person = checked;
            break;
        case "mainPersonIds":
            personnel.mainPerson = checked.map((item) => item.name).join("、"); // 主监考员
            personnel.mainPersonIds = checked.map((item) => item.id);
            state.mainPerson = checked;
            break;
        case "subPersonIds":
            personnel.subPerson = checked.map((item) => item.name).join("、"); // 巡考员
            personnel.subPersonIds = checked.map((item) => item.id);
            state.subPerson = checked;
            break;
        default:
            break;
    }

    // const userName = [];
    // checked.forEach((v) => {
    //     userName.push(v.name);
    // });
    // state.rulesForm.personnelName = userName.join("、");
    // state.rulesForm.studentList = checked
    //     .filter((item) => {
    //         // _source1为教师 2为学生  peopleType2为教师 1为学生
    //         return (
    //             item._type === 2 ||
    //             item._source === 2 ||
    //             item.peopleType === "1"
    //         );
    //     })
    //     .map((item) => {
    //         return {
    //             peopleId: item.id,
    //         };
    //     });
    // state.rulesForm.teacherList = checked
    //     .filter((item) => {
    //         // _source1为教师 2为学生  peopleType2为教师 1为学生
    //         return (
    //             item._type === 1 ||
    //             item._source === 1 ||
    //             item.peopleType === "2"
    //         );
    //     })
    //     .map((item) => {
    //         return { userId: item.userId, peopleId: item.id };
    //     });
    // state.checkedList = checked;
}

// 点击开启选人框
function showSelectOrigin(type) {
    switch (type) {
        case "personIds":
            state.checkedList = state.person;
            break;
        case "mainPersonIds":
            state.checkedList = state.mainPerson;
            break;
        case "subPersonIds":
            state.checkedList = state.subPerson;
            break;
        default:
            break;
    }
    state.personnelType = type;
    modelState.dataSource = stateDataSource.value;
    modelState.globalID = stateDataSource.value[0].id;
    modelState.openVisible = true;
}
provide("modelState", () => modelState);

// TODO：一些功能函数
provide("callbackFunction", () => ({
    search: searchSelect,
    onScroll,
    toggleLevel,
    submit,
    // toggleTabs,
    cancel: cancels,
}));
// ----------------- end 选人框逻辑 ---------------

/* 分页事件 */
const handleTableChange = (data) => {
    state.paginations = data;
    reqAlllist();
};

// 把人发下去啊
const setupPersonnel = () => {
    Modal.confirm({
        title: "提示",
        type: "warning",
        content: createVNode(personnelTint, {
            siteCount: state.siteCount,
            personNum: personnel.personNum,
            person: personnel.person,
        }),
        okText: "确认",
        cancelText: "取消",
        onOk() {
            updateExamGroupAdmin({
                ...personnel,
                examId: examinationId.value,
                groupId: state.targetGroupId,
            }).then((res) => {
                message.success("监考员编排成功");
                reqAlllist();
            });
        },
    });
};

const inquireSite = () => {
    state.paginations.pageNo = 1;
    reqAlllist();
};

const resetSite = () => {
    state.paginations.pageNo = 1;
    state.paginations.pageSize = 10;
    state.subStates = {
        times: [],
    };
    reqAlllist();
};
// 暴露方法和属性给父组件
defineExpose({ state });

onMounted(() => {
    reqSubList();
    getSchoolAggregate();
    reqExamPersonCountInfo();
});
</script>

<style lang="less" scoped>
.invigilate {
    .invigilate_title {
        padding-bottom: 16px;
        font-size: 16px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
    }
}

.personnel {
    font-size: 14px;
    font-weight: 400;
    padding-bottom: 8px;
    color: rgba(0, 0, 0, 0.65);
    &&::before {
        display: inline-block;
        margin-right: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: "*";
    }
}
.personnel_card {
    padding-bottom: 16px;
}

.roomNum {
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    padding-bottom: 16px;
}

.card_item {
    margin-right: 12px;
    width: max-content;
    cursor: pointer;
    display: inline-block;
    // display: flex;
    background: #f6f6f6;
    padding: 6px 12px;
    border-radius: 4px;
}

.activity_card_item {
    color: rgba(255, 255, 255, 0.85);
    background: #00b781;
}

.name_box {
    display: flex;
    .interval {
        padding: 0 6px;
    }
}
.formBox {
    display: flex;
}
.detailsTitle {
    font-size: 16px;
    padding-bottom: 16px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
}

.tipNum {
    padding-top: 2px;
    font-size: 12px;
    font-weight: 400;
    color: #595959;
}
.makeBtn {
    padding-bottom: 16px;
}
</style>
