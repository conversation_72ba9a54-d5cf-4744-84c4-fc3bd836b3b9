<!-- eslint-disable -->
<template>
    <div>
        <div>
            <div class="searchForm">
                <div class="searchHead">
                    <a-form layout="inline">
                        <a-form-item label="考场：">
                            <a-select
                                style="width: 360px"
                                @change="changeSite"
                                v-model:value="state.siteId"
                                placeholder="请选择"
                                :options="state.siteOptions"
                                :showArrow="true"
                                :field-names="{
                                    label: 'name',
                                    value: 'id',
                                }"
                            >
                            </a-select>
                        </a-form-item>
                        <a-form-item>
                            <a-button
                                type="primary"
                                style="margin-right: 10px"
                                @click="query"
                            >
                                <template #icon>
                                    <SearchOutlined
                                        style="margin-right: 10px"
                                    />
                                </template>
                                查询
                            </a-button>
                            <a-button @click="reset">
                                <template #icon>
                                    <redo-outlined style="margin-right: 10px" />
                                </template>
                                重置
                            </a-button>
                        </a-form-item>
                    </a-form>
                    <div>
                        <a-button @click="exSiteTable">导出</a-button>
                    </div>
                </div>
            </div>
        </div>
        <div>
            <div class="exchange">
                <a-button :disabled="isDisabled" @click="exchange"
                    >调换考试座位</a-button
                >
            </div>
            <div>
                <div class="paste" v-if="state.personVOList.length > 0">
                    <div>
                        <a-row
                            :gutter="[24, 32]"
                            type="flex"
                            justify="flex-start"
                            :wrap="true"
                        >
                            <a-col
                                v-for="item in state.personVOList"
                                :key="item.id"
                            >
                                <div class="siteBox">
                                    <div class="seat">
                                        <div>{{ item.seat || "-" }}</div>
                                    </div>
                                    <div class="info">
                                        <div class="infoBox">
                                            <div class="nameBox">
                                                姓名：<span>{{
                                                    item.personName || "-"
                                                }}</span>
                                            </div>
                                            <div class="examineNumberBox">
                                                准考证号：<span>{{
                                                    item.examineNumber || "-"
                                                }}</span>
                                            </div>
                                            <div class="classesNameBox">
                                                班级：<span>{{
                                                    item.classesName || "-"
                                                }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a-col>
                        </a-row>
                    </div>
                </div>
                <a-empty
                    v-else
                    image="image/empty.png"
                    :image-style="{ width: '100%', height: '180px' }"
                >
                    <template #description>
                        <span>暂无数据</span>
                    </template>
                </a-empty>
            </div>
        </div>
    </div>
    <!-- 换换座位 Modal  -->
    <a-modal
        :confirm-loading="state.seatLoading"
        :keyboard="false"
        :maskClosable="false"
        centered
        title="调换考试座位"
        v-model:visible="state.seatFormVisible"
        @ok="seatHandleOk"
        @cancel="seatHandleCancel"
        ok-text="确认"
        cancel-text="取消"
    >
        <a-form
            ref="seatFormRef"
            layout="vertical"
            :model="seatForm"
            autocomplete="off"
            name="seat"
        >
            <a-form-item
                label="选择调换人："
                name="id"
                :rules="[{ required: true, message: '请选择调换人' }]"
            >
                <a-select
                    v-model:value="seatForm.id"
                    placeholder="请选择"
                    :options="state.seatOptions"
                    :showArrow="true"
                    :field-names="{
                        label: 'name',
                        value: 'id',
                    }"
                >
                </a-select>
            </a-form-item>
            <a-form-item
                label="选择调换座位："
                name="newId"
                :rules="[{ required: true, message: '请选择调换座位' }]"
            >
                <a-select
                    v-model:value="seatForm.newId"
                    placeholder="请选择"
                    :options="state.seatOptions"
                    :showArrow="true"
                    :field-names="{
                        label: 'name',
                        value: 'id',
                    }"
                >
                </a-select>
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<script setup name="name">
/* eslint-disable */
import { reactive, onMounted, ref } from "vue";
import {
    getExamSiteList,
    examSeatStickers,
    exportExamSeatStickers,
    switchExamSeat,
} from "@/api/exam.js";
import { useExamData } from "../useExamData.js";
const { examinationId } = useExamData();
import { useRoute } from "vue-router";
import { message } from "ant-design-vue";
const route = useRoute();
const state = reactive({
    activityName: "examTable",
    siteOptions: [],
    seatOptions: [],
    seatFormVisible: false,
    personVOList: [],
});

// 表单实例
const seatFormRef = ref(null);
// 计算属性判断是否为查看页面 禁用所有表单选项
const isDisabled = computed(() => route.query.type === "look");

const seatForm = reactive({});
// 获取考场
const reqExamSiteList = async () => {
    const { data } = await getExamSiteList({
        examinationId: examinationId.value,
    });
    state.siteOptions = data;
    state.siteId = data[0]?.id;
    reqExamSeatStickers();
};

// 请求考场座位帖
const reqExamSeatStickers = async () => {
    const seatObj = {
        examinationId: examinationId.value,
        siteId: state.siteId,
    };
    const { data } = await examSeatStickers(seatObj);
    state.personVOList = data.personVOList;
    // 是否展示准考证号
    state.showed = data.showed;

    state.seatOptions = data.personVOList.map((item) => {
        return {
            id: item.id,
            name: `${item.seat} - ${item.personName}`,
        };
    });
    // state.buildingName = data.buildingName
    // state.siteName = data.siteName
    // state.examPeopleNum = data.examPeopleNum
};

const query = () => {
    reqExamSeatStickers();
};

// 更改考场 请求座位贴
// const changeSite = () => {
//     if (!state.siteId) {
//         state.personVOList = [];
//     } else {
//         reqExamSeatStickers();
//     }
// };

// 换座位
const exchange = () => {
    state.seatFormVisible = true;
};

// 把座位换了
const seatHandleOk = () => {
    seatFormRef.value.validate().then(() => {
        switchExamSeat({
            ...seatForm,
        }).then((res) => {
            seatHandleCancel()
            message.success("座位调换成功");
            reqExamSiteList();
        });
    });
};

const seatHandleCancel = () => {
    seatFormRef.value.resetFields()
    state.seatFormVisible = false
}

//  导出
const exSiteTable = async () => {
    const siteObj = {
        examinationId: examinationId.value,
        siteId: state.siteId,
    };
    await exportExamSeatStickers(siteObj).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            })
        );
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "座位贴.xlsx");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
};

// 暴露方法和属性给父组件
defineExpose({ state });

onMounted(() => {
    reqExamSiteList();
});
</script>

<style lang="less" scoped>
.paste {
    padding-top: 16px;
}

.siteBox {
    cursor: pointer;
    border: 1px dashed #595959;
    width: 304px;
    display: flex;
    border-radius: 10px;
    .seat {
        border-radius: 10px 0px 0px 10px;
        width: 76px;
        // height: 80px;
        font-size: 30px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.65);
        background: #f6f6f6;
        border-right: 1px dashed #595959;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .info {
        flex: 1;
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
        padding: 10px 20px 10px 20px;
        display: table;
        .infoBox {
            height: 100%;
            display: table-cell;
            vertical-align: middle;
            // display: flex;
            // justify-content: space-between;
            // flex-direction: column;
            // .bor {
            //     border: 1px solid #d9d9d9;
            // }
        }
    }
}

:deep(.ant-col-5) {
    flex: 0 0 20%;
    max-width: 20%;
}

.nameBox {
    padding-bottom: 8px;
    border-bottom: 1px solid #d9d9d9;
}

.examineNumberBox {
    padding-bottom: 8px;
    padding-top: 8px;
    border-bottom: 1px solid #d9d9d9;
}

.classesNameBox {
    padding-top: 8px;
}

.searchHead {
    display: flex;
    justify-content: space-between;
}

.exchange {
    background: #f6f6f6;
    padding: 8px 0px;
    margin-top: 16px;
    text-align: end;
}
</style>
