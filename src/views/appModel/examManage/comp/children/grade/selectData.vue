<!--
 * @Descripttion:分年级考试
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-11-26 10:47:29
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-11-26 19:44:34
-->
<template>
    <div class="select_container">
        <a-select class="select" :dropdownStyle="dropdownStyle" v-model:value="form.groupIds" :placeholder="placeholder" :options="state.groupListOptions" @change="changeGroupList" :max-tag-count="4" mode="multiple" :field-names="fieldNames">
        </a-select>
        <div class="select_open">
            <a-button type="primary" size="small" class="select_open__btn" @click="openSelect">
                <template #icon>
                    <plus-outlined />
                </template>
            </a-button>
            <y-select :multiple="true" :treeData="state.groupTree" :tabs="tabs" mode="depa" v-model:visible="state.visible" :selectedKeys="[]" v-model:checked="state.gradeCheckedList" @handleOk="selectClassOk"></y-select>
        </div>
    </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import Yselect from '@/components/YSelect'
import {
    getGradeTree
} from '@/api/exam.js'

defineProps({
    value: {
        type: Array,
        default: () => []
    },
    placeholder: {
        type: String,
        default: '请选择'
    },
    tabs: {
        type: Array,
        default: () => [{ tab: '选择年级', key: 2, checked: true, disabled: [1, 2] }]
    }
})

const emit = defineEmits(['update:value'])

const dropdownStyle = {
    display: 'none'
}
const fieldNames = {
    label: 'showName',
    value: 'id'
}
const form = reactive({
    groupIds: [],
    // 年级集合
    groupList: []
})

const state = reactive({
    groupListOptions: [],
    visible: false,
    // 年级tree
    groupTree: [],
    gradeCheckedList: []
})

function selectClassOk(data) {
    state.groupListOptions = [...data]
    form.groupIds = data.map((item) => item.id)
    form.groupList = data.map((item) => {
        return {
            objectId: item.id,
            objectName: item.showName
        }
    })

    state.gradeCheckedList = [...data]

}

function changeGroupList(item) {
    state.gradeCheckedList = state.groupListOptions.filter((i) =>
        item.includes(i.id)
    )


    emit('change', state.gradeCheckedList)
    emit('update:value', form.groupList)
}

function openSelect() {
    state.visible = true
}

async function init() {
    const { data } = await getGradeTree()
    state.groupTree = data
}

onMounted(() => {
    init()
})

</script>

<style lang="less" scoped>
    .select_container {
        position: relative;
        .select {
            width: 100%;
        }
        .select_open {
            position: absolute;
            right: 0;
            top: 10px;
            &__btn {
                position: relative;
                bottom: 6px;
                right: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }
</style>
