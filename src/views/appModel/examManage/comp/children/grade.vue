<template>
    <!-- 分年级考试时间选项的页面 -->
    <!-- 年级选择框 -->
    <div class="gradeBox">
        <a-form ref="gradeRef" layout="vertical" :model="state.gradeName">
            <a-form-item
                label="考试年级"
                name="gradeName"
                :rules="[
                    {
                        required: state.gradeName,
                        message: '请选择考试年级!',
                        required: true
                    }
                ]"
            >
                <a-input
                    :disabled="isDisabled"
                    style="width: 556px"
                    v-model:value="state.gradeName"
                    @click="addgradeList"
                    allowClear
                    placeholder="请选择"
                    readonly="readonly"
                >
                    <template #suffix>
                        <down-outlined style="color: rgba(0, 0, 0, 0.45)" />
                    </template>
                </a-input>
            </a-form-item>
        </a-form>
    </div>

    <!-- 循环遍历生成每年级考试场地信息 -->
    <TransitionGroup name="list" tag="ul">
        <div
            class="examPlaceInfo"
            v-for="(item, i) in state.groupConfigList"
            :key="item.objectId"
        >
            <div
                :class="{
                    gradeHead: true,
                    lookGradeHead: isDisabled
                }"
            >
                <div class="gradeTitle">
                    {{ item.objectName }}
                </div>
                <div v-if="!isDisabled">
                    <a-button type="text" @click="delgradeBox(i, item)">
                        <template #icon>
                            <delete-outlined style="color: rgb(147 147 147)" />
                        </template>
                    </a-button>
                </div>
            </div>
            <!-- 边框内容 -->
            <div class="arrange">
                <div class="btnClass titleText">
                    考场科目：
                    <a-button
                        v-if="!isDisabled"
                        type="primary"
                        @click="selectCourse(i)"
                        >选择课程</a-button
                    >
                </div>
                <div
                    class="subDelBtn"
                    v-if="!!item.subjectList.length && !isDisabled"
                >
                    <a-button
                        danger
                        :disabled="hasSelected(item.Keys)"
                        @click="delSub(i)"
                        >删除</a-button
                    >
                </div>
                <!-- 如果没有选择课程 科目teble 不显示 -->
                <div v-if="!!item.subjectList.length">
                    <Tables
                        :selectedRowKeys="item.Keys"
                        :columns="subColumns"
                        :dataSource="item.subjectList"
                        :bordered="false"
                        :rowSelection="!isDisabled"
                        :isRadio="false"
                        @selectChange="
                            (ids, data) => subSelectChange(ids, data, i)
                        "
                        :rowKeyId="(record) => record.basicsSubjectId"
                    >
                        <template #bodyCell="{ column, text, record, index }">
                            <template v-if="column.dataIndex === 'timeList'">
                                <div v-if="!isDisabled">
                                    <a-range-picker
                                        v-model:value="record.timeList"
                                        :disabled-date="
                                            (val) => disabledDate(val, index)
                                        "
                                        @calendarChange="
                                            (val) =>
                                                onCalendarChange(val, i, index)
                                        "
                                        :show-time="{
                                            hideDisabledOptions: true,
                                            defaultValue: [
                                                dayjs('00:00', 'HH:mm'),
                                                dayjs('23:59', 'HH:mm')
                                            ]
                                        }"
                                        format="YYYY-MM-DD HH:mm"
                                        valueFormat="YYYY-MM-DD HH:mm"
                                        :placeholder="['开始时间', '结束时间']"
                                    />
                                </div>
                                <div v-else>
                                    {{
                                        `${record.timeList[0]} - ${record.timeList[1]}`
                                    }}
                                </div>
                            </template>
                            <template v-if="column.dataIndex === 'duration'">
                                {{
                                    record.timeList?.length
                                        ? `${isDuration(record)}分钟`
                                        : '-'
                                }}
                            </template>
                            <!-- 主考员输入选人框 -->
                            <template
                                v-if="column.dataIndex === 'chiefListArr'"
                            >
                                <div v-if="isDisabled">
                                    {{
                                        record.chiefList
                                            .map((item) => item.personName)
                                            .join(',')
                                    }}
                                </div>
                                <div v-else>
                                    <a-select
                                        v-model:value="record.chiefListArr"
                                        style="width: 300px"
                                        :open="false"
                                        placeholder="请选择"
                                        :options="record.chiefList"
                                        showArrow
                                        @change="
                                            (val) =>
                                                changechiefListArr(val, record)
                                        "
                                        :max-tag-count="2"
                                        mode="multiple"
                                        :field-names="{
                                            label: 'personName',
                                            value: 'personId'
                                        }"
                                    >
                                        <template #suffixIcon>
                                            <a-button
                                                @click="
                                                    addPerson(
                                                        record,
                                                        index,
                                                        'chiefList',
                                                        i
                                                    )
                                                "
                                                type="primary"
                                                size="small"
                                                style="
                                                    bottom: 6px;
                                                    right: 8px;
                                                    display: flex;
                                                    align-items: center;
                                                    justify-content: center;
                                                "
                                            >
                                                <template #icon>
                                                    <plus-outlined />
                                                </template>
                                            </a-button>
                                        </template>
                                    </a-select>
                                </div>
                            </template>
                            <!-- 巡考员选人框 -->
                            <template
                                v-if="column.dataIndex === 'inspectorListArr'"
                            >
                                <div v-if="isDisabled">
                                    {{
                                        record.inspectorList
                                            .map((item) => item.personName)
                                            .join(',')
                                    }}
                                </div>
                                <div v-else>
                                    <a-select
                                        style="width: 300px"
                                        @change="
                                            (val) =>
                                                changeinspectorListArr(
                                                    val,
                                                    record
                                                )
                                        "
                                        v-model:value="record.inspectorListArr"
                                        placeholder="请选择"
                                        :options="record.inspectorList"
                                        :open="false"
                                        showArrow
                                        :max-tag-count="2"
                                        mode="multiple"
                                        :field-names="{
                                            label: 'personName',
                                            value: 'personId'
                                        }"
                                    >
                                        <template #suffixIcon>
                                            <a-button
                                                @click="
                                                    addPerson(
                                                        record,
                                                        index,
                                                        'inspectorList',
                                                        i
                                                    )
                                                "
                                                type="primary"
                                                size="small"
                                                style="
                                                    bottom: 6px;
                                                    right: 8px;
                                                    display: flex;
                                                    align-items: center;
                                                    justify-content: center;
                                                "
                                            >
                                                <template #icon>
                                                    <plus-outlined />
                                                </template>
                                            </a-button>
                                        </template>
                                    </a-select>
                                </div>
                            </template>
                        </template>
                    </Tables>
                </div>
                <div class="titleText arrangement">考场布置：</div>
                <div class="examRadio">
                    <a-radio-group
                        :disabled="isDisabled"
                        @change="
                            (e) =>
                                reqThisSiteList(e, state.groupConfigList[i], i)
                        "
                        v-model:value="
                            state.groupConfigList[i].examinationArrange
                        "
                        name="gridType"
                    >
                        <a-radio :value="1">随机排位</a-radio>
                        <a-radio :value="2">本班排位</a-radio>
                    </a-radio-group>
                </div>
                <a-button
                    v-if="!isDisabled"
                    type="primary"
                    @click="selectRoom(i)"
                    :disabled="item.examinationArrange === 2"
                    >选择考场</a-button
                >
                <div v-if="state.groupConfigList[i].siteList.length">
                    <div class="classdelBtn" v-if="!isDisabled">
                        <!-- 此处的删除按钮是动态的 要根据每个box框框的选择 场地id拼凑年级id 的数组进行判断 是不是禁用状态的  -->
                        <a-button
                            danger
                            :disabled="
                                !(
                                    state.groupConfigList[i].roomKeys?.length >
                                    0
                                ) || item.examinationArrange === 2
                            "
                            @click="delRoomLine(i)"
                            >删除</a-button
                        >
                    </div>
                    <div>
                        <Tables
                            :selectedRowKeys="state.groupConfigList[i].roomKeys"
                            :columns="
                                !isDisabled ? roomColumns : lookRoomColumns
                            "
                            :dataSource="state.groupConfigList[i].siteList"
                            :bordered="false"
                            :rowSelection="!isDisabled"
                            :isRadio="false"
                            @selectChange="
                                (ids, data) => roomSelectChange(ids, data, i)
                            "
                            :rowKeyId="(record) => record.id"
                        >
                            <template
                                #bodyCell="{ column, text, record, index }"
                            >
                                <template
                                    v-if="column.dataIndex === 'roomName'"
                                >
                                    {{
                                        `${record.construct}${record.placeName}` ||
                                        '-'
                                    }}
                                </template>
                                <template
                                    v-if="column.dataIndex === 'buildingName'"
                                >
                                    {{ record.buildingName || '-' }}
                                </template>
                                <template
                                    v-if="column.dataIndex === 'siteName'"
                                >
                                    {{ record.siteName || '-' }}
                                </template>
                                <template
                                    v-if="column.dataIndex === 'examPeopleNum'"
                                >
                                    {{ record.examPeopleNum || '-' }}
                                </template>
                                <template
                                    v-if="column.dataIndex === 'specification'"
                                >
                                    {{
                                        record.row && record.cell
                                            ? `${record.row} * ${record.cell}`
                                            : '-'
                                    }}
                                </template>
                                <template
                                    v-if="
                                        column.dataIndex ===
                                        'invigilatorTeachName'
                                    "
                                >
                                    <!-- 这段在究极麻烦的处理出字符串来显示 -->
                                    <div style="display: flex">
                                        <a-tooltip
                                            v-if="
                                                record?.invigilatorList?.length
                                            "
                                        >
                                            <template #title>{{
                                                nameJoint(
                                                    record.invigilatorList
                                                )
                                            }}</template>
                                            <div class="teachNameJoin">
                                                {{
                                                    nameJoint(
                                                        record.invigilatorList
                                                    )
                                                }}
                                            </div>
                                        </a-tooltip>
                                        <a
                                            v-if="!isDisabled"
                                            style="color: var(--primary-color)"
                                            @click="
                                                teacherSet(record, i, index)
                                            "
                                            >设置</a
                                        >
                                    </div>
                                </template>
                                <template
                                    v-if="
                                        column.dataIndex === 'operation' &&
                                        !isDisabled &&
                                        state.groupConfigList[i]
                                            .examinationArrange === 1
                                    "
                                >
                                    <a
                                        style="color: var(--primary-color)"
                                        @click="changeRooom(record, i, index)"
                                        >修改</a
                                    >
                                </template>
                            </template>
                        </Tables>
                    </div>
                </div>
            </div>
        </div>
    </TransitionGroup>
    <!-- 选择考试科目对话框 -->
    <div class="courseModal">
        <a-modal
            style="width: 492px"
            v-model:visible="state.courseVisible"
            title="选择课程"
            :maskClosable="false"
            @cancel="courseCancel"
            @ok="courseOk"
        >
            <template #footer>
                <a-button key="back" @click="courseCancel">取消</a-button>
                <a-button key="submit" type="primary" @click="courseOk"
                    >确认</a-button
                >
            </template>
            <a-checkbox-group
                v-model:value="state.courseValue"
                style="width: 100%"
            >
                <a-row :gutter="[16, 24]">
                    <a-col
                        :span="8"
                        v-for="item in courseOptions"
                        :key="item.id"
                    >
                        <a-checkbox :value="item.id">{{
                            item.name
                        }}</a-checkbox>
                    </a-col>
                </a-row>
            </a-checkbox-group>
        </a-modal>
    </div>
    <!-- 选择考场对话框 -->
    <div class="courseModal">
        <a-modal
            class="siteModal"
            v-model:visible="state.examination"
            title="选择考场"
            :maskClosable="false"
            @cancel="examinationCancel"
            @ok="examinationOk"
        >
            <a-form
                ref="roomFormRef"
                autocomplete="off"
                layout="vertical"
                :model="examRoom"
            >
                <a-form-item
                    label="考场容量"
                    name="examPeopleNum"
                    :rules="[
                        {
                            required: examRoom.examPeopleNum,
                            message: '请输入考场容量!',
                            required: true
                        }
                    ]"
                >
                    <a-input
                        @keyup="
                            examRoom.examPeopleNum =
                                examRoom.examPeopleNum.replace(/\D/g, '')
                        "
                        v-model:value.trim="examRoom.examPeopleNum"
                        placeholder="请输入"
                        suffix="人"
                    />
                </a-form-item>
                <a-form-item
                    label="考场规格"
                    validateTrigger="onSubmit"
                    name="size"
                    :rules="[
                        {
                            validator: validaSize,
                            required: true,
                            trigger: ['submit']
                        }
                    ]"
                >
                    <a-input
                        @keyup="examRoom.row = examRoom.row.replace(/\D/g, '')"
                        style="width: 50%"
                        v-model:value.trim="examRoom.row"
                        placeholder="请输入"
                        suffix="行"
                    />
                    <a-form-item-rest>
                        <a-input
                            style="width: 50%"
                            @keyup="
                                examRoom.cell = examRoom.cell.replace(/\D/g, '')
                            "
                            v-model:value.trim="examRoom.cell"
                            placeholder="请输入"
                            suffix="列"
                        />
                    </a-form-item-rest>
                </a-form-item>
                <a-form-item
                    label="考场地点"
                    name="location"
                    validateTrigger="onSubmit"
                    :rules="[
                        {
                            required: examRoom.location,
                            message: '请选择考场地点!',
                            required: true
                        },
                        {
                            validator: validaLocation,
                            required: true,
                            trigger: ['submit']
                        }
                    ]"
                >
                    <a-select
                        ref="locationSelect"
                        v-model:value="examRoom.location"
                        :options="locationOptions"
                        @dropdownVisibleChange="buildingList"
                        @change="locationChange"
                        :fieldNames="{ label: 'name', value: 'id' }"
                    ></a-select>
                </a-form-item>
                <a-form-item name="architecture">
                    <a-form-item-rest>
                        <Tables
                            :selectedRowKeys="state.buildKeys"
                            :columns="buildColumns"
                            :dataSource="state.buildSource"
                            :bordered="false"
                            :rowSelection="true"
                            :isRadio="false"
                            @selectChange="buildSelectChange"
                            :row-key="(record) => record.id"
                        >
                        </Tables>
                    </a-form-item-rest>
                </a-form-item>
            </a-form>
            <template #footer>
                <a-button key="back" @click="examinationCancel">取消</a-button>
                <a-button key="submit" type="primary" @click="examinationOk"
                    >确认</a-button
                >
            </template>
        </a-modal>
    </div>
    <!-- 修改考场对话框 -->
    <div class="courseModal">
        <a-modal
            v-model:visible="state.amendExamination"
            title="修改"
            :maskClosable="false"
            @cancel="revampOff"
            @ok="revampOK"
        >
            <a-form
                class="info-form"
                ref="amendFormRef"
                autocomplete="off"
                layout="vertical"
                :model="amend"
            >
                <a-form-item
                    label="考场名称"
                    name="designation"
                    :rules="[
                        {
                            required: amend.designation,
                            message: '请输入考场名称!',
                            required: true
                        }
                    ]"
                >
                    <a-input
                        :maxlength="10"
                        v-model:value.trim="amend.designation"
                        placeholder="请输入"
                    />
                </a-form-item>
                <a-form-item
                    label="考试地点"
                    name="venues"
                    :rules="[
                        {
                            required: amend.venues,
                            message: '请输入考场地点!',
                            required: true
                        }
                    ]"
                >
                    <a-input
                        disabled
                        v-model:value="amend.venues"
                        placeholder="请输入"
                    />
                </a-form-item>
                <a-form-item
                    label="考试教室"
                    name="classroom"
                    :rules="[
                        {
                            required: amend.classroom,
                            message: '请输入考试教室!',
                            required: true
                        }
                    ]"
                >
                    <a-input
                        disabled
                        v-model:value="amend.classroom"
                        placeholder="请输入"
                    />
                </a-form-item>
                <a-form-item
                    label="场地容量"
                    name="classroom"
                    :rules="[
                        {
                            required: amend.classroom,
                            message: '请输入场地容量!',
                            required: true
                        }
                    ]"
                >
                    <a-input
                        disabled
                        v-model:value="amend.capacity"
                        placeholder="请输入"
                    />
                </a-form-item>
                <a-form-item
                    label="考场容量"
                    name="districtCapacity"
                    :rules="[
                        {
                            required: amend.districtCapacity,
                            message: '请输入考场容量!',
                            required: true
                        }
                    ]"
                >
                    <a-input
                        @keyup="
                            amend.districtCapacity =
                                amend.districtCapacity.replace(/\D/g, '')
                        "
                        v-model:value.trim="amend.districtCapacity"
                        placeholder="请输入"
                    />
                </a-form-item>
                <a-form-item
                    label="考场规格"
                    name="specification"
                    validateTrigger="onSubmit"
                    :rules="[
                        {
                            validator: validaAmendSize,
                            required: true,
                            trigger: 'submit'
                        }
                    ]"
                >
                    <a-input
                        @keyup="amend.line = amend.line.replace(/\D/g, '')"
                        style="width: 50%"
                        v-model:value.trim="amend.line"
                        placeholder="请输入"
                        suffix="行"
                    />
                    <a-form-item-rest>
                        <a-input
                            @keyup="
                                amend.column = amend.column.replace(/\D/g, '')
                            "
                            style="width: 50%"
                            v-model:value.trim="amend.column"
                            placeholder="请输入"
                            suffix="列"
                        />
                    </a-form-item-rest>
                </a-form-item>
            </a-form>
            <template #footer>
                <a-button key="back" @click="revampOff">取消</a-button>
                <a-button key="submit" type="primary" @click="revampOK"
                    >确认</a-button
                >
            </template>
        </a-modal>
    </div>
    <!-- 设置监考老师对话框 -->
    <div class="teacherModal">
        <a-modal
            style="width: 723px"
            :maskClosable="false"
            v-model:visible="state.setTeacherShow"
            title="设置监考老师"
            @ok="teacherOK"
        >
            <Tables
                :columns="teachColumns"
                :dataSource="state.setTeacherList"
                :bordered="false"
                :row-key="(record) => record.id"
            >
                <template #bodyCell="{ column, text, record, index }">
                    <template v-if="column.dataIndex === 'timeList'">
                        {{
                            record.timeList[0] +
                            '-' +
                            record.timeList[1].split(' ')[1]
                        }}
                    </template>
                    <template v-if="column.dataIndex === 'invigilator'">
                        <a-select
                            v-model:value="record.invigilator"
                            style="width: 300px"
                            :open="false"
                            @change="(val) => changeinvigilator(val, record)"
                            placeholder="请选择"
                            :options="record.invigilatorTeacher"
                            showArrow
                            :max-tag-count="4"
                            mode="multiple"
                            :field-names="{
                                label: 'personName',
                                value: 'personId'
                            }"
                        >
                            <template #suffixIcon>
                                <a-button
                                    @click="
                                        addPerson(record, index, 'invigilator')
                                    "
                                    type="primary"
                                    size="small"
                                    style="
                                        bottom: 6px;
                                        right: 8px;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                    "
                                >
                                    <template #icon>
                                        <plus-outlined />
                                    </template>
                                </a-button>
                            </template>
                        </a-select>
                    </template>
                </template>
            </Tables>
            <template #footer>
                <a-button key="back" @click="teacherOff">取消</a-button>
                <a-button key="submit" type="primary" @click="teacherOK"
                    >确认</a-button
                >
            </template>
        </a-modal>
    </div>
    <!-- 选择主考员/巡考员 -->
    <Yselect
        mode="personnel"
        :tabs="state.patrolExaminer"
        v-model:visible="state.managerVisible"
        :selectedKeys="[]"
        v-model:checked="state.teacherCheckedList"
        @handleOk="personOk"
    >
    </Yselect>
    <!-- 年级选择 -->
    <Yselect
        :multiple="true"
        :treeData="gradeTrees.value"
        :tabs="state.selectTabs"
        mode="depa"
        v-model:visible="state.classVisible"
        :selectedKeys="[]"
        v-model:checked="state.gradeCheckedList"
        @handleOk="selectClassOk"
    >
    </Yselect>
</template>
<script setup>
import {
    reactive,
    shallowRef,
    computed,
    toRaw,
    onMounted,
    watch,
    nextTick
} from 'vue'
import { useDebounceFn } from '@vueuse/core'
import { v4 as uuidv4 } from 'uuid'
import { message } from 'ant-design-vue'
import {
    getSubList,
    getThisSiteList,
    getBuildList,
    getExamDetInfo
} from '@/api/exam.js'
import Yselect from '@/components/YSelect'
import Tables from '../../table/index'
import dayjs from 'dayjs'
import { useList } from '../../tableHead.js'
import { useRoute } from 'vue-router'
import { nameJoint } from '../../nameJoint.js'
import { useStore } from '@/store/index'

const store = useStore()
const { subColumns, roomColumns, buildColumns, teachColumns, lookRoomColumns } =
    useList()
const route = useRoute()

const commentJsonKey = computed(() => {
    const disabled = []
    store.state.base.commentJsonKeys.INIT_ROLL_LIST.forEach(
        (v) => v.key !== 'grade' && disabled.push(v.level)
    )
    return disabled
})
// 计算属性获取学籍树
const gradeTrees = computed(() => {
    return store.state.base.schoolRollTree
})
// 计算属性判断是否为查看页面 禁用所有表单选项
const isDisabled = computed(() => route.query.type === 'look')
const dates = shallowRef()
// 选择考场的表单校验
const roomFormRef = shallowRef()
const amendFormRef = shallowRef()
// 考场楼栋选择框信息
const locationOptions = shallowRef([])
// 选择课程内容
const courseOptions = shallowRef([])
const state = reactive({
    patrolExaminer: [{ tab: '教职工', key: 1, checked: true }],
    selectTabs: [
        {
            tab: '选择年级',
            key: 2,
            checked: true,
            disabled: commentJsonKey
        }
    ],
    teacherCheckedList: [],
    gradeCheckedList: [],
    buildArr: [],
    // gradeTree: [],
    buildKeys: [],
    classVisible: false,
    managerVisible: false,
    gridType: '1',
    setTeacherShow: false,
    amendExamination: false,
    examination: false,
    visible: false,
    courseVisible: false,
    subjectList: [],
    roomSource: [],
    buildSource: [],
    groupConfigList: [],
    courseValue: []
})

// 自定义表单校验
const validaSize = (_rule, value) => {
    if (!examRoom.row || !examRoom.cell) {
        return Promise.reject(new Error('请输入考场规格!'))
    }
    return Promise.resolve()
}
const validaAmendSize = (_rule, value) => {
    if (!amend.line || !amend.column) {
        return Promise.reject(new Error('请输入考场规格!'))
    }
    return Promise.resolve()
}
const validaLocation = (_rule, value) => {
    if (!state.buildArr.length) {
        return Promise.reject(new Error('请选择考场建筑!'))
    }
    return Promise.resolve()
}

// 选择考场对话框-收集数据
const examRoom = reactive({})
const amend = reactive({})

// 待选日期发生变化的回调
const onCalendarChange = (val, bigI, i) => {
    dates.value = val
    if (val) {
        if (dates.value[1]) {
            const [d1, d2] = dates.value.map((item) => item.split(' ')[0])
            if (d1 === d2) {
            } else {
                message.warning('考试时间不允许跨天!')
                nextTick(() => {
                    state.groupConfigList[bigI].subjectList[i].timeList = null
                })
            }
        }
    }
}

// 时间禁用
const disabledDate = (current) => {
    const subtractDays = dayjs()
        .subtract(1, 'day')
        .format('YYYY-MM-DD HH:mm:ss')
    return current && current <= dayjs(subtractDays).endOf('day')
}

// 计算属性计算出 开始时间结束时间中的分钟差
const isDuration = computed(() => (item) => {
    item.duration = dayjs(item.timeList[1]).diff(
        dayjs(item.timeList[0]),
        'minute'
    )

    return item.duration
})

// 选择课程
const selectCourse = (indexed) => {
    // 每次点选择课程 table 上的勾选框全部都去掉 Keys为空数组
    state.groupConfigList[indexed].Keys = []
    // 记录大盒子的下标
    state.roomIndex = indexed
    const subitemArr = state.groupConfigList[indexed].subjectList
    if (subitemArr.length) {
        state.courseValue = subitemArr.map((item) => item.basicsSubjectId)
    } else {
        // 如果table上没有数据 科目多选框上也没有数据
        state.courseValue = []
    }
    state.courseVisible = true
}

// 取消选择课程
const courseCancel = () => {
    state.courseVisible = false
}

// 确认选择课程提交
const courseOk = () => {
    // 点击确定按钮 把勾选数据塞入table
    state.courseVisible = false
    state.groupConfigList[state.roomIndex].subjectList = subjectList.value.map(
        (i) => {
            // map新数据的时候需要保存 到原来的值 所以用basicsSubjectId去找旧值 如果有旧值就用旧值 没有就重置为空数组
            const idx = state.groupConfigList[
                state.roomIndex
            ].subjectList.findIndex(
                (j) => j.basicsSubjectId === i.basicsSubjectId
            )

            // 时间
            const timeList =
                state.groupConfigList[state.roomIndex].subjectList[idx]
                    ?.timeList || []
            // 主考员
            const chiefListArr =
                state.groupConfigList[state.roomIndex].subjectList[idx]
                    ?.chiefListArr || []
            const chiefList =
                state.groupConfigList[state.roomIndex].subjectList[idx]
                    ?.chiefList || []
            // 巡考员
            const inspectorListArr =
                state.groupConfigList[state.roomIndex].subjectList[idx]
                    ?.inspectorListArr || []
            const inspectorList =
                state.groupConfigList[state.roomIndex].subjectList[idx]
                    ?.inspectorList || []
            return {
                ...i,
                chiefListArr,
                timeList,
                chiefList,
                inspectorListArr,
                inspectorList
            }
        }
    )
}

// 根据选中的科目数组 计算出table数组
const subjectList = computed(() => {
    return courseOptions.value
        .filter((i) => state.courseValue.includes(i.id))
        .map((i) => {
            return {
                id: i.id,
                basicsSubjectId: i.id,
                subjectName: i.name,
                chiefListArr: [],
                timeList: []
            }
        })
})

// 请求本班排位列表 table数据
const reqThisSiteList = useDebounceFn(
    async(e, item) => {
        if (e.target.value === 2) {
            try {
                const { data } = await getThisSiteList({
                    objectId: item.objectId
                })
                if (data || data.length) {
                    item.siteList = data
                } else {
                    item.examinationArrange = 1
                    item.siteList = []
                }
            } catch (error) {
                item.examinationArrange = 1
                item.siteList = []
            }
        } else {
            item.siteList = []
        }
    },
    1000,
    { maxWait: 3000 }
)
// 选择考场按钮-显示考场信息对话框
const selectRoom = async(param) => {
    // 记录大盒子的下标
    state.roomIndex = param
    state.examination = true
}

// 下拉选择考场
const buildingList = async() => {
    if (examRoom.examPeopleNum) {
        const { data } = await getBuildList({
            examPeopleNum: examRoom.examPeopleNum
        })
        locationOptions.value = data
    } else {
        message.destroy()
        message.warning('请输入考场容量!')
    }
}

// 取消按钮-关闭考场信息对话框
const examinationCancel = () => {
    roomFormRef.value.resetFields()
    examRoom.row = null
    examRoom.cell = null
    // 置为空数组 取消table上的勾选按钮
    state.buildArr = []
    state.buildKeys = []
    state.buildSource = []
    state.examination = false
}

// 确认按钮-保存考场信息-考场信息赋值给table显示
const examinationOk = () => {
    // 表单校验
    roomFormRef.value.validateFields().then(() => {
        const examRoomObjArr = state.buildArr.map((item) => {
            return {
                cell: examRoom.cell,
                examPeopleNum: examRoom.examPeopleNum,
                row: examRoom.row,
                name: item.buildingName + item.name,
                siteId: item.id,
                id: uuidv4(),
                buildingName: item.buildingName,
                siteName: item.name,
                sitePeopleNum: item.peppleNum,
                invigilatorList: [],
                teacherTableList: []
            }
        })

        // 如果选中的siteId不包含在 数组所有的siteid 则可以添加改考场 否则重复不允许添加
        let siteIdArr = []
        state.groupConfigList.forEach((k) => {
            siteIdArr = k.siteList?.map((item) => item.siteId)
        })
        const isin = state.buildArr.map((r) => siteIdArr.includes(r.id))

        if (isin.includes(true)) {
            message.warning('不允许选择相同的场地!')
        } else {
            examRoomObjArr.forEach((f) => {
                state.groupConfigList[state.roomIndex].siteList.push(f)
            })
            // 清空表单
            roomFormRef.value.resetFields()
            examRoom.row = null
            examRoom.cell = null
            // 置为空数组 取消table上的勾选按钮
            state.buildArr = []
            state.buildKeys = []
            state.buildSource = []
            state.examination = false
        }
    })
}

// 打开修改考场信息对话框
const changeRooom = (item, i, inum) => {
    state.boxIndex = i // 记录第几个年级盒子 大盒子
    state.lineIndex = inum // 记录年级盒子里面第几个table行数据
    // 回显考场信息数据
    amend.designation = item.name
    amend.venues = item.buildingName
    amend.classroom = item.siteName
    amend.capacity = item.sitePeopleNum
    amend.districtCapacity = item.examPeopleNum
    amend.line = item.row
    amend.column = item.cell
    state.amendExamination = true
}

// 打开设置监考老师对话框
const teacherSet = (data, bigNum, lineNum) => {
    state.boxIndex = bigNum // 记录第几个年级盒子 大盒子
    state.lineIndex = lineNum // 记录年级盒子里面第几个table行数据
    // 设置监考老师 需判断 考试科目对应 考试时间是否填写完整 填写完整之后可以进入设置页面
    const isokTeacher = state.groupConfigList[state.boxIndex].subjectList.every(
        (item) => item.timeList?.length > 0
    )
    if (isokTeacher) {
        const teachTable = state.groupConfigList[
            state.boxIndex
        ].subjectList.map((i) => {
            return {
                // id: i.id,
                id: i.basicsSubjectId,
                subjectName: i.subjectName,
                subjectId: i.basicsSubjectId,
                timeList: i.timeList
            }
        })
        // 年级考场考场table 行数据在 放一个 table列表
        data.setTeacherList = teachTable || []
        // 复制值 给table回显
        state.setTeacherList = [...toRaw(teachTable)]

        // TODO 回显监考老师table
        // 如果行信息上 有了invigilatorList 数组 说明 当前行上选择了监考老师 点设置按钮的时候需要回显
        if (
            state.groupConfigList[state.boxIndex].siteList[state.lineIndex]
                .invigilatorList?.length
        ) {
            // 如果是修改模式回显
            if (route.query.id) {
                if (
                    state.groupConfigList[state.boxIndex].siteList[
                        state.lineIndex
                    ].teacherTableList
                ) {
                    // 2如果是修改模式又去新增了数据 又想再回显直接掏数据赋值 因为修改里新增的数据是没有 siteid的 用之前的过滤方式 过滤不出来
                    state.setTeacherList =
                        state.groupConfigList[state.boxIndex].siteList[
                            state.lineIndex
                        ].teacherTableList
                } else {
                    // 1如果是修改模式因为接口丢了数据 想回显 必须处理数组
                    const filterArr = state.groupConfigList[
                        state.boxIndex
                    ].siteList[state.lineIndex].invigilatorList.filter(
                        (item) => item.siteId === data.id
                    )
                    state.setTeacherList.forEach((j) => {
                        j.invigilatorTeacher = filterArr.filter(
                            (h) => j.id === h.subjectId
                        )
                        j.invigilator = j.invigilatorTeacher.map(
                            (f) => f.personId
                        )
                    })
                }
            } else {
                // 如果是新增模式回显 就把行table 数据塞回给显示监考老师table
                state.setTeacherList =
                    state.groupConfigList[state.boxIndex].siteList[
                        state.lineIndex
                    ].teacherTableList
            }
        }

        state.setTeacherShow = true
    } else {
        message.warning('请填写考试开始时间及考试结束时间!')
    }
}

// table上的勾选功能 用于删除表格行数据
const subSelectChange = (ids, data, i) => {
    state.groupConfigList[i].Keys = ids
}

// 考场table上的勾选功能 用于删除表格行数据
const roomSelectChange = (ids, data, i) => {
    state.groupConfigList[i].roomKeys = ids
}

// 删除课程table
const delSub = (i) => {
    // 删除table列上的信息 根据id删除对应的行数据
    state.groupConfigList[i].Keys.forEach((item) => {
        state.groupConfigList[i].subjectList.splice(
            state.groupConfigList[i].subjectList.findIndex(
                (v) => v.basicsSubjectId === item
            ),
            1
        )
    })
    state.groupConfigList[i].Keys = []
}

// 删除考场table 行数据
const delRoomLine = (i) => {
    state.groupConfigList[i].roomKeys.forEach((item) => {
        state.groupConfigList[i].siteList.splice(
            state.groupConfigList[i].siteList.findIndex((v) => v.id === item),
            1
        )
    })

    // 每次点完删除 去重置清空table 的选中状态 数组
    state.groupConfigList[i].roomKeys = []
}

// 删除年级盒子
const delgradeBox = (i, data) => {
    state.groupConfigList.splice(i, 1)
    // 根据下标删除了循环数组的指定项 还需要处理一次 选择年级组件上显示的数据
    state.gradeCheckedList.splice(
        state.gradeCheckedList.findIndex((item) => item.id === data.objectId),
        1
    )
}

// 选择场地 - 选择后处理出关联的table 数据
const locationChange = (val) => {
    // 每次选择考场把 单选勾选 数组置为空
    state.buildArr = []
    state.buildKeys = []
    locationOptions.value.forEach((item) => {
        if (item.id === val) {
            state.buildSource = item.siteList
        }
    })
}

// table列上点击考场单选框事件
const buildSelectChange = (val, item) => {
    // 存一个val值判断有没有选中
    state.buildKeys = val
    state.buildArr = item
}

// 计算属性判断删除按钮是不是置灰的
const hasSelected = computed(() => {
    return (dataItem) => {
        return !dataItem?.length
    }
})

// 人员控件 绿色 + 号 按钮 打开弹框
const addPerson = (data, num, type, i) => {
    // 如果当前表格的行数据有 主考员 巡考员 信息的话 就把行数据的item 塞回给选人组件 没有就都清空掉
    if (type === 'chiefList' && data.chiefList?.length) {
        state.teacherCheckedList = data.chiefList.map((item) => {
            return {
                id: item.personId,
                showName: item.personName
            }
        })
    } else if (type === 'inspectorList' && data.inspectorList?.length) {
        state.teacherCheckedList = data.inspectorList.map((item) => {
            return {
                id: item.personId,
                showName: item.personName
            }
        })
    } else if (type === 'invigilator' && data.invigilatorTeacher?.length) {
        state.teacherCheckedList = data.invigilatorTeacher.map((item) => {
            return {
                id: item.personId,
                showName: item.personName
            }
        })
    } else {
        state.teacherCheckedList = []
    }
    // 年级大盒子下标
    state.outBoxIndex = i
    // 保存table行数据下标 index //如果是监考老师 这里thisNum保存的是 监考老师那个table的行下标
    state.thisNum = num
    // 点击 加号按钮 保存一次 人员类型
    state.personType = type
    // 打开人员组件弹窗
    state.managerVisible = true
}

// 选择人员的组件确定按钮
const personOk = (data) => {
    // 如果是监考老师 这里操作是就是监考老师table数据
    if (state.personType === 'invigilator') {
        if (data.length <= 10) {
            state.setTeacherList[state.thisNum].invigilatorTeacher = data.map(
                (item) => {
                    return {
                        personId: item.id,
                        personName: item.showName
                    }
                }
            )
            state.setTeacherList[state.thisNum].invigilator = data.map(
                (item) => item.id
            )
        } else {
            message.warning('每个考试科目监考老师最多只能选择10名人员!')
        }
        return
    }

    // 如果是主考员
    if (state.personType === 'chiefList' && data.length <= 2) {
        state.groupConfigList[state.outBoxIndex].subjectList[
            state.thisNum
        ].chiefList = data.map((item) => {
            return {
                personId: item.id,
                personName: item.showName
            }
        })
        state.groupConfigList[state.outBoxIndex].subjectList[
            state.thisNum
        ].chiefListArr = data.map((item) => item.id)
    }
    // 如果是巡考员
    else if (state.personType === 'inspectorList' && data.length <= 2) {
        state.groupConfigList[state.outBoxIndex].subjectList[
            state.thisNum
        ].inspectorList = data.map((item) => {
            return {
                personId: item.id,
                personName: item.showName
            }
        })
        state.groupConfigList[state.outBoxIndex].subjectList[
            state.thisNum
        ].inspectorListArr = data.map((item) => item.id)
    } else {
        message.warning('每个考试科目主考员或巡考员最多只能选择两名人员!')
    }
}

// 选择年级
const addgradeList = () => {
    state.classVisible = true
}

// 确定选择年级按钮
const selectClassOk = (data) => {
    // 年级名字用来显示input框
    state.gradeName = data.map((item) => item.showName).join('；')
    // 年级集合
    state.groupList = data.map((item) => {
        return { objectId: item.id, objectName: item.showName }
    })

    state.groupConfigList = gradeBoxArr.value.map((item) => {
      
        const idx = state.groupConfigList.findIndex(
            (j) => j.objectId === item.id
        )
        const objectId = state.groupConfigList[idx]?.objectId || item.id
        const objectName =
            state.groupConfigList[idx]?.objectName || item.objectName
        const examinationArrange =
            state.groupConfigList[idx]?.examinationArrange || 1
        const siteList = state.groupConfigList[idx]?.siteList || []
        const roomKeys = state.groupConfigList[idx]?.roomKeys || []
        const Keys = state.groupConfigList[idx]?.Keys || []
        // 用来装按年级设置考试时间的subjectList数组
        const subjectList = state.groupConfigList[idx]?.subjectList || []
        return {
            ...item,
            objectId,
            objectName,
            examinationArrange,
            siteList,
            roomKeys,
            Keys,
            subjectList
        }
    })
}

// 根据选中的年级数组 计算出用来循环盒子的数组
const gradeBoxArr = computed(() => {
    return state.gradeCheckedList.map((i) => {
        return {
            id: i.id,
            objectId: i.id,
            objectName: i.showName,
            examinationArrange: 1,
            subjectList: [],
            siteList: [],
            roomKeys: []
        }
    })
})

// 设置监考老师确定
const teacherOK = () => {
    // 需要处理一次表格数据 点确认按钮拿到的table列表数组,装回给 考场siteList的行数据上 点设置按钮的时候已经知道了 点的是哪个大盒子 和哪个siteList行
    // 往行上加 table的第三列监考老师 用来 点设置的时候回显用
    // 往大盒子的siteList的行上加 老师名字用来显示名字
    // state.boxIndex
    // state.lineIndex
    // 确定需要校验设置监考老师的table上 是否都设置了监考老师
    const isoksetTeacher = state.setTeacherList.every(
        (item) => item.invigilator?.length > 0
    )
    if (isoksetTeacher) {
        state.groupConfigList[state.boxIndex].siteList[
            state.lineIndex
        ].teacherTableList = state.setTeacherList
        const invigilatorArr = []
        state.setTeacherList.forEach((item) => {
            item.invigilatorTeacher.forEach((peo) => {
                peo.subjectName = item.subjectName
                peo.subjectId = item.subjectId
                invigilatorArr.push(peo)
            })
        })
        state.groupConfigList[state.boxIndex].siteList[
            state.lineIndex
        ].invigilatorList = invigilatorArr
        state.setTeacherShow = false
    } else {
        message.warning('考试每门科目必须设置监考老师!')
    }
}
// 取消监考老师
const teacherOff = () => {
    state.setTeacherShow = false
}

// 获取科目列表
const reqSubList = async() => {
    const { data } = await getSubList()
    courseOptions.value = data
}
// 调接口获取年级树
// const reqGradeTree = async() => {
//     const { data } = await getGradeTree()
//     state.gradeTree = data
// }
// 考试信息-回显塞数据在这里
const reqExamDetInfo = async() => {
    const { data } = await getExamDetInfo({ id: route.query.id })
    state.subjectList = data.subjectList
    state.groupList = data.groupList
    state.groupConfigList = data.groupConfigList
    // 回显示年级框
    state.gradeCheckedList = data.groupList.map((item) => {
        return {
            id: item.objectId,
            showName: item.objectName
        }
    })
    // 回显年级的名称
    state.gradeName = data.groupList.map((item) => item.objectName).join('；')
}

// 修改考场对话框取消
const revampOff = () => {
    // 清空表单
    amendFormRef.value.resetFields()
    amend.line = null
    amend.column = null
    state.amendExamination = false
}
// 修改考场对话框确定
const revampOK = () => {
    // 校验表单规则
    amendFormRef.value.validateFields().then(() => {
        if (Number(amend.districtCapacity) > Number(amend.capacity)) {
            message.warning('考场容量必须要小于等于场地容量!')
        } else {
            // 对此行进行修改数据
            state.groupConfigList[state.boxIndex].siteList[
                state.lineIndex
            ].name = amend.designation
            state.groupConfigList[state.boxIndex].siteList[
                state.lineIndex
            ].row = amend.line
            state.groupConfigList[state.boxIndex].siteList[
                state.lineIndex
            ].cell = amend.column
            state.groupConfigList[state.boxIndex].siteList[
                state.lineIndex
            ].examPeopleNum = amend.districtCapacity
            state.amendExamination = false
        }
    })
}

// 点击删除按钮 删除 主考员
const changechiefListArr = (arr, item) => {
    state.teacherCheckedList = item.chiefList.filter((i) =>
        arr.includes(i.personId)
    )
    item.chiefList = state.teacherCheckedList
}

// 点击删除按钮 删除 巡考员
const changeinspectorListArr = (arr, item) => {
    state.teacherCheckedList = item.inspectorList.filter((i) =>
        arr.includes(i.personId)
    )
    item.inspectorList = state.teacherCheckedList
}

// 点击删除监考老师
const changeinvigilator = (arr, item) => {
    state.teacherCheckedList = item.invigilatorTeacher.filter((i) =>
        arr.includes(i.personId)
    )
    item.invigilatorTeacher = state.teacherCheckedList
}

// 监听属性 监听年级数组-如果年级变化了 年级选择框上的名字也变化
watch(
    () => state.gradeCheckedList,
    (newValue, oldValue) => {
        state.groupList = newValue.map((item) => {
            return { objectId: item.id, objectName: item.showName }
        })
        state.gradeName = newValue.map((item) => item.showName).join('；')
    },
    {
        deep: true
    }
)

watch(
    () => examRoom.examPeopleNum,
    (newValue, oldValue) => {
        examRoom.location = null
        locationOptions.value = []
        state.buildArr = []
        state.buildKeys = []
        state.buildSource = []
    },
    {
        deep: true
    }
)

onMounted(() => {
    reqSubList()
    // reqGradeTree()
    if (route.query.id) {
        reqExamDetInfo()
    }
})
// 暴露方法和属性给父组件
defineExpose({ state })
</script>
<style lang="less" scoped>
.examPlaceInfo {
    padding-top: 24px;
    .gradeHead {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .gradeTitle {
            font-size: 14px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
        }
    }
    .arrange {
        border-radius: 4px;
        padding: 16px;
        border: 1px solid #d9d9d9;
        .titleText {
            font-size: 14px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            &::before {
                display: inline-block;
                margin-right: 4px;
                color: #ff4d4f;
                font-size: 14px;
                line-height: 1;
                content: '*';
            }
        }
    }
}

.gradeBox :deep(.ant-input) {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.subDelBtn {
    display: flex;
    justify-content: flex-end;
    padding-bottom: 16px;
}
.gradeBox {
    padding-top: 24px;
}
.lookGradeHead {
    padding-bottom: 8px;
}
.teachNameJoin {
    cursor: pointer;
    max-width: 350px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
}

:deep(.ant-select-selection-overflow-item .ant-select-selection-item) {
    background: #f5f5f5 !important;
    border: 1px solid #f0f0f0 !important;
}

.list-enter-active,
.list-leave-active {
    transition: all 0.5s ease;
}
.list-enter-from,
.list-leave-to {
    opacity: 0;
    transform: translateX(5px);
}
.btnClass {
    padding: 16px 0px 16px 0px;
}
.examRadio {
    padding: 16px 0px;
}
.classdelBtn {
    display: flex;
    justify-content: flex-end;
    padding: 16px 0px;
}
.arrangement {
    padding-top: 24px;
}
:deep(.ant-input-suffix) {
    color: rgba(0, 0, 0, 0.25);
}
</style>
<style lang="less">
.siteModal {
    .ant-modal-body {
        max-height: 660px;
        overflow: hidden auto;
    }
}
</style>
