<template>
    <div class="title">考试时间设置：</div>
    <a-radio-group
        v-model:value="state.timeType"
        name="radioGroup"
        :disabled="isDisabled"
    >
        <a-radio :value="1">统一考试时间</a-radio>
        <a-radio :value="2">分年级考试时间</a-radio>
    </a-radio-group>
    <!-- 统一考试页面 -->
    <div v-if="state.timeType === 1">
        <Unify ref="unifyref"></Unify>
    </div>
    <Transition name="fade" mode="out-in">
        <!-- 分年级考试页面 -->
        <div v-if="state.timeType === 2">
            <Grade ref="graderef"></Grade>
        </div>
    </Transition>
</template>
<script setup>
import {
    reactive,
    ref,
    onMounted,
    toRaw,
    computed,
    watch,
    onUpdated
} from 'vue'
import { getExamDetInfo } from '@/api/exam.js'
import Unify from './children/unify'
import Grade from './children/grade'
import { useRoute } from 'vue-router'
const route = useRoute()
// 计算属性判断是否为查看页面 禁用所有表单选项
const isDisabled = computed(() => route.query.type === 'look')
const unifyref = ref()
const graderef = ref()

const state = reactive({
    timeType: 1
})

// 如果是编辑修改考试 组件加载后发送请求
const reqExamDetInfo = async() => {
    const { data } = await getExamDetInfo({ id: route.query.id })
    state.timeType = data.type
}

onMounted(() => {
    if (route.query.id) {
        reqExamDetInfo()
    }
    state.Rawdata = unifyref.value.state
})

onUpdated(() => {
    state.timeType === 1
        ? (state.Rawdata = unifyref.value?.state)
        : (state.Rawdata = graderef.value?.state)
})

// watch(() => state.timeType, (newValue, oldValue) => {
//     
//     // newValue === 1 ? state.Rawdata = unify.value?.state : state.Rawdata = grade.value?.state
// }, {
//     deep: true
//     // immediate: true
// })

// 暴露方法和属性给父组件
defineExpose({ state })
</script>
<style lang="less" scoped>
.title {
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    padding-bottom: 16px;
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>
