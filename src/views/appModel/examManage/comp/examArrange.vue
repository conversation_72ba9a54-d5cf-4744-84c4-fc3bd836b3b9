<!-- eslint-disable -->
<template>
    <div class="examPlan">
        <div class="examPlan_left">
            <div class="plan_step_box">
                <div
                    :class="{
                        plan_step: true,
                        activity_plan_step: state.activityIndex === index,
                    }"
                    v-for="(item, index) in planList"
                    :key="item.id"
                    @click="stepItem(index, item)"
                >
                    <div
                        :class="{
                            yuan_icon: true,
                            activity_yuan_icon: state.activityIndex === index,
                        }"
                    >
                        <span
                            v-if="index === 0 && state.activityIndex !== index"
                        >
                            <check-outlined
                        /></span>
                        <span v-else> {{ item.id }}</span>
                    </div>
                    <div
                        :class="{
                            plan_title: true,
                            activity_plan_title: state.activityIndex === index,
                        }"
                    >
                        {{ item.name }}
                    </div>
                </div>
            </div>
        </div>
        <div class="examPlan_right">
            <div class="district" v-if="state.activityName === 'roomArrange'">
                <div class="district_title">
                    当前编排考试：<span>{{ formState.name }}</span>
                </div>
                <div class="district_title">
                    总人数：<span>{{ state.totalPersonCount }}</span
                    >人
                </div>
                <div class="personnel">考试人员：</div>
                <div class="personnel_card">
                    <div
                        :class="{
                            card_item: true,
                            activity_card_item: state.cardIndex === index,
                        }"
                        v-for="(item, index) in state.groups"
                        @click="getExamDetail(item, index)"
                    >
                        <div class="name_box">
                            <div>{{ item.gradeName }}</div>
                            <div class="interval">|</div>
                            <div>{{ item.classesName }}</div>
                        </div>
                    </div>
                </div>
                <div class="exam_num">
                    考试人数：<span>{{ state.totalCount }}</span
                    >人
                </div>
                <div class="selectbtn">
                    <a-button
                        :disabled="isDisabled"
                        @click="selectRoom"
                        type="primary"
                        >选择考场</a-button
                    >
                </div>
                <div class="allocationdetails">
                    <div class="allocation">分配详情</div>
                    <div class="already">
                        当前已设置：<span class="already_num">{{
                            state.settingCount
                        }}</span
                        >人
                    </div>
                    <div class="already">
                        待设置：<span class="delay_num">{{
                            state.totalCount - state.settingCount
                        }}</span
                        >人
                    </div>
                </div>
                <div class="detailsTable">
                    <a-table
                        :dataSource="state.siteList"
                        :columns="detailsColumns"
                        :pagination="false"
                    >
                        <template #headerCell="{ column }">
                            <template v-if="column.dataIndex === 'layout'">
                                <div>
                                    考场布局
                                    <span style="font-size: 12px"
                                        >（行X列）</span
                                    >
                                </div>
                            </template>
                        </template>
                        <template #bodyCell="{ column, record, index }">
                            <template v-if="column.dataIndex === 'layout'">
                                {{ record.row }} x {{ record.cell }}
                            </template>
                            <!-- <template v-if="column.dataIndex === 'sort'">
                                <a-button style="color: #00b781" type="text"
                                    >上移</a-button
                                >
                                <a-button style="color: #00b781" type="text"
                                    >下移</a-button
                                >
                            </template> -->
                            <template v-if="column.dataIndex === 'operation'">
                                <a-button
                                    :disabled="isDisabled"
                                    @click="delExamSite(record)"
                                    type="link"
                                    >删除</a-button
                                >
                            </template>
                        </template>
                    </a-table>
                </div>
            </div>
            <div class="seatBox" v-if="state.activityName === 'seatArrange'">
                <div class="seatBox_title">
                    当前编排考试：<span>{{ formState.name }}</span>
                </div>
                <div class="seatBox_title">
                    总考试人数：<span>{{ state.totalPersonCount }}</span>
                </div>
                <div class="seatBox_title">
                    已选择考场容量：<span>{{ state.totalCapacityCount }}</span>
                </div>
                <div class="personnel">考试人员：</div>
                <div class="personnel_card">
                    <div
                        :class="{
                            card_item: true,
                            activity_card_item: state.cardIndex === index,
                        }"
                        v-for="(item, index) in state.groups"
                        @click="getExamDetail(item, index)"
                    >
                        <div class="name_box">
                            <div>{{ item.gradeName }}</div>
                            <div class="interval">|</div>
                            <div>{{ item.classesName }}</div>
                        </div>
                    </div>
                </div>
                <div class="exam_num">
                    考试人数：<span>{{ state.totalCount }}</span
                    >人
                </div>
                <div class="radioBox">
                    编排方式：
                    <a-radio-group
                        v-model:value="state.arrangeType"
                        name="radioGroup"
                    >
                        <a-radio :value="1" :disabled="isDisabled">
                            <span class="radioTypeName">顺序编排</span>
                            <!-- <div class="radioType">
                                <span class="radioTypeName">顺序编排</span>
                                <a-tooltip>
                                    <template #title>顺序编排</template>
                                    <img
                                        class="imgTip"
                                        src="/image/question.png"
                                    />
                                </a-tooltip>
                            </div> -->
                        </a-radio>
                        <a-radio :value="2" :disabled="isDisabled">
                            <div class="radioType">
                                <span class="radioTypeName">S型编排</span>
                                <img class="imgTip" src="/image/question.png" />
                                <img
                                    class="tipsImg"
                                    style="width: 992px; height: 437px"
                                    src="/image/pic-shili.png"
                                />
                                <!-- <a-tooltip :defaultVisible="false" :overlayStyle="{width: '536px', height: '315px'}">
                                    <template #title>
                                        <img style="width: 536px;height: 315px;"
                                      
                                        src="/image/pic-shili.png"
                                    /></template>
                                    <img
                                        class="imgTip"
                                        src="/image/question.png"
                                    />
                                </a-tooltip> -->
                            </div>
                        </a-radio>
                    </a-radio-group>
                </div>
                <div class="arrangeBtn">
                    <a-button
                        :disabled="isDisabled"
                        type="primary"
                        @click="examGroupArrange"
                        >编排</a-button
                    >
                </div>
                <div class="allocationdetails">
                    <div class="allocation">分配详情</div>
                    <div class="already">
                        当前已设置：<span class="already_num">{{
                            state.settingCount
                        }}</span
                        >人
                    </div>
                    <div class="already">
                        待设置：<span class="delay_num">{{
                            state.totalCount - state.settingCount
                        }}</span
                        >人
                    </div>
                </div>
                <!-- <div class="arrangeBtn">分配详情</div> -->
                <div>
                    <a-table
                        :dataSource="state.siteList"
                        :columns="seatColumns"
                        :pagination="false"
                    >
                        <template #headerCell="{ column }">
                            <template v-if="column.dataIndex === 'layout'">
                                <div>
                                    考场布局
                                    <span style="font-size: 12px"
                                        >（行X列）</span
                                    >
                                </div>
                            </template>
                        </template>
                        <template #bodyCell="{ column, record, index }">
                            <template v-if="column.dataIndex === 'layout'">
                                {{ record.row }} x {{ record.cell }}
                            </template>
                            <template v-if="column.dataIndex === 'operation'">
                                <a-button
                                    @click="lookStudentInfo(record)"
                                    style="color: #00b781"
                                    type="text"
                                    >查看</a-button
                                >
                            </template>
                        </template>
                    </a-table>
                </div>
            </div>
        </div>
    </div>

    <!-- 这是考场编排里面的选择考场的弹窗 -->
    <a-modal
        :confirm-loading="state.subLoading"
        :keyboard="false"
        :maskClosable="false"
        centered
        v-model:visible="state.roomFormVisible"
        title="选择考场"
        @ok="roomHandleOk"
        :bodyStyle="{ overflow: 'auto', maxHeight: '500px' }"
        @cancel="subFormCancel"
        width="1000px"
        ok-text="确认"
        cancel-text="取消"
    >
        <div>
            <a-form layout="inline">
                <a-form-item label="楼栋：">
                    <a-select
                        style="width: 206px"
                        @change="changeSite"
                        v-model:value="state.buildingId"
                        placeholder="请选择"
                        :options="state.buildingNameList"
                        :showArrow="true"
                        :field-names="{
                            label: 'buildingName',
                            value: 'buildingId',
                        }"
                    >
                    </a-select>
                </a-form-item>
                <a-form-item label="场地类型：">
                    <a-select
                        style="width: 206px"
                        @change="changeSite"
                        v-model:value="state.siteTypeId"
                        placeholder="请选择"
                        :options="state.siteTypeNameList"
                        :showArrow="true"
                        :field-names="{
                            label: 'siteTypeName',
                            value: 'siteTypeId',
                        }"
                    >
                    </a-select>
                </a-form-item>
                <a-form-item>
                    <a-button
                        type="primary"
                        style="margin-right: 12px"
                        @click="inquireSite"
                    >
                        <template #icon>
                            <SearchOutlined style="margin-right: 4px" />
                        </template>
                        查询
                    </a-button>
                    <a-button @click="resetSite">
                        <template #icon>
                            <redo-outlined style="margin-right: 4px" />
                        </template>
                        重置
                    </a-button>
                </a-form-item>
            </a-form>
        </div>
        <div class="allocationdetails">
            <div class="tipsTitle">
                当前参与考试人数：<span>{{ state.totalCount }}</span
                >人
            </div>
            <div class="tipsTitle">
                当前已设置：<span style="color: #00b781">{{
                    state.settingCount
                }}</span
                >人
            </div>
            <div class="tipsTitle">
                待设置：<span style="color: #f5222d">{{
                    state.totalCount - state.settingCount
                }}</span
                >人
            </div>
        </div>
        <div>
            <a-table
                :dataSource="state.listExamSite"
                rowKey="id"
                :row-selection="{
                    selectedRowKeys: state.siteSelectedRowKeys,
                    onChange: onSiteSelectChange,
                }"
                :columns="roomColumns"
                :pagination="false"
            >
                <template #headerCell="{ column }">
                    <template v-if="column.dataIndex === 'row'">
                        <div>
                            考场布局
                            <span style="font-size: 12px">（行X列）</span>
                        </div>
                    </template>
                </template>
                <template #bodyCell="{ column, record, index }">
                    <template v-if="column.dataIndex === 'row'">
                        {{ record.row }} * {{ record.cell }}
                    </template>
                </template>
            </a-table>
        </div>
    </a-modal>

    <!-- 这是座位编排里面查看学生人员的的弹窗 -->
    <a-modal
        :keyboard="false"
        :footer="null"
        :maskClosable="false"
        centered
        v-model:visible="state.lookVisible"
        title="查看"
        :bodyStyle="{ overflow: 'auto', maxHeight: '500px' }"
        width="900px"
    >
        <div class="recordInfo">
            <div class="recordInfoItem">
                当前考场：<span>{{ state.recordName }}</span>
            </div>
            <div class="recordInfoItem">
                考场容量：<span>{{ state.recordExamPeopleNum }}</span
                >人
            </div>
            <div class="recordInfoItem">
                已使用量：<span>{{ state.recordArrangeNum }}</span>
            </div>
        </div>
        <div>
            <a-table
                :dataSource="state.detailsSource"
                :columns="studentColumns"
                :pagination="false"
            >
                <template #bodyCell="{ column, record, index }">
                    <template v-if="column.dataIndex === 'arrangeType'">
                        {{ arrangeObj[record.arrangeType] || "-" }}
                    </template>
                </template>
            </a-table>
        </div>
    </a-modal>
</template>

<script setup name="examPlan">
/* eslint-disable */
import { onMounted, ref, computed, reactive } from "vue";
import { message } from "ant-design-vue";
import dayjs from "dayjs";
import { useRoute } from "vue-router";

import {
    getExamPersonCountInfo,
    getDistributeExamSiteDetail,
    listExamSite,
    distributeExamSite,
    deleteExamSite,
    changeExamGroupArrange,
    pageExamSitePerson,
    listExamBuilding,
    listExamSiteType,
} from "@/api/exam.js";
import { useExamData } from "../useExamData.js";
const { formState, resetState, examinationId } = useExamData();
const infoFormRef = ref();
const route = useRoute();
// 计算属性判断是否为查看页面 禁用所有表单选项
const isDisabled = computed(() => route.query.type === "look");

const detailsColumns = [
    {
        title: "序号",
        key: "index",
        dataIndex: "index",
        customRender: ({ text, record, index }) => `${index + 1}`, // 显示每一行的序号
    },
    {
        title: "考场名称",
        key: "name",
        dataIndex: "name",
    },
    {
        title: "场地名称",
        key: "siteName",
        dataIndex: "siteName",
    },
    {
        title: "场地类型",
        key: "siteTypeName",
        dataIndex: "siteTypeName",
    },
    {
        title: "所属楼栋",
        key: "buildingName",
        dataIndex: "buildingName",
    },
    {
        title: "楼层",
        key: "floor",
        dataIndex: "floor",
    },
    {
        title: "房间号",
        key: "roomNum",
        dataIndex: "roomNum",
    },
    {
        title: "考场容量",
        key: "examPeopleNum",
        dataIndex: "examPeopleNum",
    },
    {
        title: "考场布局",
        key: "layout",
        dataIndex: "layout",
    },
    {
        title: "已使用量",
        key: "arrangeNum",
        dataIndex: "arrangeNum",
    },
    // {
    //     title: "排序",
    //     key: "sort",
    //     dataIndex: "sort",
    // },
    {
        title: "操作",
        key: "operation",
        dataIndex: "operation",
    },
];

const seatColumns = [
    {
        title: "序号",
        key: "index",
        dataIndex: "index",
        customRender: ({ text, record, index }) => `${index + 1}`, // 显示每一行的序号
    },
    {
        title: "考场名称",
        key: "name",
        dataIndex: "name",
    },
    {
        title: "场地名称",
        key: "siteName",
        dataIndex: "siteName",
    },
    {
        title: "场地类型",
        key: "siteTypeName",
        dataIndex: "siteTypeName",
    },
    {
        title: "所属楼栋",
        key: "buildingName",
        dataIndex: "buildingName",
    },
    {
        title: "楼层",
        key: "floor",
        dataIndex: "floor",
    },
    {
        title: "房间号",
        key: "roomNum",
        dataIndex: "roomNum",
    },
    {
        title: "考场容量",
        key: "examPeopleNum",
        dataIndex: "examPeopleNum",
    },
    {
        title: "考场布局",
        key: "layout",
        dataIndex: "layout",
    },
    {
        title: "已使用量",
        key: "arrangeNum",
        dataIndex: "arrangeNum",
    },
    {
        title: "操作",
        key: "operation",
        dataIndex: "operation",
    },
];

const roomColumns = [
    {
        title: "考场名称",
        key: "name",
        dataIndex: "name",
    },
    {
        title: "场地名称",
        key: "siteName",
        dataIndex: "siteName",
    },
    {
        title: "场地类型",
        key: "siteTypeName",
        dataIndex: "siteTypeName",
    },
    {
        title: "所属楼栋",
        key: "buildingName",
        dataIndex: "buildingName",
    },
    {
        title: "楼层",
        key: "floor",
        dataIndex: "floor",
    },
    {
        title: "房间号",
        key: "roomNum",
        dataIndex: "roomNum",
    },
    {
        title: "考场容量",
        key: "examPeopleNum",
        dataIndex: "examPeopleNum",
    },
    {
        title: "考场布局",
        key: "row",
        dataIndex: "row",
    },
];

const studentColumns = [
    {
        title: "序号",
        key: "index",
        dataIndex: "index",
        customRender: ({ text, record, index }) => `${index + 1}`, // 显示每一行的序号
    },
    {
        title: "学生姓名",
        key: "personName",
        dataIndex: "personName",
    },
    {
        title: "年级",
        key: "gradeName",
        dataIndex: "gradeName",
    },
    {
        title: "班级",
        key: "classesName",
        dataIndex: "classesName",
    },
    {
        title: "编排类型",
        key: "arrangeType",
        dataIndex: "arrangeType",
    },
];

const state = reactive({
    buildingNameList: [],
    siteTypeNameList: [],
    lookVisible: false,
    siteSelectedRowKeys: [],
    options: [],
    cardIndex: 0,
    activityIndex: 0,
    activityName: "roomArrange",
    roomFormVisible: false,
    subFormVisible: false,
    detailsSource: [],
    temForm: {},
});

const arrangeObj = {
    1: "顺序编排",
    2: "S型编排",
};

const planList = [
    {
        id: "1",
        name: "考场编排",
        value: "roomArrange",
    },
    {
        id: "2",
        name: "座位编排",
        value: "seatArrange",
    },
];

// 点击步骤条
// const stepItem = (index, item) => {
//     state.activityIndex = index;
//     state.activityName = item.value;
//     reqExamPersonCountInfo();
// };

// 选择考场 去获取考场列表的请求
const selectRoom = () => {
    // 每次点选择考场去之前的勾选项去掉
    state.siteSelectedRowKeys = [];
    listExamSite({
        examId: examinationId.value,
        groupId: state.targetGroupId,
    }).then((res) => {
        state.listExamSite = res.data;
        state.roomFormVisible = true;
    });
};

const reqlistExamSite = () => {
    listExamSite({
        examId: examinationId.value,
        groupId: state.targetGroupId,
        buildingId: state.buildingId,
        siteTypeId: state.siteTypeId,
    }).then((res) => {
        state.listExamSite = res.data;
    });
};

const inquireSite = () => {
    reqlistExamSite();
};

const resetSite = () => {
    delete state.buildingId;
    delete state.siteTypeId;
    reqlistExamSite();
};

// 获取已经分配了考场的这些table数据这些那些的
const reqDistributeExamSiteDetail = (examId, groupId) => {
    getDistributeExamSiteDetail({
        examId: examId,
        groupId: groupId,
    }).then((res) => {
        const { data } = res;
        state.arrangeType = data.arrangeType;
        state.siteList = data.siteList; // 考试人数
        state.totalCount = data.totalCount; // 考试人数
        state.settingCount = data.settingCount; // 已设置
    });
};

// 获取已经分配了考场的这些table数据这些那些的
const nextreqDistributeExamSiteDetail = (examId, groupId) => {
    return new Promise((resolve, reject) => {
        getDistributeExamSiteDetail({
            examId: examId,
            groupId: groupId,
        })
            .then((res) => {
                const { data } = res;
                const totalCount = data.totalCount;
                const settingCount = data.settingCount;
                const arrangeType = data.arrangeType;
                resolve({
                    diff: totalCount - settingCount,
                    arrangeType: arrangeType,
                });
            })
            .catch((error) => {
                reject(error);
            });
    });
};

const checkAllConditions = async () => {
    const promises = state.groups.map((item) =>
        nextreqDistributeExamSiteDetail(examinationId.value, item.id)
    );
    const results = await Promise.all(promises);
    const isAllZero = results.every((result) => result.diff === 0);
    const isAllTrue = results.every((result) => !!result.arrangeType !== false);
    return {
        isAllZero: isAllZero,
        isAllTrue: isAllTrue,
    };
};

// 获取 考场编排 分配详情 在这里发请求
const getExamDetail = (item, index) => {
    state.cardIndex = index;
    state.targetGroupId = item.id;
    reqDistributeExamSiteDetail(examinationId.value, item.id);
};

const reqExamPersonCountInfo = () => {
    getExamPersonCountInfo({
        id: examinationId.value,
    }).then((res) => {
        const { data } = res;
        state.totalPersonCount = data.totalPersonCount; // 总人数
        state.totalCapacityCount = data.totalCapacityCount; // 考场的总容量
        state.groups = data.groups; // 考试人员
        const firstgroup = data.groups[0];
        getExamDetail(firstgroup, 0);
    });
};

// 勾选的场地的table
const onSiteSelectChange = (selectedRowKeys) => {
    state.siteSelectedRowKeys = selectedRowKeys;
};

// 确定分配考场
const roomHandleOk = () => {
    distributeExamSite({
        examId: examinationId.value,
        groupId: state.targetGroupId,
        publicSiteIds: state.siteSelectedRowKeys,
    }).then((res) => {
        state.roomFormVisible = false;
        reqDistributeExamSiteDetail(examinationId.value, state.targetGroupId);
    });
};

// 删除考场
const delExamSite = (data) => {
    deleteExamSite({
        examId: examinationId.value,
        groupId: state.targetGroupId,
        siteId: data.id,
    }).then((res) => {
        reqDistributeExamSiteDetail(examinationId.value, state.targetGroupId);
    });
};

// 编排考试
const examGroupArrange = () => {
    changeExamGroupArrange({
        examId: examinationId.value,
        groupId: state.targetGroupId,
        arrangeType: state.arrangeType,
    }).then((res) => {
        message.success("座位编排成功");
    });
};

// 查看学生信息
const lookStudentInfo = (data) => {
    state.recordName = data.name;
    state.recordExamPeopleNum = data.examPeopleNum;
    state.recordArrangeNum = data.arrangeNum;
    pageExamSitePerson({
        pageNo: 1,
        pageSize: 100,
        examId: examinationId.value,
        groupId: state.targetGroupId,
        siteId: data.id,
    }).then((res) => {
        state.lookVisible = true;
        state.detailsSource = res.data.list;
    });
};

// 下一步去 座位编排咯
const goNextSeat = () => {
    checkAllConditions()
        .then((result) => {
            if (result.isAllZero) {
                state.activityIndex = 1;
                state.activityName = "seatArrange";
                reqExamPersonCountInfo();
            } else {
                message.warning(
                    "每一组考试人员都必须完成考场分配且待设置人数需为0"
                );
            }
        })
        .catch((error) => {
            message.warning(
                "每一组考试人员都必须完成考场分配且待设置人数需为0"
            );
            console.error(error);
        });

    // state.activityIndex = 1;
    // state.activityName = "seatArrange";
    // reqExamPersonCountInfo();
};

// 如果是去监考安排还得继续进行校验 是不是都完成了 编排方式
const verifyType = () => {
    return new Promise((resolve, reject) => {
        checkAllConditions()
            .then((result) => {
                if (result.isAllTrue) {
                    resolve({
                        data: true,
                        message: "校验通过",
                    });
                } else {
                    message.warning(
                        "每一组考试人员都必须选择座位编排方式进行编排"
                    );
                    reject({
                        data: false,
                        message: "每一组考试人员都必须选择座位编排方式进行编排",
                    });
                }
            })
            .catch((error) => {
                message.warning(
                    "每一组考试人员都必须选择座位编排方式进行编排"
                );
                reject({
                    data: false,
                    message: "每一组考试人员都必须选择座位编排方式进行编排",
                });
            });
    });
};

const goLastStep = () => {
    state.activityIndex = 0;
    state.activityName = "roomArrange";
};

// 获取选择考场里的楼栋列表
const reqlistExamBuilding = async () => {
    const { data } = await listExamBuilding();
    state.buildingNameList = data;
};

// 获取选择考场里的类型列表
const reqlistExamSiteType = async () => {
    const { data } = await listExamSiteType();
    state.siteTypeNameList = data;
};

// 暴露方法和属性给父组件
defineExpose({ state, goNextSeat, goLastStep , verifyType});

onMounted(() => {
    // 获取考试对象 考试人员 拿到groupId
    reqExamPersonCountInfo();
    reqlistExamBuilding();
    reqlistExamSiteType();
});
</script>

<style lang="less" scoped>
.examPlan {
    height: 100%;
    display: flex;
    .examPlan_left {
        padding-right: 14px;
        border-right: 1px solid #e6e6e6;
        height: 100%;
        width: 200px;
        .plan_step_box {
            border: 1px solid #d9d9d9;
            .plan_step {
                // cursor: pointer;
                padding: 9px 12px;
                display: flex;
                align-items: center;
            }
            .activity_plan_step {
                border-left: 2px solid #00b781;
                background: #fafafa;
            }
        }
    }
    .examPlan_right {
        flex: 1;
        padding-left: 24px;
    }
}

.district_title {
    padding-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
}
.personnel {
    font-size: 14px;
    font-weight: 400;
    padding-bottom: 8px;
    color: rgba(0, 0, 0, 0.65);
    &&::before {
        display: inline-block;
        margin-right: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: "*";
    }
}

.personnel_card {
    padding-bottom: 16px;
}

.card_item {
    cursor: pointer;
    margin-right: 12px;
    width: max-content;
    display: inline-block;
    // display: flex;
    background: #f6f6f6;
    padding: 6px 12px;
    border-radius: 4px;
}

.activity_card_item {
    color: rgba(255, 255, 255, 0.85);
    background: #00b781;
}

.name_box {
    display: flex;
    .interval {
        padding: 0 6px;
    }
}
.exam_num {
    padding-bottom: 16px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
}

.allocationdetails {
    display: flex;
    align-items: center;
    padding-top: 16px;
    padding-bottom: 16px;
}

.recordInfo {
    display: flex;
    align-items: center;
    padding-bottom: 16px;
    .recordInfoItem {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
        padding-right: 24px;
    }
}
.seatBox_title {
    font-size: 16px;
    padding-bottom: 16px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
}

.yuan_icon {
    height: 22px;
    width: 22px;
    line-height: 20px;
    border-radius: 11px;
    color: #8c8c8c;
    border: 1px solid #8c8c8c;
    vertical-align: middle;
    text-align: center;
    margin-right: 8px;
}

.activity_yuan_icon {
    color: #00b781;
    border: 1px solid #00b781;
}

.plan_title {
    font-size: 16px;
    font-weight: 500;
    color: #8c8c8c;
}

.activity_plan_title {
    color: #00b781;
}

.selectbtn {
    padding-bottom: 16px;
}
.allocation {
    font-size: 16px;
    font-weight: 500;
    padding-right: 24px;
    color: rgba(0, 0, 0, 0.85);
}
.already {
    font-size: 14px;
    padding-right: 24px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
}
.already_num {
    color: #00b781;
}
.delay_num {
    color: #f5222d;
}

.radioBox {
    padding-bottom: 16px;
    font-size: 14px;
    .radioType {
        display: flex;
        align-items: center;
        .radioTypeName {
            padding-right: 5px;
        }
    }
}

.arrangeBtn {
    padding-bottom: 16px;
}
.tipsTitle {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    padding-right: 24px;
}

.imgTip:hover + .tipsImg {
    display: block;
}
.tipsImg {
    display: none;
    top: 17px;
    left: 50px;
    position: absolute;
    z-index: 2;
}
</style>
