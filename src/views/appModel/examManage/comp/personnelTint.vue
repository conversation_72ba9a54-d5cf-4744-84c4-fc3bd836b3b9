<template>
    <div>
        <div class="coltips">监考员总数=考场数量 x 考场监考人数</div>
        <div class="nowColtips">
            {{
                `(当前考场数：${siteCount}) x (当前考场监考人数：${
                    personNum || 0
                }) =${siteCount * (personNum || 0)}`
            }}
        </div>
        <div class="countBox">
            所以您需选择<span style="color: #f5222d">{{
                siteCount * (personNum || 0)
            }}</span
            >名监考员，当前已选择<span style="color: #00b781">
                {{ person ? person.split("、").length : 0 }}</span
            >名
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    siteCount: [String, Number],
    personNum: [String, Number],
    person: [String, Number],
});
</script>
<style lang="less" scoped>
.coltips {
    font-size: 14px;
    font-weight: 400;
    padding-bottom: 12px;
    color: rgba(0, 0, 0, 0.65);
}

.nowColtips {
    font-size: 12px;
    padding-bottom: 12px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.45);
}

.countBox {
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
}
</style>
