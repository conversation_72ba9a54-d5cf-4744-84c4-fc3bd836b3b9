<template>
    <a-form
        class="info-form"
        ref="infoFormRef"
        autocomplete="off"
        layout="vertical"
        :model="formState"
    >
        <a-form-item
            label="考试名称"
            name="name"
            :rules="[
                {
                    required: formState.name,
                    message: '请输入考试名称!',
                    required: true
                }
            ]"
        >
            <a-input
            show-count
                :disabled="isDisabled"
                style="width: 556px"
                :maxlength="30"
                v-model:value.trim="formState.name"
                placeholder="请输入"
            />
        </a-form-item>
        <a-form-item
            label="考试日期"
            name="examTime"
            validateTrigger="onSubmit"
            :rules="[
                { validator: validaTime, required: true, trigger: ['submit'] }
            ]"
        >
            <a-date-picker
                :disabled="isDisabled"
                style="width: 47%; margin-right: 32px"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
                v-model:value="formState.startTime"
                placeholder="开始日期"
            />
            <a-form-item-rest>
            <a-date-picker
                :disabled="isDisabled"
                style="width: 47% "
                v-model:value="formState.endTime"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
                placeholder="结束日期"
            />
        </a-form-item-rest>
        </a-form-item>
        <a-form-item
            label="考试签到"
            name="signIn"
            :rules="[
                {
                    required: formState.signIn,
                    message: '请选择考试签到!',
                    required: true
                }
            ]"
        >
            <a-radio-group
                v-model:value="formState.signIn"
                :disabled="isDisabled"
                name="signIn"
            >
                <a-radio :value="true">要求签到</a-radio>
                <a-radio :value="false">不要求签到</a-radio>
            </a-radio-group>
            <a-form-item-rest v-if="formState.signIn">
                <div class="rangeBox">
                    <div class="signInEnd">
                        考试开场前
                        <a-input-number
                            :disabled="isDisabled"
                            id="inputNumber"
                            v-model:value="formState.startMinute"
                            :min="0"
                            :max="60"
                            :defaultValue="0"
                        />分钟，方可允许签到；考试开场
                        <a-input-number
                            :disabled="isDisabled"
                            id="inputNumber"
                            v-model:value="formState.endMinute"
                            :min="0"
                            :max="60"
                            :defaultValue="0"
                        />分钟后，不允许签到
                    </div>
                </div>
            </a-form-item-rest>
        </a-form-item>
        <a-form-item
            label="准考证号"
            name="showed"
            :rules="[
                {
                    required: formState.showed,
                    message: '请选择是否显示准考证号!',
                    required: true
                }
            ]"
        >
            <a-radio-group
                :disabled="isDisabled"
                v-model:value="formState.showed"
                name="showed"
            >
                <a-radio :value="false">不显示</a-radio>
                <a-radio :value="true">显示</a-radio>
            </a-radio-group>
        </a-form-item>
    </a-form>
</template>

<script setup>
import { onMounted, ref, computed, reactive } from 'vue'
import dayjs from 'dayjs'
import { useRoute } from 'vue-router'
import { getExamInfo } from '@/api/exam.js'
import { useExamData } from '../useExamData.js'
import { Local } from '@/utils/storage'
const route = useRoute()
const { formState, resetState } = useExamData()
// 计算属性判断是否为查看页面 禁用所有表单选项
const isDisabled = computed(() => route.query.type === 'look')
// 禁用日期
const disabledDate = (current) => {
    return current && current < dayjs().subtract(1, 'day')
}
const infoFormRef = ref()
const reqExamInfo = async() => {
    const { data } = await getExamInfo({ id: route.query.id })
    formState.name = data.name
    formState.showed = data.showed
    formState.signIn = data.signIn
    formState.startTime = data.startTime
    formState.endTime = data.endTime
    formState.startMinute = data.startMinute
    formState.endMinute = data.endMinute
}

// 自定义表单校验
const validaTime = (_rule, value) => {
    if (!formState.startTime || !formState.endTime) {
        return Promise.reject(new Error('请选择考试日期!'))
    }
    if (dayjs(formState.endTime).isBefore(dayjs(formState.startTime))) {
        return Promise.reject(new Error('考试结束日期不能提前于考试开始日期!'))
    }
    return Promise.resolve()
}

// 如果是编辑查看模式 用id 去发请求查看数据
onMounted(() => {
    if (route.query.id) {
        reqExamInfo()
    }
    // 如果是添加页就清空一下表单
    if (Local.get('examNewForm') === 1) {
        // 清空表单
        resetState()
    }
})
// 暴露方法和属性给父组件
defineExpose({ formState, infoFormRef })
</script>
<style lang="less" scoped>
.signInEnd {
    padding-top: 22px;
}
:deep(.ant-input-number) {
    width: 66px;
}
:deep(.ant-radio-wrapper) {
    margin-right: 100px;
}
</style>
