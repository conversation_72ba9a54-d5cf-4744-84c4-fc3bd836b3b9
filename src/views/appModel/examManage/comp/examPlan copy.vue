<!-- eslint-disable -->
<template>
    <div class="examPlan">
        <div class="examPlan_left">
            <div class="plan_step_box">
                <div :class="{
                    plan_step: true,
                    activity_plan_step: state.activityIndex === index,
                }" v-for="(item, index) in planList" :key="item.id" @click="stepItem(index, item)">
                    <div :class="{
                        yuan_icon: true,
                        activity_yuan_icon: state.activityIndex === index,
                    }">
                        <span v-if="index === 0 && state.activityIndex !== index">
                            <check-outlined /></span>
                        <span v-else> {{ item.id }}</span>
                    </div>
                    <div :class="{
                        plan_title: true,
                        activity_plan_title: state.activityIndex === index,
                    }">
                        {{ item.name }}
                    </div>
                </div>
            </div>
        </div>
        <div class="examPlan_right">
            <!-- 这一块是那些基本信息的数据 -->
            <div class="basicInfoBox" v-show="state.activityName === 'information'">
                <a-form class="info-form" ref="infoFormRef" autocomplete="off" layout="vertical" :model="formState">
                    <a-form-item label="考试名称：" name="name" :rules="[
                        {
                            message: '请输入考试名称!',
                            required: true,
                        },
                    ]">
                        <a-input show-count :disabled="isDisabled" style="width: 100%" :maxlength="30"
                            v-model:value.trim="formState.name" placeholder="请输入" />
                    </a-form-item>
                    <a-form-item label="考试日期：" name="examTime" validateTrigger="onSubmit" :rules="[
                        {
                            validator: validaTime,
                            required: true,
                            trigger: ['submit'],
                        },
                    ]">
                        <a-date-picker :disabled="isDisabled" style="width: 47%; margin-right: 35px" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledDate" v-model:value="formState.startTime"
                            placeholder="开始日期" />
                        <a-form-item-rest>
                            <a-date-picker :disabled="isDisabled" style="width: 47%" v-model:value="formState.endTime"
                                format="YYYY-MM-DD" value-format="YYYY-MM-DD" :disabled-date="disabledDate"
                                placeholder="结束日期" />
                        </a-form-item-rest>
                    </a-form-item>
                    <a-form-item label="考试签到：" name="signIn" :rules="[
                        {
                            required: formState.signIn,
                            message: '请选择考试签到!',
                            required: true,
                        },
                    ]">
                        <a-radio-group class="signInRadio" v-model:value="formState.signIn" :disabled="isDisabled"
                            name="signIn">
                            <a-radio :value="true" style="padding-right: 80px">要求签到</a-radio>
                            <a-radio :value="false">不要求签到</a-radio>
                        </a-radio-group>
                        <a-form-item-rest v-if="formState.signIn">
                            <div class="rangeBox">
                                <div class="signInEnd">
                                    考试开场前
                                    <a-input-number :disabled="isDisabled" placeholder="请输入" :precision="0"
                                        :controls="false" id="inputNumber" v-model:value="formState.startMinute" :min="0"
                                        :max="60" />分钟，方可允许签到；考试开场
                                    <a-input-number :disabled="isDisabled" placeholder="请输入" :controls="false"
                                        :precision="0" id="inputNumber" v-model:value="formState.endMinute" :min="0"
                                        :max="60" />分钟后，不允许签到
                                </div>
                            </div>
                        </a-form-item-rest>
                    </a-form-item>
                    <a-form-item label="准考证号：" name="showed" :rules="[
                        {
                            required: formState.showed,
                            message: '请选择是否显示准考证号!',
                            required: true,
                        },
                    ]">
                        <a-radio-group class="signInRadio" :disabled="isDisabled" v-model:value="formState.showed"
                            name="showed">
                            <a-radio :value="false" style="padding-right: 80px">不显示&emsp;</a-radio>
                            <a-radio :value="true">显示</a-radio>
                        </a-radio-group>
                    </a-form-item>
                    <a-form-item label="考场纪律：" name="remark">
                        <a-textarea show-count :disabled="isDisabled" v-model:value="formState.remark" placeholder="请输入"
                            :rows="4" :maxlength="500" />
                    </a-form-item>
                </a-form>
            </div>
            <div class="timePlan" v-show="state.activityName === 'schedule'">
                <div class="examName">
                    当前考试名称：<span>{{ formState.name }}</span>
                </div>
                <div class="circulation">
                    <div class="circulation_item" v-for="(item, index) in state.groupList">
                        <div class="examinersBox">
                            <div class="examiners">考试人员</div>
                            <!-- <div
                                class="examinersSelect"
                                @click="showSelectOrigin(item)"
                            >
                                <div>{{item?.objectName}}</div>

                            </div> -->
                            <div class="objectNameBox">
                                <!-- {{item.objectName}} -->
                                <a-input
                                    :disabled="isDisabled"
                                    @click="showSelectOrigin(item)"
                                    style="width: 630px"
                                    v-model:value="item.objectName"
                                    placeholder="请选择"
                                />
                                <span v-if="!isDisabled">
                                    <img class="opt_img" src="/image/icon-append.png" @click="addobjectNameBox"
                                        v-if="index === 0" />
                                    <img class="opt_img" src="/image/icon-decrease.png" @click="delobjectNameBox(index)"
                                        v-else />
                                </span>
                            </div>
                        </div>
                        <div>
                            <div class="examiners">科目时间安排</div>
                            <div class="addSubTimeBtn">
                                <a-button type="primary" :disabled="isDisabled" @click="addSubject(item)">添加</a-button>
                            </div>

                            <!-- 科目时间安排的table列表 不分页了 -->
                            <div v-if="item.subjectList.length">
                                <!-- {{ item.subjectList }} -->
                                <a-table :pagination="false" :dataSource="item.subjectList" :columns="timeColumns">
                                    <template #bodyCell="{ column, record, index }">
                                        <template v-if="column.dataIndex === 'day'">
                                            {{
                                                record.examStartTime.split(
                                                    " "
                                                )[0]
                                            }}
                                        </template>
                                        <template v-if="column.dataIndex === 'time'">
                                            {{
                                                record.examStartTime.split(
                                                    " "
                                                )[1]
                                            }}
                                            -
                                            {{
                                                record.examEndTime.split(" ")[1]
                                            }}
                                        </template>
                                        <template v-if="column.dataIndex === 'operation'
                                                ">
                                            <a-button @click="
                                                editSub(item, record, index)
                                                " type="link" :disabled="isDisabled">编辑</a-button>
                                            <a-button type="link" :disabled="isDisabled" @click="
                                                delSublistItem(
                                                    item.subjectList,
                                                    index
                                                )
                                                ">删除</a-button>
                                        </template>
                                    </template>
                                </a-table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 这是添加科目的弹窗 -->
    <a-modal :confirm-loading="state.subLoading" :keyboard="false" :maskClosable="false" centered
        v-model:visible="state.subFormVisible" :title="state.addSub ? '添加科目时间' : '修改科目时间'" @ok="subHandleOk" width="538px"
        ok-text="确认" cancel-text="取消">
        <a-form ref="subFormRef" layout="vertical" :model="state.addSubjectObj" autocomplete="off">
            <a-form-item label="考试开始时间：" name="examStartTime" :rules="[
                {
                    required: true,
                    message: '请选择考试开始时间',
                },
            ]">
                <a-date-picker style="width: 100%" :show-time="{
                    hideDisabledOptions: true,
                    defaultValue: dayjs('08:00', 'HH:mm'),
                }" :allowClear="false" placeholder="请选择" v-model:value="state.addSubjectObj.examStartTime"
                    value-format="YYYY-MM-DD HH:mm:00" format="YYYY-MM-DD HH:mm:00">
                </a-date-picker>
            </a-form-item>
            <a-form-item label="考试结束时间：" name="examEndTime" :rules="[
                {
                    required: true,
                    message: '请选择考试结束时间',
                },
            ]">
                <a-date-picker style="width: 100%" :show-time="{
                    hideDisabledOptions: true,
                    defaultValue: dayjs('09:00', 'HH:mm'),
                }" :allowClear="false" placeholder="请选择" v-model:value="state.addSubjectObj.examEndTime"
                    value-format="YYYY-MM-DD HH:mm:00" format="YYYY-MM-DD HH:mm:00"></a-date-picker>
            </a-form-item>
            <a-form-item label="考试人员：" name="objectName">
                <a-input v-model:value="state.addSubjectObj.objectName" disabled />
            </a-form-item>
            <a-form-item label="考试科目：" name="basicsSubjectId" :rules="[
                {
                    required: true,
                    message: '请选择考试科目',
                },
            ]">
                <a-select @change="basicsSubjectIdChange" v-model:value="state.addSubjectObj.basicsSubjectId"
                    placeholder="请选择" :options="state.subjectOptions" :showArrow="true" :field-names="{
                        label: 'name',
                        value: 'id',
                    }">
                </a-select>
            </a-form-item>
        </a-form>
    </a-modal>

    <!-- 年级单选 年级下的班级可以多选 -->
    <ModelSelects :tabs="state.tabs" :selected="state.checkedList" />

</template>

<script setup name="examPlan">
/* eslint-disable */
import { onMounted, ref, computed, reactive } from "vue";
import dayjs from "dayjs";
import ModelSelects from "@/components/ModelSelects/index.vue";
import { useRoute } from "vue-router";
import {
    createExam,
    updateExamInfo,
    getSubList,
    updateExamSubject,
    getExamInfo,
    getExamSubject,
} from "@/api/exam.js";
import { getSchoolRollTree } from "@/api/faceLibrary.js";
import { useExamData } from "../useExamData.js";
import { message } from "ant-design-vue";
import { v4 as uuidv4 } from "uuid";


const route = useRoute();
const { formState, examinationId, resetState, createType } = useExamData();

const subFormRef = ref(null);
// 响应式数据
const state = reactive({
    addSub: true,
    checkedList: [],
    subjectOptions: [],
    groupList: [
        {
            id: uuidv4(),
            subjectList: [],
        },
    ],
    options: [],
    activityIndex: 0,
    activityName: "information",
    subFormVisible: false,
    timedataSource: [],
    addSubjectObj: {},
    tabs: [
        {
            tab: "选择考试人员(仅限年级或者仅限班级)1",
            id: 0,
            personField: {
                key: "rollValue",
                value: ["classes", "grade", "classesOrgrade"],
            },
            // 通通可以多选
            single: true,
            // 直接默认
            checked: true,
        },
    ],
});

// 学籍
let schoolRollTree = ref([]);

// 获取学籍树
const getSchoolAggregate = async () => {
    // 学籍树 为了选择年级班级
    await getSchoolRollTree("traffic").then(({ data }) => {
        schoolRollTree.value = data;
    });
};

// 计算属性判断是否为查看页面 禁用所有表单选项
const isDisabled = computed(() => route.query.type === "look");

// 禁用日期
const disabledDate = (current) => {
    return current && current < dayjs().subtract(1, "day");
};
const infoFormRef = ref();

const reqExamInfo = async () => {
    const { data } = await getExamInfo({
        id: route.query.examinationId || examinationId.value,
    });
    // Object.assign(formState, data);
    createType.value = data.createType;
    formState.name = data.name;
    formState.showed = data.showed;
    formState.signIn = data.signIn;
    formState.startTime = data.startTime;
    formState.endTime = data.endTime;
    formState.startMinute = data.startMinute;
    formState.endMinute = data.endMinute;
    formState.remark = data.remark;
};

// 自定义表单校验
const validaTime = (_rule, value) => {
    if (!formState.startTime || !formState.endTime) {
        return Promise.reject(new Error("请选择考试日期!"));
    }
    if (dayjs(formState.endTime).isBefore(dayjs(formState.startTime))) {
        return Promise.reject(new Error("考试结束日期不能提前于考试开始日期!"));
    }
    return Promise.resolve();
};

const timeColumns = [
    {
        title: "日期",
        key: "day",
        dataIndex: "day",
    },
    {
        title: "时间",
        key: "time",
        dataIndex: "time",
    },
    // {
    //     title: "考试人员",
    //     key: "objectName",
    //     dataIndex: "objectName",
    // },
    {
        title: "科目",
        key: "subjectName",
        dataIndex: "subjectName",
    },
    {
        title: "操作",
        key: "operation",
        dataIndex: "operation",
        width: 200,
    },
];

const planList = [
    {
        id: "1",
        name: "基本信息",
        value: "information",
    },
    {
        id: "2",
        name: "科目时间安排",
        value: "schedule",
    },
];

// 点击步骤条
// const stepItem = (index, item) => {
//     state.activityIndex = index;
//     state.activityName = item.value;
// };

const addSubject = (item) => {
    state.addSub = true;
    // 把item里的考试对象加上
    state.targetId = item.id;
    state.addSubjectObj = {};
    state.addSubjectObj.objectName = item.objectName;
    state.subFormVisible = true;
};

const editSub = (data, item, index) => {
    // 深拷贝
    const itemObj = JSON.parse(JSON.stringify(item));
    // 记录一下我编辑的id
    state.targetId = data.id;
    // 记录一下我编辑数据源下标
    state.tableIndex = index;

    state.addSub = false;

    state.addSubjectObj = itemObj;

    // state.addSubjectObj.examStartTime = itemObj.examStartTime;
    // state.addSubjectObj.examEndTime = itemObj.examEndTime;
    // state.addSubjectObj.objectName = itemObj.objectName;
    // state.addSubjectObj.basicsSubjectId = itemObj.basicsSubjectId;
    // state.addSubjectObj.subjectName = itemObj.subjectName;

    state.subFormVisible = true;
};

// 这里是科目时间安排确定的信息按钮
const subHandleOk = () => {
    subFormRef.value.validate().then(() => {
        // 将考试开始时间和结束时间转换为 Date 对象
        let endDate = new Date(state.addSubjectObj.examEndTime);
        let startDate = new Date(state.addSubjectObj.examStartTime);
        // 比较考试结束时间和开始时间的大小
       const findObj =  state.groupList.find((it)=>it.id === state.targetId)
        const resIndex = findObj?.subjectList.findIndex((item) => item.subjectName === state.addSubjectObj.subjectName)
        if(state.addSub && resIndex !== -1){
            message.warning("考试科目添加重复");
            return
        }
        if (endDate <= startDate) {
            message.warning("考试结束时间不能早于考试开始时间");
        } else {
            if (state.addSub) {
                state.groupList.forEach((item) => {
                    if (item.id === state.targetId) {
                        const nonReactiveObj = toRaw(state.addSubjectObj);
                        item.subjectList.push(nonReactiveObj);
                        state.subFormVisible = false;
                    }
                });
            } else {
                state.groupList.forEach((item) => {
                    if (item.id === state.targetId) {
                        const nonReactiveObjs = toRaw(state.addSubjectObj);
                        item.subjectList[state.tableIndex].examStartTime =
                            nonReactiveObjs.examStartTime;
                        item.subjectList[state.tableIndex].examEndTime =
                            nonReactiveObjs.examEndTime;
                        item.subjectList[state.tableIndex].objectName =
                            nonReactiveObjs.objectName;
                        item.subjectList[state.tableIndex].basicsSubjectId =
                            nonReactiveObjs.basicsSubjectId;
                        item.subjectList[state.tableIndex].subjectName =
                            nonReactiveObjs.subjectName;
                        state.subFormVisible = false;
                    }
                });
            }
        }
    });
};

// 新增一些基本信息
const addExamPlanInformation = () => {
    // 校验
    infoFormRef.value.validateFields().then(() => {
        let api = createExam;

        let reqObj = {
            ...formState,
            createType: createType.value,
        };
        // 如果是编辑考试信息
        if (route.query.examinationId || examinationId.value) {
            api = updateExamInfo;
            reqObj.id = route.query.examinationId || examinationId.value;
        }

        api(reqObj)
            .then((res) => {
                // 添加成功了那就直接往下一步走了往下一步走了
                examinationId.value = res.data; // 这里把考试id 存起来接着往下走了啊啊啊
                state.activityIndex = 1;
                state.activityName = "schedule";

                // 如果往下走了 进入了 科目时间安排 其实还是得看看是不是编辑 是编辑的话 得再查一个详情出来回显一下该死的数据
                if (route.query.examinationId || examinationId.value) {
                    getExamSubject({
                        id: route.query.examinationId || examinationId.value,
                    }).then((res) => {
                        const { data } = res;
                        if (data.groupList.length) {
                            state.groupList = data.groupList.map((item) => {
                                let objectIds = item.objectIds.split(",");
                                let objectNames = item.objectName.split(",");
                                let checked = [];
                                for (let i = 0; i < objectIds.length; i++) {
                                    let obj = {
                                        id: objectIds[i],
                                        name: objectNames[i],
                                        rollValue:
                                            item.type === 1
                                                ? "grade"
                                                : "classes",
                                    };
                                    checked.push(obj);
                                }
                                return {
                                    ...item,
                                    checked,
                                };
                            });
                        } else {
                            state.groupList = [
                                {
                                    id: uuidv4(),
                                    subjectList: [],
                                },
                            ];
                        }
                    });
                }
            })
            .catch((err) => {
                console.log(err);
            })
            .finally(() => { });
    });
};

// 这是新增科目安排信息的接口那些的
const addExamPlanSchedule = () => {
    let result = state.groupList.every(
        (obj) => !!obj.objectIds !== false && obj.subjectList.length > 0
    );
    return new Promise((resolve, reject) => {
        if (result) {
            const cloneList = JSON.parse(JSON.stringify(state.groupList));
            updateExamSubject({
                examinationId: examinationId.value,
                groupList: cloneList.map((obj) => {
                    // if(obj.id)
                    // 如果是uuid id里有 - 的直接给他删了 后端不要
                    if (obj?.id.indexOf("-") != -1) {
                        delete obj.id;
                    }
                    // const { id, ...rest } = obj;
                    return obj;
                }),
            })
                .then((res) => {
                    resolve(res);
                })
                .catch((error) => {
                    reject(error);
                });
        } else {
            message.warning("请选择考试人员并添加科目时间安排");
            reject({
                message: "请选择考试人员并添加科目时间安排",
                data: false,
            });
        }
    });
};

// 页面加载的时候把科目信息也直接获取吧
const reqSubList = () => {
    getSubList().then((res) => {
        state.subjectOptions = res.data || [];
    });
};

// 这是科目选择事件 后端需要 name 那就给他
const basicsSubjectIdChange = (value, option) => {
    state.addSubjectObj.subjectName = option.name;
};

// ----------------- start 选年级班级框逻辑 ---------------
const modelState = reactive({
    spinning: false,
    isaAuthority: false, // 用于是否有权限控制选框是否可选
    openVisible: false, // 显示弹框
    oldDataSource: [], // 左侧数据源 用于返回赋值数据
    dataSource: [], // 左侧数据源
    personnelsParams: {}, // 调选人接口要传的参数
    personnels: [], // 人员数据源
    selectedData: [], // 已选中的数据
    checkVisible: "class", // dept 是否选部门, people 是否人员
    searchTable: [], // 选人搜索 table 中显示
    globalID: "", // 最顶成id
    currentDepartID: "", // 当前部门id
    sourchName: "", // sourchName 搜索名称
    isPpresent: false, // 如有人员数据 则为true(用于滚动加载人员数据)
    pages: {
        //分页器
        pageNo: 1,
        pageSize: 10,
        total: 0,
        size: "small",
    },
});

// 设置选人数据源
function setDataSource(data) {
    modelState.dataSource = data || [];
}

// 判断身份有权限选部门
function isaAuthorityFn(data) {
    // let authorityNum = 0
    // if (data.length) {
    //     data.forEach(v => {
    //         if (v.isaAuthority) authorityNum++
    //     })
    //     modelState.isaAuthority = authorityNum == !data.length
    // }
}

// const statusList = computed(() => store.state.selectSource.dictionary.emp_status_yes_id);
// 获取人员接口
function getUserPageInfo(callback) {
    // const { currentDepartID, pages, sourchName } = modelState
    // const params = {
    //     deptId: currentDepartID,
    //     name: sourchName,
    //     statusList: statusList.value,
    //     code: codes,
    //     ...pages
    // }
    // getDepartmentPersonnel(params).then(({ data }) => {
    //     const { list, total, pageNo, pageSize } = data
    //     modelState.pages.pageNo = pageNo
    //     modelState.pages.pageSize = pageSize
    //     modelState.pages.total = total
    //     modelState.isPpresent = (pageNo * pageSize) < total
    //     callback(list)
    // }).finally(() => {
    //     modelState.spinning = false
    // })
}

// 滚动加载数据
// const onScroll = debounce(() => {
//     const callback = (data) => {
//         modelState.dataSource = modelState.dataSource?.concat(data)
//     }
//     getUserPageInfo(callback)
// }, 1500)

// 查找教职工
function searchSelect(tabId, name) {
    // if (name) {
    //     // 选人组件 - 首次聚焦教职工
    //     searchStaff(name)
    // } else {
    //     // name为空时，不发送请求，恢复最原始数据
    //     !tabId ? setDataSource(state.staffList) : setDataSource(state.studentList)
    // }
}

// 搜索人员
function searchStaff(name) {
    // modelState.spinning = true
    // modelState.sourchName = name
    // modelState.currentDepartID = modelState.globalID
    // const callback = (data) => {
    //     modelState.searchTable = data
    // }
    // getUserPageInfo(callback)
}

// 点击下一步查找教职工数据
function getStaffPage(item) {
    // 如果点击到年级的下一级了  就是班级 那么班级就是多选啊
    if (item.rollValue === "grade") {
        state.tabs = [
            {
                tab: "选择考试人员(仅限年级或者仅限班级)",
                id: 0,
                // legacy_personKey: 'dept',
                personField: {
                    key: "rollValue",
                    value: ["classes", "grade", "classesOrgrade"],
                },
                // 通通可以多选
                single: false,
                // 直接默认
                checked: true,
            },
        ];
    } else {
        state.tabs = [
            {
                tab: "选择考试人员(仅限年级或者仅限班级)",
                id: 0,
                // legacy_personKey: 'dept',
                personField: {
                    key: "rollValue",
                    value: ["classes", "grade", "classesOrgrade"],
                },
                // 通通可以多选
                single: true,
                // 直接默认
                checked: true,
            },
        ];
    }
    const { id, children, type } = item;
    modelState.spinning = true;
    modelState.currentDepartID = item.id;
    const callback = (data) => {
        let childrens = children || [];
        modelState.dataSource = childrens?.concat(data);
    };
    modelState.dataSource = children;
    modelState.spinning = false;

    // 特需处理
    // 如果是学籍查找学生 只有在班级中查学生
    // if (state.toggleTabsType == 2, type == 4) {
    //     getUserPageInfo(callback)
    // } else {
    //     modelState.dataSource = children
    //     modelState.spinning = false
    // }
    // // 查老师
    // if (state.toggleTabsType == 1) {
    //     getUserPageInfo(callback)
    // }
}

// 请求下一级数据
function toggleLevel(tabId, item = {}, options) {
    const { index, trigger } = options;
    const firstLevel = !index;
    // 重置分页及搜索
    modelState.isPpresent = false;
    modelState.sourchName = "";
    modelState.pages.pageNo = 1;
    modelState.pages.pageSize = 10;
    modelState.pages.total = 0;
    if (firstLevel) {
        let newDataSource = schoolRollTree.value;
        isaAuthorityFn(newDataSource);
        // 第一层数据，恢复原本数据
        modelState.dataSource = newDataSource;
    } else {
        modelState.isaAuthority = false;
        getStaffPage(item);
    }
}

// 取消
function cancel() {
    // state.breadCrumb = []
}

function checkDuplicates(arr, classArr) {
  const duplicateNames = classArr
    .filter(classItem => arr.some(arrItem => arrItem?.objectName?.includes(classItem.name)))
    .map(classItem => classItem.name);

  if (duplicateNames.length > 0) {
      message.warning(`请注意添加的考试人员: ${duplicateNames.join(', ')} 重复`,3);
  } else {
    console.log('没有重复添加的考试人员');
  }
}

// 提交
function submit(checked) {
    // 判断 rollValue 是否只存在一种值
    let isSingleValue = checked.every(
        (item) => item.rollValue === checked[0].rollValue
    );
    // 过滤出相应的项
    let filteredItems = [];
    if (isSingleValue) {
        // 如果只存在一种值，根据该值进行过滤
        filteredItems = checked.filter(
            (item) => item.rollValue === checked[0].rollValue
        );
    } else {
        // 如果存在两种值，只过滤 "classes" 的项
        filteredItems = checked.filter((item) => item.rollValue === "classes");
    }

    // let targetObject = state.groupList.find((obj) => obj.id === state.targetId);

    // 找到目标对象进行操作
    // state.groupList.forEach((item) => {
    //     if (item.id === state.targetId) {
    //         item.objectIds = '1'
    //         item.objectName = "二年级"
    //         item.type = '1'
    //     }
    // })

    checkDuplicates(state.groupList, filteredItems);

    // ;

    // 找到目标对象进行操作
    state.groupList.forEach((item) => {
        if (item.id === state.targetId) {
            item.checked = checked; // 回显
            item.objectIds = filteredItems.map((item) => item.id).join(",");
            item.objectName = filteredItems.map((item) => item.name).join(",");
            item.type = filteredItems[0]?.rollValue === "grade" ? 1 : 2;
        }
    });

    // 提交的时候做一些操作

    // const userIds = []
    // const userName = []
    // checked.forEach(v => {
    //     userName.push(v.name)
    //     userIds.push(v.id)
    // })
    // state.juData.userIds = userIds
    // state.juData.userName = userName
    // state.checkedList = checked
}

provide("modelState", () => modelState);

// TODO：一些功能函数
provide("callbackFunction", () => ({
    search: searchSelect,
    // onScroll,
    toggleLevel,
    submit,
    cancel,
}));

// ----------------- end 选年级班级框逻辑 ---------------

// 开启选人框
const showSelectOrigin = (data) => {
    // 把item里的考试对象加上
    state.targetId = data.id;
    modelState.dataSource = schoolRollTree.value;
    modelState.globalID = schoolRollTree.value[0].id;
    state.checkedList = data.checked || [];
    modelState.openVisible = true;
};

// 加对象
const addobjectNameBox = () => {
    state.groupList.push({
        id: uuidv4(),
        subjectList: [],
    });
};

const delobjectNameBox = (index) => {
    state.groupList.splice(index, 1);
};

const delSublistItem = (data, index) => {
    data.splice(index, 1);
};

const goLastStep = () => {
    state.activityIndex = 0;
    state.activityName = "information";
};

// 暴露方法和属性给父组件
defineExpose({
    state,
    formState,
    infoFormRef,
    goLastStep,
    addExamPlanInformation,
    addExamPlanSchedule,
});

onMounted(() => {
    getSchoolAggregate();
    reqSubList();
    // 判断一下是不是编辑,用id获取一下之前的表单详情
    if (route.query.examinationId || examinationId.value) {
        reqExamInfo();
    } else {
        resetState();
    }
});
</script>

<style lang="less" scoped>
.examPlan {
    height: 100%;
    display: flex;

    .examPlan_left {
        padding-right: 14px;
        border-right: 1px solid #e6e6e6;
        height: 100%;
        width: 200px;

        .plan_step_box {
            border: 1px solid #d9d9d9;

            .plan_step {
                // cursor: pointer;
                padding: 9px 12px;
                display: flex;
                align-items: center;
            }

            .activity_plan_step {
                border-left: 2px solid #00b781;
                background: #fafafa;
            }
        }
    }

    .examPlan_right {
        flex: 1;
        padding-left: 24px;
    }
}

.yuan_icon {
    height: 22px;
    width: 22px;
    line-height: 20px;
    border-radius: 11px;
    color: #8c8c8c;
    border: 1px solid #8c8c8c;
    vertical-align: middle;
    text-align: center;
    margin-right: 8px;
}

.activity_yuan_icon {
    color: #00b781;
    border: 1px solid #00b781;
}

.plan_title {
    font-size: 16px;
    font-weight: 500;
    color: #8c8c8c;
}

.activity_plan_title {
    color: #00b781;
}

.examName {
    font-size: 16px;
    font-weight: 600;
    // padding-bottom: 16px;
    color: rgba(0, 0, 0, 0.85);
}

.rangeBox {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
}

.signInRadio {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);

    // padding-bottom: 16px;
    .ant-radio-wrapper {
        color: rgba(0, 0, 0, 0.65);
    }
}

.examinersBox {
    padding-top: 24px;
    padding-bottom: 24px;
}

.examiners {
    font-size: 14px;
    font-weight: 400;
    padding-bottom: 12px;
    color: rgba(0, 0, 0, 0.65);

    &&::before {
        display: inline-block;
        margin-right: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: "*";
    }
}

.addSubTimeBtn {
    padding-bottom: 16px;
}

.examinersSelect {
    width: 630px;
    padding: 4px 11px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    min-height: 32px;
    cursor: text;

    &:hover {
        border-color: #00b781;
    }
}

.objectNameBox {
    display: flex;
    align-items: center;
}

.opt_img {
    margin-left: 8px;
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.basicInfoBox {
    width: 600px;
}

.circulation_item {
    padding-bottom: 24px;
    border-bottom: 1px dashed #d9d9d9;
}

.signInEnd {
    padding-top: 16px;
}

.basicInfoBox :deep(.ant-form .ant-form-item:nth-child(2)) {
    margin-bottom: 0px;
}
</style>
