export function nameJoint(sourceData) {
    // 把源数据先变成目标数据的规则
    const oldDataRule = []
    sourceData.forEach((el) => {
        const oldObj = {
            name: el.subjectName,
            person: []
        }
        const personObj = {
            person_name: el.personName,
            person_id: el.personId
        }
        oldObj.person.push(personObj)
        oldDataRule.push(oldObj)
    })

    /**
     * 先去重，后合并
     * 1、源数据去重
     * 2、把去重后的数据和源数据中相同name的数据合并person
     */
    const newData = []
    const newObj = {}
    oldDataRule.forEach((el, i) => {
        if (!newObj[el.name]) {
            newData.push(el)
            newObj[el.name] = true
        } else {
            newData.forEach((el) => {
                if (el.name === oldDataRule[i].name) {
                    el.person = el.person.concat(oldDataRule[i].person)
                    // el.person = [...el.person, ...oldDataRule[i].person]; // es6语法
                }
            })
        }
    })
    const nameJoin = newData
        .map((item) => {
            const nameMap = item.person.map((k) => k.person_name)
            return `${item.name}:${nameMap.join('、')}`
        })
        .join(';')

    return nameJoin
}
