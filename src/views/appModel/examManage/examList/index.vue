<!-- 行行行 -->
<!-- 防御性代码当如是 -->
<template>
    <div class="mainPage">
        <div class="filterPart">
            <a-form layout="inline">
                <a-form-item label="考试名称：">
                    <a-input
                        v-model:value="state.name"
                        placeholder="请输入"
                        style="width: 206px"
                    />
                </a-form-item>
                <a-form-item label="考试人员：">
                    <a-input
                        v-model:value="state.objectName"
                        placeholder="请输入"
                        style="width: 206px"
                    />
                </a-form-item>
                <a-form-item label="发布状态：">
                    <a-select
                        :getPopupContainer="
                            (triggerNode) => triggerNode.parentNode
                        "
                        style="width: 206px"
                        v-model:value="state.publishStatus"
                        placeholder="请选择"
                        :showArrow="true"
                    >
                        <a-select-option :value="1">已发布</a-select-option>
                        <a-select-option :value="2">未发布</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="考试状态：">
                    <a-select
                        :getPopupContainer="
                            (triggerNode) => triggerNode.parentNode
                        "
                        style="width: 206px"
                        v-model:value="state.examStatus"
                        placeholder="请选择"
                        :showArrow="true"
                    >
                        <a-select-option :value="0">未开始</a-select-option>
                        <a-select-option :value="1">进行中</a-select-option>
                        <a-select-option :value="2">已结束</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item>
                    <a-button
                        @click="inquire"
                        type="primary"
                        style="margin-right: 12px"
                    >
                        <template #icon>
                            <SearchOutlined style="margin-right: 4px" />
                        </template>
                        查询
                    </a-button>
                    <a-button @click="resetPage">
                        <template #icon>
                            <redo-outlined style="margin-right: 4px" />
                        </template>
                        重置
                    </a-button>
                </a-form-item>
            </a-form>
        </div>
        <div class="btnArea">
            <a-button
                style="margin-right: 12px"
                @click="addLarge"
                type="primary"
                ><template #icon>
                    <plus-outlined style="margin-right: 8px" /> </template
                >新增大型考试</a-button
            >
            <a-button
                @click="addSmall"
                style="margin-right: 12px"
                type="primary"
                ><template #icon>
                    <plus-outlined style="margin-right: 8px" /> </template
                >新增小型考试</a-button
            >
            <a-button
                type="primary"
                danger
                ghost
                @click="deleteExam"
                :disabled="!state.keys.length"
                >删除</a-button
            >
        </div>
        <div class="roomTable">
            <Tables
                :isRadio="false"
                @selectChange="examSelectChange"
                @change="tableChange"
                :selectedRowKeys="state.keys"
                :rowSelection="true"
                :spinning="state.spinning"
                :paginations="state.paginations"
                :columns="examColumns"
                :dataSource="state.tableSource"
                :bordered="false"
            >
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'publishStatus'">
                        <div>
                            <span
                                :class="{
                                    'nopublished': true,
                                    'havepublished': record.publishStatus,
                                }"
                                >{{
                                    record.publishStatus ? "已发布" : "未发布"
                                }}</span
                            >
                            <a-switch
                                :disabled="record.publishStatus"
                                @change="checkedChange(record)"
                                v-model:checked="record.publishStatus"
                            />
                        </div>
                    </template>
                    <template v-if="column.dataIndex === 'examStatus'">
                        <span
                            :class="{
                                'examStatusBox': true,
                                'inexamStatusBox': record.examStatus === 1,
                            }"
                            >{{ getexamStatusObj(record.examStatus) }}</span
                        >
                    </template>
                    <template v-if="column.dataIndex === 'operation'">
                        <a-button
                            type="link"
                            :disabled="record.publishStatus"
                            @click="compileExam(record)"
                            >编辑</a-button
                        >
                        <a-button @click="lookExam(record)" type="link"
                            >查看</a-button
                        >
                        <a-button @click="deExam(record.id)" type="link"
                            >删除</a-button
                        >
                        <a-button type="link" @click="affair(record.id)"
                            >考务管理</a-button
                        >
                        <a-button type="link" @click="signTable(record)"
                            >签到表</a-button
                        >
                    </template>
                </template>
            </Tables>
        </div>
    </div>
</template>

<script setup>
import { reactive, onMounted } from "vue";
import {
    getExamPageList,
    delExam,
    deleteBatch,
    publishExam,
} from "@/api/exam.js";
import { message, Modal } from "ant-design-vue";
import { useRouter } from "vue-router";
import Tables from "@/components/Tables";
import { useExamData } from "../useExamData.js";
const { examinationId, createType } = useExamData();
const router = useRouter();

function getexamStatusObj(key) {
    const examStatusObj = {
        0: "未开始",
        1: "进行中",
        2: "已结束",
        default: "未知状态", // 默认值存储在 default 属性中
    };
    return examStatusObj[key] || examStatusObj.default;
}

// 考试列表的table 表头数据
const examColumns = [
    {
        title: "序号",
        key: "index",
        dataIndex: "index",
        customRender: ({ text, record, index }) => `${index + 1}`, // 显示每一行的序号
    },
    {
        title: "考试名称",
        key: "name",
        dataIndex: "name",
    },
    // {
    //     title: "考试年级",
    //     key: "gradeName",
    //     dataIndex: "gradeName",
    // },
    {
        title: "考试人员",
        key: "classesName",
        dataIndex: "classesName",
    },
    {
        title: "开始时间",
        key: "startTime",
        dataIndex: "startTime",
    },
    {
        title: "结束时间",
        key: "endTime",
        dataIndex: "endTime",
    },
    {
        title: "发布状态",
        key: "publishStatus",
        dataIndex: "publishStatus",
    },
    {
        title: "考试状态",
        key: "examStatus",
        dataIndex: "examStatus",
    },
    {
        title: "操作",
        align: "left",
        width: 300,
        key: "operation",
        fixed: "right",
        dataIndex: "operation",
    },
];

// 响应式数据
const state = reactive({
    spinning: false,
    keys: [],
    tableSource: [],
    paginations: {
        pageNo: 1, // 页码
        pageSize: 10, // 条数
        total: 0,
    },
});

// 发布考试的按钮事件
const checkedChange = (item) => {
    Modal.confirm({
        title: "提示",
        type: "warning",
        content: "发布考试后不可编辑，是否确认发布？",
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        onOk() {
            publishExam({ id: item.id })
                .then(() => {
                    message.warning("发布考试成功!");
                })
                .catch(() => {
                    // 有报错也关了
                    item.publishStatus = false;
                });
        },
        onCancel() {
            item.publishStatus = false;
            message.warning("已取消!");
        },
    });
};

// 增加大型考试
const addLarge = () => {
    createType.value = 1;
    examinationId.value = "";
    router.push({
        path: "/appModel/examManage/append",
        query: {
            type: "append",
            createType: "1",
        },
    });
};

// 增加小型考试
const addSmall = () => {
    createType.value = 2;
    examinationId.value = "";
    router.push({
        path: "/appModel/examManage/append",
        query: {
            type: "append",
            createType: "2",
        },
    });
};

// 考务管理
const affair = (dataid) => {
    router.push({
        path: "/appModel/examManage/affair",
        query: {
            id: dataid,
        },
    });
};

// 查看签到表
const signTable = (data) => {
    router.push({
        path: "/appModel/examManage/signTable",
        query: {
            id: data.id,
            name: data.name,
        },
    });
};

// 获取整个考试的的列表数据
const reqExamPageList = () => {
    state.spinning = true;
    getExamPageList({
        ...state.paginations,
        examStatus: state.examStatus,
        name: state.name,
        publishStatus: state.publishStatus,
        objectName: state.objectName,
    })
        .then((res) => {
            state.tableSource = res.data.list;
            state.paginations.total = res.data.total;
            state.spinning = false;
        })
        .catch((e) => {
            console.log("catch", e);
        })
        .finally(() => {
            state.spinning = false;
        });
};

// 这是查询考试列表按钮
const inquire = () => {
    state.paginations.pageNo = 1;
    reqExamPageList();
};

// 重置
const resetPage = () => {
    state.paginations.pageNo = 1;
    state.paginations.pageSize = 10;
    state.name = null;
    state.objectName = null;
    state.publishStatus = null;
    state.examStatus = null;
    reqExamPageList();
};

// 单个删除
const deExam = (id) => {
    Modal.confirm({
        title: "删除",
        type: "warning",
        content: "确定删除该条考试?",
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        onOk: async () => {
            await delExam({ id });
            message.success("删除成功！");
            await reqExamPageList();
        },
        onCancel() {
            message.warning("已取消!");
        },
    });
};

// 编辑考试
const compileExam = (data) => {
    Modal.confirm({
        title: "提示",
        type: "warning",
        content: "为确保考试信息的无误性，进行编辑时请确保每一步都完整提交哦",
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        onOk() {
            examinationId.value = data.id; // 这里把考试id 存起来接着往下走
            createType.value = data.createType;
            router.push({
                path: "/appModel/examManage/append",
                query: {
                    type: "edit",
                    examinationId: data.id,
                },
            });
        },
        onCancel() {
            // message.warning("已取消!");
        },
    });
};

// 查看考试
const lookExam = (data) => {
    examinationId.value = data.id; // 这里把考试id 存起来接着往下走
    createType.value = data.createType;
    router.push({
        path: "/appModel/examManage/append",
        query: {
            type: "look",
            examinationId: data.id,
        },
    });
};

// 批量勾选删除
const deleteExam = () => {
    Modal.confirm({
        title: "删除",
        type: "warning",
        content: "确定删除该条考试?",
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        onOk: async () => {
            await deleteBatch({ ids: state.keys });
            message.success("删除成功！");
            await reqExamPageList();
            state.keys = [];
        },
        onCancel() {
            message.warning("已取消!");
        },
    });
};

// 这个是table的勾选事件
const examSelectChange = (data) => {
    state.keys = data.map((item) => item.id);
};

// table翻页
const tableChange = (data) => {
    state.paginations = data;
    reqExamPageList();
}

onMounted(() => {
    reqExamPageList();
});
</script>

<style lang="less" scoped>
.mainPage {
    padding: 16px;
    .filterPart {
        display: flex;
        justify-content: space-between;
        padding-bottom: 16px;
    }
}
.isdis {
    color: rgba(0, 0, 0, 0.25) !important;
}

.btnArea {
    text-align: end;
    padding-bottom: 20px;
}

.examStatusBox {
    color: #595959;
}
.inexamStatusBox {
    color: #00b781;
}

.nopublished {
    padding-right: 6px;
    color: #f5222d;
}
.havepublished {
    color: #00b781;
}


.roomTable :deep(.ant-btn-link) {
    // color: @primary-color;
    padding: 0;
    // margin-left: 10px;
    margin-right: 12px;
  }

</style>
