<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-06-07 16:16:02
 * @LastEditors: jingrou
 * @LastEditTime: 2022-08-10 19:01:41
-->
<template>
    <a-modal :maskClosable="false" v-model:visible="visible" title="移动视频">
        <template #footer>
            <a-button key="back" @click="visible = false">取消</a-button>
            <a-button
                key="submit"
                type="primary"
                :loading="state.btnLoading"
                @click="moveAlbum"
                >确定</a-button
            >
        </template>
        <a-form layout="vertical">
            <a-form-item label="移动至：" name="name">
                <!-- @change="handlechange" -->
                <a-select v-model:value="state.classifyId">
                    <a-select-option
                        v-for="item in state.classifiyList"
                        :key="item.id"
                        :value="item.id"
                        >{{ item.name }}</a-select-option
                    >
                </a-select>
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<script setup>
import { reactive, ref, watch } from 'vue'
import { updateAlbumClassify } from '@/api/album'
import { message, Modal } from 'ant-design-vue'

const props = defineProps({
    classifyId: {
        type: String,
        default: ''
    },
    classifiyList: {
        type: Array,
        default: () => []
    },
    checkedAlbum: {
        type: Array,
        default: () => []
    }
})
const emit = defineEmits(['handelOk', 'update:classifyId'])
const state = reactive({
    btnLoading: false,
    classifiyList: null
})
const visible = ref(false)
const moveAlbum = () => {
    const data = {
        ids: props.checkedAlbum,
        classifyId: state.classifyId
    }
    state.btnLoading = true
    updateAlbumClassify(data)
        .then((res) => {
            message.success(res.message)
            emit('handelOk')
        })
        .finally(() => {
            visible.value = false
            state.btnLoading = false
        })
}
const handlechange = (v) => {
    emit('update:classifyId', v)
}
defineExpose({
    visible
})
watch(
    () => props.classifyId,
    (v) => {
        state.classifyId = v
    }
)
watch(
    () => props.classifiyList,
    (v) => {
        state.classifiyList = v
    }
)
</script>
