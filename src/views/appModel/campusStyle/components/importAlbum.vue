<template>
    <a-modal
        v-model:visible="modalVisible"
        title="上传图片"
        :width="890"
        :maskClosable="false"
        @ok="handleOk"
        @cancel="handelCancel"
    >
        <template #footer>
            <a-button @click="handelCancel">取消</a-button>
            <a-button
                @click="handleOk"
                :loading="state.btnLoading"
                :disabled="!state.imageList.length"
                type="primary"
                >确认</a-button
            >
        </template>
        <div class="con">
            <a-upload
                :file-list="state.fileList"
                :showUploadList="false"
                accept="image/png,image/jpg,image/jpeg"
                action="/"
                :before-upload="beforeUpload"
                :multiple="true"
            >
                <a-button class="btn">
                    <template #icon>
                        <plus-outlined />
                    </template>
                    添加图片
                </a-button>
            </a-upload>
            <p>
                <!-- 最多可上传20张图片，已添加{{
                    state.imageList.length
                }}张，还可添加{{
                    20 - state.imageList.length > 0
                        ? 20 - state.imageList.length
                        : 0
                }}张 -->
                已添加{{ state.imageList.length }}张
            </p>
            <div class="list">
                <div
                    class="list_item"
                    v-for="(item, index) in state.imageList"
                    :key="index"
                >
                    <img :src="item.url" alt="" />
                    <div class="del" @click="removeUpload(index)">×</div>
                </div>
            </div>
        </div>
    </a-modal>
</template>

<script setup>
import { reactive, ref } from 'vue'
import api from '@/api'
import { message } from 'ant-design-vue'
import { ImportOutlined } from '@ant-design/icons-vue'
import { uploadFiles, albumCreate } from '@/api/album'
import { imgCompress } from '@/utils/util'
const modalVisible = ref(false)
const emit = defineEmits(['handleOk'])
const props = defineProps({
    classifyId: {
        type: String,
        default: ''
    },
    classesId: {
        type: String,
        default: ''
    },
    kind: {
        type: String,
        default: '1'
    }
})
const state = reactive({
    fileList: [],
    videoUrl: '',
    imageList: [],
    btnLoading: false,
    action: `${import.meta.env.VITE_BASE_API}${api.fileUpload}`
})

const getBase64 = (file) => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => resolve(reader.result)
        reader.onerror = (error) => reject(error)
    })
}

const beforeUpload = async(file) => {
    const { size, name, uid } = file
    const isLt1M = size / 1024 / 1024 < 10
    if (size / 1024 / 1024 > 9 && isLt1M) {
        const imgs = await imgCompress(file)
        file = imgs
    }
    if (!isLt1M) {
        message.warning('上传图像须小于10MB！')
    } else {
        const url = await getBase64(file)
        state.imageList.push({
            name,
            file,
            url,
            uid,
            path: null,
            errorMsg: ''
        })
        state.fileList.push(file)
    }
    return false
}

const handelCancel = () => {
    state.fileList = []
    state.imageList = []
    modalVisible.value = false
}
const removeUpload = (index) => {
    state.imageList.splice(index, 1)
    state.fileList.splice(index, 1)
}
const handleOk = async() => {
    // if (state.imageList.length > 20) {
    //     message.error("最多只能添加20张");
    //     return;
    // }
    try {
        state.btnLoading = true
        const urls = []
        for (let i = 0; i < state.imageList.length; i++) {
            const file = state.imageList[i]
            const formData = new FormData()
            formData.append('file', file.file)
            const { data } = await uploadFiles(formData)
            data.forEach((item) => {
                urls.push(item.url)
                return item.url
            })
        }

        const params = {
            classifyId: props.classifyId,
            classesId: props.classesId,
            urls,
            type: 1,
            kind: props.kind
        }
        const res1 = await albumCreate(params)
        message.success(res1.message)
        modalVisible.value = false
        state.btnLoading = false
        emit('handleOk')
    } catch (err) {
        state.btnLoading = false
        message.error(err.data.message)
    }
}
defineExpose({
    modalVisible,
    state
})
</script>

<style lang="less" scoped>
// :deep(.ant-modal-body) {
//     max-height: calc(100vh - 300px) !important;
//     overflow: hidden auto !important;
// }
.con {
    min-height: 228px;
    text-align: center;
    border: 1px dashed @border-color-base;
    .btn {
        height: 32px;
        color: @primary-color;
        margin: 24px 0 24px;
        background: @acitve-background;
        border: 1px solid @primary-color;
        border-radius: 4px;
    }
    .list {
        display: flex;
        flex-wrap: wrap;
        margin-top: 20px;

        // justify-content: ;
        &_item {
            width: 100px;
            height: 100px;
            margin: 10px 10px;
            position: relative;
            img {
                width: 100%;
                height: 100%;
            }
            .del {
                display: none;
                width: 20px;
                height: 20px;
                line-height: 20px;
                color: @body-background;
                position: absolute;
                top: -10px;
                right: -10px;
                cursor: pointer;
                background: @error-color;
                border-radius: 50%;
            }
            &:hover .del {
                display: block;
            }
        }
    }
}
</style>
