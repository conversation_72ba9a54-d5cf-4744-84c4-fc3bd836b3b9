<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-04-18 16:28:16
 * @LastEditors: 舒志建 <EMAIL>
 * @LastEditTime: 2023-06-25 15:48:33
-->
<template>
    <!-- 视频相册 -->
    <div class="videoAlbum">
        <div class="left">
            <a-spin :spinning="state.leftSpinLoad">
                <div style="margin-bottom: 10px; text-align: center">
                    <a-button
                        type="primary"
                        @click="addEditClassifiy(false, {})"
                    >
                        <template #icon>
                            <plus-outlined />
                        </template>
                        新建相册
                    </a-button>
                </div>
                <DempTree
                    ref="dempTree"
                    :treeData="state.classifiyList"
                    :fieldNames="fieldNames"
                    @emitSelect="selectSemester"
                    :selectedKeys="state.selectedKeys"
                    :isShowEmpty="false"
                >
                    <template #treeIcon>
                        <span class="tree_icon">
                            <img
                                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAMCAYAAABSgIzaAAAAAXNSR0IArs4c6QAAAIVJREFUKFNj/L+A4RYDA4MqAyr4wsDMYMIYy3ATTRzOZfy/gOE/Dsm7DAwMz3HI3YZoVIxmYJB0wmU4qvjzfQwM95cyMP5fz/efQTuXgYFHnjiNXx4yMFydzMD4/6T6fwZGRuI0wVT9/8/A+P+UBi4/4jVsVCOe4AEFzn0GBgYF0uKD4QEA2DoyATFp0w8AAAAASUVORK5CYII="
                                alt
                            />
                        </span>
                    </template>
                    <template #handleItem="{ handleItem }">
                        <a-dropdown :trigger="['hover']">
                            <a
                                class="ant-dropdown-link handle_icon"
                                style="margin-right: 5px"
                                @click.prevent.stop
                            >
                                <more-outlined
                                    v-if="handleItem.name !== '未分组'"
                                />
                            </a>
                            <template #overlay>
                                <a-menu>
                                    <a-menu-item
                                        @click="
                                            addEditClassifiy(true, handleItem)
                                        "
                                    >
                                        <a-button type="text">编辑</a-button>
                                    </a-menu-item>
                                    <a-menu-item
                                        @click="delClassifiy(handleItem)"
                                    >
                                        <a-button type="link" danger
                                            >删除</a-button
                                        >
                                    </a-menu-item>
                                </a-menu>
                            </template>
                        </a-dropdown>
                    </template>
                </DempTree>
            </a-spin>
        </div>
        <div class="right">
            <a-spin :spinning="state.rightSpinLoad">
                <div v-if="state.classifiyList.length > 0">
                    <div class="header">
                        <div v-if="!state.editFlag">
                            是否展示在班牌
                            <a-switch
                                :checked="state.isShow"
                                @change="changeStatus"
                            ></a-switch>
                        </div>
                        <div v-if="!state.editFlag">
                            <a-button
                                type="primary"
                                @click="state.editFlag = !state.editFlag"
                                >编辑</a-button
                            >
                        </div>
                        <div v-if="state.editFlag">
                            <a-checkbox
                                v-model:checked="state.checkAll"
                                :indeterminate="state.indeterminate"
                                @change="onCheckAllChange"
                                >全选</a-checkbox
                            >
                            <span>已选择{{ state.checkedVideo.length }}个</span>
                        </div>
                        <div class="btn_list" v-if="state.editFlag">
                            <a-button
                                @click="del"
                                :disabled="!state.checkedVideo.length"
                                danger
                                >删除</a-button
                            >
                            <a-button
                                @click="moveAlbumItem"
                                :disabled="!state.checkedVideo.length"
                                >移动</a-button
                            >
                            <a-button @click="cancalEdit">取消</a-button>
                        </div>
                    </div>
                    <div class="list" v-if="!state.editFlag">
                        <div class="list_item" @click="openUpload">
                            <p>+</p>
                            <p>上传图片</p>
                        </div>
                        <a-image
                            v-for="item in state.albumList"
                            :key="item.id"
                            :src="item.url"
                        />
                    </div>
                    <div class="list" v-if="state.editFlag">
                        <a-checkbox-group v-model:value="state.checkedVideo">
                            <div class="list_item1" @click="openUpload">
                                <p>+</p>
                                <p>上传图片</p>
                            </div>
                            <a-checkbox
                                v-for="item in state.albumList"
                                :key="item.id"
                                :value="item.id"
                            >
                                <img :src="item.url" />
                                <div class="mask"></div>
                            </a-checkbox>
                        </a-checkbox-group>
                    </div>
                    <div
                        style="margin: 16px 16px 0 0; text-align: right"
                        v-if="state.albumList.length > 0"
                    >
                        <a-pagination
                            v-model:current="state.paginationData.current"
                            v-model:pageSize="state.paginationData.pageSize"
                            show-quick-jumper
                            :pageSizeOptions="['15', '23', '39', '55', '71']"
                            show-size-changer
                            :total="state.paginationData.total"
                            @change="handleChange"
                            :show-total="(total) => `共 ${total} 条`"
                        />
                    </div>
                </div>
                <div v-else class="noData">
                    <img src="/image/empty.png" alt="" />
                    <p>暂无校级相册，快去新建一个吧</p>
                </div>
            </a-spin>
        </div>
        <a-modal
            :maskClosable="false"
            v-model:visible="state.classifyShow"
            :title="state.classifyTitle"
            @ok="saveClassify"
            width="530px"
        >
            <template #footer>
                <a-button key="back" @click="state.classifyShow = false"
                    >取消</a-button
                >
                <a-button
                    key="submit"
                    type="primary"
                    :loading="state.btnLoading"
                    @click="saveClassify"
                    >确定</a-button
                >
            </template>
            <a-form
                layout="vertical"
                :rules="rules"
                ref="classifyRules"
                name="base"
                :model="state.classifyInfo"
            >
                <a-form-item label="相册名称：" name="name">
                    <a-input
                        :maxlength="20"
                        v-model:value="state.classifyInfo.name"
                        placeholder="请输入相册名称"
                    />
                </a-form-item>
            </a-form>
        </a-modal>
        <importAlbum
            ref="importExportRef"
            :classifyId="state.selectedKeys[0]"
            kind="2"
            @handleOk="createAlbum"
        />
        <!-- v-model -->
        <moveAlbum
            ref="moveAlbumRef"
            :classifiyList="state.classifiyList"
            :classifyId="state.classifyId"
            @handelOk="handelOk"
            :checkedAlbum="state.checkedVideo"
        />
    </div>
</template>
<script setup>
import {
    albumClassifyList,
    albumClassifyCreate,
    albumClassifyUpdate,
    albumPage,
    // albumCreate,
    // albumDelete
} from "@/api/album";
import {
    reactive,
    onMounted,
    // createVNode,
    getCurrentInstance,
    watch,
    ref,
} from "vue";
import DempTree from "comps/DempTree/index.vue";
import { message } from "ant-design-vue";
import importAlbum from "./importAlbum.vue";
import moveAlbum from "./moveAlbum.vue";
import { albumOperation } from "../model";
const { proxy } = getCurrentInstance();
const classifyRules = ref();
const rules = { name: [{ required: true, message: "请输入相册名称" }] };

const { delAlbum, deleteClassifiy, changeIsShow } = albumOperation();
const state = reactive({
    classList: [],
    albumList: [],
    checkedVideo: [],
    videoUrl: "",
    classifyId: "",
    tabKey: "classes",
    classifyTitle: "新建相册",
    classifyShow: false,
    classifiyList: [],
    selectedKeys: [],
    editFlag: false,
    btnLoading: false,
    classifyInfo: {
        id: "",
        name: "",
    },
    checkAll: false,
    isShow: "",
    indeterminate: false,
    leftSpinLoad: false,
    rightSpinLoad: false,
    paginationData: {
        total: 0,
        current: 1,
        pageSize: 15,
    },
    delFlag: false,
});
const fieldNames = {
    key: "id",
    title: "name",
};
const getAlbumClassifiy = (val) => {
    const data = {
        kind: 2,
    };
    state.leftSpinLoad = true;
    albumClassifyList(data)
        .then((res) => {
            state.classifiyList = res.data;
            if (val == state.selectedKeys[0] || val == "") {
                state.selectedKeys = [res.data[0]?.id];
                state.classifyId = res.data[0]?.id;
                state.isShow = res.data[0]?.isShow;
                getAlbumList();
            }
        })
        .finally(() => {
            state.leftSpinLoad = false;
        });
};
const addEditClassifiy = (flag, data) => {
    if (flag) {
        state.classifyTitle = "编辑相册";
        for (const i in state.classifyInfo) {
            state.classifyInfo[i] = data[i];
        }
    } else {
        for (const i in state.classifyInfo) {
            state.classifyInfo[i] = "";
        }
        state.classifyTitle = "新增相册";
    }
    state.classifyShow = true;
};
const saveClassify = () => {
    classifyRules.value
        .validate()
        .then(() => {
            if (!state.classifyInfo.name) {
                message.info("请输入相册名称");
                return;
            }
            state.btnLoading = true;
            if (state.classifyTitle == "编辑相册") {
                editClassifiy();
            } else {
                createClassifiy();
            }
        })
        .catch((err) => {
            console.log("error", err);
        });
};
const editClassifiy = () => {
    albumClassifyUpdate(state.classifyInfo)
        .then((res) => {
            message.success("操作成功");
            state.classifyShow = false;
            getAlbumClassifiy();
        })
        .finally(() => {
            state.btnLoading = false;
        });
};
const createClassifiy = () => {
    const data = {
        kind: 2,
        name: state.classifyInfo.name,
    };
    albumClassifyCreate(data)
        .then((res) => {
            message.success("操作成功");
            state.classifyShow = false;
            getAlbumClassifiy();
        })
        .finally(() => {
            state.btnLoading = false;
        });
};
const getAlbumList = () => {
    const data = {
        type: 1,
        kind: 2,
        pageNo: state.paginationData.current,
        classifyId: state.selectedKeys[0],
        pageSize: state.paginationData.pageSize,
    };
    state.rightSpinLoad = true;
    albumPage(data)
        .then((res) => {
            state.albumList = res.data.list;
            state.paginationData.total = res.data.total;
        })
        .finally(() => {
            state.rightSpinLoad = false;
        });
};
const delClassifiy = (val) => {
    deleteClassifiy(val).then((res) => {
        state.delFlag = true;
        message.success(res.message);
        state.checkedVideo = [];
        getAlbumClassifiy(val.id);
    });
};
const selectSemester = (dataId, data) => {
    state.paginationData.current = 1;
    state.selectedKeys = [dataId];
    state.classifyId = dataId;
    state.isShow = data.isShow;
    getAlbumList();
};
const createAlbum = (url) => {
    getAlbumList();
};
const del = () => {
    delAlbum({ ids: state.checkedVideo }).then((res) => {
        message.success(res.message);
        state.paginationData.current = 1;
        getAlbumList();
        state.checkedVideo = [];
        state.editFlag = !state.editFlag;
    });
};
const openUpload = () => {
    proxy.$refs.importExportRef.modalVisible = true;
    proxy.$refs.importExportRef.state.fileList = [];
    proxy.$refs.importExportRef.state.imageList = [];
};
const moveAlbumItem = () => {
    proxy.$refs.moveAlbumRef.visible = true;
};
const handelOk = () => {
    state.editFlag = false;
    state.checkedVideo = [];
    state.paginationData.current = 1;
    getAlbumList();
};
const cancalEdit = () => {
    state.editFlag = false;
    state.checkedVideo = [];
};
const handleChange = (page, pageSize) => {
    state.paginationData.current = page;
    state.paginationData.pageSize = pageSize;
    getAlbumList();
};
const onCheckAllChange = (e) => {
    Object.assign(state, {
        checkedVideo: e.target.checked
            ? state.albumList.map((item) => item.id)
            : [],
        indeterminate: false,
        checkAll: e.target.checked,
    });
};
const changeStatus = (val) => {
    const data = {
        id: state.selectedKeys[0],
        isShow: val,
    };
    changeIsShow(data).then((res) => {
        message.success(res.message);
        state.isShow = val;
        getAlbumClassifiy();
    });
};
watch(
    () => state.checkedVideo,
    (val) => {
        state.indeterminate =
            !!val.length && val.length < state.albumList.length;
        state.checkAll = val.length === state.albumList.length;
    }
);
onMounted(() => {
    getAlbumClassifiy();
});
</script>

<style lang="less" scoped>
.videoAlbum {
    display: flex;
    position: relative;
    height: calc(100% - 57px);

    .left {
        border-right: 1px solid #ccc;
        height: 100%;
        width: 275px;
        padding: 16px 12px;
        position: relative;
    }

    .right {
        width: 100%;

        .noData {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin: 200px;

            img {
                width: 180px;
                height: 180px;
            }

            p {
                margin: 0;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.65);
                line-height: 20px;

                span {
                    color: @primary-color;
                    cursor: pointer;
                }
            }
        }

        .header {
            display: flex;
            height: 72px;
            align-items: center;
            padding: 8px;
            justify-content: space-between;

            .btn_list {
                button {
                    margin: 0 5px;
                }
            }
        }

        .list {
            display: flex;
            flex-wrap: wrap;

            .list_item:first-child {
                display: block;
                text-align: center;
                box-sizing: border-box;
                background: #f6f6f6;
                border: 1px dashed #d9d9d9;
                border-radius: 4px;

                P:first-child {
                    font-size: 60px;
                    color: #bfbfbf;
                    margin-top: 30px;
                }

                p:last-child {
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.55);
                }
            }

            .list_item1 {
                width: 156px;
                height: 156px;
                margin: 8px;
                text-align: center;
                box-sizing: border-box;
                background: #f6f6f6;
                border: 1px dashed #d9d9d9;
                border-radius: 4px;

                P:first-child {
                    font-size: 60px;
                    color: #bfbfbf;
                    margin-top: 30px;
                    line-height: 60px;
                }

                p:last-child {
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.55);
                }
            }

            .list_item,
            :deep(.ant-checkbox-wrapper),
            :deep(.ant-image) {
                display: flex;
                width: 156px;
                height: 156px;
                margin: 8px;
                cursor: pointer;
                align-items: center;
                justify-content: center;
                position: relative;

                span {
                    padding: 0;
                }

                img {
                    max-width: 100%;
                    max-height: 100%;
                }

                .ant-image-img {
                    width: auto;
                }
            }

            :deep(.ant-checkbox-group) {
                display: flex;
                flex-wrap: wrap;
            }

            :deep(.ant-checkbox-wrapper) {
                .ant-checkbox {
                    z-index: 99;
                    position: absolute;
                    top: 5px;
                    right: 5px;
                }

                img {
                    max-width: 156px;
                    max-height: 156px;
                }

                .mask {
                    display: block;
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    left: 0;
                    right: 0;
                    top: 0;
                    border: 0;
                    background: rgba(0, 0, 0, 0.25);
                }
            }
        }
    }

    .mask {
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.25);
    }
}

// 3.0
.layout_content_view {
    .videoAlbum {
        height: calc(100vh - 130px);
    }
}
</style>
