import { albumDelete, albumClassifyDel, albumClassifyUpdate } from '@/api/album'
import { createVNode, h } from 'vue'
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { Modal } from 'ant-design-vue'

export function albumOperation() {
    const delAlbum = (data) => {
        return new Promise((relove, reject) => {
            Modal.confirm({
                title: '提示',
                icon: createVNode(ExclamationCircleOutlined),
                content: `请确认删除${data.ids.length}张照片`,
                okText: "确定",
                okType: "danger",
                cancelText: "取消",
                onOk() {
                    albumDelete(data)
                        .then((res) => {
                            relove(res);
                        })
                        .catch(() => {
                            // eslint-disable-next-line prefer-promise-reject-errors
                            reject();
                        });
                },
                onCancel() {
                    // eslint-disable-next-line prefer-promise-reject-errors
                    reject();
                },
            });
        });
    }
    
    const deleteClassifiy = (data) => {
        return new Promise((relove, reject) => {
            Modal.confirm({
                title: '删除提示',
                icon: createVNode(ExclamationCircleOutlined),
                content: h('div', {}, [
                    h(
                        'p',
                        `确定删除本相册，并清空里面的所有照片？？`
                    )
                ]),
                okText: '确认',
                cancelText: '取消',
                onOk() {
                    albumClassifyDel({id: data.id}).then((res) => {
                        relove(res)
                    }).catch(() => {
                        reject()
                    })
                },
                onCancel() {
                    reject()
                }
            })
        })
    }

    const changeIsShow = (data) => {
        return new Promise((relove, reject) => {
            albumClassifyUpdate(data).then((res) => {
                relove(res)
            }).catch(() => {
                reject()
            })
        })
    }

    return { delAlbum, deleteClassifiy, changeIsShow }
}