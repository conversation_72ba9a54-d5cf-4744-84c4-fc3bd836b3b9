<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-06-07 16:16:02
 * @LastEditors: jingrou
 * @LastEditTime: 2022-08-10 19:00:12
-->
<template>
    <div class="videoAlbum">
        <div class="header">
            <a-tabs v-model:activeKey="menuIndex" @change="changeTab">
                <template #leftExtra>
                    <slot name="leftExtra">
                        <a-button type="text" class="tabs_back" @click="back">
                            <i class="iconfont icon-xingzhuangjiehe19" />
                            <!-- <arrow-left-outlined class="tabs_back__icon" /> -->
                        </a-button>
                    </slot>
                </template>
                <a-tab-pane
                    v-for="item in tabList"
                    :key="item.index"
                    :tab="item.label"
                ></a-tab-pane>
            </a-tabs>
        </div>
        <component :is="currentComponent"></component>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import classes from './components/classes.vue'
import schoolLevel from './components/schoolLevel.vue'
import { useRoute, useRouter } from 'vue-router'
const Route = useRoute()
const Router = useRouter()
const menuIndex = ref(0)
const tabList = ref([
    {
        label: '班级',
        index: 0
    },
    {
        label: '校级',
        index: 1
    }
])
const changeTab = (val) => {
    menuIndex.value = val
}

const currentComponent = computed(() => {
    return [classes, schoolLevel][menuIndex.value]
})
const back = () => {
    Router.push({
        name: Route.query.source
    })
}
</script>

<style lang="less" scoped>
// .tabs_back {
//     .tabs_back__icon {
//         color: @primary-color;
//     }
// }

:deep(.ant-tabs-nav) {
    padding-left: v-bind(showBackStyle);
    margin-bottom: 0;
    border-bottom: 1px solid @border-color-base;
    line-height: 33px;
}

.faceLibrary_warp_pageitem {
    height: calc(100% - 57px);
}

</style>
