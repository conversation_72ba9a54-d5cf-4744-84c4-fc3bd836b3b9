<!-- eslint-disable -->
<template>
    <div class="dataCenterPage">
        <a-row :gutter="[25, 16]">
            <a-col :span="8">
                <div class="scoreoftheday">
                    <div class="scoreoftheday_title">
                        <div class="scoreoftheday_title_box">
                            <div class="scoreoftheday_icon">
                                <img src="/image/scoreoftheday.png" />
                            </div>
                            <div class="scoreoftheday_text">今日得分排行</div>
                        </div>

                        <div class="selectType">
                            <a-select
                                v-model:value="state.dayListType"
                                style="width: 120px; margin-right: 10px"
                                @change="changeDayListType"
                            >
                                <a-select-option :value="0"
                                    >学生</a-select-option
                                >
                                <a-select-option :value="1"
                                    >老师</a-select-option
                                >
                            </a-select>
                            <div
                                class="allBtn"
                                @click="allCountPage"
                                v-if="state.dayList?.length"
                            >
                                全部<RightOutlined />
                            </div>
                        </div>
                    </div>
                    <div class="scoreoftheday_con" v-if="state.dayList?.length">
                        <div class="con_2 conbox">
                            <img src="/image/con_2.png" alt="" />
                            <div class="con_2_text con_text">
                                <div class="score">
                                    {{
                                        state.dayList[1]?.maxScore
                                            ? `${state.dayList[1]?.maxScore}分`
                                            : "0分"
                                    }}
                                </div>
                                <div class="monicker">
                                    {{
                                        substrName(
                                            state.dayList[1]?.toPersonName
                                        ) || "-"
                                    }}
                                </div>
                                <div class="grade">
                                    {{
                                        sliceName(state.dayList[1]?.orgName) ||
                                        "-"
                                    }}
                                </div>
                            </div>
                        </div>
                        <div class="con_1 conbox">
                            <img src="/image/con_1.png" alt="" />
                            <div class="con_1_text con_text">
                                <div class="score">
                                    {{
                                        state.dayList[0]?.maxScore
                                            ? `${state.dayList[0]?.maxScore}分`
                                            : "0分"
                                    }}
                                </div>
                                <div class="monicker">
                                    {{
                                        substrName(
                                            state.dayList[0]?.toPersonName
                                        ) || "-"
                                    }}
                                </div>
                                <div class="grade">
                                    {{
                                        sliceName(state.dayList[0]?.orgName) ||
                                        "-"
                                    }}
                                </div>
                            </div>
                        </div>
                        <div class="con_3 conbox">
                            <img src="/image/con_3.png" alt="" />
                            <div class="con_3_text con_text">
                                <div class="score">
                                    {{
                                        state.dayList[2]?.maxScore
                                            ? `${state.dayList[2]?.maxScore}分`
                                            : "0分"
                                    }}
                                </div>
                                <div class="monicker">
                                    {{
                                        substrName(
                                            state.dayList[2]?.toPersonName
                                        ) || "-"
                                    }}
                                </div>
                                <div class="grade">
                                    {{
                                        sliceName(state.dayList[2]?.orgName) ||
                                        "-"
                                    }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="withoutBox" v-else>
                        <img src="/image/without.png" alt="" />
                        <div class="withoutBox_title">暂无数据</div>
                    </div>
                </div>
            </a-col>
            <a-col :span="8">
                <div class="participant">
                    <div class="participant_title">
                        <div class="selectType">
                            <div class="participant_icon">
                                <img src="/image/participant.png" />
                            </div>
                            <div class="participant_text">参与评价人员</div>
                        </div>
                        <div>
                            <a-select
                                v-model:value="state.participantOptionType"
                                style="width: 120px; margin-right: 10px"
                                @change="changeparticipantOption"
                            >
                                <a-select-option :value="0"
                                    >学生</a-select-option
                                >
                                <a-select-option :value="1"
                                    >老师</a-select-option
                                >
                            </a-select>
                        </div>
                    </div>
                    <div
                        class="participant_con"
                        v-if="participantOption.series[0]?.data?.length"
                    >
                        <Echarts :option="participantOption" />
                    </div>
                    <div class="withoutBox" v-else>
                        <img src="/image/without.png" alt="" />
                        <div class="withoutBox_title">暂无数据</div>
                    </div>
                </div>
            </a-col>
            <a-col :span="8">
                <div class="fastBox">
                    <div class="fastBox_top">
                        <div class="fastBox_top_item">
                            <div class="fastBox_top_item_num">
                                {{ state.exchangeCount || "-" }}
                            </div>
                            <div class="fastBox_top_item_title">总兑换次数</div>
                        </div>
                        <div class="fastBox_top_item">
                            <div class="fastBox_top_item_num">
                                {{ state.medalCount || "-" }}
                            </div>
                            <div class="fastBox_top_item_title">勋章总数</div>
                        </div>
                        <div class="fastBox_top_item">
                            <div class="fastBox_top_item_num">
                                {{ state.scoreCardCount || "-" }}
                            </div>
                            <div class="fastBox_top_item_title">积分卡总数</div>
                        </div>
                    </div>
                    <div class="fastBox_bottom">
                        <div class="fastBox_bottom_title">
                            <div class="fastBox_bottom_icon">
                                <img src="/image/fastBox_bottom.png" />
                            </div>
                            <div class="fastBox_bottom_text">快捷功能</div>
                        </div>
                        <div class="fastBox_bottom_con">
                            <div
                                class="fastBox_bottom_con_item"
                                @click="toEvaluate"
                            >
                                <img src="/image/fast_1.png" />
                                <div>我要评价</div>
                            </div>
                            <div
                                class="fastBox_bottom_con_item"
                                @click="newRewardsCard"
                            >
                                <img src="/image/fast_2.png" />
                                <div>新建积分卡</div>
                            </div>
                            <div
                                class="fastBox_bottom_con_item"
                                @click="issueMedals"
                            >
                                <img src="/image/fast_3.png" />
                                <div>发放勋章</div>
                            </div>
                        </div>
                    </div>
                </div>
            </a-col>
            <!-- 积分总榜单 -->
            <a-col :span="8">
                <div class="scoreboard">
                    <div class="scoreboard_title">
                        <div class="selectType">
                            <div class="scoreboard_icon">
                                <img src="/image/scoreboard.png" />
                            </div>
                            <div class="scoreboard_text">积分总榜</div>
                        </div>
                        <div>
                            <a-select
                                v-model:value="state.scoreboardDataType"
                                style="width: 120px; margin-right: 10px"
                                @change="changescoreboardData"
                            >
                                <a-select-option :value="0"
                                    >学生</a-select-option
                                >
                                <a-select-option :value="1"
                                    >老师</a-select-option
                                >
                            </a-select>
                        </div>
                    </div>
                    <div class="scoreboard_con" v-if="scoreboardData.length">
                        <a-table
                            size="middle"
                            :pagination="false"
                            :columns="scoreboardColumns"
                            :data-source="scoreboardData"
                            :row-selection="false"
                        >
                            <template #bodyCell="{ column, record, index }">
                                <template
                                    v-if="column.dataIndex === 'personName'"
                                >
                                    <span :title="record.personName">{{
                                        substrName(record.personName)
                                    }}</span>
                                </template>
                                <template
                                    v-if="column.dataIndex === 'groupNameList'"
                                >
                                    <span :title="record.groupNameList">{{
                                        sliceName(
                                            record.groupNameList.join("、")
                                        )
                                    }}</span>
                                </template>
                            </template>
                        </a-table>
                    </div>
                    <div class="withoutBox" v-else>
                        <img src="/image/without.png" alt="" />
                        <div class="withoutBox_title">暂无数据</div>
                    </div>
                </div>
            </a-col>
            <!-- 评分统计 -->
            <a-col :span="8">
                <div class="markTotal">
                    <div class="markTotal_title">
                        <div class="markTotal_title_box">
                            <div class="markTotal_icon">
                                <img src="/image/markTotal.png" />
                            </div>
                            <div class="markTotal_text">评分统计</div>
                        </div>
                        <div>
                            <a-select
                                style="width: 200px"
                                @change="markTotalChange"
                                v-model:value="state.activityId"
                                placeholder="请选择"
                                :options="state.activityOptions"
                                :showArrow="true"
                                :field-names="{
                                    label: 'title',
                                    value: 'id',
                                }"
                            >
                            </a-select>
                        </div>
                    </div>
                    <div
                        class="markTotal_con"
                        v-if="markTotalOption.xAxis.data?.length"
                    >
                        <Echarts :option="markTotalOption" />
                    </div>
                    <div class="withoutBox" v-else>
                        <img src="/image/without.png" alt="" />
                        <div class="withoutBox_title">暂无数据</div>
                    </div>
                </div>
            </a-col>

            <!-- 勋章统计 -->
            <a-col :span="8">
                <div class="statistics">
                    <div class="statistics_title">
                        <div class="statistics_icon">
                            <img src="/image/statistics.png" />
                        </div>
                        <div class="statistics_text">勋章统计</div>
                    </div>
                    <div
                        class="statistics_con"
                        v-if="statisticsOption.series[0]?.data?.length"
                    >
                        <Echarts :option="statisticsOption" />
                    </div>
                    <div class="withoutBox" v-else>
                        <img src="/image/without.png" alt="" />
                        <div class="withoutBox_title">暂无数据</div>
                    </div>
                </div>
            </a-col>
        </a-row>

        <!-- 得分的一个弹窗 -->
        <a-modal
            :footer="null"
            :keyboard="false"
            :maskClosable="false"
            v-model:visible="state.maxPageVisible"
            title="今日得分排行"
            width="600px"
        >
            <a-table
                size="middle"
                :pagination="pagination"
                @change="tableChange"
                :columns="countPageColumns"
                :data-source="countPageData"
            ></a-table>
        </a-modal>
    </div>
</template>

<script setup name="dataCenter">
/* eslint-disable */
import { reactive, onMounted, ref } from "vue";
import { message } from "ant-design-vue";
import Echarts from "@/components/Echarts";

import {
    pageEvalPersonScore,
    getEmsCount,
    listEvalPersonPortion,
    pageEvalMedalPersonCount,
    listEvalActivity,
    getstatisticsList,
    dayScoreMaxCount,
    dayScoreMaxCountPage,
} from "@/api/appraise.js";

const route = useRoute();
const router = useRouter();
const state = ref({
    dayListType: 0,
    scoreboardDataType: 0,
    participantOptionType: 0,
    maxPageVisible: false,
    dayList: [],
    ringChartData: [],
    activityOptions: [],
    exchangeCount: 0,
    medalCount: 0,
    scoreCardCount: 0,
});

const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    hideOnSinglePage: true,
    showLessItems: true,
    showQuickJumper: true,
    showSizeChanger: true,
    pageSizeOptions: ["10", "20", "30", "40", "100"],
    showTotal: (total) => `共 ${total}  条`,
});

// 参与评价人员 饼图配置项
const participantOption = reactive({
    tooltip: {
        trigger: "item",
    },
    color: ["#12C685", "#F6A503", "#38AEFA", "#7998FF", "#99CC10"],
    legend: {
        right: 30,
        top: "center",
        itemWidth: 10,
        itemHeight: 10,
        icon: "circle",
        // align: "left",
        // bottom: 9,
        itemGap: 24,
        orient: "vertical",
        formatter: (name) => {
            console.log("name", name);
            let params1;
            // 图例的 69.43%，150832，name是图例文字描述，for循环是格式化
            for (
                let i = 0, len = state.value.ringChartData.length;
                i < len;
                i++
            ) {
                if (state.value.ringChartData[i].objName == name) {
                    params1 =
                        (state.value.ringChartData[i].portion * 100).toFixed(
                            2
                        ) + "%";
                }
            }
            let arr = [
                "{a|" +
                    `${name?.length > 5 ? `${name.substr(0, 5)}...` : name}` +
                    "}",
                "{b|" + params1 + "}",
            ];
            return arr.join("");
        },
        textStyle: {
            rich: {
                a: {
                    width: 50,
                    fontsize: 14,
                    color: "rgba(0,0,0,0.45)",
                    padding: [0, 14, 0, 0],
                },
                b: {
                    align: "left",
                    width: 40,
                    fontsize: 14,
                    color: "rgba(0,0,0,0.85)",
                    padding: [0, 10, 0, 0],
                },
            },
        },
    },
    series: [
        {
            // name: "Access From",
            type: "pie",
            right: 200,
            radius: ["40%", "70%"],
            avoidLabelOverlap: false,
            itemStyle: {
                // borderRadius: 10,
                borderColor: "#fff",
                borderWidth: 2,
            },
            label: {
                show: false,
                position: "center",
            },
            // emphasis: {
            //     label: {
            //         show: true,
            //         fontSize: 40,
            //         fontWeight: "bold",
            //     },
            // },
            labelLine: {
                show: false,
            },
            data: [],
        },
    ],
});

// 评分统计 折线图的配置
const markTotalOption = reactive({
    tooltip: {
        trigger: "axis",
    },
    legend: {
        align: "left",
        left: 0,
        // orient: 'vertical',
        icon: "roundRect",
        itemHeight: 1, // 粗细
        itemWidth: 15, // 粗细
        // data: ["加分", "扣分"],
    },
    grid: {
        left: "10px",
        // top: "30px",
        right: "10px",
        bottom: "10px",
        containLabel: true,
    },
    toolbox: {},
    xAxis: {
        axisTick: {
            show: false,
        },
        axisLabel: {
            interval: 0,
            fontSize: 10, // 顺便调小一点字体大小
            rotate: 55,
            show: true, // 显示标签,
            width: 50, //将内容的宽度固定
            overflow: "truncate", //超出的部分截断
            truncate: "...", //截断的部分用...代替
        },
        type: "category",
        boundaryGap: false,
        data: [],
    },
    yAxis: {
        type: "value",
        axisTick: {
            show: false,
        },
        splitLine: {
            lineStyle: {
                type: "dashed", // 线型为虚线
            },
        },
    },
    series: [
        {
            name: "加分",
            type: "line",
            lineStyle: {
                color: "#00B781", // 这里设置折线的颜色为红色
                // width: 2, // 设置线宽
            },
            itemStyle: {
                normal: {
                    color: "#00B781", // 小圆点颜色
                    borderColor: "#00B781", // 小圆点边框颜色
                    borderWidth: 2, // 小圆点边框宽度
                },
            },
            symbol: "circle",
            symbolSize: 5, //小圆点的大小
            data: [],
        },
        {
            name: "扣分",
            type: "line",
            lineStyle: {
                color: "#FF786E", // 这里设置折线的颜色为红色
                // width: 2, // 设置线宽
            },
            itemStyle: {
                normal: {
                    color: "#FF786E", // 小圆点颜色
                    borderColor: "#FF786E", // 小圆点边框颜色
                    borderWidth: 2, // 小圆点边框宽度
                },
            },
            symbol: "circle",
            symbolSize: 5, //小圆点的大小
            data: [],
        },
    ],
});

// 勋章统计 柱状图
const statisticsOption = reactive({
    tooltip: {
        trigger: "axis",
        backgroundColor: "rgba(0, 0, 0, 0.75)", // 背景色
        padding: [5, 15, 5, 15], // 边距
        borderColor: "rgba(0, 0, 0, 0.75)", // 边框颜色
        borderWidth: 1, // 边框线宽度
        textStyle: {
            // 文字样式
            color: "#FFFFFF",
        },
        extraCssText: "text-align: left;", // 文字对齐方式
        axisPointer: {
            type: "none",
        },
        formatter(params) {
            // 格式化函数
            return params[0].axisValueLabel;
        },
    },
    toolbox: {
        show: false,
    },
    legend: {
        show: false,
    },
    grid: {
        left: 0,
        right: 0,
        top: 0, //拉伸距离顶部高度
        bottom: 0, //拉伸距离底部高度
        containLabel: true,
    },
    xAxis: {
        show: false,
        position: "top",
        axisTick: {
            show: false,
        },
        // type: 'value',
        // boundaryGap: [0, 0.01]
    },
    yAxis: {
        type: "category",
        inverse: true,
        max: 5,
        axisTick: {
            show: false,
        },
        axisLine: {
            show: false,
        },
        // triggerEvent: true, //记住要加上 设置为true后，可触发事件
        axisLabel: {
            show: true, // 显示标签,
            width: 70, //将内容的宽度固定
            overflow: "truncate", //超出的部分截断
            truncate: "...", //截断的部分用...代替
            textStyle: {
                color: "rgba(0,0,0,0.85)", // 文字颜色
                fontSize: 14, // 文字字号
            },
        },
        data: [],
    },
    series: [
        {
            showBackground: true,
            itemStyle: {
                color: "#12C685",
            },
            backgroundStyle: {
                color: "#F0F2F5",
            },
            label: {
                show: true,
                position: "insideLeft",
                fontSize: 13, // 文本字体大小
                color: "rgba(0,0,0,0.45)",
                formatter: "{c}人获得", // 自定义文本内容
            },
            type: "bar",
            data: [],
        },
    ],
});

// 积分总榜的table 表头
const scoreboardColumns = [
    {
        title: "排名",
        key: "index",
        dataIndex: "index",
        customRender: ({ text, record, index }) => `${index + 1}`, // 显示每一行的序号
    },
    {
        title: "姓名",
        dataIndex: "personName",
        key: "personName",
    },
    {
        title: "班级",
        dataIndex: "groupNameList",
        key: "groupNameList",
    },
    {
        title: "累计获得总积分",
        dataIndex: "totalScore",
        key: "totalScore",
    },
];

const scoreboardData = ref([
    // {
    //     ranking: "1",
    //     personName: "我我我我是是是啊",
    //     groupNameList: "高一1班及考核方式啊",
    //     totalScore: "50",
    // },
    // {
    //     ranking: "2",
    //     personName: "我我我我是啊",
    //     groupNameList: "高一1班及考核方式方法啊",
    //     totalScore: "50",
    // },
    // {
    //     ranking: "3",
    //     personName: "",
    //     groupNameList: "高一1班及考核方式方法啊",
    //     totalScore: "50",
    // },
    // {
    //     ranking: "4",
    //     personName: "我我我我是是是啊",
    //     groupNameList: "",
    //     totalScore: "50",
    // },
    // {
    //     ranking: "5",
    //     personName: "我我我我是是是啊",
    //     groupNameList: "高一1班及考核方式方法啊",
    //     totalScore: "50",
    // },
]);

const countPageData = ref([]);

// 获取积分总榜前5名的数据
const getScoreboardData = (identity = 0) => {
    pageEvalPersonScore({
        pageNo: 1,
        pageSize: 5, // 只获取前5个
        identity: identity,
        evalTypeId: route.query.codeId,
    }).then((res) => {
        scoreboardData.value = res.data.list;
    });
};

// 获取兑换次数、勋章总数、积分卡总数
const getScoreboardCount = () => {
    getEmsCount({
        evalTypeId: route.query.codeId,
    }).then((res) => {
        state.value.exchangeCount = res.data.exchangeCount;
        state.value.medalCount = res.data.medalCount;
        state.value.scoreCardCount = res.data.scoreCardCount;
    });
};

// 参与评价活动占比-饼图
const getEvalActivityRatio = (identity = 0) => {
    listEvalPersonPortion({
        evalTypeId: route.query.codeId,
        identity: identity,
    }).then((res) => {
        // res.data = [
        //     {
        //         classesName: "高一",
        //         portion: 10,
        //     },
        //     {
        //         classesName: "高二",
        //         portion: 20,
        //     },
        //     {
        //         classesName: "高三",
        //         portion: 30,
        //     },
        //     {
        //         classesName: "我啊的",
        //         portion: 50,
        //     },
        //     {
        //         classesName: "其他",
        //         portion: 40,
        //     },
        // ];
        state.value.ringChartData = res.data;
        let listData = [];
        listData = res.data.map((item) => {
            return {
                value: item.portion,
                name: item.objName,
            };
        });

        participantOption.series[0].data = listData;
    });
};

// 获取勋章统计
const getMedalStatistics = () => {
    pageEvalMedalPersonCount({
        pageNo: 1,
        pageSize: 5, // 只获取前5个
        evalTypeId: route.query.codeId,
    }).then((res) => {
        let medalList = res.data.list || [];
        // const medalList = [
        //     {
        //         evalTypeId: 1,
        //         evalTypeName: "劳动评价",
        //         medalCode: "YDXZ320237",
        //         medalName: "金牌辅助",
        //         personCount: 6,
        //     },
        //     {
        //         evalTypeId: 1,
        //         evalTypeName: "劳动评价",
        //         medalCode: "YDXZ320237",
        //         medalName: "勋章名字1",
        //         personCount: 40,
        //     },
        // ];
        let nameList = [];
        let valueList = [];
        medalList.forEach((v) => {
            nameList.push(v.medalName);
            valueList.push(v.personCount);
        });

        statisticsOption.yAxis.data = nameList;
        statisticsOption.series[0].data = valueList;
    });
};

// 根据活动id获取折线图数据
const getLineChartData = (id) => {
    console.log("id", id);
    getstatisticsList({
        activityId: id,
    }).then((res) => {
        console.log("根据活动id获取折线图数据", res);
        // res.data = [
        //     {
        //         addScore: 10,
        //         indicatorName: "我我我我",
        //         minusScore: 8,
        //     },
        //     ,
        //     {
        //         addScore: 30,
        //         indicatorName: "我我我我",
        //         minusScore: 50,
        //     },
        //     {
        //         addScore: 90,
        //         indicatorName: "帮忙看对面不可能的十",
        //         minusScore: 20,
        //     },
        //     {
        //         addScore: 30,
        //         indicatorName: "看美女大楼v了和他好",
        //         minusScore: 40,
        //     },
        //     {
        //         addScore: 15,
        //         indicatorName: "科技含量就是",
        //         minusScore: 11,
        //     },
        //     {
        //         addScore: 16,
        //         indicatorName: "的活动我i",
        //         minusScore: 26,
        //     },
        //     {
        //         addScore: 52,
        //         indicatorName: "看胜率哦那算了欧式",
        //         minusScore: 16,
        //     },
        //     {
        //         addScore: 32,
        //         indicatorName: "成本伸出援手vu啊",
        //         minusScore: 12,
        //     },
        //     {
        //         addScore: 52,
        //         indicatorName: "i欧龙武器都完全",
        //         minusScore: 16,
        //     },
        //     {
        //         addScore: 15,
        //         indicatorName: "比赛徐u阿萨",
        //         minusScore: 11,
        //     },
        //     {
        //         addScore: 16,
        //         indicatorName: "沉默俺从1",
        //         minusScore: 14,
        //     },
        //     {
        //         addScore: 18,
        //         indicatorName: "杀到十几年的才是啊不bi",
        //         minusScore: 16,
        //     },
        // ];
        const lineChartData = res.data;
        let lineNameList = [];
        let addScoreList = [];
        let minusScoreList = [];
        lineChartData.forEach((v) => {
            lineNameList.push(v.indicatorName);
            addScoreList.push(v.addScore);
            minusScoreList.push(v.minusScore);
        });

        markTotalOption.xAxis.data = lineNameList;
        markTotalOption.series[0].data = addScoreList;
        markTotalOption.series[1].data = minusScoreList;
    });
};

// 切换评价活动
const markTotalChange = () => {
    getLineChartData(state.value.activityId);
};
// 获取活动列表数据
const getactivityList = () => {
    listEvalActivity({ evalTypeId: route.query.codeId }).then((res) => {
        console.log("活动列表数据", res);

        // res.data = [
        //     {
        //         id: "123",
        //         title: "活动1",
        //     },
        //     {
        //         id: "456",
        //         title: "活动2",
        //     },
        // ];

        state.value.activityOptions = res.data;
        state.value.activityId = res.data[0]?.id || "";
        state.value.activityId && getLineChartData(state.value.activityId);
    });
};

// 获取进天得分前三的数据
const getDayScoreMaxCount = (identity = 0) => {
    dayScoreMaxCount({
        evalTypeId: route.query.codeId,
        identity: identity,
    }).then((res) => {
        // res.data = [
        //     {
        //         maxScore: 10,
        //         orgName: "的文化威风",
        //         toPersonId: "1681117273789599745",
        //         toPersonName: "是发射点发生还是哈哈",
        //     },
        //     {
        //         maxScore: null,
        //         orgName: "我还是的味道",
        //         toPersonId: "1681117273789599745",
        //         toPersonName: "的完全大旗网大旗网",
        //     },
        //     {
        //         maxScore: 0.0,
        //         orgName: "大撒撒",
        //         toPersonId: "1681117273789599745",
        //         toPersonName: "阿大",
        //     },
        //     {
        //         maxScore: 0.0,
        //         orgName: "我我我我",
        //         toPersonId: "1681117273789599745",
        //         toPersonName: "我我是的饿啊下我了是`",
        //     },
        // ];
        state.value.dayList = res.data.slice(0, 3);
    });
};

// 快捷新建积分卡
const newRewardsCard = () => {
    message.info("暂未开放");
};

// 快捷去评价
const toEvaluate = () => {
    router.push({
        path: "/appModel/evalActivity/evaluationManage/activity",
        query: {
            ...route.query,
        },
    });
};

// 快捷去发勋章
const issueMedals = () => {
    router.push({
        path: "/appModel/evalActivity/growthSystem/medalRecords",
        query: {
            ...route.query,
        },
    });
};

// table 表头
const countPageColumns = [
    {
        title: "排名",
        key: "sort",
        dataIndex: "sort",
    },
    {
        title: "姓名",
        dataIndex: "toPersonName",
        key: "toPersonName",
    },
    {
        title: "班级",
        dataIndex: "orgName",
        key: "orgName",
    },
    {
        title: "今日总得分",
        dataIndex: "maxScore",
        key: "maxScore",
    },
];

const reqdayScoreMaxCountPage = (identity = 0) => {
    dayScoreMaxCountPage({
        pageNo: pagination.value.current,
        pageSize: pagination.value.pageSize,
        evalTypeId: route.query.codeId,
        identity: identity,
    }).then(({ data }) => {
        countPageData.value = data.list;
        pagination.value.total = data.total;
    });
};

const allCountPage = () => {
    reqdayScoreMaxCountPage(state.value.dayListType);
    state.value.maxPageVisible = true;
};

const tableChange = ({ current, pageSize }) => {
    pagination.value.current = current;
    pagination.value.pageSize = pageSize;
    reqdayScoreMaxCountPage(state.value.dayListType);
};

// 超出长度截取
function substrName(value) {
    return value?.length > 4 ? `${value.substr(0, 4)}...` : value;
}

// 超出长度截取
function sliceName(value) {
    return value?.length > 10 ? `${value.slice(0, 10)}...` : value;
}

const changeDayListType = () => {
    getDayScoreMaxCount(state.value.dayListType);
};

const changescoreboardData = () => {
    getScoreboardData(state.value.scoreboardDataType);
};


const changeparticipantOption = () => {
    getEvalActivityRatio(state.value.participantOptionType);
}

onMounted(() => {
    getScoreboardData();
    getScoreboardCount();
    getEvalActivityRatio();
    getMedalStatistics();
    getactivityList();
    getDayScoreMaxCount();
});
</script>

<style lang="less" scoped>
.dataCenterPage {
    padding: 16px;
}

.scoreoftheday {
    background: #ffffff;
    border-radius: 10px;
    border: 1px solid #d9d9d9;
    height: 386px;
    .scoreoftheday_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        .scoreoftheday_title_box {
            display: flex;
            align-items: center;
            .scoreoftheday_icon {
                width: 24px;
                height: 24px;
                img {
                    width: 24px;
                    height: 24px;
                }
            }
            .scoreoftheday_text {
                padding-left: 12px;
                font-size: 18px;
                font-weight: 600;
                color: #333333;
            }
        }
        .allBtn {
            color: #00b781;
            cursor: pointer;
        }
    }
    .scoreoftheday_con {
        display: flex;
        align-items: center;
        justify-content: space-around;
        .con_2 {
            padding-top: 30px;
        }
        .con_3 {
            padding-top: 40px;
        }
        .conbox {
            position: relative;
            width: 146px;
            height: 201px;
            .con_1_text {
                position: absolute;
                right: 13px;
                bottom: 20px;
            }
            .con_2_text {
                position: absolute;
                right: 13px;
                bottom: -10px;
            }
            .con_3_text {
                position: absolute;
                right: 13px;
                bottom: -33px;
            }
            .con_text {
                text-align: center;
                width: 120px;
                .score {
                    font-weight: 600;
                    font-size: 22px;
                    padding-bottom: 5px;
                    color: rgba(0, 0, 0, 0.85);
                }
                .monicker {
                    font-weight: 600;
                    font-size: 16px;
                    padding-bottom: 5px;
                    color: rgba(0, 0, 0, 0.85);
                }
                .grade {
                    font-weight: 400;
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.85);
                }
            }

            img {
                width: 146px;
                height: 201px;
            }
        }
    }
}

.participant {
    background: #ffffff;
    border-radius: 10px;
    border: 1px solid #d9d9d9;
    height: 386px;
    display: flex;
    flex-direction: column;
    .participant_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        .participant_icon {
            width: 24px;
            height: 24px;
            img {
                width: 24px;
                height: 24px;
            }
        }
        .participant_text {
            padding-left: 12px;
            font-size: 18px;
            font-weight: 600;
            color: #333333;
        }
    }
    .participant_con {
        flex: auto;
    }
}

.fastBox {
    background: #ffffff;
    // border-radius: 10px;
    // border: 1px solid #d9d9d9;
    // height: 386px;
    .fastBox_top {
        border-radius: 10px;
        border: 1px solid #d9d9d9;
        height: 185px;
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        .fastBox_top_item {
            text-align: center;
            .fastBox_top_item_num {
                font-weight: 600;
                font-size: 36px;
                color: #00b781;
            }
            .fastBox_top_item_title {
                padding-top: 8px;
                font-weight: 400;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.85);
            }
        }
    }
    .fastBox_bottom {
        border-radius: 10px;
        border: 1px solid #d9d9d9;
        height: 185px;
        .fastBox_bottom_title {
            display: flex;
            align-items: center;
            padding: 16px;
            .fastBox_bottom_icon {
                width: 24px;
                height: 24px;
                img {
                    width: 24px;
                    height: 24px;
                }
            }
            .fastBox_bottom_text {
                padding-left: 12px;
                font-size: 18px;
                font-weight: 600;
                color: #333333;
            }
        }
        .fastBox_bottom_con {
            display: flex;
            align-items: center;
            justify-content: space-around;
            .fastBox_bottom_con_item {
                cursor: pointer;
                text-align: center;
                img {
                    width: 78px;
                    height: 78px;
                }
            }
        }
    }
}

.scoreboard {
    background: #ffffff;
    border-radius: 10px;
    border: 1px solid #d9d9d9;
    height: 386px;
    .scoreboard_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        .scoreboard_icon {
            width: 24px;
            height: 24px;
            img {
                width: 24px;
                height: 24px;
            }
        }
        .scoreboard_text {
            padding-left: 12px;
            font-size: 18px;
            font-weight: 600;
            color: #333333;
        }
    }
    .scoreboard_con {
        padding-right: 16px;
        padding-left: 16px;
    }
}

.markTotal {
    background: #ffffff;
    border-radius: 10px;
    border: 1px solid #d9d9d9;
    height: 386px;
    display: flex;
    flex-direction: column;
    .markTotal_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        .markTotal_title_box {
            display: flex;
            .markTotal_icon {
                width: 24px;
                height: 24px;
                img {
                    width: 24px;
                    height: 24px;
                }
            }
            .markTotal_text {
                padding-left: 12px;
                font-size: 18px;
                font-weight: 600;
                color: #333333;
            }
        }
    }
    .markTotal_con {
        padding: 0px 24px;
        flex: auto;
    }
}

.statistics {
    background: #ffffff;
    border-radius: 10px;
    border: 1px solid #d9d9d9;
    height: 386px;
    display: flex;
    flex-direction: column;
    .statistics_title {
        display: flex;
        align-items: center;
        padding: 16px;
        .statistics_icon {
            width: 24px;
            height: 24px;
            img {
                width: 24px;
                height: 24px;
            }
        }
        .statistics_text {
            padding-left: 12px;
            font-size: 18px;
            font-weight: 600;
            color: #333333;
        }
    }
    .statistics_con {
        padding: 0px 24px;
        flex: auto;
    }
}

.withoutBox {
    text-align: center;
    padding-top: 50px;
    .withoutBox_title {
        font-size: 14px;
        font-weight: 400;
        color: #595959;
        .touchTitle {
            color: #00b781;
        }
    }
}
.selectType {
    display: flex;
    align-items: center;
}
</style>
