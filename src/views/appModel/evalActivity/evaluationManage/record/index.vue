<template>
    <div class="recordIframe">
        <MyIframe :src="IframeSrc" :isPostMessage="true"></MyIframe>
    </div>
</template>
<script setup>
import { ref, onMounted } from "vue";
import MyIframe from "@/components/Iframe/index";
import config from "@/config/config";
import { ls } from "@/utils/util";
const evaluation = import.meta.env.VITE_BASE_API_EVALUATION;
const route = useRoute();
const { ACCESS_TOKEN } = config;
const token = `Bearer ${ls.get(ACCESS_TOKEN)}`;
const IframeSrc = ref("");
onMounted(() => {
    IframeSrc.value = `${evaluation}/#/evaluationManage/record?token=${token}&sysCode=cloud&codeId=${route.query.codeId}`;
});
</script>
<style lang="less" scoped>
.recordIframe {
    height: 100%;
}
</style>
