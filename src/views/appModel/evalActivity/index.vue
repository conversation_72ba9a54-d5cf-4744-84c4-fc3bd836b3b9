<!-- 这个是一整个生成评价应用的主页面 -->
<template>
    <div class="appraise">
        <div class="appraise_head_back">
            <i class="iconfont icon-xingzhuangjiehe19" @click="goBackList"></i>
            <span class="title_con">评价应用</span>
        </div>
        <div class="appraise_page">
            <!-- 这块再放一个树形的结构菜单切换的功能 -->
            <div class="appraise_page_left">
                <a-menu
                    :inlineCollapsed="false"
                    v-model:selectedKeys="selectedKeys"
                    v-model:openKeys="openKeys"
                    mode="inline"
                    @click="handleClick"
                    class="yd_root_menu"
                >
                    <template v-for="item in menuList" :key="item.name">
                        <template v-if="!item.children">
                            <a-menu-item :key="item.name">
                                <template #icon v-if="item.meta.icon">
                                    <i
                                        class="sider"
                                        :class="item.meta.icon"
                                    ></i>
                                </template>
                                {{ item.meta.title }}
                            </a-menu-item>
                        </template>
                        <template v-else>
                            <SubMenu :menu-list="item" />
                        </template>
                    </template>
                </a-menu>
            </div>

            <!-- 这块放切换的页面 -->
            <div class="appraise_page_right">
                <router-view style="height: 100%"></router-view>
            </div>
        </div>
    </div>
</template>

<script setup name="evalActivity">
import { reactive, onMounted, ref } from "vue";
import SubMenu from "./SubMenu.vue";
import { useRoute, useRouter } from "vue-router";
const router = useRouter();
const route = useRoute();
const openKeys = ref([]);
const selectedKeys = ref(route.matched.map((i) => i.name));

watch(
    () => route.path,
    () => {
        selectedKeys.value = route.matched.map((i) => i.name);
        openKeys.value = route.matched.map((i) => i.name);
    },
    { immediate: true }
);

const menuList = ref([
    {
        path: "/appModel/evalActivity/dataCenter",
        name: "dataCenter",
        meta: { title: "数据中心", icon: "icon-nav_sy-nor" },
    },
    {
        path: "/appModel/evalActivity/evaluationManage",
        name: "evaluationManage",
        meta: { title: "评价管理", icon: "icon-nav_dz-nor" },

        children: [
            {
                path: "/appModel/evalActivity/evaluationManage/activity",
                name: "evaluationActivity",
                meta: { title: "评价活动", icon: "" },
            },
            {
                path: "/appModel/evalActivity/evaluationManage/record",
                name: "evaluationRecord",
                meta: { title: "评价记录", icon: "" },
            },
        ],
    },
    {
        path: "/appModel/evalActivity/growthSystem",
        name: "growthSystem",
        meta: { title: "成长体系", icon: "icon-nav_dz-nor" },

        children: [
            {
                path: "/appModel/evalActivity/growthSystem/medalRecords",
                name: "medalRecords",
                meta: { title: "勋章记录", icon: "" },
            },
        ],
    },
]);

// 返回应用中心
const goBackList = () => {
    router.push({ path: "/app/list" });
};

const state = ref({});

const handleClick = (e) => {
    router.push({ name: e.key, query: { ...route.query } });
};
</script>

<style lang="less" scoped>
.appraise {
    height: calc(100vh - 74px);
    overflow: hidden auto;
    display: flex;
    flex-direction: column;
}

.appraise_head_back {
    padding-left: 20px;
    height: 60px;
    line-height: 60px;
    border-bottom: 1px solid #d9d9d9;
}

.appraise_page {
    display: flex;

    flex: auto;
    .appraise_page_left {
        flex: 0 0 200px;
        width: 200px;
        overflow: hidden;
        border-right: 1px solid #d9d9d9;
    }
    .appraise_page_right {
        flex: auto;
    }
}

.yd_menu {
    margin: 20px 0 0 20px;

    .yd_menu_item {
        height: 40px;
        line-height: 40px;
        border-radius: 4px 0 0 4px;
        text-indent: 22px;
        cursor: pointer;
        font-size: 14px;
        display: flex;

        .sider {
            width: 26px;
        }

        &.active {
            background: @gray-background;
            color: @primary-color;
        }
    }
}

.yd_root_menu {
    padding-top: 20px;
    :deep(.ant-menu-item) {
        &:hover {
            background-color: #e8f8f3;
            color: #19be8d;
        }
    }
    :deep(.ant-menu-submenu-title) {
        &:hover {
            background-color: #e8f8f3;
            color: #19be8d;
        }
    }
    :deep(.ant-menu-item-selected) {
        background-color: #e8f8f3;
        color: #19be8d;

        // &:before {
        //     content: "";
        //     width: 4px;
        //     height: 26px;
        //     background-color: #19be8d;
        //     position: absolute;
        //     top: 5px;
        //     left: 0;
        //     border-radius: 0px 2px 2px 0px;
        // }

        & > .ant-menu-submenu-title {
            color: #19be8d;
        }
        &:active {
            background-color: #e8f8f3;
        }
        &:hover {
            background-color: #e8f8f3;
        }
    }
    :deep(.ant-menu-submenu-selected) {
        & > .ant-menu-submenu-title {
            color: #19be8d;
        }
    }
    :deep(.ant-menu-item-active) {
        background-color: #e8f8f3;
        color: #19be8d;
    }
}

.title_con {
    font-weight: 600;
    font-size: 16px;
    color: #262626;
}
</style>
