<template>
    <div class="borrowbooks_info">
        <a-spin :spinning="state.spinning">
            <a-row
                justify="start"
                align="top"
                :gutter="[24, 16]"
                v-if="state.list && state.list.length != 0"
            >
                <a-col
                    :sm="14"
                    :lg="12"
                    :xl="8"
                    :xxl="6"
                    v-for="(item, index) in state.list"
                    :key="item.id + index"
                >
                    <div class="borrowbooks_list">
                        <div class="item_background">
                            <a-tooltip placement="top">
                                <template #title>
                                    <span>{{ item.title }}</span>
                                </template>
                                <div class="title">
                                    {{ item.title }}
                                </div>
                            </a-tooltip>
                        </div>
                        <div class="item_info">
                            <span>
                                场地：
                                <span class="info">
                                    {{ item.siteName }}
                                </span>
                            </span>
                            <span>
                                序列号：
                                <span class="info">
                                    {{ item.no }}
                                </span>
                            </span>
                        </div>
                    </div>
                </a-col>
            </a-row>
            <div v-else class="noData">
                <img src="/image/empty.png" alt="" />
                <p>暂无借还书机信息</p>
            </div>
        </a-spin>
        <!-- 分页 -->
        <div class="pagination" v-if="state.pagination.total > 1">
            <span v-if="state.pagination.total > 0" style="padding-right: 6px"
                >共{{ state.pagination.total }}条</span
            >
            <a-pagination
                :defaultPageSize="8"
                class="pagination_class"
                v-model:current="state.pagination.pageNo"
                show-quick-jumper
                :total="state.pagination.total"
                @change="onPaginationChange"
            />
        </div>
    </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import { getBorrowBooksList } from '@/api/borrowBooks.js'
const state = reactive({
    pagination: {
        pageNo: 1,
        pageSize: 8,
        total: 0
    },
    spinning: false,
    list: []
})

function getBorrowBooksListFn() {
    state.spinning = true
    const params = {
        ...state.pagination
    }
    getBorrowBooksList(params)
        .then((res) => {
            const { list, total, pageNo, pageSize } = res.data
            state.list = list
            state.pagination.total = total
            state.pagination.pageSize = pageSize
            state.pagination.pageNo = pageNo
        })
        .finally(() => {
            state.spinning = false
        })
}

function onPaginationChange(pageNo, pageSize) {
    state.pagination.pageNo = pageNo
    state.pagination.pageSize = pageSize
    getBorrowBooksListFn()
}

onMounted(() => {
    getBorrowBooksListFn()
})
</script>

<style lang="less" scoped>
.borrowbooks_info {
    padding: 16px;
    .borrowbooks_list {
        min-width: 290px;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        flex-direction: column;
        background: #ffffff;
        border-radius: 6px;
        max-height: 306px;
        border: 1px solid #d9d9d9;
        .item_background {
            background: url('/image/borrowBooks.png') center center no-repeat;
            background-size: cover;
            height: 222px;
            width: 100%;
            .title {
                cursor: pointer;
                width: 100%;
                padding: 0px 10px;
                margin-top: 28px;
                text-align: center;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
                font-size: 18px;
                font-family: PingFangSC-Semibold, PingFang SC;
                font-weight: 600;
                color: #ffffff;
                line-height: 25px;
                text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.3);
            }
        }
        .item_info {
            height: 84px;
            width: 100%;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #595959;
            line-height: 20px;
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            padding: 8px 16px;
            .info {
                color: #262626;
            }
            span {
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
            }
        }
    }

    .noData {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin: 200px;

        img {
            width: 180px;
            height: 180px;
        }

        p {
            margin: 0;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.65);
            line-height: 20px;

            span {
                color: @primary-color;
                cursor: pointer;
            }
        }
    }
    .pagination {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: 22px;
    }
}
</style>
