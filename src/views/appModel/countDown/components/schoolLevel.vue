<template>
    <div class="classess">
        <!-- <div class="header">
            <a-select
                v-model:value="state.classesId"
                @change="changeClass"
                style="width: 300px"
            >
                <a-select-option
                    v-for="item in state.classList"
                    :key="item.classesId"
                    >{{ item.classesName }}
                </a-select-option>
            </a-select>
        </div> -->
        <div class="classes">
        <div class="list">
            <div class="list_item">
                <div class="add_count_downadd" @click="state.visible = true">
                    <p class="iconfont icon-xingzhuangjiehe14"></p>
                    <div class="add_title">新建倒计时</div>
                </div>
            </div>
            <div
                class="list_item"
                v-for="item in state.countDownList"
                :key="item.id"
            >
                <div class="add_count_down imgBg" :style="{ 'background': `url(${bgObj[item.templateUrl]})` }">
                    <div class="count_time">
                        {{
                            item.countDown
                                ? item.countDown
                                : "0"
                        }}
                        <span>天</span>
                    </div>
                    <div class="count_title">
                        {{
                            item.activityTheme
                                ? "距离" + item.activityTheme
                                : ""
                        }}
                    </div>
                    <div class="del" @click="del(item)">×</div>
                </div>
            </div>
        </div>
    </div>
    </div>
    <a-drawer
        :maskClosable="false"
        v-model:visible="state.visible"
        title="新增倒计时"
        :footerStyle="{ textAlign: 'center' }"
        @ok="saveCountDown"
        @close="cancal"
        width="530px"
    >
        <template #footer>
            <a-button @click="cancal">取消</a-button>
            <a-button
                type="primary"
                style="margin-left: 12px"
                :loading="state.btnLoading"
                @click="saveCountDown"
                >确定</a-button
            >
        </template>
        <a-form ref="formRef" :rules="rules" :model="state.sendData">
            <a-form-item label="倒计时主题" name="activityTheme">
                <a-input
                placeholder="请填写倒计时主题"
                show-count :maxlength="8"
                    v-model:value="state.sendData.activityTheme"
                ></a-input>
            </a-form-item>
            <a-form-item label="截止时间" name="activityTime">
                <a-date-picker
                    style="width: 100%"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                    v-model:value="state.sendData.activityTime"
                />
            </a-form-item>
            <!-- <a-form-item label="应用到班级首页信息窗口" name="isPush">
                <a-switch v-model:checked="state.sendData.isPush" checked-children="开" un-checked-children="关" :checkedValue="1" :unCheckedValue="0" />
            </a-form-item> -->
            <a-form-item label="选择模板">
                <a-select
      v-model:value="state.sendData.templateUrl"
    >
      <a-select-option :value="1">模板一</a-select-option>
      <a-select-option :value="2">模板二</a-select-option>
      <a-select-option :value="3">模版三</a-select-option>
      <a-select-option :value="4">模板四</a-select-option>
      <a-select-option :value="5">模板五</a-select-option>
      <a-select-option :value="6">模板六</a-select-option>
      <a-select-option :value="7">模板七</a-select-option>
      <a-select-option :value="8">模板八</a-select-option>
      <a-select-option :value="9">模板九</a-select-option>
    </a-select>
                <!-- <a-radio-group
                    v-model:value="state.sendData.templateUrl"
                    option-type="button"
                >
                    <a-radio :value="1">模版一</a-radio>
                    <a-radio :value="2">模版二</a-radio>
                    <a-radio :value="3">模版三</a-radio>
                </a-radio-group> -->
            </a-form-item>
        </a-form>
        <div
            class="add_count_downs imgBg"
            :style="{ 'background': `url(${bgObj[state.sendData.templateUrl]})` }">
            <div class="count_time">
                {{
                    state.sendData.activityTime
                        ? getOffsetDays(state.sendData.activityTime)
                        : "0"
                }}
                <span>天</span>
            </div>
            <div class="count_title">
                {{
                    state.sendData.activityTheme
                        ? "距离" + state.sendData.activityTheme
                        : ""
                }}
            </div>
        </div>
    </a-drawer>
</template>

<script setup>

/* eslint-disable */
import dayjs from "dayjs";
// import { classInfo } from "@/api/classSign";
import { onMounted, reactive, ref, h, createVNode } from "vue";
import {
    countdownCreate,
    countdownList,
    countdownDelete,
} from "@/api/countdown";
import { message, Modal } from "ant-design-vue";
import { ExclamationCircleFilled } from "@ant-design/icons-vue";
import { Local } from "@/utils/storage.ts";


const bgObj = {
    1: '/image/count1.png',
    2: '/image/count2.png',
    3: '/image/count3.png',
    4: '/image/count4.png',
    5: '/image/count5.png',
    6: '/image/count6.png',
    7: '/image/count7.png',
    8: '/image/count8.png',
    9: '/image/count9.png'
}

const state = reactive({
    classList: [],
    classesId: Local.get("director_classId") || "",
    visible: false,
    btnLoading: false,
    sendData: {
        // isPush: 0,
        activityTheme: "",
        activityTime: "",
        templateUrl: 1,
        kind: 2,
    },
    countDownList: [],
});
const formRef = ref();
const rules = {
    activityTime: [
        { required: true, message: "请选择截止时间", trigger: "blur" },
    ],
    activityTheme: [{ required: true, message: "请输入倒计时主题" }],
};

// const getClassList = () => {
//     classInfo({ code: 'countDown' }).then((res) => {
//         state.classList = res.data
//         state.classesId = res.data[0]?.classesId
//         getCountdownList()
//     })
// }


const getCountdownList = () => {
    const data = {
        kind: 2,
    };
    countdownList(data).then((res) => {
        state.countDownList = res.data;
    });
};
const getOffsetDays = (time) => {
    if (time) {
        const currentTime = Date.now();
        const date = Date.parse(time);
        let offsetTime = date - currentTime;
        offsetTime = Math.abs(offsetTime);
        const offsetDays = Math.floor(offsetTime / 1000 / 60 / 60 / 24);
        return offsetDays + 1;
    }
};
const cancal = () => {
    state.visible = false;
    formRef.value.resetFields();
};
const saveCountDown = () => {
    formRef.value
        .validate()
        .then(() => {
            state.btnLoading = true;
            state.submitBtnLoading = true;
            const data = {
                ...state.sendData,
                classesId: state.classesId,
            };
            countdownCreate(data)
                .then((res) => {
                    message.success("操作成功");
                    formRef.value.resetFields();
                })
                .finally(() => {
                    state.btnLoading = false;
                    state.visible = false;
                    getCountdownList();
                });
        })
        .catch((err) => {
            console.log(err);
        });
};
const disabledDate = (current) => {
    return current && current <= dayjs().endOf("day");
};
const del = (val) => {
    Modal.confirm({
        title: "删除提示",
        icon: createVNode(ExclamationCircleFilled),
        content: h("div", {}, [
            h("p", `确定删除倒计时【${val.activityTheme}】？`),
        ]),
        okText: "确认",
        cancelText: "取消",
        onOk() {
            countdownDelete({ id: val.id })
                .then((res) => {
                    message.success("操作成功");
                })
                .finally(() => {
                    getCountdownList();
                });
        },
    });
};
const changeClass = () => {
    getCountdownList();
};
onMounted(() => {
    getCountdownList()
    // getClassList();
});


</script>

<style lang="less" scoped>
.classess {
    // height: 100vh;
    margin: 24px;
    .header {
        width: 100%;
        // background: url("/image/540x360-1.png");
    }
    .list {
        display: flex;
        flex-wrap: wrap;
        .list_item {
            // width: 470px;
            .add_count_down {
                position: relative;
                .del {
                    position: absolute;
                    text-align: center;
                    width: 20px;
                    height: 20px;
                    line-height: 18px;
                    top: 10px;
                    right: 10px;
                    color: #fff;
                    cursor: pointer;
                    background: #f5222d;
                    border-radius: 50%;
                }
            }
        }
        .list_item:first-child {
            text-align: center;
            cursor: pointer;
            p:first-child {
                font-size: 30px;
                color: #bfbfbf;
                line-height: 60px;
            }
            p:last-child {
                font-size: 20px;
                color: rgba(0, 0, 0, 0.55);
            }
        }
    }
}
.add_count_downadd{
    width: 394px;
    height: 222px;
    margin: 10px 10px 10px 0px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    border: 1px dashed #D9D9D9;
    border-radius: 6px;
    background: #F6F6F6;
    font-size: 20px;
}
.add_count_down {
    width: 394px;
    height: 222px;
    margin: 10px 10px 10px 0px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    // border: 1px solid #e1e1e1;
    // border-radius: 6px;
    background: #F6F6F6;
    font-size: 20px;
    .count_time {
        color: #fcc23d;
        font-size: 2em;
        span {
            font-size: 0.5em;
        }
    }
    .count_title {
        line-height: 20px;
        color: #909399;
        font-size: 16px;
        margin-top: 6px;
    }
}

.add_count_downs {
    height: 254px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    font-size: 20px;
    .count_time {
        color: #fcc23d;
        font-size: 2em;
        span {
            font-size: 0.5em;
        }
    }
    .count_title {
        line-height: 20px;
        color: #909399;
        font-size: 16px;
        margin-top: 6px;
    }
}

.imgBg {
    padding-left: 30px;
    background-repeat: no-repeat, repeat !important;
    background-position: center !important;
    background-size: 100% 100% !important;
}
.add_title{
font-size: 14px;
font-weight: 400;
color: rgba(0,0,0,0.65);
}
</style>
