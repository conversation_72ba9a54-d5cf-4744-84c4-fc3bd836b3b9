<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-04-18 16:28:16
 * @LastEditors: jingrou
 * @LastEditTime: 2023-05-06 11:38:06
-->
<template>
    <div class="videoAlbum">
        <div class="header">
            <a-tabs v-model:activeKey="menuIndex" @change="changeTab">
                <template #leftExtra>
                    <slot name="leftExtra">
                        <a-button type="text" class="tabs_back" @click="back">
                            <i class="iconfont icon-xingzhuangjiehe19" />
                            <!-- <arrow-left-outlined class="tabs_back__icon" /> -->
                        </a-button>
                    </slot>
                </template>
                <a-tab-pane
                    v-for="item in tabList"
                    :key="item.key"
                    :tab="item.label"
                ></a-tab-pane>
            </a-tabs>
        </div>
        <component :is="comName"></component>
    </div>
</template>

<script setup>
import { shallowRef, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import classes from './components/classes.vue'
import schoolLevel from './components/schoolLevel.vue'
const Route = useRoute()
const Router = useRouter()
const menuIndex = ref('classes')
const comName = shallowRef(classes)

const tabList = ref([
    {
        label: '班级',
        key: 'classes'
    },
    {
        label: '校级',
        key: 'schoolLevel'
    }
])
const changeTab = (val) => {
    comName.value = val === 'classes' ? classes : schoolLevel
}
const back = () => {
    Router.push({
        name: Route.query.source
    })
}
</script>

<style lang="less" scoped>
// .tabs_back {
//     .tabs_back__icon {
//         color: @primary-color;
//     }
// }

:deep(.ant-tabs-nav) {
    padding-left: v-bind(showBackStyle);
    margin-bottom: 0;
    border-bottom: 1px solid @border-color-base;
    line-height: 33px;
}

.faceLibrary_warp_pageitem {
    height: calc(100% - 57px);
}
</style>
