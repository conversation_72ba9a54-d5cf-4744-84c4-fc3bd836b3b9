<template>
    <div>
        <div class="app" v-if="loding">
            <a-skeleton active />
            <a-skeleton active />
            <a-skeleton active />
            <a-skeleton active />
            <a-skeleton active />
            <a-skeleton active />
        </div>
        <router-view></router-view>
    </div>
</template>
<script setup>
import { useRoute, useRouter } from "vue-router";
import ViewCom from "@/router/view.vue";
import { constantRoutes } from "@/config/router.configs.js";
import modules from "@/store/modules/modules.js";
import { useStore } from "vuex";
import { getNewRouters } from "@/api/user.js";
const store = useStore();
const route = useRoute();
const loding = ref(false);
const router = useRouter();
// 应用 路由处理
const regRoute = (arr) => {
    let list = [];
    arr?.forEach((i) => {
        if (!(i.type == "C" || i.type == "A")) return;
        const btnList = i.btnList.map((v) => {
            return {
                label: v.name,
                value: v.perms,
            };
        });

        let item = {
            path: i.path,
            name: i.component,
            meta: {
                btnList,
                title: i.name,
                icon: i.icon,
                tabList: i.tabList,
                customParams: i.customParams,
            },
            hideInMenu: i.type != "C",
            redirect: i.children.length ? i.children[0].path : "",
        };
        // 场地巡查 特需处理
        if (i.component === "patrol" && !i.children.length) {
            router.push({ path: "/401" });
            return;
        }
        if (i.path.indexOf("/appModel") !== -1) {
            const paths = i.path.split("/");
            item.meta.code = paths[2];
        }
        if (i.children.length > 0) {
            item.children = regRoute(i.children);
        }
        if (i.filePath) {
            item.component = modules[i.filePath];
        } else {
            item.component = ViewCom;
        }
        list.push(item);
    });
    return list;
};
const init = (Platform) => {
    if (Platform == "oaApproveAdmin") {
        return false;
    }
    loding.value = true
    getNewRouters({ Platform })
        .then(({ data }) => {
            const appRouter = regRoute(data);
            const list = constantRoutes[0];
            list.children[0].children = appRouter;
            router.addRoute(list);
            store.commit("base/PERMISSION_LIST", data);
            loding.value = false;
            setTimeout(() => {
                router.push({
                    path: appRouter[0]?.redirect || appRouter[0]?.path,
                    query: route.query,
                });
            }, 1);
        })
        .catch((error) => {
            if (error.data.code === 1002003014) {
                router.push({ path: "/401", query: { redirect: "app/list" } });
            }
        }).finally(() => loding.value = false);
};
let old_code = null;
watch(() => route.params.pathMatch,
    (val) => {
        setTimeout(() => {
            const code = val?.[0];
            if (!code) return false;
            if (old_code == code) {
            } else {
                old_code = code;
                init(code);
            }
        }, 100);
    },
    { immediate: true, deep: true }
);

</script>
<style lang="less" scoped></style>
