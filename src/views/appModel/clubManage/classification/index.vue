<!-- 社团分类 -->
<template>
    <div class="classification club-pd">
        <EnquiryForm class="enquiry_form" formLayout="horizontal" :formState="state.formState"
            :enquiryFormItme="formDataItme" @emitEnquiryFormSubmitReset="handeEnquiryFormSubmit"
            @emitEnquiryFormReset="handeEnquiryFormSubmit" />

        <YTable :spinning="state.spinning" :rowSelection="false" :totals="state.paginations" :columns="columns"
            :dataSource="state.tableSource" :bordered="false" @onSelectedRowKeys="handleTableChange">
            <template #handleItem>
                <div class="table_info" style="padding-top: 0">
                    <div class="table_title"></div>
                    <div class="table_hander">
                        <a-button type="primary" v-auth="'clubManage.classification.add'" @click="handlerEdit">
                            <template #icon>
                                <plus-outlined />
                            </template>
                            新增
                        </a-button>
                    </div>
                </div>
            </template>
            <template #bodyCell="{ column, record, index, text }">
                <div v-if="column.dataIndex === 'index'" :style="{ width: column.width + 'px' }">
                    {{ index + 1 }}
                </div>
                <a-badge v-else-if="column.dataIndex === 'isEnabled'" :status="record.isEnabled ? 'success' : 'error'"
                    :text="record.isEnabled ? '启用' : '禁用'" />
                <div v-else-if="column.dataIndex === 'operation'" :style="{ width: column.width + 'px' }">
                    <a-space>
                        <a-button class="button-link" type="link" v-auth="'clubManage.classification.disable'"
                            @click="handlerDisable(record)">
                            {{ !record.isEnabled ? '启用' : '禁用' }}
                        </a-button>
                        <a-button class="button-link" type="link" v-auth="'clubManage.classification.edit'"
                            @click="handlerEdit(record)">编辑</a-button>
                        <a-button class="button-link" type="link" v-auth="'clubManage.classification.delete'"
                            :style="{ color: !record.isEnabled ? '#999' : 'red' }" :disabled="!record.isEnabled"
                            @click="handlerDelete(record.id)">删除</a-button>
                    </a-space>
                </div>
                <div v-else :style="{ width: column.width + 'px' }">
                    <Tooltip :title="text" :maxWidth="600">
                    </Tooltip>
                </div>
            </template>
        </YTable>
        <ClassificationDrawer v-model:visible="state.classificationVisible" :addEditParams="state.addEditParams"
            @updateClassificationList="classificationList" />
    </div>
</template>

<script setup>
import { createVNode, reactive } from 'vue'
import EnquiryForm from '@/components/enquiryForm'
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import ClassificationDrawer from './classificationDrawer.vue'
import { columns, formDataItme } from './tableData'
import { Modal, message } from 'ant-design-vue'
import { deletePageSize } from "@/utils/util.ts";
import { postClubCategoryPage, postClubCategoryEnabled, postClubCategoryDelete } from '@/api/clubManage'

const state = reactive({
    formState: {
        name: '',
        isEnabled: null,
    },
    spinning: false,
    tableSource: [],
    paginations: {
        pageNo: 1, // 页码
        pageSize: 10, // 条数
        total: 0
    },
    classificationVisible: false,
    addEditParams: {}
});
const classificationList = () => {
    const params = {
        ...state.formState,
        ...state.paginations
    }
    state.spinning = true
    postClubCategoryPage(params).then(({ data }) => {
        const { list, pageNo, pageSize, total } = data
        state.tableSource = list
        state.paginations.pageNo = pageNo
        state.paginations.pageSize = pageSize
        state.paginations.total = total
    }).finally(() => {
        state.spinning = false
    })

}
/* 表单查询結果回調 、 重置表单查询 */
const handeEnquiryFormSubmit = (item) => {
    state.formState = item
    state.paginations.pageNo = 1
    classificationList()
}

/* 分页事件 */
const handleTableChange = (data) => {
    const { current, pageSize, total } = data;
    state.paginations.pageNo = current
    state.paginations.pageSize = pageSize
    state.paginations.total = total
    classificationList()
}
// 禁用
const handlerDisable = (item) => {
    const { id, isEnabled } = item
    const params = {
        id,
        isEnabled: !isEnabled
    }
    postClubCategoryEnabled(params).then((res) => {
        message.success(res.message)
        classificationList()
    })
}
// 删除
const handlerDelete = (id) => {
    Modal.confirm({
        title: '删除提示',
        icon: createVNode(ExclamationCircleOutlined),
        content: '该类别下所有社团及活动内容将被删除，是否确认删除？',
        okText: '确认',
        cancelText: '取消',
        onOk() {
            postClubCategoryDelete({ id }).then((res) => {
                message.success(res.message)
                deletePageSize(state.paginations, state.tableSource.length, classificationList);
            })
        }
    })
}
// 编辑
const handlerEdit = (item) => {
    state.classificationVisible = true
    state.addEditParams = {}
    if (item?.id) {
        state.addEditParams = item
    }
}

onMounted(() => {
    classificationList()
})
</script>

<style lang="less"></style>
