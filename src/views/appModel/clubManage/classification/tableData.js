export const columns = [
    {
        title: "序号",
        key: "index",
        dataIndex: "index",
        width: 80,
    },
    {
        title: "社团类别",
        key: "name",
        dataIndex: "name",
        width: 250,
    },
    {
        title: "介绍",
        key: "description",
        dataIndex: "description",
        width: 250,
    },
    {
        title: "社团数",
        key: "clubCount",
        dataIndex: "clubCount",
        width: 200,
    },
    {
        title: "状态",
        key: "isEnabled",
        dataIndex: "isEnabled",
        width: 200,
    },
    {
        title: "操作",
        key: "operation",
        dataIndex: "operation",
        width:100,
    },
];

// 条件查询
export const formDataItme = [
    {
        label: "社团类别",
        type: "a-input",
        modelValue: "name",
        placeholder: "请输入",
    },
    {
        label: "是否禁用",
        type: "a-select",
        modelValue: "isEnabled",
        placeholder: "请选择",
        fieldNames: { label: "label", value: "value" },
        attributeValue: [
            {
                value: null,
                label: "全部",
            },
            {
                value: true,
                label: "启用",
            },
            {
                value: false,
                label: "禁用",
            },
        ],
    },
];
