<template>
    <a-drawer width="500" @close="closeDrawer" class="drawerButton" :title="state.title" :maskClosable="false"
        v-model:visible="props.visible">
        <a-form ref="addEditRef" layout="vertical" name="base" :rules="rules" :model="state.addEditForm">
            <a-form-item label="社团类别：" name="name">
                <a-input :maxlength="40" showCount v-model:value="state.addEditForm.name" autocomplete="off"
                    placeholder="请输入" />
            </a-form-item>
            <a-form-item label="介绍：" name="description">
                <a-textarea class="reset-textarea" :maxlength="300" showCount
                    v-model:value="state.addEditForm.description" :auto-size="{ minRows: 2, maxRows: 20 }"
                    placeholder="请输入" />
            </a-form-item>
        </a-form>
        <template #footer>
            <div class="footer">
                <a-space>
                    <a-button @click="closeDrawer">取消</a-button>
                    <a-button type="primary" :loading="state.loading" @click="saveConfirm">确认</a-button>
                </a-space>
            </div>
        </template>
    </a-drawer>
</template>

<script setup>
import { postClubCategoryCreate, postClubCategoryUpdate } from '@/api/clubManage'
import { message } from 'ant-design-vue'
const addEditRef = shallowRef()
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    addEditParams: {
        type: Object,
        default: () => ({})
    }
})
const emit = defineEmits(['update:visible', 'updateClassificationList'])

const state = reactive({
    title: '新增',
    loading: false,
    addEditForm: { id: '', name: '', description: '' }
})
const rules = {
    name: [
        {
            required: true,
            message: '请输入社团类别',
            trigger: 'blur'
        }
    ],
    description: [
        {
            required: true,
            message: '请输入社团介绍',
            trigger: 'blur'
        }
    ]
}
const closeDrawer = () => {
    addEditRef.value.resetFields()
    emit('update:visible', false)
}
const saveConfirm = () => {
    addEditRef.value
        .validate()
        .then(() => {
            let Api = postClubCategoryCreate
            if (state.addEditForm.id) {
                Api = postClubCategoryUpdate
            }
            state.loading = true
            Api(state.addEditForm).then((res) => {
                message.success(res.message)
                emit('updateClassificationList')
                closeDrawer()
            }).finally(() => {
                state.loading = false
            })
        })
}
watch(() => props.visible, (newVal) => {
    if (newVal) {
        const { id, name, description } = props.addEditParams
        state.title = id ? '编辑' : '新增'
        state.addEditForm = { id, name, description }
    }
})

</script>

<style lang="less" scoped>
.reset-textarea {
    :deep(textarea) {
        padding-bottom: 20px !important;
        min-height: 160px !important;
    }
}
</style>
