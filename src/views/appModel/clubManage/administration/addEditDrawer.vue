<template>
    <a-drawer width="500" @close="closeDrawer" class="drawerButton" :title="state.title" :maskClosable="false"
        v-model:visible="props.visible">
        <a-form ref="addEditAdminRef" layout="vertical" name="base" :rules="rules" :model="state.addEditForm">
            <a-form-item label="社团名称：" name="name">
                <a-input :maxlength="40" showCount v-model:value="state.addEditForm.name" placeholder="请输入" />
            </a-form-item>
            <a-form-item label="社团封面：" name="iconUrl">
                <!-- <CropPicture class="reset-crop-picture" :imgHeight="'104px'" v-model:value="state.addEditForm.iconUrl"
                    @change="handleChange" :minCropBoxWidth="140" :minCropBoxHeight="140" :isNum="1" :title="state.addEditForm.iconUrl?'修改图片':'点击上传图片'" /> -->
                <a-upload :showUploadList="false" name="coverImg" accept=".jpg,.jpeg,.png,.bmp,.JPG,.JPEG,.BMP"
                    :maxCount="1" list-type="picture-card" :beforeUpload="beforeUploadcoverImg" @change="handleChange"
                    v-model:file-list="state.fileList">
                    <a-image v-if="state.addEditForm.iconUrl" class="upload_item_image" :preview="false" width="86px"
                        height="86px" :src="state.addEditForm.iconUrl"></a-image>
                    <div v-else>
                        <plus-outlined />
                        <div style="margin-top: 8px">点击上传</div>
                    </div>
                </a-upload>
            </a-form-item>
            <a-form-item label="社团口号：" name="slogan">
                <a-input :maxlength="100" showCount v-model:value="state.addEditForm.slogan" autocomplete="off"
                    placeholder="请输入" />
            </a-form-item>
            <a-form-item label="社团分类：" name="categoryId">
                <a-select v-model:value="state.addEditForm.categoryId" style="width: 100%" placeholder="请选择">
                    <a-select-option v-for="item in attributeValue" :key="item.id" :value="item.id">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="社团等级：" name="level">
                <a-select v-model:value="state.addEditForm.level" style="width: 100%" placeholder="请选择">
                    <a-select-option value="university">校级</a-select-option>
                    <a-select-option value="department">院级</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="社团规模：" name="size">
                <a-select v-model:value="state.addEditForm.size" style="width: 100%" :disabled="isEdit"
                    placeholder="请选择">
                    <a-select-option value="small">小</a-select-option>
                    <a-select-option value="medium">中</a-select-option>
                    <a-select-option value="large">大</a-select-option>
                </a-select>
            </a-form-item>

            <a-form-item label="社团介绍：" name="description">
                <a-textarea class="reset-textarea" :maxlength="5000" showCount
                    v-model:value="state.addEditForm.description" :auto-size="{ minRows: 2, maxRows: 20 }"
                    placeholder="请输入" />
            </a-form-item>

            <a-form-item label="社团负责人：" name="ownerId">
                <a-input v-model:value="state.addEditForm.ownerName" autocomplete="off" placeholder="请选择"
                    :disabled="isEdit" @click="openControlRef('ownerId')" />
            </a-form-item>

            <a-form-item label="指导老师：" name="advisorSelection">
                <a-input v-model:value="state.addEditForm.advisorName" autocomplete="off" placeholder="请选择"
                    :disabled="isEdit" @click="openControlRef('advisorSelection')" />
            </a-form-item>
        </a-form>
        <template #footer>
            <a-space>
                <a-button @click="closeDrawer">取 消</a-button>
                <a-button type="primary" :loading="state.loading" @click="saveConfirm">提交审核</a-button>
            </a-space>
        </template>
    </a-drawer>

    <PersonSelectionControl ref="controlRef" :type="SELECT_TYPE.PEOPLE" :maxOptional="3" :selected="state.checkedList"
        :tabs="state.tabsSelection" @search="searchSelect" @toggleLevel="toggleLevel" @toggleTabs="toggleTabs"
        @submit="submit" />
</template>

<script setup>
// import CropPicture from '@/components/CropPicture/index.vue'
import { message } from 'ant-design-vue'
import { rules } from './tableData'
import { getFileUpload, postClubGet, postClubCreate, postClubUpdate } from '@/api/clubManage'
import PersonSelectionControl from "@/components/PersonSelectionControl/index.vue";
import {
    SELECT_TYPE,
    DISPLAY_MODE,
} from "@/components/PersonSelectionControl/constants.js";
import { useStore } from "@/store/index";
import {
    getSchoolRollStudentPage,
    getDepartmentPersonnel,
} from "@/api/faceLibrary.js";

const store = useStore();
const addEditAdminRef = shallowRef()

const isEdit = shallowRef(false)

const clubLeader = [
    {
        tab: "社团负责人",
        checked: true,
        id: 1,
        key: "ownerId",
        // 有userId 则是人
        personField: { key: "studentCode", value: ["studentCode"] },
        // 单选 true 多选 false
        single: true,
        // 输入框是否显示
        searchOption: {
            show: true,
            displayMode: DISPLAY_MODE.NEW,
        },
    },
];
const Instructor = [
    {
        tab: "指导老师",
        checked: true,
        id: 2,
        key: "advisorSelection ",
        // 有userId 则是人
        personField: { key: "userId", value: ["userId"] },
        // 单选 true 多选 false
        single: false,
        // 输入框是否显示
        searchOption: {
            show: true,
            displayMode: DISPLAY_MODE.NEW,
        },
    },
];
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    addEditParams: {
        type: Object,
        default: () => ({})
    },
    attributeValue: {
        type: Array,
        default: () => []
    }
})
const emit = defineEmits(['update:visible', 'updateAffiliatedUnitList'])
const state = reactive({
    title: '新增',
    loading: false,
    fileList: [],
    addEditForm: {
        id: '',
        name: "",
        iconUrl: '',
        categoryId: null,
        ownerId: null,
        ownerName: null,
        slogan: null,
        level: null,
        size: null,
        description: "",
        advisorSelection: [],
        advisorName: [],
    },
    // 选认框
    tabsSelection: clubLeader,
    tableState: {
        deptId: "", // 部门ID
        status: "", // 状态
        id: "", // 教师id
        name: "",
    },
    checkedList: [],
})
const closeDrawer = () => {
    addEditAdminRef.value.resetFields()
    emit('update:visible', false)
}
const saveConfirm = () => {
    addEditAdminRef.value
        .validate()
        .then(() => {
            let Api = postClubCreate
            if (state.addEditForm.id) {
                Api = postClubUpdate
            }
            state.loading = true
            const params = {
                ...state.addEditForm
            }
            if (params.advisorSelection.length) {
                params.advisorSelection = JSON.stringify(params.advisorSelection)
            }

            Api(params).then((res) => {
                message.success(res.message)
                emit('updateAffiliatedUnitList')
                closeDrawer()
            }).finally(() => {
                state.loading = false
            })

        })
}

// 图片上传之前的回调
const beforeUploadcoverImg = (file) => {
    // file.size 文件大小
    const isSize = file.size / 1024 / 1024 < 2;
    if (!isSize) {
        message.error("上传图片大小不能超过2MB!");
        return false;
    }
    return false;
};

// const getBase64 = (file) => {
//     return new Promise((resolve, reject) => {
//         const reader = new FileReader()
//         reader.readAsDataURL(file)
//         reader.onload = () => resolve(reader.result)
//         reader.onerror = (error) => reject(error)
//     })
// }


// const handleChange = async (info) => {
//     try {
//         const base64Img = await getBase64(info.file);
//         await getFileUpload(params).then(( { data }) => {
//             const { url } = data[0];
//             state.imgPathsList.push(url);
//             fileList.value = [base64Img];
//             state.addEditForm.iconUrl = fileList.value
//         });
//     } catch (error) {
//         console.log(error);
//     }
// };
// -
const handleChange = async (info) => {
    const params = new FormData();
    params.append("file", info.file);
    await getFileUpload(params).then(({ data }) => {
        const { url } = data[0];
        state.addEditForm.iconUrl = url;
        addEditAdminRef.value.validateFields(['iconUrl']);

    });
    state.fileList = info.fileList;
}
const handlerClubGetInfo = () => {
    const { id } = props.addEditParams
    postClubGet({ id }).then(({ data }) => {
        let _advisorSelection = []
        if (data.advisorSelection?.length) {
            _advisorSelection = JSON.parse(data.advisorSelection)
        }
        state.addEditForm = data
        state.addEditForm.advisorSelection = []
        let advisorName = []
        _advisorSelection?.forEach((v) => {
            state.addEditForm.advisorSelection.push(v.id)
            advisorName.push(v.name)
        })
        state.addEditForm.advisorName = advisorName
    })
}
watch(() => props.visible, (newVal) => {
    if (newVal) {
        state.title = '新增'
        state.addEditForm.advisorName = []
        state.addEditForm.advisorSelection = []
        state.addEditForm.ownerId = ''
        state.addEditForm.ownerName = ''
        state.addEditForm.id = ''
        isEdit.value = false
        if (props.addEditParams?.id) {
            isEdit.value = true
            handlerClubGetInfo()
            state.title = '编辑'
        }
    }
})
const departmentTree = computed(() => store.state.base.departmentTree)
const schoolRollTree = computed(() => store.state.base.schoolRollTree)
// ----------------- start 选年级班级框逻辑 ---------------
const controlRef = shallowRef();
// 开启选人弹框
const openControlRef = (item) => {
    // 社团负责人：单选
    state.checkedList = []
    const { ownerId, ownerName, advisorSelection, advisorName } = state.addEditForm
    if (item == 'ownerId') {
        state.tabsSelection = clubLeader
        if (ownerId) {
            state.checkedList = [{
                id: ownerId, name: ownerName,
                studentCode: ''
            }]
        }
        controlRef.value.modelState.dataSource = schoolRollTree.value;
        state.tableState.deptId = schoolRollTree.value[0]?.id || "";
    } else {
        // 指导leader：多选
        state.tabsSelection = Instructor
        if (advisorSelection.length) {
            state.checkedList = advisorSelection.map((v, idx) => {
                return {
                    id: v,
                    name: advisorName[idx],
                    userId: ''
                }
            })
        }
        controlRef.value.modelState.dataSource = departmentTree.value;
        state.tableState.deptId = departmentTree.value[0]?.id || "";
    }
    controlRef.value.modelState.open = true;
};
// 获取部门 学籍
const baseDepPersonnel = () => {
    store.dispatch("base/getDepartmentTree", "cloud");
    store.dispatch("base/getSchoolRollTree", "cloud");
};

const { roll_status_yes_id = [], emp_status_yes_id = [] } =
    store.state.selectSource.dictionary;
// 获取教职工人员列表
const getStaffPages = (callback) => {
    let Api = getDepartmentPersonnel;
    const { name, deptId, _type = 0 } = state.tableState;
    let params = {
        name,
        code: 'cloud',
        pageNo: 1,
        pageSize: 500,
    };
    if (state.tabsSelection[0].key === "ownerId") {
        // 学生
        Api = getSchoolRollStudentPage;
        params = {
            ...params,
            id: deptId,
            filterNonEltern: false,
            queryEltern: false,
            type: _type,
            statusList: roll_status_yes_id,
        };
    } else {
        params = {
            ...params,
            deptId,
            statusList: emp_status_yes_id,
        };
    }
    Api(params)
        .then(({ data }) => {
            let { list } = data;
            if (state.tabsSelection[0].key === "ownerId") {
                list = data?.studentPageListVO?.list || [];
            }
            callback(list);
        })
        .finally(() => {
            controlRef.value.modelState.loading = false;
        });
};

// 下級
const toggleLevel = (tabId, item = {}, options) => {
    const { index, trigger } = options;
    // 清空输入框
    state.tableState.name = "";
    if (tabId == 2) {
        // 这句用于到选角色是只选下一级的问题
        state.select_type = !index
            ? SELECT_TYPE.PEOPLE
            : SELECT_TYPE.DEPARTMENT;
    }
    // 面包屑
    if (!index) {
        // 第一层数据，恢复原本数据
        controlRef.value.modelState.dataSource =
            tabId == 1 ? state.treeDataStudent : state.roles;
    } else {
        state.tableState.deptId = item.id;
        state.tableState._type = item.type;
        const callback = (data) => {
            let children = item.children || [];
            controlRef.value.modelState.dataSource = children?.concat(
                data || []
            );
        };
        getStaffPages(callback);
    }
};

// Tabs切换
const toggleTabs = (item) => {
    state.tableState.name = "";
    if (item.key === "roles") {
        state.tableState.deptId = state.roles[0]?.id || "";
        controlRef.value.modelState.dataSource = state.roles;
    } else {
        // 重置
        state.tableState.deptId = state.treeDataStudent[0]?.id || "";
        controlRef.value.modelState.dataSource = state.treeDataStudent;
    }
};

// 搜人  查找教职工
function searchSelect(tabId, item) {
    if (item.name) {
        // 清空搜索输入框
        // 清空搜索查询list数据
        controlRef.value.modelState.searchTable.list = [];
        // 选人组件 - 首次聚焦教职工
        state.tableState.name = item.name;
        const callback = (data) => {
            controlRef.value.modelState.searchTable.list =
                controlRef.value.modelState.searchTable.list?.concat(data);
        };
        getStaffPages(callback);
    }
}


// 提交選人數據
const submit = (checked) => {
    let _id = []
    let _name = []
    checked?.forEach((item) => {
        if (state.tabsSelection[0].key === 'ownerId') {
            _id.push(item.id)
        } else {
            _id.push(item)
        }
        _name.push(item.name)
    })
    if (state.tabsSelection[0].key === 'ownerId') {
        state.addEditForm.ownerName = _name[0]
        state.addEditForm.ownerId = _id[0]
    } else {
        state.addEditForm.advisorName = _name
        state.addEditForm.advisorSelection = _id
    }
    state.checkedList = checked;
};

onMounted(() => {
    baseDepPersonnel()
})
// ----------------- end 选年级班级框逻辑 ---------------
</script>

<style lang="less" scoped>
.reset-textarea {
    :deep(textarea) {
        padding-bottom: 20px !important;
        min-height: 160px !important;
    }

    .reset-img {
        img {
            width: 80px !important;
            height: 80px !important;
        }
    }
}
</style>
