<template>
    <div class="details">
        <div class="header">
            <i class="iconfont icon-xingzhuangjiehe19" @click="handlerBack"></i>
            <span class='title'>详情</span>
        </div>
        <div class="content">
            <a-image :width="100" :height="100" :preview="false" :src="state.iconUrl || '/image/pic-mr.png'" />
            <div class="right">
                <ul class="list">
                    <li v-for="(item, idx) in state.massOrgan" :key="idx">
                        <span class="title">{{ item.title }}</span>
                        <span class="value"
                            :class="{ active: item.key === 'description', linHeight: ['description', 'slogan'].includes(item.key) }">
                            {{ item.value }}
                        </span>
                    </li>
                </ul>
                <a-divider style="border-color: #D9D9D9;" dashed />
                <ul class="list inline">
                    <li v-for="(item, idx) in state.massMember" :key="idx">
                        <span class="title">{{ item.title }}</span>
                        <span class="value">{{ item.value }}</span>
                    </li>
                </ul>
                <a-typography-title :level="5" style="margin-top: 10px;">社团审核</a-typography-title>
                <ul class="list">
                    <li v-for="(item, idx) in state.auditStatus" :key="idx">
                        <a-radio disabled v-if="item.key === 'approvalStatus'" :checked="true">同意</a-radio>
                        <template v-else>
                            <span class="title">{{ item.title }}</span>
                            <span class="value" :class="{ linHeight: ['approvalComment'].includes(item.key) }">
                                {{ item.value }}
                            </span>
                        </template>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>
<script setup>
import { inject } from "vue";
import { postClubGet } from '@/api/clubManage'
import dayjs from 'dayjs'

const state = reactive({
    iconUrl: '',
    massOrgan: [
        { title: '社团名称：', key: 'name', value: '' },
        { title: '社团分类：', key: 'categoryName', value: '' },
        { title: '社团口号：', key: 'slogan', value: '' },
        { title: '社团简介：', key: 'description', value: '' },
    ],
    massMember: [
        { title: '社团等级：', key: 'levelName', value: '' },
        { title: '成员数：', key: 'memberCount', value: '' },
        { title: '指导老师：', key: 'advisorSelection', value: '' },
        { title: '社团负责人：', key: 'ownerName', value: '' },
    ],
    auditStatus: [
        { title: '同意', key: 'approvalStatus', value: '' },
        { title: '审核意见：', key: 'approvalComment', value: '' },
        { title: '审核人：', key: 'approverName', value: '' },
        { title: '审核时间：', key: 'approvalTime', value: '' },
    ]
})
const _setComponentObj = inject('setComponentObj')()
// 返回
const handlerBack = () => {
    _setComponentObj.setComponent = "";
};
const handlerClubGet = () => {
    const { id, attributeValue } = _setComponentObj.params
    postClubGet({ id }).then(({ data }) => {
        state.iconUrl = data.iconUrl
        state.massOrgan.forEach(v => {
            v.value = data[v.key]
        })
        state.massMember.forEach(v => {
            if (v.key == 'advisorSelection') {
                const _advisorSelection = JSON.parse(data[v.key])
                v.value = _advisorSelection?.map(item => item.name).join(',') || ''
            } else {
                v.value = data[v.key]
            }
        })
        state.auditStatus.forEach(v => {
            if (v.key == 'approvalStatus') {
                v.value = data[v.key] === 'approved'
            } else if (v.key == 'approvalTime') {
                const _value = data[v.key]
                v.value = _value ? dayjs(_value).format('YYYY-MM-DD HH:mm:ss') : '--'
            } else {
                v.value = data[v.key]
            }
        })
    })

}
onMounted(() => {
    handlerClubGet()
})
</script>
<style scoped lang="less">
.header {
    border-bottom: 1px solid @border-color-base;
    padding-left: 20px;
    line-height: 57px;
    height: 57px;

    span.title {
        padding-left: 10px;
        font-weight: 600;
        font-size: 16px;
    }
}

.content {
    padding: 23px 16px;
    display: flex;

    :deep(.ant-image) {
        margin-right: 12px;

        .ant-image-img {
            height: 100%;
        }
    }

    .right {
        flex: 1;

        .list {
            &.inline {
                display: flex;

                li {
                    width: 340px;
                    margin: 0;
                }
            }

            li {
                margin-bottom: 20px;
                display: flex;
                font-size: 14px;

                .title {
                    color: @text-color-secondary;
                    width: 86px;
                }

                .value {
                    flex: 1;
                    color: #262626;

                    &.active {
                        max-height: 300px;
                        min-height: 20px;
                        overflow: auto;
                    }

                    &.linHeight {
                        line-height: 20px;
                    }
                }
            }
        }
    }
}
</style>