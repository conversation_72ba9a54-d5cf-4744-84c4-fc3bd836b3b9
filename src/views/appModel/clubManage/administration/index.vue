<!-- 社团管理 -->
<template>
    <div class="affiliatedUnit club-pd">
        <EnquiryForm class="enquiry_form" formLayout="horizontal" :formState="state.formState"
            :enquiryFormItme="formDataItme" @emitEnquiryFormSubmitReset="handeEnquiryFormSubmit"
            @emitEnquiryFormReset="handeEnquiryFormSubmit" />

        <YTable :spinning="state.spinning" :rowSelection="false" :totals="state.paginations" :columns="columns"
            :isScroll="true" :isSrollXW="2000" :dataSource="state.tableSource" :bordered="false"
            @onSelectedRowKeys="handleTableChange">
            <template #handleItem>
                <div class="table_info" style="padding-top: 0">
                    <div class="table_title"></div>
                    <div class="table_hander">
                        <a-space>
                            <a-button type="primary" v-auth="'clubManage.administration.add'" @click="handlerEdit">
                                <template #icon>
                                    <plus-outlined />
                                </template>
                                新增
                            </a-button>
                        </a-space>
                    </div>
                </div>
            </template>
            <template #bodyCell="{ column, record, index, text }">
                <div v-if="column.dataIndex === 'index'" :style="{ width: column.width + 'px' }">
                    {{ index + 1 }}
                </div>
                <div v-else-if="column.dataIndex === 'advisors'" :style="{ width: column.width + 'px' }">
                    <Tooltip :title="advisorsName(record.advisors)" :maxWidth="600">
                    </Tooltip>
                </div>
                <div v-else-if="column.dataIndex === 'iconUrl'" :style="{ width: column.width + 'px' }">

                    <a-image class="reset-ant-image-img" :width="36" :height="36"
                        :src="record.iconUrl || '/image/pic-mr.png'" />
                </div>

                <a-badge v-else-if="column.dataIndex === 'isEnabled'" :style="{ width: column.width + 'px' }"
                    :status="record.isEnabled ? 'success' : 'error'" :text="record.isEnabled ? '启用' : '禁用'" />
                <a-space v-else-if="column.dataIndex === 'operation'">
                    <a-button class="button-link" type="link" v-auth="'clubManage.administration.disable'"
                        @click="handlerDisable(record)">
                        {{ record.isEnabled ? '禁用' : '启用' }}
                    </a-button>
                    <a-button class="button-link" type="link" @click="handlerDetails(record.id)">详情</a-button>
                    <a-button class="button-link" type="link" v-auth="'clubManage.administration.edit'"
                        @click="handlerEdit(record)">编辑</a-button>
                    <a-button class="button-link" type="link" v-auth="'clubManage.administration.delete'"
                        :style="{ color: record.isEnabled ? '#999' : 'red' }" :disabled="record.isEnabled"
                        @click="handlerDelete(record.id)">删除</a-button>
                </a-space>

                <div v-else :style="{ width: column.width + 'px' }">
                    <Tooltip :title="text" :maxWidth="600">
                    </Tooltip>
                </div>
            </template>
        </YTable>
        <AddEditDrawer v-model:visible="state.addEditDrawerVisible" :addEditParams="state.addEditParams"
            :attributeValue="state.attributeValue" @updateAffiliatedUnitList="affiliatedUnitList" />
    </div>
</template>

<script setup>
import { createVNode, reactive, defineAsyncComponent } from 'vue'
import EnquiryForm from '@/components/enquiryForm'
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import AddEditDrawer from './addEditDrawer'
import { columns, formDataItme } from './tableData'
import { Modal, message } from 'ant-design-vue'
import { deletePageSize } from "@/utils/util.ts";

import { postClubPage, postClubDelete, postClubSetEnabled, postClubCategoryPage } from '@/api/clubManage'

const state = reactive({
    formState: {
        name: '',
        categoryId: null,
        isEnabled: null,
        level: null,
    },
    spinning: false,
    tableSource: [],
    paginations: {
        pageNo: 1, // 页码
        pageSize: 10, // 条数
        total: 0
    },
    addEditDrawerVisible: false,
    addEditParams: {},
    attributeValue: [],
    sortField: {
        field: "memberCount",
        order: ""
    }
});


const advisorsName = computed(() => {
    return (data) => {
        const _names = data?.map(item => item.name) || []
        return _names.join(',') || '--'
    }
})
const categoryName = computed(() => {
    return (data) => {
        let _names = ''
        state.attributeValue.forEach(v => {
            if (v.id == data) {
                _names = v.name
            }
        })
        return _names || '--'
    }
})
const affiliatedUnitList = () => {
    const params = {
        ...state.formState,
        sortField: state.sortField,
        ...state.paginations,
    }
    state.spinning = true
    postClubPage(params).then(({ data }) => {
        const { list, pageNo, pageSize, total } = data
        state.paginations.pageNo = pageNo
        state.paginations.pageSize = pageSize
        state.paginations.total = total
        state.tableSource = list
    }).finally(() => {
        state.spinning = false
    })
}
/* 表单查询結果回調 、 重置表单查询 */
const handeEnquiryFormSubmit = (item) => {
    state.formState = item
    state.paginations.pageNo = 1
    affiliatedUnitList()
}

/* 分页事件 */
const handleTableChange = (data, filters) => {
    const { current, pageSize, total } = data;
    state.paginations.pageNo = current
    state.paginations.pageSize = pageSize
    state.paginations.total = total
    state.sortField.order = ''
    if (filters.order) {
        state.sortField.order = filters.order == "ascend" ? 'asc' : 'desc'
    }
    // [asc: 正序, desc: 倒序]
    affiliatedUnitList()
}
// 禁用
const handlerDisable = (item) => {
    const params = {
        id: item.id,
        isEnabled: !item.isEnabled
    }
    postClubSetEnabled(params).then((res) => {
        message.success(res.message)
        affiliatedUnitList()
    })
}

const examDetails = defineAsyncComponent(() => import("./details.vue"));
const _handlerDetails = inject('handlerDetails')
// 详情 在最外的index中注入
const handlerDetails = async (id) => {
    const params = {
        id,
        attributeValue: state.attributeValue
    }
    _handlerDetails(examDetails, params)
}
// 删除
const handlerDelete = (id) => {
    Modal.confirm({
        title: '删除提示',
        icon: createVNode(ExclamationCircleOutlined),
        content: '确认删除该社团，删除后无法恢复',
        okText: '确认',
        cancelText: '取消',
        onOk() {
            postClubDelete({ id }).then((res) => {
                message.success(res.message)
                deletePageSize(state.paginations, state.tableSource.length, affiliatedUnitList);
            })
        }
    })
}
// 编辑
const handlerEdit = (item) => {
    state.addEditDrawerVisible = true
    state.addEditParams = {}
    if (item?.id) {
        state.addEditParams = item
    }
}
const handlerClubCategoryPage = () => {
    const params = {
        isEnabled: true,
        pageNo: 1, // 页码
        pageSize: 500, // 条数
        total: 0
    }
    postClubCategoryPage(params).then(({ data }) => {
        if (data?.list.length) {
            state.attributeValue = data?.list
            formDataItme.value.forEach((item) => {
                if (item.modelValue === "categoryId") {
                    item.attributeValue = [{
                        id: null,
                        name: "全部",
                    },
                    ...data?.list
                    ]
                }
            })

        }
    })
}
onMounted(() => {
    affiliatedUnitList()
    handlerClubCategoryPage()
})
</script>

<style lang="less">
.reset-ant-image-img {
    height: 100%
}

:deep(.ant-image-mask-info) {
    // 将div下的所有元素隐藏文字大小为8
    visibility: hidden;
    font-size: 0;

    span {
        //再单独给span标签添加样式 让其显示并且给出文字大小visibility: visible;
        font-size: 20px;
    }
}
</style>
