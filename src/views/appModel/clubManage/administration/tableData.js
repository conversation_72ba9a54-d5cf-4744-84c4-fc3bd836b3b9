export const columns = [
    {
        title: "序号",
        key: "index",
        dataIndex: "index",
        width: 80,
    },
    {
        title: "社团封面",
        key: "iconUrl",
        dataIndex: "iconUrl",
        width: 100,
    },
    {
        title: "社团名称",
        key: "name",
        dataIndex: "name",
        width: 180,
    },
    {
        title: "社团分类",
        key: "categoryName",
        dataIndex: "categoryName",
        width: 180,
    },
    {
        title: "社团等级",
        key: "levelName",
        dataIndex: "levelName",
        width: 180,
    },

    {
        title: "指导老师",
        key: "advisors",
        dataIndex: "advisors",
        width: 180,
    },
    {
        title: "社团负责人",
        key: "ownerName",
        dataIndex: "ownerName",
        width: 180,
    },
    {
        title: "成员数",
        key: "memberCount",
        dataIndex: "memberCount",
        sorter: true,
        filterDropdown: (vnode) => {},
        width: 140,
    },
    {
        title: "社团状态",
        key: "isEnabled",
        dataIndex: "isEnabled",
        width: 140,
    },
    {
        title: "操作",
        key: "operation",
        dataIndex: "operation",
        width: 180,
        fixed: "right",
    },
];

// 条件查询
export const formDataItme = ref([
    {
        label: "社团名称",
        type: "a-input",
        modelValue: "name",
        placeholder: "请输入",
    },
    {
        label: "社团分类",
        type: "a-select",
        modelValue: "categoryId",
        placeholder: "请选择",
        fieldNames: { label: "name", value: "id" },
        attributeValue: [],
    },
    {
        label: "社团状态",
        type: "a-select",
        modelValue: "isEnabled",
        placeholder: "请选择",
        fieldNames: { label: "label", value: "value" },
        attributeValue: [
            {
                value: null,
                label: "全部",
            },
            {
                value: true,
                label: "启用",
            },
            {
                value: false,
                label: "禁用",
            },
        ],
    },
    {
        label: "社团等级",
        type: "a-select",
        modelValue: "level",
        placeholder: "请选择",
        fieldNames: { label: "label", value: "value" },
        attributeValue: [
            {
                value: null,
                label: "全部",
            },
            {
                value: "university",
                label: "校级",
            },
            {
                value: "department",
                label: "院级",
            },
        ],
    },
]);

export const rules = {
    name: [
        {
            required: true,
            message: "请输入社团名称！",
        },
    ],
    iconUrl: [
        {
            required: true,
            message: "请选择社团封面！",
        },
    ],
    slogan: [
        {
            required: true,
            message: "请输入社团口号！",
        },
    ],
    categoryId: [
        {
            required: true,
            message: "请选择社团分类！",
        },
    ],
    level: [
        {
            required: true,
            message: "请选择社团等级！",
        },
    ],
    size: [
        {
            required: true,
            message: "请选择社团规模！",
        },
    ],
    description: [
        {
            required: true,
            message: "请输入介绍",
        },
    ],
    ownerId: [
        {
            required: true,
            message: "请选择社团负责人！",
        },
    ],
    advisorSelection: [
        {
            required: true,
            message: "请选择指导老师！",
        },
    ],
};
