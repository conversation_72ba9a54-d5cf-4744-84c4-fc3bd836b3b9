<!-- 社团管理 -->
<template>
    <div class="club-manage">
        <component v-if="state.setComponent" :is="state.setComponent" :params="state.params"></component>
        <RouterTabs v-else showBack authRoutesName="visitorSystem" leftExtraText activeKey="/appModel/visitorSystem" />
    </div>
</template>

<script setup>
import { reactive, provide } from "vue";
import RouterTabs from "@/components/RouterTabs";

// 查看
const handlerDetails = (components, item = null) => {
    state.setComponent = components;
    state.params = item;
};
const state = reactive({
    setComponent: "",
    params: {},
});
provide("setComponentObj", () => state);
provide("handlerDetails", handlerDetails);
</script>

<style lang="less" scoped>
:deep(.club-pd) {
    padding: 20px;
}

:deep(.button-link) {
    padding: 0;
}
</style>
