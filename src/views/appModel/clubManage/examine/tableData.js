export const columns = [
    {
        title: "序号",
        key: "index",
        dataIndex: "index",
        width: 80,
    },
    {
        title: "社团封面",
        key: "iconUrl",
        dataIndex: "iconUrl",
        width: 100,
    },
    {
        title: "社团名称",
        key: "name",
        dataIndex: "name",
        width: 180,
    },
    {
        title: "社团分类",
        key: "categoryName",
        dataIndex: "categoryName",
        width: 100,
    },
    {
        title: "社团等级",
        key: "levelName",
        dataIndex: "levelName",
        width: 180,
    },

    {
        title: "指导老师",
        key: "advisors",
        dataIndex: "advisors",
        width: 180,
    },
    {
        title: "社团负责人",
        key: "ownerName",
        dataIndex: "ownerName",
        width: 140,
    },
    {
        title: "成员数",
        key: "memberCount",
        dataIndex: "memberCount",
        sorter: true,
        width: 100,
        filterDropdown: (vnode) => {},
    },
    {
        title: "社团状态",
        key: "approvalStatus",
        dataIndex: "approvalStatus",
        width: 100,
    },

    {
        title: "审核人",
        key: "approverName",
        dataIndex: "approverName",
        width: 100,
    },

    {
        title: "审核时间",
        key: "approvalTime",
        dataIndex: "approvalTime",
        width: 180,
    },
    {
        title: "操作",
        key: "operation",
        dataIndex: "operation",
        width: 100,
        fixed: "right",
    },
];

// 条件查询
export const formDataItme = [
    {
        label: "社团名称",
        type: "a-input",
        modelValue: "name",
        placeholder: "请输入",
    },
    {
        label: "审核状态",
        type: "a-select",
        modelValue: "approvalStatus",
        placeholder: "请选择",
        fieldNames: { label: "label", value: "value" },
        attributeValue: [
            {
                value: null,
                label: "全部",
            },
            {
                value: "approved",
                label: "审核通过",
            },
            {
                value: "rejected",
                label: "审核不通过",
            },
            {
                value: "pending",
                label: "待审核",
            },
        ],
    },
    {
        label: "审核时间",
        type: "a-range-picker",
        modelValue: "approvalDateStartEnd",
        placeholder: ["开始时间", "结束时间"],
        format: "YYYY-MM-DD",
    },
];
