<!-- 社团审核 -->
<template>
    <div class="affiliatedUnit club-pd">
        <EnquiryForm class="enquiry_form" formLayout="horizontal" :formState="state.formState" colSpan="5"
            :enquiryFormItme="formDataItme" @emitEnquiryFormSubmitReset="handeEnquiryFormSubmit"
            @emitEnquiryFormReset="handeEnquiryFormSubmit" />

        <YTable :spinning="state.spinning" :rowSelection="false" :totals="state.paginations" :columns="columns"
            :dataSource="state.tableSource" :isScroll="true" :isSrollXW="2000" :bordered="false"
            @onSelectedRowKeys="handleTableChange">
            <template #bodyCell="{ column, record, index, text }">
                <div v-if="column.dataIndex === 'index'" :style="{ width: column.width + 'px' }">
                    {{ index + 1 }}
                </div>

                <div v-else-if="column.dataIndex === 'iconUrl'" :style="{ width: column.width + 'px' }">
                    <a-image class="reset-ant-image-img" v:width="36" :height="36"
                        :src="record.iconUrl || '/image/pic-mr.png'" />

                </div>

                <div v-else-if="column.dataIndex === 'advisors'" :style="{ width: column.width + 'px' }">
                    <Tooltip :title="advisorsName(record.advisors)" :maxWidth="600" />
                </div>

                <div v-else-if="column.dataIndex === 'approvalStatus'" :style="{ width: column.width + 'px' }">
                    <a-badge :status="badgeStatus[record.approvalStatus]" :text="record.approvalStatusName" />
                </div>

                <div v-else-if="column.dataIndex === 'approvalTime'" :style="{ width: column.width + 'px' }">
                    {{ }}
                    {{ setApprovalTime(record.approvalTime) }}
                </div>

                <div v-else-if="column.dataIndex === 'operation'" :style="{ width: column.width + 'px' }">
                    <a-space>
                        <a-button class="button-link" type="link" @click="handlerExamine(record.id)">详情</a-button>
                        <a-button class="button-link" type="link" v-if="record.approvalStatus == 'pending'"
                            v-auth="'clubManage.examine.examinebtn'" @click="handlerExamine(record.id)">审核</a-button>
                    </a-space>
                </div>

                <div v-else :style="{ width: column.width + 'px' }">
                    <Tooltip :title="text" :maxWidth="600" />
                </div>
            </template>
        </YTable>
    </div>
</template>

<script setup>
import { reactive } from 'vue'
import EnquiryForm from '@/components/enquiryForm'
import { columns, formDataItme } from './tableData'
import { postClubApplicationPage } from '@/api/clubManage'
import dayjs from 'dayjs'
const badgeStatus = {
    approved: 'success', // 审核通过
    rejected: 'error', // 审核不通过
    pending: 'warning' // 待审核
}
const state = reactive({
    formState: {
        name: '',
        approvalStatus: null,
        approvalDateStartEnd: [],
        approvalDateStart: '',
        approvalDateEnd: ''
    },
    spinning: false,
    tableSource: [],
    paginations: {
        pageNo: 1, // 页码
        pageSize: 10, // 条数
        total: 0
    },
    sortField: {
        field: "memberCount",
        order: ""
    }
});

const setApprovalTime = computed(() => {
    return (data) => {
        return data ? dayjs(data).format('YYYY-MM-DD HH:mm:ss') : '--'
    }
})
const advisorsName = computed(() => {
    return (data) => {
        const _names = data?.map(item => item.name) || []
        return _names.join(',') || '--'
    }
})
const affiliatedUnitList = () => {
    const params = {
        ...state.formState,
        sortField: state.sortField,
        ...state.paginations
    }
    state.spinning = true
    postClubApplicationPage(params).then(({ data }) => {
        const { list, pageNo, pageSize, total } = data
        state.tableSource = list
        state.paginations.pageNo = pageNo
        state.paginations.pageSize = pageSize
        state.paginations.total = total
    })
}
/* 表单查询結果回調 、 重置表单查询 */
const handeEnquiryFormSubmit = (item) => {
    const [Start, End] = item.approvalDateStartEnd
    state.formState = item
    state.formState.approvalDateStart = Start || ''
    state.formState.approvalDateEnd = End || ''
    state.paginations.pageNo = 1
    affiliatedUnitList()
}

/* 分页事件 */
const handleTableChange = (data, filters) => {
    const { current, pageSize, total } = data;
    state.paginations.pageNo = current
    state.paginations.pageSize = pageSize
    state.paginations.total = total
    state.sortField.field = filters.field
    state.sortField.order = ''
    if (filters.order) {
        state.sortField.order = filters.order == "ascend" ? 'asc' : 'desc'
    }
    // [asc: 正序, desc: 倒序]
    affiliatedUnitList()
}
const examDetails = defineAsyncComponent(() => import("./details.vue"));
const _handlerDetails = inject('handlerDetails')
// 详情 在最外的index中注入
const handlerExamine = async (id) => {
    _handlerDetails(examDetails, { id })
}

onMounted(() => {
    affiliatedUnitList()
})
</script>

<style lang="less">
.reset-ant-image-img {
    height: 100%
}
</style>
