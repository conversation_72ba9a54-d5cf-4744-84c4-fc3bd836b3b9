<template>
    <a-drawer width="500" @close="closeDrawer" class="drawerButton" :title="state.title" :maskClosable="false"
        v-model:visible="props.visible">
        <a-form ref="addEditRef" layout="vertical" name="base" :rules="rules" :model="state.addEditForm">
            <a-form-item label="单位名称：" name="name">
                <a-input :maxlength="40" showCount v-model:value="state.addEditForm.name" autocomplete="off"
                    placeholder="请输入" />
            </a-form-item>
            <a-form-item label="等级：" name="Introduction">
                <a-select v-model:value="state.addEditForm.Introduction" :options="IntroductionOptions"
                    placeholder="请输入">
                </a-select>
            </a-form-item>
            <a-form-item label="所属院系：" name="Introduction1" v-if="state.addEditForm.Introduction == 2">
                <a-select v-model:value="state.addEditForm.Introduction1" placeholder="请输入">
                    <a-select-option value="lucy">Lucy</a-select-option>
                </a-select>
            </a-form-item>
        </a-form>
        <template #footer>
            <div class="footer">
                <a-space>
                    <a-button @click="closeDrawer">取消</a-button>
                    <a-button type="primary" @click="saveConfirm">确认</a-button>
                </a-space>
            </div>
        </template>
    </a-drawer>
</template>

<script setup>
const addEditRef = shallowRef()
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    id: {
        type: Number,
        default: 0
    }
})
const emit = defineEmits(['update:visible'])

const state = reactive({
    title: '新增',
    addEditForm: { name: '', Introduction: null, Introduction1: null }
})
const IntroductionOptions = [
    {
        value: 1,
        label: "校级",
    },
    {
        value: 2,
        label: "院级",
    },
]
const rules = {
    name: [
        {
            required: true,
            message: '请输入社团类别',
        }
    ],
    Introduction: [
        {
            required: true,
            message: '请选择等级',
        }
    ],

    Introduction1: [
        {
            required: true,
            message: '请选择所属院系',
        }
    ]
}
const closeDrawer = () => {
    addEditRef.value.resetFields()
    emit('update:visible', false)
}
const saveConfirm = () => {
    addEditRef.value
        .validate()
        .then(() => {
            closeDrawer()
        })
}
watch(() => props.visible, (newVal) => {
    if (newVal) {
        state.title = props.id ? '编辑' : '新增'
    }
})

</script>

<style lang="less" scoped>
.reset-textarea {
    :deep(textarea) {
        padding-bottom: 20px !important;
        min-height: 160px !important;
    }
}
</style>
