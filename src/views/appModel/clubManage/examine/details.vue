<template>
    <div class="details">
        <div class="header">
            <i class="iconfont icon-xingzhuangjiehe19" @click="handlerBack"></i>
            <span class='title'>详情</span>
        </div>
        <div class="content">
            <a-image :width="100" :height="100" :preview="false" :src="state.iconUrl || '/image/pic-mr.png'" />
            <div class="right">
                <ul class="list">
                    <li v-for="(item, idx) in state.massOrgan" :key="idx">
                        <span class="title">{{ item.title }}</span>
                        <span class="value"
                            :class="{ active: item.key === 'description', linHeight: ['description', 'slogan'].includes(item.key) }">
                            {{ item.value }}
                        </span>
                    </li>
                </ul>
                <a-divider style="border-color: #D9D9D9;" dashed />
                <ul class="list inline">
                    <li v-for="(item, idx) in state.massMember" :key="idx">
                        <span class="title">{{ item.title }}</span>
                        <span class="value">{{ item.value }}</span>
                    </li>
                </ul>
                <a-typography-title :level="5" style="margin-top: 10px;">社团审核</a-typography-title>


                <ul class="list" v-if="state.isApprovalStatus">
                    <li v-for="(item, idx) in state.auditStatus" :key="idx">
                        <a-radio disabled v-if="item.key === 'approvalStatus'" :checked="true">
                            {{ item.title }}
                        </a-radio>
                        <template v-else>
                            <span class="title">{{ item.title }}</span>
                            <span class="value"
                                :class="{ active: item.key === 'approvalComment', linHeight: ['approvalComment'].includes(item.key) }">
                                {{ item.value }}
                            </span>
                        </template>
                    </li>
                </ul>

                <a-form v-else ref="addEditRef" layout="vertical" :rules="rules" :model="state.form">
                    <a-form-item label="">
                        <a-radio-group v-model:value="state.form.approvalStatus">
                            <a-radio value="approved">同意</a-radio>
                            <a-radio value="rejected">拒绝</a-radio>
                        </a-radio-group>
                    </a-form-item>

                    <a-form-item label="" name="approvalComment">
                        <a-textarea v-model:value="state.form.approvalComment" placeholder="请输入审核原因（必填）" showCount
                            :maxlength="500" />
                    </a-form-item>
                </a-form>
            </div>
        </div>
        <div class="footer" v-if="!state.isApprovalStatus">
            <a-space>
                <a-button @click="closeDrawer">取消</a-button>
                <a-button type="primary" :loading="state.loading" @click="saveConfirm">确认</a-button>
            </a-space>
        </div>
    </div>
</template>
<script setup>
import { inject } from "vue";
import { postClubApplicationReview, postClubApplicationGet } from '@/api/clubManage'
import { message } from "ant-design-vue";
import dayjs from 'dayjs'
const addEditRef = shallowRef()
const rules = {
    approvalComment: [
        {
            required: true,
            message: '请输入审核原因',
        }
    ]
}
const state = reactive({
    form: {
        approvalStatus: 'approved',
        approvalComment: '',
    },
    loading: false,
    isApprovalStatus: false,
    iconUrl: '/image/pic-mr.png',
    massOrgan: [
        { title: '社团名称：', key: 'name', value: '' },
        { title: '社团分类：', key: 'sizeName', value: '' },
        { title: '社团口号：', key: 'slogan', value: '' },
        { title: '社团简介：', key: 'description', value: '' },
    ],
    massMember: [
        { title: '社团等级：', key: 'levelName', value: '' },
        { title: '成员数：', key: 'memberCount', value: '' },
        { title: '指导老师：', key: 'advisors', value: '' },
        { title: '社团负责人：', key: 'ownerName', value: '' },
    ],
    auditStatus: [
        { title: '同意', key: 'approvalStatus', value: '' },
        { title: '审核意见：', key: 'approvalComment', value: '' },
        { title: '审核人：', key: 'approverName', value: '' },
        { title: '审核时间：', key: 'approvalTime', value: '' },
    ]
})
const _setComponentObj = inject('setComponentObj')()
// 返回
const handlerBack = () => {
    _setComponentObj.setComponent = "";
};
const handlerClubApplicationGet = () => {
    const { id } = _setComponentObj.params
    postClubApplicationGet({ id }).then(({ data }) => {
        state.iconUrl = data.iconUrl
        state.massOrgan.forEach(v => {
            v.value = data[v.key]
        })
        state.massMember.forEach(v => {
            if (v.key == 'advisors') {
                v.value = data[v.key]?.map(item => item.name).join(',') || ''
            } else {
                v.value = data[v.key]
            }
        })
        if (data.approvalStatus !== "pending") {
            state.isApprovalStatus = true
            state.auditStatus.forEach(v => {
                if (v.key == 'approvalStatus') {
                    const _Status = {
                        pending: '待审核',
                        approved: "同意",
                        rejected: "不同意",
                    }
                    v.title = _Status[data[v.key]]
                } else if (v.key == 'approvalTime') {
                    const _value = data[v.key]
                    v.value = _value ? dayjs(_value).format('YYYY-MM-DD HH:mm:ss') : '--'
                } else {
                    v.value = data[v.key]
                }
            })
        }
    })
}

const closeDrawer = () => {
    addEditRef.value.resetFields()
    emit('update:visible', false)
}
const saveConfirm = () => {
    addEditRef.value
        .validate()
        .then(() => {
            const { id } = _setComponentObj.params
            const params = {
                ...state.form,
                id
            }
            state.loading = true
            postClubApplicationReview(params).then((res) => {
                message.success(res.message)
                handlerClubApplicationGet()
                closeDrawer()
            }).finally(() => {
                state.loading = false
            })
        })
}
onMounted(() => {
    handlerClubApplicationGet()
})
</script>
<style scoped lang="less">
.details {
    position: relative;
    height: calc(100vh - 74px);

    .header {
        border-bottom: 1px solid @border-color-base;
        padding-left: 20px;
        line-height: 57px;
        height: 57px;

        span.title {
            padding-left: 10px;
            font-weight: 600;
            font-size: 16px;
        }
    }

    .content {
        padding: 23px 16px;
        display: flex;
        height: calc(100vh - 200px);
        overflow: auto;

        :deep(.ant-image) {
            margin-right: 12px;

            .ant-image-img {
                height: 100%;
            }
        }

        .right {
            flex: 1;

            .list {
                &.inline {
                    display: flex;
                    margin-bottom: 10px;

                    li {
                        width: 310px;
                        margin: 0;
                    }
                }

                li {
                    margin-bottom: 20px;
                    font-size: 14px;
                    display: flex;

                    .title {
                        color: @text-color-secondary;
                        width: 96px;
                    }

                    .value {
                        flex: 1;
                        color: #262626;

                        &.active {
                            line-height: 20px;
                            max-height: 300px;
                            min-height: 20px;
                        }

                        &.linHeight {
                            overflow: auto;
                        }
                    }
                }
            }

            :deep(.ant-input) {
                height: 120px !important;
            }
        }
    }

    .footer {
        border-top: 1px solid @border-color-base;
        position: absolute;
        bottom: 0;
        text-align: center;
        left: 0;
        right: 0;
        padding: 16px 0;
    }
}
</style>