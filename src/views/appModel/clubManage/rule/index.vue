<!-- 社团规则 -->
<template>
    <div class="rule club-pd">
        <a-typography-title :level="5">社团规模</a-typography-title>
        <ul class="rule-list">
            <li v-for="(item, idx) in state.ruleForm" :key="idx">
                <span class="rule-list-title">
                    社团规模：{{ item.name }}
                    <!-- <a-input style="width: 150px;" v-model:value="item.name" placeholder="请输入" /> -->
                </span>
                <span class="rule-list-num">
                    限制人数：
                    <a-input-number v-model:value="item.capacity" :min="1" :max="99999" style="width:100px;"
                        placeholder="请输入" />
                </span>
            </li>
        </ul>
    </div>
    <a-space class="footer">
        <a-button type="primary" ghost @click="handlerCancel">取 消</a-button>
        <a-button type="primary" @click="handlerSvan">确 定</a-button>
    </a-space>
</template>
<script setup>
import { reactive } from "vue";
import { Modal, message } from 'ant-design-vue'

import { postClubSizeList, postClubSizeSave } from '@/api/clubManage'
const state = reactive({
    ruleForm: [{
        name: '',
        code: '',
        capacity: null
    }]
});
// 确定
const handlerSvan = () => {
    const params = {}
    state.ruleForm.forEach(v => {
        params[v.code] = v.capacity
    })
    postClubSizeSave(params).then((res) => {
        message.success(res.message)
    })
}
// 取消
const handlerCancel = () => {
    init()
}
const init = () => {
    postClubSizeList().then(({ data }) => {
        state.ruleForm = data
    })
}
onMounted(() => {
    init()
})
</script>

<style lang="less">
.rule-list {
    width: 500px;

    li {
        margin: 12px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .rule-list-title {
            width: 140px;
        }

        .rule-list-num {
            flex: 1;
        }
    }
}

.footer {
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 20px;
    display: flex;
    justify-content: center;
    width: 100%;
    border-top: 1px solid @border-color-base;
}
</style>
