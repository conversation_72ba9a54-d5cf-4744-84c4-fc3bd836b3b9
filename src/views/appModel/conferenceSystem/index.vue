<!-- 会议系统 conferenceSystem -->
<template>
    <div class="entrance_guard">
        <div v-if="state.showNewPage">
            <a-tabs
                class="tabs"
                v-model:activeKey="state.activeKey"
                v-bind="$attrs"
                @change="handerChange"
            >
                <template #leftExtra>
                    <slot name="leftExtra">
                        <a-button type="text" class="tabs_back" @click="goBack">
                            <i class="iconfont icon-xingzhuangjiehe19"></i>
                        </a-button>
                    </slot>
                </template>
                <a-tab-pane
                    v-for="item in permissionList"
                    :key="item.component"
                    :tab="item.name"
                >
                </a-tab-pane>
            </a-tabs>
            <template v-if="state.activeKey == 'doorOpenAdmin'"></template>
            <div class="meeting_equipment" v-else>
                <a-radio-group
                    v-model:value="state.meetingType"
                    button-style="solid"
                >
                    <template
                        v-for="item in btnLists(state.activeKey)"
                        :key="item.component"
                    >
                        <a-radio-button
                            :value="item.component"
                            v-if="item.component == 'all'"
                        >
                            {{ item.name }}
                        </a-radio-button>
                        <a-radio-button :value="item.component" v-else
                            >{{ item.name }}
                        </a-radio-button>
                    </template>
                </a-radio-group>
            </div>
        </div>
        <component
            :is="state.components[state.meetingType]"
            :passId="state.passId"
        />
    </div>
</template>

<script setup>
import { provide, computed, reactive, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useStore } from "vuex";
import siteBook from "./siteBooking/siteBook/index.vue";
import appointmentRecord from "./siteBooking/appointmentRecord/index.vue";
import siteIdleList from "./siteBooking/siteIdleList/index.vue";
import appointmentReview from "./siteBooking/appointmentReview/index.vue";
import appointmentType from "./siteBooking/appointmentType/index.vue";
import booksFaceDatabase from "./booksFaceDatabase/index.vue";
import siteInfo from "./booksFaceDatabase/siteInfo.vue";
import classCardSet from "./booksFaceDatabase/classCardSet/index.vue";
import classLayout from "./booksFaceDatabase/classLayout/index.vue";
import trafficRule from "./booksFaceDatabase/trafficRule/index.vue";
import doorOpenAdmin from "./doorOpenAdmin/index.vue";

const store = useStore();
const route = useRoute();
const router = useRouter();
const permissionList = computed(
    () => store.state.base.permissionList[0]?.children
);
const btnLists = computed(() => {
    return (key) => {
        let btnList =
            permissionList.value.find((v) => v.component === key)?.btnList ||
            [];
        return [
            ...btnList,
            key === "meetingAppointment"
                ? { name: "场地闲置一览表", component: "siteIdleList" }
                : { name: "通行规则", component: "trafficRule" },
        ];
    };
});
const state = reactive({
    components: {
        siteBook,
        appointmentRecord,
        siteIdleList,
        appointmentReview,
        appointmentType,
        classLayout,
        classCardSet,
        booksFaceDatabase,
        trafficRule,
        siteInfo,
        doorOpenAdmin,
    },
    activeKey: "meetingAppointment",
    meetingType: "siteBook",
    passId: "",
    siteBookingId: "",
    showNewPage: true,
});
provide("showNewPage", () => state);

const goBack = () => {
    router.replace({ path: "/app/list" });
};
const handerChange = (event) => {
    if (event === "meetingAppointment") {
        state.meetingType = "siteBook";
    } else if (event === "equipmentManagement") {
        state.meetingType = "siteInfo";
    } else {
        state.meetingType = "doorOpenAdmin";
    }
};
</script>
<style lang="less" scoped>
.entrance_guard {
    :deep(.ant-tabs-top > .ant-tabs-nav::before) {
        border-bottom: 1px solid @border-color-base;
    }

    .meeting_equipment {
        margin: 10px 15px 0;
        display: flex;
        justify-content: space-between;

        :deep(.ant-tabs-nav) {
            margin: 0;
        }
    }
}
</style>
