<template>
    <a-drawer v-model:visible="props.visiblePaswrd" title="新建密码" width="500" @close="closeVisible">
        <a-form :model="state.rulesForm" layout="vertical" ref="rulesRef" :rules="rulesForm">
            <a-form-item label="开门密码：" name="password">
                <a-input v-model:value="state.rulesForm.password" placeholder="请输入" readonly disabled />
            </a-form-item>
            <a-form-item label="密码有效期：" name="startEndTime">
                <a-range-picker v-model:value="state.rulesForm.startEndTime" value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 100%" show-time separator="至" />
            </a-form-item>
            <a-form-item label="选择设备：" name="deviceIds">
                <a-tree-select class="select_site_class" v-model:value="state.rulesForm.deviceIds" show-search
                    placeholder="请选择" allow-clear tree-default-expand-all tree-checkable :tree-data="state.siteOption"
                    :field-names="{
                        children: 'children',
                        label: 'machineName',
                        key: 'id',
                        value: 'id',
                    }" :showSearch="false"></a-tree-select>
            </a-form-item>
        </a-form>
        <template #footer>
            <div class="footer">
                <a-button style="margin-right: 8px" @click="closeVisible">
                    取消
                </a-button>
                <a-button type="primary" :loading="state.loading" @click="saveConfirm">确认</a-button>
            </div>
        </template>
    </a-drawer>
</template>

<script setup>
import { reactive } from "vue";
import { message } from "ant-design-vue";
import { addDoorPassword, getBindingSiteTreeeV2 } from "@/api/doorOpenAdmin.js";

const rulesRef = shallowRef();
const state = reactive({
    siteOption: [],
    loading: false,
    disabled: false,
    rulesForm: {
        id: "",
        password: "",
        startEndTime: [],
        startTime: "",
        endTime: "",
        deviceType: 9, // 8门禁、9会议
        deviceIds: [],
    },
});
const props = defineProps({
    visiblePaswrd: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(["update:visiblePaswrd", "emitPageList"]);
const closeVisible = () => {
    rulesRef.value.resetFields();
    emit("update:visiblePaswrd", false);
};

const rulesForm = {
    password: [{ required: true, message: "请输入开门密码" }],
    startEndTime: [{ required: true, message: "请选择密码有效期" }],
    deviceIds: [{ required: true, message: "请选择设备" }],
};

// 保存新建密码
const saveConfirm = () => {
    rulesRef.value.validateFields().then(() => {
        const params = {
            ...state.rulesForm,
            startTime: "",
            endTime: "",
        };
        if (params.startEndTime.length) {
            params.startTime = params.startEndTime[0];
            params.endTime = params.startEndTime[1];
        }
        state.loading = true;
        addDoorPassword(params)
            .then((res) => {
                message.success(res.message);
                emit("emitPageList");
                closeVisible();
            })
            .finally(() => {
                state.loading = false;
            });
    });
};

// 禁用选中父节点
const disabledP = (tree) => {
    tree.forEach((item) => {
        if (item.children) {
            item.disabled = true;
            disabledP(item.children);
        }
    });
};

// 获取场地
function processTreeData(data) {
    return data.filter((item) => {
        if (item.type === 4) {
            return false;
        }
        if (item.children && item.children.length > 0) {
            item.children = processTreeData(item.children);
        }
        return true;
    });
}

// 获取绑定场地的班牌
const getSignSite = () => {
    const params = {
        deviceType: 9,
        deviceTypeList: [9],
        pageNo: 1,
        pageSize: 500,
    };
    getBindingSiteTreeeV2(params).then(({ data }) => {
        // 默认是按场地  
        state.siteOption = []
        data?.list?.forEach((item) => {
            if (item.no) {
                state.siteOption.push(item)
            }
        });
    });
};

watch(
    () => props.visiblePaswrd,
    (valr) => {
        if (valr) {
            // 随机6位数字
            state.rulesForm.password = Math.random().toString().slice(-6);

            getSignSite();
        }
    }
);
</script>

<style scoped lang="less"></style>
