<!-- issuanceRecord 下发记录 -->
<template>
    <div class="issuance-record">
        <div class="issuance-record_hander">
            <a-button type="text" class="tabs_back" @click="handerBack">
                <i class="iconfont icon-xingzhuangjiehe19"></i>
            </a-button>
            <span>下发记录</span>
        </div>
        <div class="issuance-record_body">
            <a-spin :spinning="state.tableSpinning">
                <YTable
                    :columns="attendanceColumns"
                    :dataSource="state.dataSource"
                    :rowSelection="false"
                    :isScroll="false"
                    :isSrollY="true"
                    :isSrollYH="clientHeight - 265"
                    :totals="state.pagination"
                    @onSelectedRowKeys="handerSelectedRowKeys"
                >
                    <template #bodyCell="{ column, text, record, index }">
                        <templage v-if="column.dataIndex === 'index'">
                            {{ index + 1 }}
                        </templage>

                        <div v-else>
                            <Tooltip :title="text"></Tooltip>
                        </div>
                    </template>
                </YTable>
            </a-spin>
        </div>
    </div>
</template>

<script setup>
import { reactive, onMounted } from "vue";
import YTable from "comps/YTable/index.vue";
import { postIssuanceRecordList } from "@/api/doorOpenAdmin.js";

const props = defineProps({
    visibleIssuanceRecord: {
        type: Boolean,
        default: false,
    },
    viewDevicesId: {
        type: String,
        default: "",
    },
});
const emit = defineEmits(["update:visibleIssuanceRecord"]);

const state = reactive({
    tableSpinning: false,
    dataSource: [],
    pagination: {
        total: 0,
        pageNo: 1,
        pageSize: 10,
    },
});

// 考勤表格
const attendanceColumns = [
    {
        title: "序号",
        dataIndex: "index",
    },
    {
        title: "设备名称",
        dataIndex: "deviceName",
    },
    {
        title: "密码下发状态",
        dataIndex: "deviceStatusName",
    },
    {
        title: "下发时间",
        dataIndex: "lastIssuanceTime",
        width: 180,
    },
];
// 列表
const getPageList = () => {
    state.tableSpinning = true;
    const params = {
        ...state.pagination,
        desc: true,
        deviceType: 9, // 8门禁、9会议
        pwdId: props.viewDevicesId,
    };
    postIssuanceRecordList(params)
        .then(({ data }) => {
            const { list, pageNo, pageSize, total } = data;
            state.dataSource = list || [];
            state.pagination.pageNo = pageNo;
            state.pagination.pageSize = pageSize;
            state.pagination.total = total;
        })
        .finally(() => {
            state.tableSpinning = false;
        });
};
const handerSelectedRowKeys = ({ pageSize, current }) => {
    state.pagination.pageNo = current;
    state.pagination.pageSize = pageSize;
    getPageList();
};
// 返回下发记录
const handerBack = () => {
    emit("update:visibleIssuanceRecord", false);
};
// 获取窗口高度
const clientHeight = window.document.body.clientHeight;
onMounted(() => {
    getPageList();
});
</script>

<style scoped lang="less">
.issuance-record {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    .issuance-record_hander {
        height: 46px;
        line-height: 46px;
        background: @body-background;
        border-radius: 12px 12px 0 0;
        border-bottom: 1px solid @border-color-base;
    }
    .issuance-record_body {
        padding: 20px;
    }
}
</style>
