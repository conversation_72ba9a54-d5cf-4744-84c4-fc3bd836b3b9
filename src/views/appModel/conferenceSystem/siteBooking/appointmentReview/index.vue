<template>
    <div class="warps">
        <div class="site_booking">
            <div class="search_form">
                <SearchForm ref="searRef" @queryList="queryList" @resetList="resetList" :showBooker="true" />
            </div>
            <div class="table">
                <YTable :columns="columnsThree" :dataSource="state.reviewTable" :slots="['operation']"
                    :rowSelection="false" :totals="state.pagination" :spinLoad="state.spinLoad"
                    @onSelectedRowKeys="onSelectedRowKeys">
                    <template #headerCell="{ column }">
                        <template v-if="column.dataIndex === 'status'">
                            <a-dropdown>
                                <a style="color: #000" @click.prevent>
                                    审核状态
                                    <caret-down-outlined style="color: var(--primary-color)" />
                                </a>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item v-for="(
                                                item, index
                                            ) in state.statusList" :key="index" @click="chooseStatus(item, index)">
                                            <span :class="state.statusIndex === index
                    ? 'acti'
                    : ''
                    ">{{ item.statusName }}</span>
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </template>
                    </template>
                    <template #bodyCell="{ column, text, record }">
                        <template v-if="column.dataIndex === 'signIn'">
                            <span v-if="record.signIn">需签到</span>
                            <span v-else>无需签到</span>
                        </template>
                        <template v-else-if="column.dataIndex === 'status'">
                            <!-- // 0.拒绝、1.审批中、2.已通过、3.已取消、4.已过期 -->
                            <a-badge v-if="record.status === 0" status="error" text="已拒绝" />
                            <a-badge v-if="record.status === 1" status="warning" text="审批中" />
                            <a-badge v-if="record.status === 2" status="success" text="已通过" />
                            <a-badge v-if="record.status === 3" status="default" text="已取消" />
                            <a-badge v-if="record.status === 4" status="default" style="color: #ccc" text="已过期" />
                        </template>
                        <template v-else-if="column.dataIndex === 'operation'">
                            <a @click="toReview(record)" v-if="record.status === 1"
                                style="color: var(--primary-color)">审核</a>
                            <a @click="toDetail(record)" v-else style="color: var(--primary-color)">查看</a>
                        </template>
                        <Tooltip v-else :title="text" />
                    </template>
                </YTable>
            </div>
        </div>
        <!-- 预约详情 @emitReEdit="emitReEdit" -->
        <siteDetail @emithandleCancel="emithandleCancel" @opened="opened" @emitCancelBook="emitCancelBook"
            @emitRefuse="emitRefuse" @emitPass="emitPass" :title="state.title" :onStatus="state.onStatus"
            :review="false" :open="state.open" :close="state.close" :isOpen="state.isOpen" :audits="state.audits"
            :isVisible="state.isVisible" :themeitem="state.themeitem" :status="Number(state.statuss)"
            v-model:name="state.name" />
        <!-- 拒绝原因弹框 -->
        <a-modal v-model:visible="state.visibleJ" title="拒绝原因" @ok="handleOk" @cancel="handleCancelJu">
            <a-textarea :maxlength="state.maxNum" v-model:value="state.ju" placeholder="请输入拒绝原因"
                style="height: 120px; overflow-y: scroll" showCount />
        </a-modal>
    </div>
</template>

<script setup>
import { reactive, onMounted, ref, createVNode } from "vue";
import YTable from "comps/YTable/index.vue";
import SearchForm from "../components/SearchForm.vue";
import siteDetail from "../components/siteDetail.vue";
import {
    bookingApprovalPage,
    getBookingSiteNums,
    bookingDetail,
    bookingUpdate,
    checkApproved,
} from "@/api/siteBooking";
import { columnsThree } from "../siteBook/tableData";
import { message, Modal } from "ant-design-vue";
import { ExclamationCircleFilled } from "@ant-design/icons-vue";

// const bookDetail = shallowRef({}) // 场地预约详情
const searRef = ref();
const state = reactive({
    spinLoad: false,
    // 预约详情弹框----start
    isVisible: false,
    onStatus: false,
    isOpen: false,
    open: true,
    close: false,
    title: "",
    name: "",
    status: "",
    statuss: "",
    themeitem: {
        signIn: false,
        bookingTime: "",
        sponsorName: "",
        signList: [],
        describe: "",
        siteInfo: {},
        she: "会议机，投影仪",
    },
    audits: {
        sponsorName: "",
        sponsorTime: "",
        approvalTimeStr: "",
        shenpis: [],
        refuseReason: "",
    },
    // 预约详情弹框----end
    reviewTable: [],
    // 0.拒绝、1.审批中、2.已通过、3.已取消、4.已过期
    statusList: [
        { statusName: "全部", status: null },
        { statusName: "已拒绝", status: 0 },
        { statusName: "审批中", status: 1 },
        { statusName: "已通过", status: 2 },
        // { statusName: '已取消', status: 3 },
        { statusName: "已过期", status: 4 },
    ],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    appointType: [],
    appointTypes: [],
    statusIndex: 0,
    typeIndex: 0,
    typeId: "",
    ju: "",
    maxNum: 300,
    textNum: 0,
});
const chooseStatus = (item, index) => {
    state.statusIndex = index;
    state.pagination.pageNo = 1;
    state.typeIndex = 0;
    state.status = item.status;
    getBookingReviewPage();
};
const toDetail = (record) => {
    state.isVisible = true;
    state.title = "详情";
    state.onStatus = false;
    state.myBookDetailId = record.id;

    bookingDetail({ id: record.id }).then((res) => {
        const { data } = res;
        state.name = data.name;
        state.themeitem.signIn = data.signIn;
        state.themeitem.bookingTime = data.bookingTime;
        state.themeitem.sponsorName = data.sponsorName;
        state.themeitem.sponsorOrgName = data.sponsorOrgName
        state.themeitem.signList = data.signList;
        state.themeitem.describe = data.describe;
        state.themeitem.siteInfo = data.siteInfo;
        state.audits.shenpis = data.siteInfo.list;
        state.audits.sponsorName = data.sponsorName;
        state.audits.refuseReason = data.refuseReason;
        state.audits.sponsorTime = data.sponsorTime;
        state.audits.approvalTimeStr = data.approvalTimeStr;
        state.statuss = data.status
    });
};
const toReview = (record) => {
    state.myBookDetailId = record.id;
    state.onStatus = false;
    bookingDetail({ id: record.id }).then((res) => {
        const { data } = res;
        state.name = data.name;
        state.themeitem.signIn = data.signIn;
        state.themeitem.bookingTime = data.bookingTime;
        state.themeitem.sponsorName = data.sponsorName;
        state.themeitem.signList = data.signList;
        state.themeitem.describe = data.describe;
        state.themeitem.siteInfo = data.siteInfo;
        state.audits.shenpis = data.siteInfo.list;
        state.audits.sponsorName = data.sponsorName;
        state.audits.refuseReason = data.refuseReason;
        state.audits.sponsorTime = data.sponsorTime;
        state.audits.approvalTimeStr = data.approvalTimeStr;
        state.statuss = data.status
        // state.status = data.status;
    });
    state.title = "审核详情";
    state.isVisible = true;
};
const emithandleCancel = () => {
    state.isVisible = false;
};
const opened = (e) => {
    if (e) {
        state.isOpen = true;
        state.open = false;
        state.close = true;
    } else {
        state.isOpen = false;
        state.open = true;
        state.close = false;
    }
};

// 获取地预约类型
const getTypeNum = async () => {
    state.siteBookingTypeId = "";
    await getBookingSiteNums().then(({ data }) => {
        data.forEach((v) => {
            if (v.type == 1) {
                state.siteBookingTypeId = v.id;
            }
        });
    });
};
const getBookingReviewPage = () => {
    const params = {};
    const obj = searRef.value.sear;
    params.pageNo = state.pagination.pageNo;
    params.pageSize = state.pagination.pageSize;
    params.name = obj.name;
    params.startTime = obj.time[0];
    params.endTime = obj.time[1];
    params.siteName = obj.siteName;
    params.siteTypeId = obj.siteTypeId;
    params.buildingId = obj.buildingId;
    params.sponsorName = obj.sponsorName;
    params.siteBookingTypeId = state.siteBookingTypeId;
    params.status = state.status;
    state.spinLoad = true;
    bookingApprovalPage(params)
        .then((res) => {
            const { list, total, pageNo, pageSize } = res.data;
            state.reviewTable = list;
            state.pagination.pageNo = pageNo;
            state.pagination.pageSize = pageSize;
            state.pagination.total = total;
            state.spinLoad = false;
        })
        .finally(() => {
            state.spinLoad = false;
        });
};
// 取消预约
const emitCancelBook = () => {
    bookingUpdate({ status: 3, id: state.myBookDetailId }).then((res) => {
        if (res.code === 0) {
            getBookingReviewPage();
            state.isVisible = false;
            message.success(res.message);
        }
    });
};
// 拒绝
const emitRefuse = () => {
    state.visibleJ = true;
};
// 取消拒绝
const handleCancelJu = () => {
    state.ju = "";
    state.textNum = 0;
};
// 确定拒绝
const handleOk = () => {
    if (!state.ju.trim()) {
        message.warning("请输入拒绝原因");
    } else {
        bookingUpdate({
            status: 0,
            refuseReason: state.ju,
            id: state.myBookDetailId,
        }).then((res) => {
            if (res.code === 0) {
                getBookingReviewPage();
                state.visibleJ = false;
                state.isVisible = false;
                message.success(res.message);
            }
        });
    }
};
// 通过
const emitPass = () => {
    checkApproved({ id: state.myBookDetailId }).then((res) => {
        if (res.data) {
            bookingUpdate({ status: 2, id: state.myBookDetailId }).then(
                (res) => {
                    if (res.code === 0) {
                        getBookingReviewPage();
                        state.isVisible = false;
                        message.success(res.message);
                    }
                }
            );
        } else {
            Modal.confirm({
                title: "审核提示",
                icon: createVNode(ExclamationCircleFilled),
                content: "该时间场地不开放，确认继续审核通过吗？",
                okType: "danger",
                okText: "确定",
                cancelText: "取消",
                onOk() {
                    bookingUpdate({ status: 2, id: state.myBookDetailId }).then(
                        (res) => {
                            if (res.code === 0) {
                                getBookingReviewPage();
                                message.success(res.message);
                            }
                        }
                    );
                },
            });
        }
    });
};
/* 分页事件 */
const onSelectedRowKeys = (data) => {
    const { current, pageSize } = data;
    state.pagination.pageNo = current;
    state.pagination.pageSize = pageSize;
    getBookingReviewPage();
};
// 查询
const queryList = () => {
    state.pagination.pageNo = 1;
    state.pagination.pageSize = 10;
    getBookingReviewPage();
};
// 重置
const resetList = () => {
    state.pagination.pageNo = 1;
    getBookingReviewPage();
};
onMounted(async () => {
    await getTypeNum();
    await getBookingReviewPage();
});
</script>

<style lang="less" scoped>
.acti {
    color: var(--primary-color);
}

.actt {
    color: var(--primary-color);
}

.max {
    position: absolute;
    right: 38px;
    bottom: 80px;
}

.site_booking {
    padding: 0 16px;

    .top {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
    }
}
</style>
