<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2023-05-04 14:11:49
 * @LastEditors: jingrou
 * @LastEditTime: 2023-05-05 18:06:49
-->
<template>
    <RouterTabs :showBack="true" leftExtraText activeKey="/appModel/siteBooking/siteBook" />
</template>

<script setup>
import RouterTabs from '@/components/RouterTabs'
import { useRoute } from 'vue-router'
const Route = useRoute()
</script>
