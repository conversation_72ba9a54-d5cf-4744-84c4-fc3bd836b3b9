<template>
    <div class="dao">
        <a-layout-header class="reset-layout-header" style="position: relative;">
            <i class="iconfont icon-xingzhuangjiehe19" @click="goBack" />
            <span style="margin-left: 10px;">详情</span>
        </a-layout-header>
        <div class="warps">
            <div class="tit_name">{{ state.siteFormInfo.name }}</div>
            <div class="item">
                <span>场地位置 ：</span><i>
                    {{
                        state.siteFormInfo.floor != null ? state.siteFormInfo.floor + '-' : ''
                    }}
                    {{ state.siteFormInfo.roomNum || '-' }}</i>
                <span>场地类型 ：</span><i>{{ state.siteFormInfo.siteTypeName || '-' }}</i>
                <span>容纳人数 ：</span><i>{{ state.siteFormInfo.peppleNum || '-' }}</i>
                <span>设备 ：</span><i>{{ state.siteFormInfo.deviceNames || '-' }}</i>
            </div>
            <div class="tit_name">场地占用情况</div>
            <div class="table-header">
                <div><span class="box1 span"></span>课程</div>
                <div><span class="box2 span"></span>已预约</div>
                <div><span class="box3 span"></span>审核中</div>
                <div><span class="box4 span"></span>可预约</div>
                <div><span class="box5 span"></span>不可预约</div>
            </div>
            <div class="tubiao">
                <ScheduleTable @nextWeeks="nextWeeks" @prevWeeks="prevWeeks" ref="schedRef" :initialEvents="state.occupy" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { reactive, onMounted, onUnmounted, ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { dateOccupyList, getfreeSiteInfo } from '@/api/siteBooking'
import dayjs from 'dayjs'
import { v4 as uuidv4 } from 'uuid'
import ScheduleTable from '@/components/ScheduleTable/index.vue'


const schedRef = ref()
const route = useRoute()
const state = reactive({
    siteFormInfo: {},
    weekNum: 0,
    occupy: [], // 场地占用的数据
    actnum: 1
})
const props = defineProps({
    siteId: {
        type: String,
        default: ''
    },
    siteBookingTypeId: {
        type: String,
        default: ''
    }
})
const siteId = computed(() => {
    return props.siteId
})
const siteBookingTypeId = computed(() => {
    return props.siteBookingTypeId
})
const emit = defineEmits(['showView'])
const goBack = () => {
    emit('showView')
}
const nextWeeks = (e) => {
    state.weekNum++
    const weekStart = dayjs().startOf('week').add(state.weekNum, 'week').format('YYYY-MM-DD')
    const weekEnd = dayjs().endOf('week').add(state.weekNum, 'week').format('YYYY-MM-DD')
    getdateOccupyList(weekStart, weekEnd)
}
// 上一周
const prevWeeks = (e) => {
    state.weekNum--
    const weekStart = dayjs().startOf('week').add(state.weekNum, 'week').format('YYYY-MM-DD')
    const weekEnd = dayjs().endOf('week').add(state.weekNum, 'week').format('YYYY-MM-DD')
    getdateOccupyList(weekStart, weekEnd)
}
const getTableTime = (arr, color, title = null) => {
    const newArr = arr.map((item, index) => {
        return {
            id: uuidv4(),
            // title,
            color,
            start: `${item.startTime.replace(/\s+/g, 'T')}:00`,
            end: `${item.endTime.replace(/\s+/g, 'T')}:00`
        }
    })
    return newArr
}
const getdateOccupyList = (
    startTime = dayjs(route.query.siteTime).startOf('week').format('YYYY-MM-DD'),
    endTime = dayjs(route.query.siteTime).endOf('week').format('YYYY-MM-DD')) => {
    const params = {
        startTime,
        endTime,
        siteId: siteId.value,
        siteBookingTypeId: siteBookingTypeId.value
    }
    dateOccupyList(params).then((res) => {
        const { inApprovalDateList, noBookingDateList, timetableDateList, yesBookingDateList } = res.data
        const t1 = getTableTime(timetableDateList, '#00B781') // 课程
        const t2 = getTableTime(yesBookingDateList, '#F5222D') // 已预约
        const t3 = getTableTime(inApprovalDateList, '#FAAD14') // 审核中
        const t4 = getTableTime(noBookingDateList, '#cccccc')// 不可预约
        if (state.actnum === 1 && state.occupy.length === 0) {
            schedRef.value.calendarOptions.events = t1.concat(t2, t3, t4)
        } else {
            state.occupy = t1.concat(t2, t3, t4)
        }
    })
}
// 详情
function getfreeSiteInfoFn() {
    getfreeSiteInfo({ id: siteId.value }).then((res) => {
        state.siteFormInfo = res.data
    })
}

onMounted(() => {
    getdateOccupyList()
    getfreeSiteInfoFn()

})
onUnmounted(() => {
    state.actnum = 1
    state.occupy = []
})
</script>

<style lang="less" scoped>
.act {
    color: var(--primary-color);
}

.dao {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;

    .reset-layout-header {
        height: 57px;
        line-height: 57px;
        background: transparent;
        border-bottom: 1px solid @border-color-base;
    }

    .warps {
        padding: 16px;

        .tit_name {
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 500;
            // color: rgba(0,0,0,0.85);
        }

        .item {
            margin-bottom: 20px;

            span {
                color: rgba(0, 0, 0, 0.45);
            }

            i {
                margin-right: 16px;
            }
        }

        .table-header {
            display: flex;

            div {
                margin-right: 20px;

                .span {
                    margin-right: 10px;
                    display: inline-block;
                    width: 16px;
                    height: 16px;
                }

                .box1 {
                    background-color: @primary-color;
                }

                .box2 {
                    background-color: #F5222D;

                }

                .box3 {
                    background-color: #FAAD14;

                }

                .box5 {
                    background-color: #D9D9D9;

                }

                .box4 {
                    background-color: #fff;
                    border: 1px solid #D9D9D9;

                }
            }
        }

        .tubiao {
            height: calc(100vh - 280px);
            overflow: auto;
        }
    }
}

::-webkit-scrollbar {
    width: 0 !important;
    height: 0;
}

:deep(.fc-event-main) {
    opacity: 0;
}

:deep(.fc-col-header-cell-cushion) {
    color: #000;
}

:deep(.fc-col-header) {
    background-color: #ebfaf5;
    height: 40px;
    line-height: 40px;
}
</style>
