<template>
    <div class="warps">
        <div class="site_booking" v-if="!state.showFlag">
            <div class="search_form">
                <SearchForm ref="searRef" :time1="false" :time2="true" :name="false" :sheb="true"
                    :optionshe="state.optionshe" @queryList="queryList" @resetList="resetList" />
            </div>
            <div class="table">
                <YTable :columns="columns" :dataSource="state.toDoTable" :slots="['operation']" :rowSelection="false"
                    :totals="state.pagination" :spinLoad="state.spinLoad" @onSelectedRowKeys="onSelectedRowKeys">
                    <template #bodyCell="{ column, text, record }">
                        <template v-if="column.dataIndex === 'deviceName'">
                            <span v-if="!record.deviceName">-</span>
                        </template>
                        <template v-else-if="column.dataIndex === 'status'">
                            <a-badge v-if="record.status === 0" status="success" text="可预约" />
                            <a-badge v-if="record.status === 1" status="error" text="已约满" />
                            <a-badge v-if="record.status === 2" status="default" text="封禁" />
                        </template>
                        <template v-else-if="column.dataIndex === 'operation'">
                            <a @click="toDetail(record)" style="
                                    color: var(--primary-color);
                                    margin-right: 10px;
                                ">详情</a>
                        </template>
                        <Tooltip v-else :title="text" />
                    </template>
                </YTable>
            </div>
        </div>
        <idleDetails v-else :class="{ top0: state.showFlag }" :siteBookingTypeId="state.siteBookingTypeId"
            :siteId="state.siteId" @showView="showView"></idleDetails>
    </div>
</template>

<script setup>
import { reactive, onMounted, ref, nextTick, inject } from "vue";
import { useRouter, useRoute } from "vue-router";
import YTable from "comps/YTable/index.vue";
import SearchForm from "../components/SearchForm.vue";
import idleDetails from "./idleDetails.vue";
import { freeSite, getYesBookingSiteNum } from "@/api/siteBooking";
import dayjs from "dayjs";

let stateShowNewPage = inject("showNewPage")();
const router = useRouter();
const route = useRoute();
const searRef = ref();
const state = reactive({
    siteBookingTypeId: "",
    siteId: "",
    showFlag: false,
    appointType: [],
    type: 0,
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    toDoTable: [],
    statusList: [
        { statusName: "全部", status: null },
        { statusName: "可预约", status: 0 },
        { statusName: "已约满", status: 1 },
        { statusName: "封禁", status: 2 },
    ],
    statusIndex: 0,
    optionshe: [
        // { label: '投影仪', value: '6666' },
        // { label: '电脑', value: '7777' },
        // { label: '会议机', value: '8888' }
    ],
});
const columns = [
    {
        title: "场地名称",
        dataIndex: "siteName",
    },
    {
        title: "所在建筑",
        dataIndex: "buildingName",
    },
    {
        title: "楼层",
        dataIndex: "floor",
    },
    {
        title: "房间号",
        dataIndex: "roomNum",
    },
    {
        title: "场地类型",
        dataIndex: "siteTypeName",
    },
    {
        title: "容纳人数",
        dataIndex: "peppleNum",
    },
    {
        title: "设备",
        dataIndex: "deviceName",
    },
    {
        title: "状态",
        dataIndex: "status",
    },
    {
        title: "操作",
        dataIndex: "operation",
        width: 100,
    },
];
// 0.拒绝、1.审批中、2.已通过、3.已取消、4.已过期
const toDetail = (item) => {
    state.siteId = item.id;
    router.replace({
        query: {
            siteTime: searRef.value.sear.time2,
            siteId: item.id,
            ...route.query,
            type: "true",
        },
    });
    state.showFlag = true;
    stateShowNewPage.showNewPage = false;
};
const showView = () => {
    state.showFlag = false;
    state.siteId = null;
    stateShowNewPage.showNewPage = true;
    nextTick(() => {
        searRef.value.sear.time2 = dayjs(new Date()).format("YYYY-MM-DD");
    });
};

const getFreePage = (status = null) => {
    const obj = searRef.value.sear;
    const params = {
        siteBookingTypeId: state.siteBookingTypeId,
        ...state.pagination,
        startTime: obj.time2,
        siteName: obj.siteName,
        siteTypeIds: obj.siteTypeId && obj.siteTypeId.split(" "),
        buildingId: obj.buildingId,
        status,
    };
    state.spinLoad = true;
    freeSite(params)
        .then((res) => {
            const { list, total, pageNo, pageSize } = res.data;
            state.toDoTable = list;
            state.pagination.pageNo = pageNo;
            state.pagination.pageSize = pageSize;
            state.pagination.total = total;
            state.spinLoad = false;
        })
        .finally(() => {
            state.spinLoad = false;
        });
};
// 查询
const queryList = () => {
    state.pagination.pageNo = 1;
    state.pagination.pageSize = 10;
    getFreePage();
};
// 重置
const resetList = () => {
    state.pagination.pageNo = 1;
    getFreePage();
};
/* 分页事件 */
const onSelectedRowKeys = (data) => {
    const { current, pageSize } = data;
    state.pagination.pageNo = current;
    state.pagination.pageSize = pageSize;
    getFreePage();
};
// 获取地预约类型列表及预约数量
const getTypeNum = async () => {
    state.siteBookingTypeId = "";
    await getYesBookingSiteNum().then(({ data }) => {
        data.forEach((v) => {
            if (v.type == 1) {
                state.siteBookingTypeId = v.id;
            }
        });
    });
};
onMounted(async () => {
    searRef.value.sear.time2 = dayjs(new Date()).format("YYYY-MM-DD");
    await getTypeNum();
    await getFreePage();
});
</script>

<style lang="less" scoped>
.acti {
    color: var(--primary-color);
}

.warps {
    position: relative;

    .top0 {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }
}

.site_booking {
    padding: 0 16px;

    .top {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
    }

    .tabs {
        margin: 16px 0;

        .radio {
            width: 120px;
            text-align: center;
            font-size: 14px;
        }
    }

    .act {
        color: var(--primary-color);
    }
}

.acti {
    color: var(--primary-color);
}
</style>
