<template>
    <div class="appoin_set">
        <div class="title">
            会议预约设置
            <a-button
                v-if="state.isShowEdit"
                class="button-link"
                type="link"
                @click="state.isShowEdit = false"
            >
                <template #icon>
                    <EditFilled />
                </template>
                <span style="margin: 0 4px">编辑</span>
            </a-button>
        </div>
        <a-form
            ref="setRef"
            layout="vertical"
            :model="state.setForm"
            :rules="state.setRules"
        >
            <a-form-item label="关联场地类型:" name="siteTypeId">
                <a-select
                    v-model:value="state.setForm.siteTypeId"
                    :disabled="state.isShowEdit"
                    mode="multiple"
                    placeholder="请选择"
                    :max-tag-count="3"
                    :options="state.typeOptions"
                    :fieldNames="{ label: 'name', value: 'id' }"
                >
                </a-select>
            </a-form-item>
            <a-form-item label="关联具体场地:" name="siteName">
               

                <a-input
                    :disabled="state.isShowEdit"
                    v-model:value="state.setForm.siteName"
                    mode="tags"
                    placeholder="请选择(多选)"
                    @click="selectedSite"
                >
                </a-input>
            </a-form-item>
            <a-form-item label="使用人员类型:" name="userType">
                <a-select
                    :disabled="state.isShowEdit"
                    placeholder="请选择"
                    v-model:value="state.setForm.userType"
                    mode="tags"
                >
                    <a-select-option value="0">学生</a-select-option>
                    <a-select-option value="1">教职工</a-select-option>
                </a-select>
            </a-form-item>
            <label>开放时间:</label>

            <a-form-item v-for="(item, idx) in state.configRange" :key="idx">
                <div class="item">
                    <a-checkbox
                        v-model:checked="item.weekday"
                        :disabled="state.isShowEdit"
                        >{{ item.table }}</a-checkbox
                    >
                    <a-radio
                        v-if="item.tableTime"
                        :disabled="state.isShowEdit || state.showTime"
                        v-model:checked="item.copWeekday"
                        @click="handleRadio(idx, item)"
                    >
                        {{ item.tableTime }}
                    </a-radio>
                </div>
                <a-range-picker
                    style="width: 100%"
                    :disabled-date="disabledDate"
                    @change="changeDate"
                    :disabled="!item.weekday || state.isShowEdit"
                    v-model:value="item.time"
                    valueFormat="YYYY-MM-DD"
                    format="YYYY-MM-DD"
                />
            </a-form-item>
            <a-form-item v-if="!state.isShowEdit">
                <a-button
                    type="primary"
                    style="margin-right: 8px"
                    :loading="state.loading"
                    @click="handerSave"
                    >保存</a-button
                >
                <a-button @click="resetForm">取消</a-button>
            </a-form-item>
        </a-form>

        <!-- 选择具体场地（场地类型多选）-->
        <chooseSite
            title="选择场地"
            ref="statusRef"
            :showStatus="false"
            :types="state.changeTypeOptions"
            v-model:isVisible="state.chooseSiteVisible"
            :siteTypeId="state.setForm.siteTypeId"
            :siteIds="state.setForm.siteIds"
            @emitHandleOk="emitHandleOk"
            @emithandleCancel="state.chooseSiteVisible = false"
        />
    </div>
</template>

<script setup>
import { reactive, onMounted, watch, shallowRef } from "vue";
import dayjs from "dayjs";
import chooseSite from "../components/chooseSite.vue";
import { message } from "ant-design-vue";
import {
    getBookingSiteNums,
    siteTypeList,
    siteTypeDetail,
    editSiteType,
} from "@/api/siteBooking";
const setRef = shallowRef();
const state = reactive({
    showTime: false,
    isShowEdit: true,
    loading: false,
    siteBookingTypeId: "",
    configRange: [
        {
            table: "工作日",
            tableTime: "",
            type: 1,
            weekday: false,
            copWeekday: false,
            time: [],
        },
        {
            table: "周末",
            tableTime: "时间同工作日",
            type: 2,
            weekday: false,
            copWeekday: false,
            time: [],
        },
        {
            table: "节假日",
            tableTime: "时间同工作日",
            type: 3,
            weekday: false,
            copWeekday: false,
            time: [],
        },
    ],
    changeTypeOptions: [],
    typeOptions: [],
    siteOptions: [],
    setForm: {
        id: "",
        userType: [],
        siteTypeId: [],
        temporaryClosed: 0,
        siteIds: [],
        siteName: "",
        configList: [],
    },
    setRules: {
        siteTypeId: [{ required: true, message: "请选择" }],
        siteName: [{ required: true, message: "请选择" }],
        userType: [{ required: true, message: "请选择" }],
    },
});
const handleRadio = (idx, item) => {
    if (state.configRange[0].time) {
        state.configRange[idx].time = !item.copWeekday
            ? state.configRange[0].time
            : [];
    }
    state.configRange[idx].copWeekday = !item.copWeekday;
};
// 场地弹框提交
const emitHandleOk = (siteIds, idds) => {
    let siteNames = [];
    idds.forEach((v) => {
        if (siteIds.includes(v.id)) {
            siteNames.push(v.siteName);
        }
    });
    if (siteNames.length) {
        state.setForm.siteName = siteNames.join("、");
        setRef.value.validateFields("siteName");
    }
    state.setForm.siteIds = siteIds;
    state.chooseSiteVisible = false;
};
const selectedSite = () => {
    state.changeTypeOptions = [];
    if (state.setForm.siteTypeId.length) {
        state.changeTypeOptions = state.typeOptions.filter((optionId) =>
            state.setForm.siteTypeId.some(
                (siteTypeId) => optionId.id === siteTypeId
            )
        );
    }
    state.chooseSiteVisible = true;
};
// 禁用开始的日期
const disabledDate = (current) => {
    return current && current < dayjs().endOf("day").add(-1, "day");
};
const changeDate = (evnet) => {
    if (!evnet) {
        state.showTime = true;
        state.configRange.forEach((v) => {
            v.copWeekday = false;
        });
    } else {
        state.showTime = false;
    }
};

// // 获取地预约类型列表及预约数量
const getTypeNum = async () => {
    state.siteBookingTypeId = "";
    await getBookingSiteNums().then(({ data }) => {
        data.forEach((v) => {
            if (v.type == 1) {
                state.siteBookingTypeId = v.id;
            }
        });
    });
};

// 关联场地类型
const getTypeList = async () => {
    await siteTypeList({ name: "会议室" }).then(({ data }) => {
        state.typeOptions = data;
    });
};
const toEdit = () => {
    siteTypeDetail({ id: state.siteBookingTypeId }).then(({ data }) => {
        const {
            id,
            name,
            temporaryClosed,
            siteTypeId,
            userType,
            siteList,
            configList,
        } = data;
        state.setForm.id = id;
        state.setForm.name = name;
        state.inputNumber = name.length;
        state.setForm.allClose = temporaryClosed;
        state.setForm.siteTypeId = siteTypeId.split(",");
        state.setForm.userType = userType.split(",");
        let siteNames = [];
        state.siteOptions = siteList.map((item) => {
            state.setForm.siteIds.push(item.id);
            siteNames.push(item.siteName);
            return {
                label: item.siteName,
                value: item.id,
            };
        });
        if (siteNames.length) {
            state.setForm.siteName = siteNames.join(",");
        }
        // 开放时间
        if (configList.length) {
            state.configRange.forEach((v) => {
                configList.forEach((k) => {
                    if (v.type == k.type) {
                        v.weekday = true;
                        v.copWeekday = true;
                        v.time = [k.startTime, k.endTime];
                    }
                });
            });
        }
    });
};

const resetForm = () => {
    // state.setForm.siteIds = []
    // state.configRange.forEach(v => {
    //     v.weekday = false
    //     v.copWeekday = false
    //     v.time = []
    // })
    // setRef.value.resetFields();
    state.isShowEdit = true;
};
const handerSave = () => {
    setRef.value.validate().then(() => {
        const params = { ...state.setForm };
        params.configList = [];
        state.configRange.forEach((v) => {
            if (v.weekday) {
                const [startTime = "", endTime = ""] = v.time || [];
                params.configList.push({ type: v.type, startTime, endTime });
            }
        });
        if (params.userType.length) {
            params.userType = params.userType.join(",");
        }
        if (params.siteTypeId.length) {
            params.siteTypeId = params.siteTypeId.join(",");
        }
        state.loading = true;
        editSiteType(params)
            .then((res) => {
                message.success(res.message);
                state.isShowEdit = true;
            })
            .finally(() => (state.loading = false));
    });
};
onMounted(async () => {
    await getTypeNum();
    await getTypeList();
    await toEdit();
});
</script>

<style scoped lang="less">
.appoin_set {
    margin: 20px;
    width: 452px;

    .title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .item {
        margin: 10px 0;
        display: flex;
        justify-content: space-between;
    }
}
</style>
