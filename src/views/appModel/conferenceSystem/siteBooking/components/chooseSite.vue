<template>
    <div class="card_set">
        <a-modal
            v-model:visible="isVisible"
            :title="props.title"
            @ok="handleOk"
            @cancel="handleCancel"
            width="816px"
        >
            <a-form layout="vertical">
                <div style="display: flex">
                    <a-form-item
                        label="场地类型："
                        style="width: 180px; margin-right: 10px"
                    >
                        <!-- allowClear -->
                        <a-select
                            v-model:value="state.siteForm.siteTypeIds"
                            mode="multiple"
                            placeholder="请选择"
                            :max-tag-count="1"
                            :options="types"
                            :fieldNames="field"
                        >
                            <template #maxTagPlaceholder="omittedValues">
                                <span style="color: var(--primary-color)"
                                    >+ {{ omittedValues.length }} ...</span
                                >
                            </template>
                        </a-select>
                    </a-form-item>
                    <a-form-item
                        label="所在建筑："
                        style="width: 180px; margin-right: 10px"
                    >
                        <a-select
                            placeholder="请选择"
                            v-model:value="state.siteForm.buildingId"
                        >
                            <a-select-option
                                v-for="(item, index) in state.bulidList"
                                :key="index"
                                :value="item.id"
                                >{{ item.name }}</a-select-option
                            >
                        </a-select>
                    </a-form-item>
                    <a-form-item
                        label="设备："
                        style="width: 180px; margin-right: 10px"
                    >
                        <a-select
                            placeholder="请选择"
                            v-model:value="state.siteForm.deviceId"
                        >
                            <a-select-option :value="0">教学楼</a-select-option>
                            <a-select-option :value="1">图书馆</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item
                        label="状态："
                        placeholder="请选择"
                        style="width: 180px; margin-right: 10px"
                        v-show="props.showStatus"
                    >
                        <a-select
                            placeholder="请选择"
                            v-model:value="state.siteForm.status"
                        >
                            <a-select-option :value="0">已约满</a-select-option>
                            <a-select-option :value="1">可预约</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item
                        label="场地名称："
                        style="width: 180px; margin-right: 10px"
                    >
                        <a-input
                            placeholder="请输入"
                            v-model:value="state.siteForm.siteName"
                        />
                    </a-form-item>
                </div>
                <a-form-item>
                    <a-button
                        type="primary"
                        style="margin-right: 12px"
                        @click="queryStatistics"
                    >
                        <template #icon>
                            <SearchOutlined style="margin-right: 12px" />
                        </template>
                        查询
                    </a-button>
                    <a-button @click="resetStatistics">
                        <template #icon>
                            <redo-outlined style="margin-right: 12px" />
                        </template>
                        重置
                    </a-button>
                </a-form-item>
            </a-form>
            <div class="table">
                <YTable
                    :columns="state.columns"
                    :isRadio="props.isRadio"
                    :dataSource="state.siteTable"
                    :isSrollY="true"
                    :slots="['operation']"
                    :rowSelection="true"
                    :totals="state.pagination"
                    :spinLoad="state.spinLoad"
                    :selectedRowKey="state.selectedRowKeys"
                    @onSelectedArry="onSelectedArry"
                    @onSelectedRowKeys="onSelectedRowKeys"
                >
                    <template #bodyCell="{ column, text, record }">
                        <template v-if="column.dataIndex === 'status'">
                            <a-badge
                                :status="
                                    record.status === 0
                                        ? 'success'
                                        : record.status === 1
                                        ? 'error'
                                        : 'default'
                                "
                                :text="
                                    record.status === 0
                                        ? '可预约'
                                        : record.status === 1
                                        ? '已约满'
                                        : '封禁'
                                "
                            />
                        </template>
                        <Tooltip v-else :title="text" />
                    </template>
                    <template #emptyText> 暂无数据 </template>
                </YTable>
            </div>
        </a-modal>
    </div>
</template>

<script setup>
import { toRefs, reactive, computed, onMounted, watch } from "vue";
import { buildingSiteList, sitePages } from "@/api/siteBooking";
import YTable from "comps/YTable/index.vue";
const props = defineProps({
    isRadio: {
        type: Boolean,
        default: false,
    },
    isVisible: {
        type: Boolean,
        default: false,
    },
    showStatus: {
        type: Boolean,
        default: true,
    },
    title: {
        type: String,
        default: "",
    },
    typeId: {
        type: String,
        default: "",
    },
    types: {
        type: Array,
        default: () => [],
    },
    siteTypeId: {
        type: Array,
        default: () => [],
    },
    siteIds: {
        type: Array,
        default: () => [],
    },
    siteBookingTypeId: {
        type: String,
        default: "",
    },
    chooseSiteId: {
        type: String,
        default: "",
    },
});
const emit = defineEmits([
    "update:isVisible",
    "emitHandleOk",
    "emithandleCancel",
    "selectChangeArr",
]);
const field = {
    label: "name",
    value: "id",
};
const isVisible = computed(() => {
    return props.isVisible;
});

// 确定
const handleOk = () => {
    emit("emitHandleOk", state.sites, state.siteTable);
};
// 取消
const handleCancel = () => {
    // if (state.sites) {
    //     state.sites.length = 0
    // }
    emit("emithandleCancel");
};

const state = reactive({
    // types: JSON.parse(JSON.stringify(location.getItem('types_'))),
    bulidList: [],
    siteForm: {
        siteTypeIds: [],
        status: undefined,
        siteName: "",
        // buildingId: '',
        // deviceId: ''
    },
    columns: [
        {
            title: "场地名称",
            dataIndex: "siteName",
        },
        {
            title: "所在建筑",
            dataIndex: "buildingName",
        },
        {
            title: "楼层",
            dataIndex: "floor",
        },
        {
            title: "房间号",
            dataIndex: "roomNum",
        },
        {
            title: "场地类型",
            dataIndex: "siteTypeName",
        },
        {
            title: "容纳人数",
            dataIndex: "peppleNum",
        },
        {
            title: "设备",
            dataIndex: "deviceNames",
        },
        {
            title: "状态",
            dataIndex: "status",
        },
    ],
    siteTable: [],
    siteTabless: [], // 所有场地
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    spinLoad: false,
    sites: [],
    selectedRowKeys: [],
});

const getSitePages = async () => {
    const params = {
        siteBookingTypeId: props.typeId || props.siteBookingTypeId,
        ...state.pagination,
        ...state.siteForm,
    };
    if (!params.siteTypeIds.length) {
        params.siteTypeIds = props.types.map((v) => v.id);
    }
    sitePages(params).then(({ data }) => {
        const { list, total, pageNo, pageSize } = data;
        state.siteTable = list;
        state.pagination = { total, pageSize, pageNo };

        if (props.siteIds.length) {
            state.selectedRowKeys = props.siteIds;
        }
        if (props.chooseSiteId) {
            state.selectedRowKeys = [props.chooseSiteId];
        }
    });
};

const onSelectedArry = (e, data) => {
    state.sites = e;
    emit("selectChangeArr", e);
};
/* 分页事件 */
const onSelectedRowKeys = (data) => {
    const { current, pageSize } = data;
    state.pagination.pageNo = current;
    state.pagination.pageSize = pageSize;
    getSitePages();
};
const getBuildingSiteList = () => {
    buildingSiteList().then((res) => {
        state.bulidList = res.data;
    });
};
// 查询
const queryStatistics = () => {
    getSitePages();
    state.pagination.pageNo = 1;
};
// 重置
const resetStatistics = () => {
    // state.siteForm.siteTypeIds.length = 0
    state.siteForm.buildingId = null;
    state.siteForm.deviceId = null;
    // state.siteForm.buildingId.length = 0
    // state.siteForm.deviceId.length = 0
    state.siteForm.siteName = "";
    state.siteForm.status = undefined;
    state.pagination.pageNo = 1;
    getSitePages();
};
watch(
    () => props.isVisible,
    (val) => {
        if (val) {
            state.siteForm.siteTypeIds = props.siteTypeId;
            state.sites = props.siteIds;
            queryStatistics();
        } else {
            state.selectedRowKeys = [];
        }
    }
);
onMounted(() => {
    getBuildingSiteList(); // 获取建筑
    getSitePages(); // 场地分页
});
defineExpose({
    ...toRefs(state),
});
</script>

<style lang="less" scoped>
.card_set {
}

:deep(.ant-select-selection-overflow) {
    height: 28px !important;
}
</style>
