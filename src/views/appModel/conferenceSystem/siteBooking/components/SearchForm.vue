<template>
    <div class="searchHead">
        <a-form layout="inline">
            <a-form-item label="时间：" v-if="props.time1">
                <a-range-picker
                    class="picker"
                    v-model:value="state.sear.time"
                    valueFormat="YYYY-MM-DD"
                    format="YYYY-MM-DD"
                />
            </a-form-item>
            <a-form-item label="时间：" v-if="props.time2">
                <!-- :defaultValue="new Date()" -->
                <a-date-picker
                    valueFormat="YYYY-MM-DD"
                    format="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                    style="width: 180px"
                    v-model:value="state.sear.time2"
                />
            </a-form-item>
            <a-form-item
                label="所在建筑："
                style="min-width: 180px; max-width: 460px"
            >
                <!-- 多选
                    <a-tree-select
                    style="width:180px"
                    tree-checkable
                    :multiple="false"
                    v-model:value="state.sear.buildingId"
                    placeholder="请选择场地"
                    allow-clear
                    :tree-data="state.siteOptions"
                    :fieldNames="fieldNames"
                    @dropdownVisibleChange="dropdownVisibleChange"
                >
                    <template #title="title">
                        <home-outlined class="icon-yellow" />
                        {{ title.name }}
                    </template>
                </a-tree-select> -->
                <!-- 单选 -->
                <a-select
                    style="width: 180px"
                    placeholder="请选择"
                    allow-clear
                    v-model:value="state.sear.buildingId"
                >
                    <a-select-option
                        v-for="(item, index) in state.bulidList"
                        :key="index"
                        :value="item.id"
                        >{{ item.name }}</a-select-option
                    >
                </a-select>
            </a-form-item>
            <a-form-item label="场地类型：">
                <a-select
                    placeholder="请选择场地"
                    allow-clear
                    v-model:value="state.sear.siteTypeId"
                    style="width: 180px"
                >
                    <a-select-option
                        :value="item.id"
                        v-for="(item, index) in state.typeOptions"
                        :key="index"
                        >{{ item.name }}</a-select-option
                    >
                </a-select>
            </a-form-item>
            <a-form-item label="设备：" v-show="sheb">
                <a-select
                    v-model:value="state.sear.sheName"
                    mode="multiple"
                    allow-clear
                    style="width: 220px"
                    placeholder="请选择"
                    :max-tag-count="props.max"
                    :options="props.optionshe"
                >
                    <template #maxTagPlaceholder="omittedValues">
                        <span style="color: red"
                            >+ {{ omittedValues.length }} ...</span
                        >
                    </template>
                </a-select>
            </a-form-item>
            <a-form-item label="场地名称：">
                <a-input
                    v-model:value="state.sear.siteName"
                    allow-clear
                    placeholder="请输入"
                    style="width: 180px"
                />
            </a-form-item>
            <a-form-item label="预约主题：" v-if="props.name">
                <a-input
                    v-model:value="state.sear.name"
                    allow-clear
                    placeholder="请输入"
                    style="width: 180px"
                />
            </a-form-item>
            <a-form-item label="预约人：" v-if="props.showBooker">
                <a-input
                    v-model:value="state.sear.sponsorName"
                    allow-clear
                    placeholder="请输入"
                    style="width: 180px"
                />
            </a-form-item>
            <a-form-item>
                <a-button
                    type="primary"
                    style="margin-right: 12px"
                    @click="queryData()"
                >
                    <template #icon>
                        <SearchOutlined style="margin-right: 12px" />
                    </template>
                    查询
                </a-button>
                <a-button @click="resetForm()">
                    <template #icon>
                        <redo-outlined style="margin-right: 12px" />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
import { reactive, onMounted, toRefs } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getUnSite } from "@/api/classSign";
import { buildingSiteList, siteTypeList } from "@/api/siteBooking";
import dayjs from "dayjs";

const fieldNames = {
    label: "name",
    value: "id",
    children: "children",
};
const router = useRouter();
const route = useRoute();
const state = reactive({
    bulidList: [],
    typeOptions: [],
    siteOptions: [],
    sear: {
        time: [],
        time2: null,
        buildingId: undefined,
        siteTypeId: undefined,
        siteName: "",
        name: "",
        sponsorName: "",
        siteBookingTypeId: "",
        status: null,
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
});

defineExpose({
    ...toRefs(state),
});

// 获取未创建班牌账号的场地列表 新增时用
// const getNoSignSite = async() => {
//     const { data } = await getUnSite()
//     state.siteOptions = data
// }
// const dropdownVisibleChange = () => {
//     getNoSignSite()
// }
const props = defineProps({
    max: {
        type: Number,
        default: 2,
    },
    sheb: {
        type: Boolean,
        default: false,
    },
    name: {
        type: Boolean,
        default: true,
    },
    time1: {
        type: Boolean,
        default: true,
    },
    time2: {
        type: Boolean,
        default: false,
    },
    showBooker: {
        type: Boolean,
        default: false,
    },
    optionshe: {
        type: Array,
        default: () => [],
    },
    // search: {
    //     type: Object,
    //     default: () => {
    //         return {
    //             pageNo: 1,
    //             pageSize: 1
    //         }
    //     }
    // }
});
const emit = defineEmits(["queryList", "resetList"]);

// 查询
const queryData = () => {
    emit("queryList");
};
// 重置
const resetForm = () => {
    for (const key in state.sear) {
        state.sear[key] = "";
    }
    state.sear.buildingId = undefined;
    state.sear.siteTypeId = undefined;
    state.sear.pageNo = 1;
    state.sear.pageSize = 10;
    emit("resetList");
};
// 禁用开始的日期
const disabledDate = (current) => {
    return current && current < dayjs().endOf("day").add(-1, "day");
};
// 获取建筑
const getBuildingSiteList = () => {
    buildingSiteList().then((res) => {
        state.bulidList = res.data;
    });
};
// 关联场地类型
const getTypeList = () => {
    siteTypeList().then((res) => {
        state.typeOptions = res.data;
    });
};
onMounted(() => {
    getBuildingSiteList(); // 获取建筑
    getTypeList(); // 关联场地类型
});
</script>

<style lang="less" scoped>
.searchHead {
    display: flex;
    justify-content: space-between;
    margin: 24px 0 16px 0;
}

// :deep(.ant-form-item):nth-child(5){
//     margin-top: 16px;
// }
// :deep(.ant-form-item):nth-child(6){
//     margin-top: 16px;
// }
// :deep(.ant-form-item):nth-child(7){
//     margin-top: 16px;
// }
:deep(.ant-form-item):nth-child(8) {
    // margin-top: 16px;
}
</style>

<style lang="less">
.searchHead {
    .ant-form-inline {
        .ant-form-item {
            margin-bottom: 12px;
        }
    }
}
</style>
