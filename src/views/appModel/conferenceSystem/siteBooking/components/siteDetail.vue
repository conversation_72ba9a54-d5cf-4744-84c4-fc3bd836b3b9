<template>
    <div class="create-creative" ref="createCreative">
        <a-modal :getContainer="() => createCreative" @cancel="handleCancel" v-model:visible="isVisible"
            :title="props.title" style="width: 788px; height: 660px; z-index: 999">
            <template #footer>
                <div></div>
                <!-- <div v-else> -->
                <div v-if="status === 2 && !onStatus">
                    <a-button @click="cancel" v-if="review">取消预约</a-button>
                </div>
                <div v-if="(status === 0 || status === 3 || status === 4) &&
            !onStatus
            ">
                    <a-button @click="edit" v-if="review">重新编辑</a-button>
                </div>
                <div v-if="status === 1 && !onStatus">
                    <a-button @click="refuse" v-if="nobtn">拒绝</a-button>
                    <a-button @click="pass" v-if="nobtn" type="primary">通过</a-button>
                    <a-button @click="cancel" v-if="review">取消预约</a-button>
                </div>
                <!-- </div> -->
            </template>
            <a-spin :spinning="!name">
                <div class="headr">
                    <template v-if="name">
                        <div class="theme">{{ name }}</div>
                        <div class="top_status 2" v-if="!onStatus" :style="{ background: approveStatu[status].color }">
                            {{ approveStatu[status].text }}
                        </div>
                    </template>
                </div>
                <div class="sits www">
                    <!-- 0.已拒绝、1.待审核、2.已通过、3.已取消、4.已过期 -->
                    <div class="label">
                        <div class="left">签到状态：</div>
                        <div>{{ themeitem.signIn ? "需签到" : "无需签到" }}</div>
                    </div>
                    <div class="label">
                        <div class="left">预约时间：</div>
                        <div>{{ themeitem.bookingTime }}</div>
                    </div>
                    <div class="label">
                        <div class="left">预约人：</div>
                        {{ themeitem.sponsorName }}
                    </div>
                    <div v-if="onStatus">
                        <div class="label">
                            <div class="left no">
                                参会人： (应到人数：
                                {{ themeitem.signList.length }} 人)
                            </div>
                            <div>
                                <span v-for="(item, index) in themeitem.signList" :key="index">{{ item.userName }}
                                    ,</span>
                            </div>
                        </div>
                        <div class="label">
                            <div class="left no">预约说明：</div>
                            <div>{{ themeitem.describe }}</div>
                        </div>
                        <div class="theme" style="
                            font-weight: 600;
                            font-size: 16px;
                            margin-bottom: 16px;
                            margin-top: -4px;
                        ">
                            预约场地
                        </div>
                        <div class="label">
                            <div class="left">场地类型：</div>
                            <div>{{ themeitem.siteInfo.siteTypeName }}</div>
                        </div>
                        <div class="label">
                            <div class="left">场地位置：</div>
                            <div>
                                <span>{{ themeitem.siteInfo.buildingName }}</span>
                                <span v-if="themeitem.siteInfo.floor">- {{ themeitem.siteInfo.floor }}</span>
                                <span v-if="themeitem.siteInfo.roomNum">- {{ themeitem.siteInfo.roomNum }}</span>
                            </div>
                        </div>
                        <div class="label">
                            <div class="left">容纳人数：</div>
                            <div>{{ themeitem.siteInfo.peppleNum }}</div>
                        </div>
                        <div class="label">
                            <div class="left">设备：</div>
                            <div>{{ themeitem.siteInfo.deviceNames }}</div>
                        </div>
                    </div>
                    <div v-else>
                        <div class="off_on" v-if="open" @click="opened(true)">
                            展开更多&nbsp;<down-outlined />
                        </div>
                        <div v-if="isOpen">
                            <div class="label">
                                <div class="left no">
                                    参会人： (应到人数：
                                    {{ themeitem.signList.length }} 人)
                                </div>
                                <div>
                                    <span v-for="(item, index) in themeitem.signList" :key="index">{{ item.userName }}
                                        ,</span>
                                </div>
                            </div>
                            <div class="label">
                                <div class="left no">预约说明：</div>
                                <div>{{ themeitem.describe }}</div>
                            </div>
                            <div class="theme" style="
                                font-weight: 600;
                                font-size: 16px;
                                margin-bottom: 16px;
                                margin-top: -4px;
                            ">
                                预约场地
                            </div>
                            <div class="label">
                                <div class="left">场地类型：</div>
                                <div>{{ themeitem.siteInfo.siteTypeName }}</div>
                            </div>
                            <div class="label">
                                <div class="left">场地位置：</div>
                                <div>
                                    <span>{{
            themeitem.siteInfo.buildingName
        }}</span>
                                    <span v-if="themeitem.siteInfo.floor">- {{ themeitem.siteInfo.floor }}</span>
                                    <span v-if="themeitem.siteInfo.roomNum">- {{ themeitem.siteInfo.roomNum }}</span>
                                </div>
                            </div>
                            <div class="label">
                                <div class="left">容纳人数：</div>
                                <div>{{ themeitem.siteInfo.peppleNum }}</div>
                            </div>
                            <!-- <div class="label"><div class="left">设备： </div><div>{{ themeitem.she }}</div></div> -->
                            <div class="label">
                                <div class="left">设备：</div>
                                <div>{{ themeitem.siteInfo.deviceNames }}</div>
                            </div>
                        </div>
                        <div class="off_on" v-if="close" @click="opened(false)">
                            收起&nbsp;<up-outlined />
                        </div>
                    </div>
                </div>
                <div v-if="!onStatus">
                    <div class="st_tit">审核状态</div>
                    <div class="st_con">
                        <div class="one">
                            <div class="left">
                                <div class="le">
                                    <img class="head" src="/image/begain_one.png" alt="" />
                                    <img class="aggr" src="/image/agreen.png" alt="" />
                                </div>
                                <div class="ri">
                                    <div class="begin 1">发起申请</div>
                                    <div class="name yi">
                                        {{ audits.sponsorName }}
                                    </div>
                                </div>
                            </div>
                            <div class="yi">{{ audits.sponsorTime }}</div>
                        </div>
                        <div class="line"></div>
                        <div class="two">
                            <div class="left">
                                <div class="le">
                                    <img class="head" :style="`border: 1px solid ${approveStatu[status].color}`"
                                        src="/image/tephoto1.png" alt="" />
                                    <img class="aggr" v-if="status === 0" src="/image/jujue.png" alt="" />
                                    <img class="aggr" v-if="status === 1" src="/image/wait.png" alt="" />
                                    <img class="aggr" v-if="status === 2" src="/image/agreen.png" alt="" />
                                </div>
                                <div class="ri">
                                    <div class="begin">审批人</div>
                                    <div class="name yi">
                                        <span v-for="(item, index) in audits.shenpis" :key="index">
                                            {{ item.name }} <i
                                                v-if="!index && audits.shenpis.length - 1 !== index">,</i>
                                        </span>&nbsp;
                                        <span v-if="[0, 1, 2].includes(status)" :style="{ color: approve.color }">
                                            {{ approve.text }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="yi">{{ audits.approvalTimeStr }}</div>
                        </div>
                        <div class="ju" v-if="status === 0">
                            {{ audits.refuseReason }}
                        </div>
                    </div>
                </div>
            </a-spin>
        </a-modal>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
const createCreative = ref(null);
const props = defineProps({
    isVisible: {
        type: Boolean,
        default: false,
    },
    open: {
        type: Boolean,
        default: true,
    },
    close: {
        type: Boolean,
        default: false,
    },
    isOpen: {
        type: Boolean,
        default: false,
    },
    onStatus: {
        // 没有审核状态
        type: Boolean,
        default: true,
    },
    nobtn: {
        // 没有审核状态
        type: Boolean,
        default: true,
    },
    review: {
        type: Boolean,
        default: true,
    },
    title: {
        type: String,
        default: "",
    },
    status: {
        type: Number,
        default: 0,
    },
    bg: {
        type: String,
        default: "red",
    },
    name: {
        type: String,
        default: "",
    },
    themeitem: {
        // 预约信息
        type: Object,
        default: () => { },
    },
    audits: {
        // 审核信息
        type: Object,
        default: () => { },
    },
});
// 0.拒绝、1.待审核、2.已通过、3.已取消、4.已过期

const emit = defineEmits([
    "update:isVisible",
    "emitHandleOk",
    "emithandleCancel",
    "update:name",
    "opened",
    "emitCancelBook",
    "emitReEdit",
    "emitRefuse",
    "emitPass",
]);
const isVisible = computed(() => {
    return props.isVisible;
});
const handleCancel = () => {
    emit("update:name", '');
    emit("emithandleCancel");
};
const opened = (e) => {
    emit("opened", e);
};
const cancel = () => {
    emit("emitCancelBook");
};
const edit = () => {
    emit("emitReEdit");
};
const refuse = () => {
    emit("emitRefuse");
};
const pass = () => {
    emit("emitPass");
};
// const state = reactive({
//     dealStatus: 0
// })
// 处理后台返回的状态

const getContainer = () => {
    return document.getElementById("modalSet");
};
const approveStatu = [
    { text: "已拒绝", color: '#F5222D' },
    { text: "待审核", color: '#FAAD14' },
    { text: "已通过", color: '#00B781' },
    { text: "已取消", color: '#BFBFBF' },
    { text: "已过期", color: '#BFBFBF' }]

const approveColor = [
    { text: "(已拒绝)", color: '#F5222D' },
    { text: "(其中1人审批即可)", color: '#FAAD14' },
    { text: "(已同意)", color: '#00B781' },
]
const approve = computed(() => {
    if (props.status === 1) {
        if (!props.audits.shenpis.length) {
            return {
                text: "",
                color: '#FAAD14'
            }
        }
    }
    return approveColor[props.status]
})
onMounted(() => { });
</script>

<style lang="less" scoped>
.create-creative {
    .ant-modal-body {
        .headr {
            display: flex;
            justify-content: space-between;
            border-radius: 4px 4px 0 0;
            background: rgba(0, 183, 129, 0.08);

            .theme {
                padding-top: 12px;
                padding-left: 12px;
                font-size: 16px;
                font-weight: 600;
            }

            .top_status {
                color: #fff;
                width: 52px;
                height: 25px;
                line-height: 25px;
                text-align: center;
                border-radius: 0 4px 0 4px;
            }
        }

        .sits {
            max-height: 450px;
            overflow-y: scroll;
            background: rgba(0, 183, 129, 0.08);
            padding: 12px;
            border-radius: 0 0 4px 4px;
            position: relative;

            .label {
                display: flex;
                padding-bottom: 8px;

                .left {
                    color: rgba(0, 0, 0, 0.45);
                }

                .no {
                    white-space: nowrap;
                }
            }

            .off_on {
                color: @primary-color;
                text-align: center;
                cursor: pointer;
            }
        }

        .st_tit {
            font-size: 16px;
            font-weight: 600;
            padding: 16px 0;
        }

        .st_con {
            .one {
                display: flex;
                justify-content: space-between;

                .left {
                    display: flex;

                    .le {
                        position: relative;
                        margin-right: 10px;

                        .head {
                            width: 40px;
                            height: 40px;
                            border-radius: 20px;
                            overflow: hidden;
                            border: 1px solid #00b781;
                        }

                        .aggr {
                            width: 14px;
                            height: 14px;
                            position: absolute;
                            top: 26px;
                            left: 26px;
                        }
                    }

                    .ri {
                        .begin {
                            color: rgba(0, 0, 0, 0.65);
                            font-size: 14px;
                        }
                    }
                }

                .yi {
                    color: rgba(0, 0, 0, 0.45);
                    font-size: 12px;
                }
            }

            .line {
                height: 28px;
                border: 1px dashed #00b781;
                width: 1px;
                margin-left: 18px;
                margin-top: -6px;
            }

            .two {
                display: flex;
                justify-content: space-between;

                .left {
                    display: flex;

                    .le {
                        position: relative;
                        margin-right: 10px;

                        .head {
                            width: 40px;
                            height: 40px;
                            border-radius: 20px;
                            overflow: hidden;
                            // border: 1px solid #00B781;
                        }

                        .aggr {
                            width: 14px;
                            height: 14px;
                            position: absolute;
                            top: 26px;
                            left: 26px;
                        }
                    }

                    .ri {
                        .begin {
                            color: rgba(0, 0, 0, 0.65);
                            font-size: 14px;
                        }
                    }
                }

                .yi {
                    color: rgba(0, 0, 0, 0.45);
                    font-size: 12px;
                }
            }

            .ju {
                margin-left: 50px;
            }
        }
    }
}
</style>
