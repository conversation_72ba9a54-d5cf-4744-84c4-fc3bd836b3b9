<!-- 预约记录 -->
<template>
    <div class="warps">
        <div class="site_booking" v-if="!state.showFlag">
            <div class="search_form">
                <SearchForm ref="searRef" @queryList="queryList" @resetList="resetList" :showBooker="true" />
            </div>
            <div class="table">
                <YTable :columns="columnsTwo" :dataSource="state.recordTable" :slots="['operation']"
                    :rowSelection="false" :totals="state.pagination" :spinLoad="state.spinLoad"
                    @onSelectedRowKeys="onSelectedRowKeys">
                    <template #headerCell="{ column }">
                        <template v-if="column.dataIndex === 'status'">
                            <a-dropdown>
                                <a style="color: #000" @click.prevent>
                                    审核状态
                                    <caret-down-outlined style="color: var(--primary-color)" />
                                </a>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item v-for="(
                                                item, index
                                            ) in state.statusList" :key="index" @click="chooseStatus(item, index)">
                                            <span :class="state.statusIndex === index
            ? 'acti'
            : ''
            ">{{ item.statusName }}</span>
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </template>
                    </template>
                    <template #bodyCell="{ column, text, record }">
                        <template v-if="column.dataIndex === 'signIn'">
                            <span v-if="record.signIn">需签到</span>
                            <span v-else>无需签到</span>
                        </template>
                        <template v-else-if="column.dataIndex === 'status'">
                            <a-badge v-if="record.status != 4" :status="statusArr[record.status]"
                                :text="statusTextArr[record.status]" />
                            <a-badge v-else style="color: #ccc" :status="statusArr[record.status]"
                                :text="statusTextArr[record.status]" />
                        </template>
                        <template v-else-if="column.dataIndex === 'operation'">
                            <a @click="toDetail(record)" style="color: var(--primary-color);margin-right: 10px;">详情</a>
                            <a @click="toSheet(record)" v-if="record.status === 2 && record.signIn"
                                style="color: var(--primary-color)">签到表</a>
                            <a style="color: #ccc" :disabled="true" v-else>签到表</a>
                        </template>
                        <Tooltip v-else :title="text" />
                    </template>
                </YTable>
            </div>
        </div>
        <!-- 签到表 -->
        <signInSheet @showViews="showViews" v-if="state.showFlag1" :class="state.showFlag1 ? 'top01' : ''" />
        <!-- 预约申请 -->
        <toBooking @showView="showView" :siteBookingTypeId="state.siteBookingTypeId" :typeId="state.typeId"
            :bookDetail="state.bookDetail" v-if="state.showFlag2" :class="state.showFlag2 ? 'top02' : ''" />

        <!-- 预约详情 -->
        <siteDetail @emithandleCancel="emithandleCancel" @opened="opened" @emitCancelBook="emitCancelBook"
            @emitReEdit="emitReEdit" @emitRefuse="emitRefuse" @emitPass="emitPass" title="查看详情"
            :onStatus="state.onStatus" :open="state.open" :nobtn="false" :close="state.close" :isOpen="state.isOpen"
            :audits="state.audits" :isVisible="state.isVisible" :themeitem="state.themeitem"
            :status="Number(state.statuss)" v-model:name="state.name" />
        <!-- 拒绝原因弹框 -->
        <a-modal v-model:visible="state.visibleJ" title="拒绝原因" @ok="handleOk" @cancel="handleCancelJu">
            <a-textarea :maxlength="state.maxNum" v-model:value="state.ju" @input="GetTextNumber()" showCount
                placeholder="请输入拒绝原因" :auto-size="{ minRows: 3, maxRows: 5 }" />
        </a-modal>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, shallowRef, nextTick, inject } from "vue";
import { useRouter, useRoute } from "vue-router";
import { columnsTwo } from "../siteBook/tableData";
import YTable from "comps/YTable/index.vue";
import SearchForm from "../components/SearchForm.vue";
import siteDetail from "../components/siteDetail.vue";
import signInSheet from "./signInSheet.vue";
import toBooking from "../siteBook/toBooking.vue";
import { message } from "ant-design-vue";
import {
    bookingRecordPage,
    getBookingSiteNums,
    bookingDetail,
    bookingUpdate,
} from "@/api/siteBooking";
let stateShowNewPage = inject("showNewPage")();
// <!-- // 0.拒绝、1.审批中、2.已通过、3.已取消、4.已过期 -->
const statusTextArr = ["拒绝", "审批中", "已通过", "已取消", "已过期"];
const statusArr = ["error", "warning", "success", "default", "default"];
const searRef = ref();
const router = useRouter();
const route = useRoute();
const state = reactive({
    bookDetail: {},
    spinLoad: false,
    showFlag: false,
    showFlag1: false, // 签到表
    showFlag2: false, // 预约申请
    // 预约详情弹框----start
    onStatus: false,
    isVisible: false,
    visibleJ: false,
    isOpen: false,
    open: true,
    close: false,
    name: "",
    status: "",
    statuss: "",
    themeitem: {
        signIn: false,
        bookingTime: "",
        sponsorName: "",
        signList: [],
        describe: "",
        siteInfo: {},
        she: "会议机，投影仪",
    },
    audits: {
        sponsorName: "",
        sponsorTime: "",
        approvalTimeStr: "",
        shenpis: [],
        refuseReason: "",
    },
    myBookDetailId: "",
    ju: "",
    maxNum: 300,
    textNum: 0,
    // 预约详情弹框----end
    recordTable: [],
    // 0.拒绝、1.审批中、2.已通过、3.已取消、4.已过期
    statusList: [
        { statusName: "全部", status: null },
        { statusName: "已拒绝", status: 0 },
        { statusName: "审批中", status: 1 },
        { statusName: "已通过", status: 2 },
        { statusName: "已取消", status: 3 },
        { statusName: "已过期", status: 4 },
    ],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    siteBookingTypeId: "",
    statusIndex: 0,
    typeIndex: 0,
    typeId: "",
});

const getBookingRecordPage = () => {
    const params = {};
    const obj = searRef.value.sear;
    params.pageNo = state.pagination.pageNo;
    params.pageSize = state.pagination.pageSize;
    params.name = obj.name;
    params.sponsorName = obj.sponsorName;
    params.startTime = obj.time[0];
    params.endTime = obj.time[1];
    params.siteName = obj.siteName;
    params.siteTypeId = obj.siteTypeId;
    params.buildingId = obj.buildingId;
    params.siteBookingTypeId = state.siteBookingTypeId;
    params.status = state.status;
    state.spinLoad = true;
    bookingRecordPage(params)
        .then(({ data }) => {
            const { list, total, pageNo, pageSize } = data;
            state.recordTable = list;
            state.pagination = { total, pageNo, pageSize };
        })
        .finally(() => {
            state.spinLoad = false;
        });
};
// 签到表
const toSheet = (e) => {
    router.replace({
        query: {
            ...route.query,
            type: "true",
        },
    });

    state.showFlag = true;
    state.showFlag1 = true;
    stateShowNewPage.showNewPage = false;
    stateShowNewPage.siteBookingId = e.id;
};
// 签到表的返回
const showViews = () => {
    state.showFlag = false;
    state.showFlag1 = false;
    stateShowNewPage.showNewPage = true;
};
// 预约申请的返回
const showView = () => {
    state.showFlag = false;
    state.showFlag2 = false;
    stateShowNewPage.showNewPage = true;
    nextTick(() => {
        getBookingRecordPage();
    });
};
const chooseType = (item, index) => {
    state.typeIndex = index;
    state.typeId = item.id;
    state.pagination.pageNo = 1;
    state.statusIndex = 0;
    state.myBookDetailId = item.id;
    getBookingRecordPage();
};
const chooseStatus = (item, index) => {
    state.statusIndex = index;
    state.pagination.pageNo = 1;
    state.typeIndex = 0;
    state.status = item.status;
    state.statuss = JSON.parse(JSON.stringify(item.status));

    getBookingRecordPage();
};
const toDetail = (record) => {
    state.myBookDetailId = record.id;
    state.onStatus = false;
    bookingDetail({ id: record.id }).then((res) => {
        const { data } = res;
        state.name = data.name;
        state.themeitem.signIn = data.signIn;
        state.themeitem.bookingTime = data.bookingTime;
        state.themeitem.sponsorName = data.sponsorName;
        state.themeitem.signList = data.signList;
        state.themeitem.describe = data.describe;
        state.themeitem.siteInfo = data.siteInfo;
        state.themeitem.sponsorOrgName = data.sponsorOrgName
        state.audits.shenpis = data.siteInfo.list;
        state.audits.sponsorName = data.sponsorName;
        state.audits.refuseReason = data.refuseReason;
        state.audits.sponsorTime = data.sponsorTime;
        state.audits.approvalTimeStr = data.approvalTimeStr;
        state.statuss = JSON.parse(JSON.stringify(data.status));
    });
    state.isVisible = true;
};
// 关闭详情弹框
const emithandleCancel = () => {
    state.isVisible = false;
    stateShowNewPage.showNewPage = true;
};
// 展开，收起
const opened = (e) => {
    if (e) {
        state.isOpen = true;
        state.open = false;
        state.close = true;
    } else {
        state.isOpen = false;
        state.open = true;
        state.close = false;
    }
};
// 取消预约
const emitCancelBook = () => {
    bookingUpdate({ status: 3, id: state.myBookDetailId }).then((res) => {
        if (res.code === 0) {
            getBookingRecordPage();
            state.isVisible = false;
            stateShowNewPage.showNewPage = true;
            message.success(res.message);
        }
    });
};
// 重新编辑
const emitReEdit = () => {
    state.isVisible = false;
    state.showFlag = true;
    state.showFlag2 = true;
    stateShowNewPage.showNewPage = false;
    bookingDetail({ id: state.myBookDetailId })
        .then(({ data }) => {
            state.bookDetail = data;
        })
        .finally(() => (stateShowNewPage.showNewPage = false));
};
// 拒绝
const emitRefuse = () => {
    state.visibleJ = true;
};
// 取消拒绝
const handleCancelJu = () => {
    state.ju = "";
    state.textNum = 0;
};
// 确定拒绝
const handleOk = () => {
    if (!state.ju.trim()) {
        message.warning("请输入拒绝原因");
    } else {
        bookingUpdate({
            status: 0,
            refuseReason: state.ju,
            id: state.myBookDetailId,
        }).then((res) => {
            if (res.code === 0) {
                getBookingRecordPage();
                state.visibleJ = false;
                state.isVisible = false;
                stateShowNewPage.showNewPage = true;
                message.success(res.message);
            }
        });
    }
};
// 通过
const emitPass = () => {
    bookingUpdate({ status: 2, id: state.myBookDetailId }).then((res) => {
        if (res.code === 0) {
            getBookingRecordPage();
            state.isVisible = false;
            stateShowNewPage.showNewPage = true;
            message.success(res.message);
        }
    });
};
/* 分页事件 */
const onSelectedRowKeys = (data) => {
    const { current, pageSize } = data;
    state.pagination.pageNo = current;
    state.pagination.pageSize = pageSize;
    getBookingRecordPage();
};
// 查询
const queryList = () => {
    state.pagination.pageNo = 1;
    state.pagination.pageSize = 10;
    getBookingRecordPage();
};
// 重置
const resetList = () => {
    state.pagination.pageNo = 1;
    getBookingRecordPage();
};
// 获取地预约类型列表及预约数量
const getTypeNum = async () => {
    state.siteBookingTypeId = "";
    await getBookingSiteNums().then(({ data }) => {
        data.forEach((v) => {
            if (v.type == 1) {
                state.siteBookingTypeId = v.id;
            }
        });
    });
};
onMounted(async () => {
    await getTypeNum();
    await getBookingRecordPage();
});
</script>

<style lang="less" scoped>
.act {
    color: var(--primary-color);
}

.warps {
    position: relative;

    .top01 {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }

    .top02 {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }
}

.site_booking {
    padding: 0 16px;

    .top {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
    }
}

.acti {
    color: var(--primary-color);
}

.actt {
    color: var(--primary-color);
}

.max {
    position: absolute;
    right: 38px;
    bottom: 80px;
}
</style>
