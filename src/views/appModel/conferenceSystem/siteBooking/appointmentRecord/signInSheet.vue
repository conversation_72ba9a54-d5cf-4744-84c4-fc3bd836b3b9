<template>
    <div class="dao">
        <a-layout-header class="reset-layout-header" style="position: relative">
            <i class="iconfont icon-xingzhuangjiehe19" @click="backs" />
            <span style="margin-left: 10px">签到表</span>
        </a-layout-header>
        <div class="warps">
            <div class="sear">
                <a-form layout="inline">
                    <a-form-item label="姓名：">
                        <a-input v-model:value.trim="state.userName" placeholder="请输入" style="width: 240px" />
                    </a-form-item>
                    <a-form-item>
                        <a-button type="primary" style="margin-right: 12px" @click="search">
                            <template #icon>
                                <SearchOutlined style="margin-right: 12px" />
                            </template>
                            查询
                        </a-button>
                        <a-button @click="resetForm">
                            <template #icon>
                                <redo-outlined style="margin-right: 12px" />
                            </template>
                            重置
                        </a-button>
                    </a-form-item>
                </a-form>
            </div>

            <YTable :columns="columns" :dataSource="state.tables" :slots="['operation']" :rowSelection="false"
                :totals="state.pagination" :spinLoad="state.spinLoad" @onSelectedRowKeys="onSelectedRowKeys">
                <template #headerCell="{ column }">
                    <template v-if="column.dataIndex === 'status'">
                        <a-dropdown>
                            <a style="color: #000" @click.prevent>
                                状态
                                <caret-down-outlined style="color: var(--primary-color)" />
                            </a>
                            <template #overlay>
                                <a-menu>
                                    <a-menu-item v-for="(
                                            item, index
                                        ) in state.statusList" :key="index" @click="chooseStatus(item, index)">
                                        <span :class="state.statusIndex === index
                                            ? 'act'
                                            : ''
                                            ">
                                            {{ item.statusName }}
                                        </span>
                                    </a-menu-item>
                                </a-menu>
                            </template>
                        </a-dropdown>
                    </template>
                </template>
                <template #bodyCell="{ column, text, index, record }">
                    <template v-if="column.dataIndex === 'index'">
                        {{ index + 1 }}
                    </template>
                    <template v-else-if="column.dataIndex === 'status'">
                        <a-badge :status="['warning', 'success', 'default'][record.status]
                            " :text="['未签到', '已签到', '未开始'][record.status]
                                " />
                    </template>
                    <Tooltip v-else :title="text"></Tooltip>
                </template>
            </YTable>
        </div>
    </div>
</template>

<script setup>
import { reactive, onMounted, watch, nextTick, inject } from "vue";
import YTable from "comps/YTable/index.vue";
import { useRouter, useRoute } from "vue-router";
import { bookingSignPage } from "@/api/siteBooking";
import { getColumnCheckboxGroupFilterDropdown } from "@/components/FilterDropdown.jsx";
let stateShowNewPage = inject("showNewPage")();
const router = useRouter();
const route = useRoute();

const state = reactive({
    visible: false,
    spinLoad: false,
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    status: null,
    userName: "",
    order: '',
    siteBookingId: route.query.siteBookingId,
    tables: [],
    statusList: [
        { statusName: "全部", status: null },
        { statusName: "未签到", status: 0 },
        { statusName: "已签到", status: 1 },
        { statusName: "未开始", status: 2 },
    ],
    statusIndex: 0,
});
const columns = [
    {
        title: "序号",
        key: "id",
        dataIndex: "index",
        ellipsis: true,
        fixed: "left",
    },
    {
        title: "姓名",
        dataIndex: "userName",
    },
    {
        title: "所在部门",
        dataIndex: "dept",
    },
    {
        title: "状态",
        dataIndex: "status",
    },
    {
        title: "签到时间",
        dataIndex: "signInTime",
        sorter: true,
        width: 180,
        filterDropdown: (vnode) => { },
    },
];
const emit = defineEmits(["showViews"]);
const backs = () => {
    router.replace({ query: {} });
    emit("showViews");
    stateShowNewPage.showNewPage = true;
};

const getSheetList = () => {
    state.spinLoad = true;
    const { status, userName, order, pagination } = state;
    const params = {
        status,
        userName,
        siteBookingId: stateShowNewPage.siteBookingId,
        orderSort: order,
        orderField: 'sign_in_time',
        ...pagination,
    };

    bookingSignPage(params)
        .then((res) => {
            const { list, total, pageNo, pageSize } = res.data;
            state.tables = list;
            state.pagination.pageNo = pageNo;
            state.pagination.pageSize = pageSize;
            state.pagination.total = total;
            state.spinLoad = false;
        })
        .finally(() => {
            state.spinLoad = false;
        });
};
const chooseStatus = (item, index) => {
    state.statusIndex = index;
    state.status = item.status;
    getSheetList();
};
const search = () => {
    state.pagination.pageNo = 1;
    state.order = "";
    getSheetList();
};
// 重置
const resetForm = () => {
    state.userName = "";
    state.status = null;
    search();
};
/* 分页事件 */
const onSelectedRowKeys = (data, filters) => {
    const { current, pageSize } = data;
    state.pagination.pageNo = current;
    state.pagination.pageSize = pageSize;
    if (filters.order) {
        state.order = filters.order == "ascend" ? "asc" : "desc";
    } else {
        state.order = "";
    }
    getSheetList();
};

onMounted(() => {
    state.tables = [];
    getSheetList();
});
</script>

<style lang="less" scoped>
.act {
    color: var(--primary-color);
}

.dao {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;

    .reset-layout-header {
        height: 57px;
        line-height: 57px;
        border-bottom: 1px solid @border-color-base;
        background: transparent;
    }

    .warps {
        padding: 20px 16px;

        .sear {
            margin-bottom: 20px;
        }
    }
}
</style>
