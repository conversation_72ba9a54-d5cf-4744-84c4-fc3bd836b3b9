<template>
    <div>
        <div class="records">
            <a-layout-header
                class="reset-layout-header layout-header-radius"
                style="position: relative"
            >
                <i class="iconfont icon-xingzhuangjiehe19" @click="goBack" />
                <span style="margin-left: 10px">新增预约</span>
            </a-layout-header>
            <div class="content">
                <div class="top">
                    <div class="left">
                        <div class="titl">预约内容</div>
                        <a-form
                            ref="formRef"
                            layout="vertical"
                            :model="state.bookForm"
                            name="base"
                            :rules="state.formRules"
                        >
                            <a-form-item name="name" label="预约主题 :">
                                <a-input
                                    :maxlength="30"
                                    v-model:value.trim="state.bookForm.name"
                                    @input="GetInpNumber"
                                    placeholder="请输入"
                                    showCount
                                />
                            </a-form-item>
                            <a-form-item name="site" label="场地 :">
                                <a-input
                                    v-model:value="state.bookForm.site"
                                    :readonly="true"
                                    @click="chooseOneSite"
                                    placeholder="请选择(单选)"
                                />
                            </a-form-item>
                            <div
                                style="
                                    margin-bottom: 6px;
                                    display: flex;
                                    align-items: center;
                                "
                            >
                                <span
                                    style="color: red; padding: 4px 4px 0px 0px"
                                    >*
                                </span>
                                设置开始时间 :
                            </div>
                            <div
                                style="
                                    display: flex;
                                    align-items: center;
                                    width: 100%;
                                "
                            >
                                <a-form-item name="dateTime" style="width: 70%">
                                    <a-date-picker
                                        :format="`${
                                            'YYYY-MM-DD  ' + state.changeWeek
                                        }`"
                                        valueFormat="YYYY-MM-DD"
                                        style="width: 95%"
                                        v-model:value="state.bookForm.dateTime"
                                        @change="startDateTimeFn"
                                        placeholder="请选择"
                                        :disabled-date="disabledDate"
                                    />
                                </a-form-item>
                                <a-form-item name="time" style="width: 30%">
                                    <a-time-picker
                                        style="width: 101%"
                                        hideDisabledOptions
                                        format="HH:mm"
                                        valueFormat="HH:mm"
                                        :showNow="false"
                                        v-model:value="state.bookForm.time"
                                        placeholder="请选择"
                                        :disabledHours="getDisabledHours"
                                        :disabledMinutes="getDisabledMinutes"
                                        :minuteStep="15"
                                    />
                                </a-form-item>
                            </div>
                            <a-form-item name="duration" label="预约时长：">
                                <a-select
                                    ref="select"
                                    v-model:value="state.bookForm.duration"
                                    placeholder="请选择预约时长"
                                >
                                    <a-select-option
                                        :value="item"
                                        v-for="item in durationList"
                                        :key="item"
                                    >
                                        <span v-if="item.slice(0, 2) !== '00'">
                                            {{ item.slice(0, 2) + "小时" }}
                                        </span>
                                        <span
                                            v-if="
                                                item.slice(3, 5) !== '00' &&
                                                item.slice(3, 5) !== '0'
                                            "
                                        >
                                            {{ item.slice(3, 5) + "分钟" }}
                                        </span>
                                    </a-select-option>
                                </a-select>
                            </a-form-item>
                            <a-form-item name="participants" label="参会人 :">
                                <a-input
                                    @click="choosePeople()"
                                    allow-clear
                                    v-model:value="state.bookForm.participants"
                                    placeholder="请选择"
                                />
                            </a-form-item>
                            <a-form-item name="shuom" label="预约说明 :">
                                <a-textarea
                                    :maxlength="300"
                                    v-model:value="state.bookForm.describe"
                                    @input="GetTextNumber()"
                                    placeholder="请输入"
                                    showCount
                                    :auto-size="{ minRows: 3, maxRows: 5 }"
                                />
                            </a-form-item>
                            <div style="display: flex">
                                <a-form-item>
                                    <a-checkbox
                                        v-model:checked="
                                            state.bookForm.schedule
                                        "
                                        >是否加入日程安排</a-checkbox
                                    >
                                </a-form-item>
                                <a-form-item>
                                    <a-checkbox
                                        v-model:checked="state.bookForm.signIn"
                                        >是否需要签到</a-checkbox
                                    >
                                </a-form-item>
                            </div>
                        </a-form>
                    </div>
                    <div class="right">
                        <div class="titl">场地可选预约时间</div>
                        <div v-if="state.chooseSiteId">
                            <div class="table-header">
                                <div><span class="box1 span"></span>课程</div>
                                <div><span class="box2 span"></span>已预约</div>
                                <div><span class="box3 span"></span>审核中</div>
                                <div><span class="box4 span"></span>可预约</div>
                                <div>
                                    <span class="box5 span"></span>不可预约
                                </div>
                            </div>
                            <div class="tubiao">
                                <ScheduleTable
                                    @nextWeeks="nextWeeks"
                                    @prevWeeks="prevWeeks"
                                    ref="schedRef"
                                    :initialEvents="state.occupy"
                                />
                            </div>
                        </div>
                        <div class="empty" v-else>
                            <img src="/image/siteImg.png" alt />
                            <p>请先在左侧选择场地</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer">
                <a-button
                    type="primary"
                    class="footer_btn"
                    :loading="state.btnLoading"
                    @click="submitApply"
                >
                    提交
                </a-button>
            </div>
        </div>
        <!-- 选择场地 -->
        <chooseSite
            title="选择场地"
            ref="statusRef"
            :isRadio="true"
            :types="state.typeOptions"
            :typeId="props.typeId"
            :isVisible="state.chooseSiteVisible"
            :siteBookingTypeId="props.siteBookingTypeId"
            :chooseSiteId="state.chooseSiteId"
            @selectChangeArr="selectChangeArr"
            @emitHandleOk="emitHandleOk"
            @emithandleCancel="emithandleCancel"
        />
        <!-- 选人组件 -->
        <y-select
            mode="personnel"
            :tabs="[
                { tab: '教职工', key: 1, checked: true },
                { tab: '学生组', key: 2, checked: false },
            ]"
            :treeData="state.selectData"
            v-model:visible="state.visible"
            v-model:checked="state.checkedList"
            codes="siteBooking"
            @handleOk="handerCallbackParameter"
            @emitChangeTabs="handleEmitChangeTabs"
        />
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, watch } from "vue";
import chooseSite from "../components/chooseSite.vue";
import ScheduleTable from "@/components/ScheduleTable/index.vue";
import dayjs from "dayjs";
import { createApply, siteTypeList, dateOccupyList } from "@/api/siteBooking";
import { message } from "ant-design-vue";
import { v4 as uuidv4 } from "uuid";
import { shiftNumber } from "./tableData";
import { useStore } from "@/store";
const store = useStore();
const schedRef = ref();
const statusRef = ref();
const formRef = ref();
const state = reactive({
    btnLoading: false,
    organizatioData: [],
    studentstatusData: [],
    selectData: [],
    weekNum: 0,
    initProps: false,
    houred: [],
    mined: [],
    houreds: [],
    mineds: [],
    codes: "",
    visible: false,
    statusNum: null,
    checkedList: [],
    chooseSiteVisible: false,
    bookForm: {
        schedule: true,
        signIn: false,
        dateTime: "",
        time: "",
        duration: null,
    },
    formRules: {
        name: [
            { trigger: "change", required: true, message: "请输入预约主题" },
        ],
        dateTime: [
            { trigger: "change", required: true, message: "请选择日期" },
        ],
        time: [{ trigger: "blur", required: true, message: "请选择时间" }],
        duration: [
            { trigger: "blur", required: true, message: "请选择预约时长" },
        ],
        site: [{ trigger: "change", required: true, message: "请选择场地" }],
    },
    textNum: 0,
    inputNumber: 0,
    maxInput: 30,
    typeId: "",
    typeOptions: [], // 场地类型
    siteId: [], // 场地id
    // begainDay: '', // 设置的开始日期
    begainMins: "", // 设置的开始的分钟
    siteBookingSignList: [], // 参与人集合
    chooseSiteId: "", // 选中的场地id
    addOneWeek: 0, // 获取下一周数据
    deviceType: 9,
    duration: {
        hour: 0,
        min: 0,
    },
    nowHourMinute: `${dayjs().hour() + ":" + dayjs().minute()}`,
    changeWeek: "", // 选择的周
    occupy: [], // 场地占用的数据
    actnum: 1,
});
const durationList = testme();
function testme() {
    const tmp = [];
    for (let i = 0; i < 1440; i += 15) {
        const m = i % 60;
        let h = parseInt(i / 60);
        h = formatZero(h, 2);
        tmp.push(h + ":" + m);
    }
    tmp.shift();
    return tmp;
}
function formatZero(num, len) {
    if (String(num).length > len) return num;
    return (Array(len).join(0) + num).slice(-len);
}
const props = defineProps({
    typeId: {
        type: String,
        default: "",
    },
    bookDetail: {
        type: Object,
        default: () => {},
    },
    siteBookingTypeId: {
        type: String,
        default: "",
    },
});
watch(
    () => props.bookDetail,
    (news) => {
        if (Object.keys(news).length) {
            state.siteId[0] = news.siteId;
            state.siteBookingSignList = news.signList.map((item) => {
                return {
                    userId: item.userId,
                    // _source: 1为教师 0为学生
                    userType: item.userType,
                    dept: item.dept,
                };
            });
            // 回显预约表单数据
            state.bookForm = news;
            state.bookForm.site = news.siteInfo.name;
            state.chooseSiteId = news.siteInfo.id; // 场地id
            state.bookForm.participants = news.signList.map(
                (item) => item.userName
            );
            state.bookForm.dateTime = news.startTimeStr.substring(0, 10);
            state.bookForm.time = news.startTimeStr.substring(16, 11);
            if (news.duration) {
                state.duration = news.duration;
                state.bookForm.duration =
                    news.duration && news.duration?.hour < 10
                        ? "0" + news.duration?.hour + ":" + news.duration?.min
                        : news.duration?.hour + ":" + news.duration?.min;
            } else {
                state.duration = {
                    hour: 0,
                    min: 0,
                };
            }
        }
    }
);

watch(
    () => state.bookForm.begainHour,
    (n, o) => {},
    { immediate: true, deep: true }
);

const emit = defineEmits(["showView"]);
const goBack = () => {
    emit("showView");
};

// 获取禁用当前之前的‘时’
function getDisabledHours() {
    const hours = [];
    const timeArr = state.nowHourMinute?.split(":");
    if (state.bookForm.dateTime === dayjs().format("YYYY-MM-DD")) {
        for (let i = 0; i < parseInt(timeArr[0]); i++) {
            hours.push(i);
        }
    }
    const displayArr = [];
    for (let i = 0; i < 8; i++) {
        displayArr.push(i);
    }
    const hourArr = displayArr.concat(hours).filter((item, index, array) => {
        return array.indexOf(item) === index;
    });
    return hourArr;
}
// 获取禁用当前之前的‘分’
function getDisabledMinutes(selectedHour) {
    const timeArr = state.nowHourMinute?.split(":");
    const minutes = [];
    if (
        selectedHour === parseInt(timeArr[0]) &&
        state.bookForm.dateTime === dayjs().format("YYYY-MM-DD")
    ) {
        for (let i = 0; i < parseInt(timeArr[1]); i++) {
            minutes.push(i);
        }
    }
    return minutes;
}
const GetInpNumber = () => {
    state.inputNumber = state.bookForm.name.length;
};
const GetTextNumber = () => {
    state.textNum = state.bookForm.describe.length;
};
// 禁用开始的日期
const disabledDate = (current) => {
    return current && current < dayjs().endOf("day").add(-1, "day");
};
const chooseOneSite = () => {
    state.chooseSiteVisible = true;
};
const choosePeople = () => {
    state.visible = true;
};
const emithandleCancel = () => {
    state.chooseSiteVisible = false;
    // state.bookForm.site = ''
    // props.siteBookingTypeId = ''
};
const emitHandleOk = (id, ids) => {
    state.siteId = id;
    ids.forEach((i) => {
        if (i.id === id[0]) {
            state.chooseSiteId = i.id;
            state.bookForm.site = i.siteName;
            state.statusNum = i.status;
        }
    });
    if (!state.chooseSiteId) {
        message.warning("请先选择场地");
    } else if (state.statusNum === 1) {
        message.error("该场地已约满");
        state.chooseSiteId = null;
        state.bookForm.site = "";
    } else {
        state.chooseSiteVisible = false;
        const start = dayjs().startOf("week").format("YYYY-MM-DD");
        const end = dayjs().endOf("week").format("YYYY-MM-DD");
        const id = state.chooseSiteId;
        state.actnum = 1;
        state.occupy = [];
        getdateOccupyList(start, end, id);

        state.actnum = 1;
        if (schedRef.value) {
            schedRef.value.calendarOptions.events = [];
        }

        state.occupy = [];
        const start_ = dayjs().startOf("week").format("YYYY-MM-DD");
        const end_ = dayjs().endOf("week").format("YYYY-MM-DD");
        const id_ = state.chooseSiteId;
        getdateOccupyList(start_, end_, id_);
    }
};
const handleEmitChangeTabs = (item) => {
    state.selectData =
        item > 1 ? state.studentstatusData : state.organizatioData;
};
const handerCallbackParameter = (data) => {
    state.siteBookingSignList = data.map((item) => {
        let dept = "";
        if (item._source === 1) {
            dept = item.deptString;
            // state.codes = 'siteBooking'
        } else {
            dept = item.className;
        }
        return {
            userId: item.id,
            // _source: 1为教师 0为学生
            userType: item._source === 1 ? 1 : 0,
            // dept: item.deptString || item.className
            dept,
        };
    });
    // 回显参会人
    state.bookForm.participants = data.map((item) => item.name);

    state.visible = false;
};
const selectChangeArr = (e) => {
    state.siteId = e;
};
const submitApply = () => {
    formRef.value.validate().then(() => {
        const { chooseSiteId } = state;
        const { name, dateTime, time, describe, duration, schedule, signIn } =
            state.bookForm;
        const params = {
            name,
            siteId: chooseSiteId,
            siteBookingTypeId: props.siteBookingTypeId,
            startTime: dateTime + " " + time,
            describe,
        };
        const hour = duration.substring(0, 2);
        const min = duration.substring(3, 5);
        state.duration.hour =
            hour === "00" ? "0" : hour.replace(/\b(0+)/gi, "");
        state.duration.min = min;
        params.duration = state.duration; // 预约时长
        params.schedule = schedule ? 1 : 0;
        params.signIn = signIn ? 1 : 0;
        params.siteBookingSignList = state.siteBookingSignList;
        state.btnLoading = true;
        createApply(params)
            .then((res) => {
                message.success(res.message);
                goBack();
            })
            .finally(() => (state.btnLoading = false));
    });
};
// 关联场地类型
const getTypeList = () => {
    siteTypeList({ name: "会议室" }).then((res) => {
        state.typeOptions = res.data;
    });
};
const startDateTimeFn = () => {
    const dateTime = dayjs(state.bookForm.dateTime).format();
    const weekNub = shiftNumber(dayjs(dateTime).day());
    const week = dayjs(dateTime).day() ? weekNub : "日";
    state.changeWeek = `${"周" + week}`;
};
// 下一周
const nextWeeks = (e) => {
    state.weekNum++;
    const weekStart = dayjs()
        .startOf("week")
        .add(state.weekNum, "week")
        .format("YYYY-MM-DD");
    const weekEnd = dayjs()
        .endOf("week")
        .add(state.weekNum, "week")
        .format("YYYY-MM-DD");
    getdateOccupyList(weekStart, weekEnd, state.chooseSiteId);
};
// 上一周
const prevWeeks = (e) => {
    state.weekNum--;
    const weekStart = dayjs()
        .startOf("week")
        .add(state.weekNum, "week")
        .format("YYYY-MM-DD");
    const weekEnd = dayjs()
        .endOf("week")
        .add(state.weekNum, "week")
        .format("YYYY-MM-DD");
    getdateOccupyList(weekStart, weekEnd, state.chooseSiteId);
};
const getTableTime = (arr, color, title = null) => {
    const newArr = arr.map((item, index) => {
        return {
            id: uuidv4(),
            // title,
            color,
            start: `${item.startTime.replace(/\s+/g, "T")}:00`,
            end: `${item.endTime.replace(/\s+/g, "T")}:00`,
        };
    });
    return newArr;
};

// 获取场地占用时间
const getdateOccupyList = (
    start,
    end,
    siteId,
    siteBookingTypeId = props.siteBookingTypeId
) => {
    dateOccupyList({
        startTime: start,
        endTime: end,
        siteId,
        siteBookingTypeId,
    }).then((res) => {
        const {
            inApprovalDateList,
            noBookingDateList,
            timetableDateList,
            yesBookingDateList,
        } = res.data;
        const t1 = getTableTime(timetableDateList, "#00B781"); // 课程
        const t2 = getTableTime(yesBookingDateList, "#F5222D"); // 已预约
        const t3 = getTableTime(inApprovalDateList, "#FAAD14"); // 审核中
        const t4 = getTableTime(noBookingDateList, "#cccccc"); // 不可预约
        if (state.actnum === 1 && state.occupy.length === 0) {
            schedRef.value.calendarOptions.events = t1.concat(t2, t3, t4);
        } else {
            state.occupy = t1.concat(t2, t3, t4);
        }
    });
};
const getOrganizationTree = () => {
    // 权限成员   获取组织架构
    store
        .dispatch("selectSource/setOrganizationTree", { code: "siteBooking" })
        .then((res) => {
            state.selectData = res;
            state.organizatioData = res;
        });
};

const getStudentStatusTree = () => {
    // 权限成员   获取组织架构
    store
        .dispatch("selectSource/setStudentStatusTree", { code: "siteBooking" })
        .then((res) => {
            state.studentstatusData = res;
        });
};
onMounted(() => {
    getOrganizationTree();
    getStudentStatusTree();
    getTypeList();
    testme();
});
</script>

<style lang="less" scoped>
.records {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    height: calc(100vh - 111px);

    .reset-layout-header {
        height: 57px;
        line-height: 57px;
        border-bottom: 1px solid @border-color-base;
    }

    .content {
        width: 100%;
        position: relative;
        // height: calc(100% - 63px);
        // overflow: hidden auto;

        .top {
            display: flex;

            .left {
                width: 30%;
                // min-height: 800px;
                max-height: 736px;
                border-right: 1px solid #d9d9d9;
                padding: 20px 16px 0 16px;
                height: calc(100vh - 200px);
                overflow: hidden auto;

                .titl {
                    font-size: 18px;
                    font-weight: 600;
                    color: rgba(0, 0, 0, 0.85);
                    margin-bottom: 24px;
                }
            }

            .right {
                width: 70%;
                // min-height: 800px;
                max-height: 712px;
                padding: 20px 16px 0 16px;
                height: calc(100vh - 200px);
                overflow: hidden auto;

                .titl {
                    font-size: 18px;
                    font-weight: 600;
                    color: rgba(0, 0, 0, 0.85);
                    margin-bottom: 24px;
                }

                .table-header {
                    display: flex;

                    div {
                        margin-right: 20px;

                        .span {
                            margin-right: 10px;
                            display: inline-block;
                            width: 16px;
                            height: 16px;
                        }

                        .box1 {
                            background-color: #00b781;
                        }

                        .box2 {
                            background-color: #f5222d;
                        }

                        .box3 {
                            background-color: #faad14;
                        }

                        .box5 {
                            background-color: #d9d9d9;
                        }

                        .box4 {
                            background-color: #fff;
                            border: 1px solid #d9d9d9;
                        }
                    }
                }

                .per_next {
                    display: flex;
                    justify-content: flex-end;
                    margin-top: 10px;
                    text-align: right;

                    .per {
                        text-align: center;
                        height: 30px;
                        width: 80px;
                        line-height: 30px;
                        border: 1px solid #ccc;
                        border-radius: 3px;
                        cursor: pointer;
                    }

                    .next {
                        text-align: center;
                        height: 30px;
                        width: 80px;
                        line-height: 30px;
                        background-color: #00b781;
                        border: 1px solid #00b781;
                        color: #fff;
                        border-radius: 3px;
                        cursor: pointer;
                    }
                }

                .tubiao {
                    overflow-y: scroll !important;
                }

                .empty {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    flex-direction: column;
                    height: 100%;

                    p {
                        font-size: 14px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: rgba(0, 0, 0, 0.65);
                        margin-top: 12px;
                        margin-bottom: 40px;
                    }
                }
            }
        }
    }

    .footer {
        border-top: 1px solid #d9d9d9;
        position: absolute;
        bottom: -31px;
        left: 0;
        width: 100%;
        height: 63px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        background: #fff;

        .footer_btn {
            font-size: 14px;
            border-radius: 3px;
            text-align: center;
            margin-top: 4px;
            margin-left: 20px;
            cursor: pointer;
        }
    }
}

.input_max {
    position: absolute;
    top: 6px;
    right: 10px;
}

.max {
    position: absolute;
    right: 6px;
    bottom: 8px;
}

:deep(.fc-col-header-cell-cushion) {
    color: #000;
}

:deep(.fc-col-header) {
    background-color: #ebfaf5;
    height: 40px;
    line-height: 40px;
}

::-webkit-scrollbar {
    width: 0 !important;
    height: 0;
}

:deep(.fc-event-main) {
    opacity: 0;
}
</style>
