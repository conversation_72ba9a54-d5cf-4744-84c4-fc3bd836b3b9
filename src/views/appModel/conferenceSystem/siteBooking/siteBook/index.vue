<!-- 会议场地预约 -->
<template>
    <div class="warps">
        <a-button
            class="add_appointment"
            type="primary"
            @click="addAppointment"
            v-if="state.isAddAppointment"
        >
            <template #icon>
                <plus-outlined />
            </template>
            新增预约
        </a-button>
        <div class="site_booking" v-if="!state.showFlag">
            <a-tabs
                v-model:activeKey="state.type"
                type="card"
                @change="changeType"
            >
                <a-tab-pane :key="0" tab="我参与的预约"></a-tab-pane>
                <a-tab-pane :key="1" tab="我的预约"></a-tab-pane>
            </a-tabs>

            <div class="search_form">
                <SearchForm
                    ref="searRef"
                    @queryList="queryList"
                    @resetList="resetList"
                    :showAudit="state.type == 1"
                />
            </div>
            <div class="table">
                <YTable
                    :columns="!state.type ? columnsOne : columnsTwo"
                    :dataSource="
                        !state.type ? state.bookingTable : state.bookingTableMy
                    "
                    :slots="['operation']"
                    :rowSelection="false"
                    :totals="state.pagination"
                    :spinLoad="state.spinLoad"
                    @onSelectedRowKeys="onSelectedRowKeys"
                >
                    <template #headerCell="{ column }">
                        <template
                            v-if="
                                column.dataIndex === 'status' && state.type == 1
                            "
                        >
                            <a-dropdown>
                                <a
                                    style="color: #000"
                                    @click.prevent
                                    v-show="state.type == 1"
                                >
                                    审核状态
                                    <caret-down-outlined
                                        style="color: var(--primary-color)"
                                    />
                                </a>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item
                                            v-for="(
                                                item, index
                                            ) in state.statusList"
                                            :key="index"
                                            @click="chooseStatus(item, index)"
                                        >
                                            <span
                                                :class="
                                                    state.statusx === index
                                                        ? 'acti'
                                                        : ''
                                                "
                                                >{{ item.statusName }}</span
                                            >
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </template>
                    </template>
                    <template #bodyCell="{ column, record }">
                        <template
                            v-if="
                                column.dataIndex === 'operation' && !state.type
                            "
                        >
                            <a
                                @click="toDetail(record)"
                                style="
                                    color: var(--primary-color);
                                    margin-right: 10px;
                                "
                                >查看</a
                            >
                        </template>
                        <template
                            v-if="
                                column.dataIndex === 'operation' &&
                                state.type === 1
                            "
                        >
                            <a
                                @click="toDetail(record)"
                                style="
                                    color: var(--primary-color);
                                    margin-right: 10px;
                                "
                                >详情</a
                            >
                            <a
                                @click="toSheet(record)"
                                v-if="record.status === 2 && record.signIn"
                                style="color: var(--primary-color)"
                                >签到表</a
                            >
                            <a style="color: #ccc" :disabled="true" v-else
                                >签到表</a
                            >
                        </template>
                        <template
                            v-if="column.dataIndex === 'siteBookingTypeName'"
                        >
                            <span>{{
                                record.siteBookingTypeName
                                    ? record.siteBookingTypeName
                                    : "-"
                            }}</span>
                        </template>
                        <template v-if="column.dataIndex === 'signIn'">
                            <span v-if="record.signIn">需签到</span>
                            <span v-else>无需签到</span>
                        </template>
                        <template v-if="column.dataIndex === 'status'">
                            <!-- // 0.拒绝、1.审批中、2.已通过、3.已取消、4.已过期 -->
                            <a-badge
                                v-if="record.status === 0"
                                status="error"
                                text="已拒绝"
                            />
                            <a-badge
                                v-if="record.status === 1"
                                status="warning"
                                text="审批中"
                            />
                            <a-badge
                                v-if="record.status === 2"
                                status="success"
                                text="已通过"
                            />
                            <a-badge
                                v-if="record.status === 3"
                                status="default"
                                text="已取消"
                            />
                            <a-badge
                                v-if="record.status === 4"
                                status="default"
                                style="color: #ccc"
                                text="已过期"
                            />
                        </template>
                    </template>
                </YTable>
            </div>
        </div>
        <!-- 签到表 -->
        <signInSheet
            @showViews="showViews"
            v-if="state.showFlag1"
            :class="state.showFlag1 ? 'top01' : ''"
        />
        <!-- 预约申请 -->
        <toBooking
            @showView="showView"
            :siteBookingTypeId="state.siteBookingTypeId"
            :bookDetail="state.bookDetail"
            :typeId="state.typeId"
            v-if="state.showFlag2"
            :class="state.showFlag2 ? 'top02' : ''"
        />
        <!-- 预约详情 -->
        <siteDetail
            @emithandleCancel="emithandleCancel"
            @opened="opened"
            @emitCancelBook="emitCancelBook"
            @emitReEdit="emitReEdit"
            @emitRefuse="emitRefuse"
            @emitPass="emitPass"
            title="查看详情"
            :onStatus="state.onStatus"
            :nobtn="false"
            :open="state.open"
            :close="state.close"
            :isOpen="state.isOpen"
            :audits="state.audits"
            :isVisible="state.isVisible"
            :themeitem="state.themeitem"
            :status="Number(state.statuss)"
            v-model:name="state.name"
        />
        <!-- 拒绝原因弹框 -->
        <a-modal
            v-model:visible="state.visibleJ"
            title="拒绝原因"
            @ok="handleOk"
            @cancel="handleCancelJu"
        >
            <a-textarea
                :maxlength="state.maxNum"
                v-model:value="state.ju"
                placeholder="请输入拒绝原因"
                style="height: 120px; overflow-y: scroll"
                showCount
            />
        </a-modal>
    </div>
</template>

<script setup>
import { reactive, onMounted, ref, shallowRef, inject } from "vue";
import { useRouter, useRoute } from "vue-router";
import YTable from "comps/YTable/index.vue";
import { columnsOne, columnsTwo } from "./tableData";
import {
    getBookingSiteNums,
    myParticipatePage,
    myBookingPage,
    bookingUpdate,
    bookingDetail,
} from "@/api/siteBooking";
import SearchForm from "../components/SearchForm.vue";
import siteDetail from "../components/siteDetail.vue";
import toBooking from "./toBooking.vue";
import signInSheet from "../appointmentRecord/signInSheet.vue";
import { message } from "ant-design-vue";
let stateShowNewPage = inject("showNewPage")();
const searRef = ref();
const router = useRouter();
const route = useRoute();
const state = reactive({
    bookDetail: {},
    isAddAppointment: true,
    showFlag: false,
    showFlag1: false, // 签到表
    showFlag2: false, // 预约申请
    onStatus: false,
    isVisible: false,
    visibleJ: false,
    isOpen: false,
    open: true,
    close: false,
    name: "",
    status: "",
    statuss: "",
    themeitem: {
        signIn: false,
        bookingTime: "",
        sponsorName: "",
        signList: [],
        describe: "",
        siteInfo: {},
        she: "会议机，投影仪",
    },
    audits: {
        sponsorName: "",
        sponsorTime: "",
        approvalTimeStr: "",
        shenpis: [],
        refuseReason: "",
    },
    siteBookingTypeId: "",
    type: 0,
    bookingTable: [],
    bookingTableMy: [],
    statusList: [
        { statusName: "全部", status: null },
        { statusName: "已拒绝", status: 0 },
        { statusName: "审批中", status: 1 },
        { statusName: "已通过", status: 2 },
        { statusName: "已取消", status: 3 },
        { statusName: "已过期", status: 4 },
    ],
    statusx: 0,
    typeIndex: 0,
    typeId: "",
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    spinLoad: false,
    myBookDetailId: "",
    ju: "",
    maxNum: 300,
    textNum: 0,
});
// 0.拒绝、1.审批中、2.已通过、3.已取消、4.已过期

const addAppointment = (item) => {
    state.typeId = item.id;
    // bookDetail.value = {}
    router.replace({
        query: {
            ...route.query,
            type: "true",
        },
    });
    state.showFlag = true;
    state.showFlag2 = true;
    state.isAddAppointment = false;
    stateShowNewPage.showNewPage = false;
};
// 签到表
const toSheet = (e) => {
    router.replace({
        query: {
            ...route.query,
            type: "true",
        },
    });
    state.showFlag = true;
    state.showFlag1 = true;
    state.isAddAppointment = false;
    stateShowNewPage.siteBookingId = e.id;
    stateShowNewPage.showNewPage = false;
};
// 签到表的返回
const showViews = () => {
    state.showFlag = false;
    state.showFlag1 = false;
    state.isAddAppointment = true;
};
const toDetail = (record) => {
    state.myBookDetailId = record.id;
    if (state.type === 0) {
        state.onStatus = true;
        state.status = "";
    } else {
        state.onStatus = false;
    }
    bookingDetail({ id: record.id }).then((res) => {
        const { data } = res;
        state.name = data.name;
        state.themeitem.signIn = data.signIn;
        state.themeitem.bookingTime = data.bookingTime;
        state.themeitem.sponsorName = data.sponsorName;
        state.themeitem.signList = data.signList;
        state.themeitem.describe = data.describe;
        state.themeitem.siteInfo = data.siteInfo;
        state.themeitem.sponsorOrgName = data.sponsorOrgName;
        state.audits.shenpis = data.siteInfo.list;
        state.audits.sponsorName = data.sponsorName;
        state.audits.refuseReason = data.refuseReason;
        state.audits.sponsorTime = data.sponsorTime;
        state.audits.approvalTimeStr = data.approvalTimeStr;
        state.statuss = JSON.parse(JSON.stringify(data.status));
    });
    state.isVisible = true;
};
const emithandleCancel = () => {
    state.isVisible = false;
    stateShowNewPage.showNewPage = true;
};
const opened = (e) => {
    if (e) {
        state.isOpen = true;
        state.open = false;
        state.close = true;
    } else {
        state.isOpen = false;
        state.open = true;
        state.close = false;
    }
};
// 取消预约
const emitCancelBook = () => {
    bookingUpdate({ status: 3, id: state.myBookDetailId }).then((res) => {
        if (res.code === 0) {
            getMyBooking();
            state.isVisible = false;
            stateShowNewPage.showNewPage = true;
            message.success(res.message);
        }
    });
};
// 重新编辑
const emitReEdit = () => {
    state.isVisible = false;
    state.showFlag = true;
    state.showFlag2 = true;
    state.isAddAppointment = false;
    bookingDetail({ id: state.myBookDetailId })
        .then(({ data }) => {
            state.bookDetail = data;
        })
        .finally(() => (stateShowNewPage.showNewPage = false));
};
// 拒绝
const emitRefuse = () => {
    state.visibleJ = true;
    stateShowNewPage.showNewPage = true;
};
// 取消拒绝
const handleCancelJu = () => {
    state.ju = "";
    state.textNum = 0;
};
// 确定拒绝
const handleOk = () => {
    if (!state.ju.trim()) {
        message.warning("请输入拒绝原因");
    } else {
        bookingUpdate({
            status: 0,
            refuseReason: state.ju,
            id: state.myBookDetailId,
        }).then((res) => {
            if (res.code === 0) {
                getMyBooking();
                state.visibleJ = false;
                state.isVisible = false;
                stateShowNewPage.showNewPage = true;
                message.success(res.message);
            }
        });
    }
};
// 通过
const emitPass = () => {
    bookingUpdate({ status: 2, id: state.myBookDetailId }).then((res) => {
        if (res.code === 0) {
            getMyBooking();
            state.isVisible = false;
            stateShowNewPage.showNewPage = true;
            message.success(res.message);
        }
    });
};
// 获取地预约类型列表及预约数量
const getTypeNum = async () => {
    state.siteBookingTypeId = "";
    await getBookingSiteNums().then(({ data }) => {
        data.forEach((v) => {
            if (v.type == 1) {
                state.siteBookingTypeId = v.id;
            }
        });
    });
};

// 我参与的预约
const getMyJoinBooking = () => {
    const { siteBookingTypeId, pagination } = state;
    const params = {
        ...pagination,
        siteBookingTypeId,
    };
    if (searRef.value) {
        const {
            time = [],
            name,
            siteName,
            siteTypeId,
            buildingId,
        } = searRef.value.sear;
        params.name = name;
        params.buildingId = buildingId;
        params.siteName = siteName;
        params.siteTypeId = siteTypeId;
        params.startTime = time[0];
        params.endTime = time[1];
    }
    state.spinLoad = true;
    myParticipatePage(params)
        .then((res) => {
            const { list, total, pageNo, pageSize } = res.data;
            state.bookingTable = list;
            state.pagination = { total, pageNo, pageSize };
        })
        .finally(() => {
            state.spinLoad = false;
        });
};
// 我的预约
const getMyBooking = () => {
    const { siteBookingTypeId, pagination, status } = state;
    const params = {
        ...pagination,
        status,
        siteBookingTypeId,
    };
    if (searRef.value) {
        const {
            time = [],
            name,
            siteName,
            siteTypeId,
            buildingId,
        } = searRef.value.sear;
        params.name = name;
        params.buildingId = buildingId;
        params.siteName = siteName;
        params.siteTypeId = siteTypeId;
        params.startTime = time[0];
        params.endTime = time[1];
    }
    state.spinLoad = true;
    myBookingPage(params)
        .then((res) => {
            const { list, total, pageNo, pageSize } = res.data;
            state.bookingTableMy = list;
            state.pagination = { total, pageNo, pageSize };
        })
        .finally(() => {
            state.spinLoad = false;
        });
};

// 预约申请的返回
const showView = () => {
    const params = {};
    params.pageNo = 1;
    params.pageSize = 10;
    state.spinLoad = true;
    if (state.type === 0) {
        // 我参与的预约
        getMyJoinBooking();
    } else {
        // 我的预约
        state.status = "";
        getMyBooking();
    }
    state.showFlag = false;
    state.showFlag2 = false;
    state.isAddAppointment = true;
    stateShowNewPage.showNewPage = true;
};

// 查询
const queryList = () => {
    state.pagination.pageNo = 1;
    state.pagination.pageSize = 10;
    if (state.type === 0) {
        getMyJoinBooking();
    } else {
        getMyBooking();
    }
};
// 重置
const resetList = () => {
    state.pagination.pageNo = 1;
    state.pagination.pageSize = 10;
    if (state.type === 0) {
        getMyJoinBooking();
    } else {
        getMyBooking();
    }
};

/* 分页事件 */
const onSelectedRowKeys = (data) => {
    const { current, pageSize } = data;
    state.pagination.pageNo = current;
    state.pagination.pageSize = pageSize;
    if (state.type === 0) {
        getMyJoinBooking();
    } else {
        getMyBooking();
    }
};
const chooseType = (item, index) => {
    state.typeIndex = index;
    state.typeId = item.id;
    state.pagination.pageNo = 1;
    if (state.type === 0) {
        getMyJoinBooking();
    } else {
        state.statusx = 0;
        getMyBooking();
    }
};
const chooseStatus = (item, index) => {
    state.status = item.status;
    state.statusx = index;
    state.typeIndex = 0;
    getMyBooking();
};
const changeType = (event) => {
    state.pagination.pageNo = 1;

    if (event === 0) {
        // 我参与的预约
        getMyJoinBooking();
    } else {
        // 我的预约
        state.status = "";
        getMyBooking();
    }
};
// 我的预约
onMounted(async () => {
    await getTypeNum();
    await getMyJoinBooking();
});
</script>

<style lang="less" scoped>
.warps {
    position: relative;
    z-index: 1;

    .add_appointment {
        position: absolute;
        top: -32px;
        right: 16px;
    }

    .top1 {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }

    .top2 {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }
}

.site_booking {
    padding: 23px 16px;

    .act {
        color: var(--primary-color);
    }

    :deep(.ant-tabs-nav) {
        margin: 0;

        .ant-tabs-tab {
            margin: 0 !important;
            border-color: transparent;
            border-bottom: 1px solid @border-color-base;
            background: @body-background;

            &.ant-tabs-tab-active {
                border: 1px solid @border-color-base;
                border-top-color: @primary-color;
                border-bottom-color: transparent;
            }
        }
    }
}

.acti {
    color: var(--primary-color);
}

.actt {
    color: var(--primary-color);
}

.max {
    position: absolute;
    right: 38px;
    bottom: 80px;
}
</style>
