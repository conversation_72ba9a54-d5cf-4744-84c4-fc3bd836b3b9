export const columnsOne = [
    {
        title: "预约主题",
        dataIndex: "name",
    },
    {
        title: "预约类型",
        dataIndex: "siteBookingTypeName",
    },
    {
        title: "场地名称",
        dataIndex: "siteName",
    },
    {
        title: "所在建筑",
        dataIndex: "buildingName",
    },
    {
        title: "场地类型",
        dataIndex: "siteTypeName",
    },
    {
        title: "预约时间",
        ellipsis: true,
        width: "250px",
        dataIndex: "bookingTime",
    },
    {
        title: "预约人",
        dataIndex: "sponsorName",
    },
    {
        title: "签到要求",
        dataIndex: "signIn",
    },
    {
        title: "操作",
        dataIndex: "operation",
        width: 100,
    },
];
export const columnsTwo = [
    {
        title: "预约主题",
        dataIndex: "name",
    },
    {
        title: "预约类型",
        dataIndex: "siteBookingTypeName",
    },
    {
        title: "场地名称",
        dataIndex: "siteName",
    },
    {
        title: "所在建筑",
        dataIndex: "buildingName",
    },
    {
        title: "场地类型",
        dataIndex: "siteTypeName",
    },
    {
        title: "预约时间",
        ellipsis: true,
        width: "250px",
        dataIndex: "bookingTime",
    },
    {
        title: "预约人",
        dataIndex: "sponsorName",
    },
    {
        title: "签到要求",
        dataIndex: "signIn",
    },
    {
        title: "审核状态",
        dataIndex: "status",
    },
    {
        title: "操作",
        dataIndex: "operation",
        width: 120,
    },
];
export const columnsThree = [
    {
        title: "预约主题",
        dataIndex: "name",
    },
    {
        title: "场地名称",
        dataIndex: "siteName",
    },
    {
        title: "所在建筑",
        dataIndex: "buildingName",
    },
    {
        title: "场地类型",
        dataIndex: "siteTypeName",
    },
    {
        title: "预约时间",
        ellipsis: true,
        width: "250px",
        dataIndex: "bookingTime",
    },
    {
        title: "预约人",
        dataIndex: "sponsorName",
    },
    {
        title: "签到要求",
        dataIndex: "signIn",
    },
    {
        title: "审核状态",
        dataIndex: "status",
    },
    {
        title: "操作",
        dataIndex: "operation",
        width: 100,
    },
];
export const time1 = [
    { clock: "08", dis: false },
    { clock: "09", dis: false },
    { clock: "10", dis: false },
    { clock: "11", dis: false },
    { clock: "12", dis: false },
    { clock: "13", dis: false },
    { clock: "14", dis: false },
    { clock: "15", dis: false },
    { clock: "16", dis: false },
    { clock: "17", dis: false },
    { clock: "18", dis: false },
    { clock: "19", dis: false },
    { clock: "20", dis: false },
    { clock: "21", dis: false },
    { clock: "22", dis: false },
    { clock: "23", dis: false },
    { clock: "24", dis: false },
];
export const over1 = [
    { clock: "08", dis: false },
    { clock: "09", dis: false },
    { clock: "10", dis: false },
    { clock: "11", dis: false },
    { clock: "12", dis: false },
    { clock: "13", dis: false },
    { clock: "14", dis: false },
    { clock: "15", dis: false },
    { clock: "16", dis: false },
    { clock: "17", dis: false },
    { clock: "18", dis: false },
    { clock: "19", dis: false },
    { clock: "20", dis: false },
    { clock: "21", dis: false },
    { clock: "22", dis: false },
    { clock: "23", dis: false },
    { clock: "24", dis: false },
];
export const time2 = [
    { mins: "00", dis: false },
    { mins: "15", dis: false },
    { mins: "30", dis: false },
    { mins: "45", dis: false },
];
export const over2 = [
    { mins: "00", dis: false },
    { mins: "15", dis: false },
    { mins: "30", dis: false },
    { mins: "45", dis: false },
];

// 将阿拉伯数字转换为大写数字
const cnum = ["日", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十"];
export function shiftNumber(n) {
    let s = "";
    n = "" + n; // 数字转为字符串
    for (let i = 0; i < n.length; i++) {
        s += cnum[parseInt(n.charAt(i))];
    }
    if (s.length == 2) {
        // 两位数的时候
        // 如果个位数是0的时候，0改成十
        if (s.charAt(1) == cnum[0]) {
            s = s.charAt(0) + cnum[10];
            // 如果是一十改成十
            if (s == cnum[1] + cnum[10]) {
                s = cnum[10];
            }
        } else if (s.charAt(0) == cnum[1]) {
            // 如果十位数是一的话改成十
            s = cnum[10] + s.charAt(1);
        } else if (s.charAt(0) == cnum[2]) {
            // 如果十位数是二的话改成十二
            s = cnum[2] + cnum[10] + s.charAt(1);
        } else if (s.charAt(0) == cnum[3]) {
            s = cnum[3] + cnum[10] + s.charAt(1);
        } else if (s.charAt(0) == cnum[4]) {
            s = cnum[4] + cnum[10] + s.charAt(1);
        } else if (s.charAt(0) == cnum[5]) {
            s = cnum[5] + cnum[10] + s.charAt(1);
        } else if (s.charAt(0) == cnum[6]) {
            s = cnum[6] + cnum[10] + s.charAt(1);
        } else if (s.charAt(0) == cnum[7]) {
            s = cnum[7] + cnum[10] + s.charAt(1);
        } else if (s.charAt(0) == cnum[8]) {
            s = cnum[8] + cnum[10] + s.charAt(1);
        } else if (s.charAt(0) == cnum[9]) {
            s = cnum[9] + cnum[10] + s.charAt(1);
        } else if (s.charAt(0) == cnum[10]) {
            s = cnum[10] + cnum[10] + s.charAt(1);
        }
    }
    return s;
}
