<!--eslint-disable-->
<template>
    <div class="homepage_container_main">
        <div class="container_main">
            <div class="header">
                <a-button type="text" class="tabs_back" @click="showList">
                    <!-- <arrow-left-outlined class="tabs_back__icon" /> -->
                    <i class="iconfont icon-xingzhuangjiehe19" />
                </a-button>
                <span>{{ props.templateID ? "编辑模板" : "新建模板" }}</span>
            </div>
            <div>
                <a-button @click="showList">返回</a-button>
                <a-button
                    type="primary"
                    style="margin: 0px 12px"
                    @click="saveSubmissionFn"
                    >保存</a-button
                >
            </div>
        </div>
        <div style="transform: translateY(-57px)">
            <div class="homepageLeft_chlidmain">
                <div class="moulde_index">选择功能尺寸</div>
                <!-- <div class="tab_style">
          <span class="tab_style_title">功能</span>
          <div class="tab_style_list">
            <span v-for="(items, index) in state.templateTabList" :key="index"
              @click="templateNameTabHandleClick(items, index)" :class="activeClass == index ? 'actived' : ''"
              class="tab_content">{{ items.name.length > 4 ? items.name.substring(0, 4) + '...' : items.name }}</span>
          </div>
        </div> -->

                <div class="tab_style">
                    <span class="tab_style_title">尺寸</span>
                    <div class="tab_style_list">
                        <span
                            v-for="(item, index) in state.activeList"
                            @click="tabHandleClick(item, index)"
                            class="tab_content"
                            :class="sizeActiveClass == index ? 'actived' : ''"
                            :key="index"
                            >{{
                                item.width == "" && item.high == ""
                                    ? "全部"
                                    : item.width + "*" + item.high
                            }}</span
                        >
                    </div>
                </div>

                <div class="manu_message">
                    <div v-if="state.selectModuleList.length > 0">
                        <el-checkbox-group v-model="state.selectModuleModel">
                            <el-checkbox
                                v-for="(
                                    itemss, index
                                ) in state.selectModuleList"
                                @change="
                                    handleCheckedselectModuleChange(
                                        index,
                                        $event
                                    )
                                "
                                :label="itemss.images"
                                :key="index"
                            >
                                <div style="margin-bottom: 20px">
                                    <div
                                        :class="
                                            itemss.width < itemss.high
                                                ? 'manu_img manu_v'
                                                : 'manu_img manu_h'
                                        "
                                    >
                                        <img
                                            :src="itemss.images"
                                            :class="
                                                itemss.width == itemss.high &&
                                                state.direction == '1'
                                                    ? 'v_img'
                                                    : itemss.width >
                                                          itemss.high &&
                                                      state.direction == '1'
                                                    ? 'h_img'
                                                    : itemss.width <
                                                          itemss.high &&
                                                      state.direction == '0'
                                                    ? 'v_img'
                                                    : itemss.width >=
                                                          itemss.high &&
                                                      state.direction == '0'
                                                    ? 'h_img'
                                                    : ''
                                            "
                                            alt
                                        />
                                    </div>
                                    <div class="manu_title">
                                        <span v-if="itemss.descriptive"
                                            >说明：{{
                                                itemss.descriptive.length > 15
                                                    ? itemss.descriptive.substring(
                                                          0,
                                                          15
                                                      ) + "..."
                                                    : itemss.descriptive
                                            }}</span
                                        >
                                        <span
                                            style="
                                                float: right;
                                                margin-right: 10px;
                                            "
                                            >{{ itemss.width }}*{{
                                                itemss.high
                                            }}</span
                                        >
                                    </div>
                                </div>
                            </el-checkbox>
                        </el-checkbox-group>
                    </div>
                    <div class="no_dataLayout" v-else>
                        <img src="/image/empty.png" alt="" />
                        <p>暂无数据</p>
                    </div>
                </div>
            </div>
            <div class="homepageRight_chlidmain">
                <div class="screen_main">
                    <span class="screen_title">
                        <span style="padding-right: 8px">
                            <span style="color: #ff0000">*</span>模板名称：
                        </span>
                        <a-input
                            v-model:value="state.establish.name"
                            style="width: 300px"
                            :maxlength="10"
                            placeholder="请输入模板名称"
                            show-count
                        ></a-input>
                    </span>
                    <!-- 走马灯区域 样式控制小组件可显示区域 区分横竖屏 -->
                    <div
                        class="screen_content"
                        :class="
                            state.direction == '0'
                                ? 'hengscreen_content'
                                : 'shuscreen_content'
                        "
                        :style="
                            state.direction == '0'
                                ? {
                                      backgroundImage:
                                          'url(' +
                                          state.establish.backgropPhoto +
                                          ')',
                                      backgroundSize: '100% 100%',
                                      backgroundRepeat: 'no-repeat',
                                      paddingTop: '40px',
                                  }
                                : {
                                      backgroundImage:
                                          'url(' +
                                          state.establish.backgropPhoto +
                                          ')',
                                      backgroundSize: '100% 100%',
                                      backgroundRepeat: 'no-repeat',
                                      paddingTop: '20px',
                                  }
                        "
                    >
                        <el-carousel
                            ref="slideshow"
                            @change="carouselChangeFn"
                            indicator-position="outside"
                            trigger="click"
                            :initial-index="state.initialIndex"
                            :autoplay="false"
                        >
                            <!-- 走马灯内容模板区域 遍历后生成多页 -->
                            <el-carousel-item
                                v-for="(item, a) in state.photoList"
                                :key="a"
                            >
                                <!-- 自定义模板拖拽布局 横竖屏动态调整 -->
                                <grid-layout
                                    :layout.sync="item.layoutclassmodellist"
                                    :col-num="state.direction == '0' ? 8 : 6"
                                    :row-height="
                                        state.direction == '0'
                                            ? state.hengscreenHeight
                                            : state.shuscreenHeight
                                    "
                                    :max-rows="state.direction == '0' ? 6 : 8"
                                    :is-draggable="true"
                                    :is-resizable="false"
                                    :vertical-compact="true"
                                    :margin="[10, 10]"
                                    :use-css-transforms="false"
                                    :responsive="false"
                                    :autoSize="true"
                                    @layout-updated="layoutUpdatedEvent"
                                >
                                    <!-- 模板拖拽item -->
                                    <grid-item
                                        v-for="(
                                            items, index
                                        ) in item.layoutclassmodellist"
                                        :key="items.i"
                                        :x="items.x"
                                        :y="items.y"
                                        :w="items.w"
                                        :h="items.h"
                                        :i="items.i"
                                    >
                                        <div class="show-img">
                                            <!-- 模块删除按钮 -->
                                            <div
                                                class="del-icon"
                                                @click.stop="
                                                    newDeleteFn(items.img, a)
                                                "
                                            >
                                                x
                                            </div>
                                            <img
                                                :src="items.img"
                                                style="
                                                    width: 100%;
                                                    height: 100%;
                                                "
                                                alt
                                            />
                                        </div>
                                    </grid-item>
                                </grid-layout>
                            </el-carousel-item>
                        </el-carousel>
                        <div class="add_btn">
                            <minus-outlined
                                @click.stop="reduceLayout"
                                v-if="state.photoList.length > 1"
                            />
                            <plus-outlined @click.stop="addLayout" />
                        </div>
                    </div>
                    <div class="application_main">
                        <span class="application_title">应用到：</span>
                        <a-radio-group
                            v-model:value="state.establish.isAllBrand"
                        >
                            <a-radio :value="true">全部会议机</a-radio>
                            <a-radio :value="false">指定会议机</a-radio>
                        </a-radio-group>
                        <a-tree-select
                            v-if="!state.establish.isAllBrand"
                            style="width: 300px"
                            tree-checkable
                            multiple
                            v-model:value="state.brandId"
                            placeholder="请选择会议机"
                            allow-clear
                            :tree-data="state.equipment"
                            :fieldNames="fieldNames"
                            @dropdownVisibleChange="dropdownVisibleChange"
                            :maxTagCount="2"
                        >
                            <template #title="title">
                                <home-outlined class="icon-yellow" />
                                {{ title.name || title.showName }}
                            </template>
                        </a-tree-select>
                    </div>
                    <div class="background_box">
                        <span>选择背景样式：</span>
                        <a-radio-group
                            v-model:value="state.establish.backgropPhoto"
                            buttonStyle="solid"
                        >
                            <a-radio
                                v-for="(item, index) in state.backgroundList"
                                :key="index"
                                :value="item.imagebg"
                                @change="backgroundTuiRadioChange(item)"
                            >
                                <!-- <div class="background_main" :style="{backgroundImage:'url('+item.imagebg+')'}"></div> -->
                                <img
                                    class="background_main"
                                    :src="item.imagebg"
                                    alt=""
                                />
                            </a-radio>
                        </a-radio-group>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup name="addOrEditPage">
/* eslint-disable */
import { reactive, onMounted, ref, nextTick } from "vue";
import { useRouter } from "vue-router";
import { useThrottleFn } from "@vueuse/core";
import { message, Modal } from "ant-design-vue";
import {
    getClassifyList,
    getLayoutModuleList,
    creatLayout,
    classLayoutInfo,
    updateClassTemplate,
    getBindingSiteTree,
    getSignImageList,
} from "@/api/classSign";
const Router = useRouter();
// 获取走马灯组件实例
const slideshow = ref();
// 默认加载背景图片数据
const backdrop = reactive({
    henImage:
        "https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/horizontal%20background/hbtjbj1-1.png",
    shuImage:
        "https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/sbtjbj1-1.png",
    henPhoto:
        "https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/horizontal%20background/hbtjbj1.png",
    shuPhoto:
        "https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/sbtjbj1.png",
});

const fieldNames = {
    label: "name",
    value: "id",
    children: "children",
};

// 保存横竖屏状态
const props = defineProps({
    direction: String,
    templateID: String,
});

// 状态数据
const state = reactive({
    updateKey: 1,
    brandId: [],
    result: [],
    equipment: [],
    backgroundList: [],
    // 布局初始化数据
    layout: [],
    deviceType: 9,
    // 横屏竖屏标识  横屏为0 / 竖屏为1
    direction: "",
    // 添加小组件到走马灯区域的数据
    photoList: [{ layoutclassmodellist: [] }],
    // 模板名称
    templateTabList: [],
    // 模板尺寸
    activeList: [],
    // 多选框模板小组件数据
    selectModuleList: [],
    // 多选框选择后的小组件数据
    selectModuleModel: [],
    // 横竖屏样式
    hengscreenHeight: 79,
    shuscreenHeight: 67.5,
    // 当前走马灯轮播图页码 默认为第一页
    paginationIndex: 0,
    initialIndex: 0,
    // 获取模块列表请求
    modules: {
        direction: "",
        width: "",
        high: "",
        schoolClassifyId: "",
    },
    // 创建模板请求
    establish: {
        isAllBrand: true,
        direction: "",
        brandIds: [],
        details: [],
        name: "",
        // 纯色背景 默认蓝色
        backgropPhoto: "",
        // 带状态栏背景 默认蓝色
        photo: "",
    },
});

state.direction = props.direction;
state.modules.direction = props.direction;
state.establish.direction = props.direction;

const emit = defineEmits(["showList"]);
const showList = () => {
    Router.replace({
        query: {},
    });
    emit("showList", false);
};

// 选择功能尺寸点击文字颜色控制
let activeClass = ref(0); // 0为默认颜色选择第一个，-1为不选择
let sizeActiveClass = ref(0);

// 获取小组件列表
const moduleListFn = async () => {
    window.dispatchEvent(new Event("resize"));
    const params = {
        name: "10寸设备",
        ...state.modules,
    };
    const res = await getLayoutModuleList(params);
    state.selectModuleList = res.data;
    for (let a = 0; a < state.selectModuleList.length; a++) {
        state.selectModuleList[a].width = Number(
            state.selectModuleList[a].width
        );
        state.selectModuleList[a].high = Number(state.selectModuleList[a].high);
    }
    for (let index = 0; index < state.photoList.length; index++) {
        const list = state.photoList[index].layoutclassmodellist;
        for (let a = 0; a < list.length; a++) {
            const img = list[a].img;
            if (state.selectModuleModel.indexOf(img) === -1) {
                state.selectModuleModel.push(img);
            }
        }
    }
};
// 查询功能名称尺寸的接口
const gainClassifyList = async () => {
    const { direction, deviceType } = state;
    let obj = { direction, deviceType, types: [1, 2] };
    const { data } = await getClassifyList(obj);
    state.templateTabList = data.classifyList;
    // 重命名名称数据
    let templateObj = { name: "全部", id: "" };
    state.templateTabList.unshift(templateObj);
    // 重命名尺寸数据
    let activeObj = { high: "", width: "" };
    state.activeList = data.sizeList;
    state.activeList.unshift(activeObj);
    if (data.classifyList.length) {
        data.classifyList.forEach((v) => {
            if (v.name === "10寸设备") {
                state.modules.schoolClassifyId = v.id;
            }
        });
    }
    moduleListFn();
};

// 点击功能名称切换 获取小组件
const templateNameTabHandleClick = (items, index) => {
    state.modules.schoolClassifyId = items.id;
    activeClass.value = index;
    moduleListFn();
};
// 点击尺寸切换 获取小组件
const tabHandleClick = (item, index) => {
    state.modules.high = item.high;
    state.modules.width = item.width;
    sizeActiveClass.value = index;
    moduleListFn();
};

// 布局updated事件
// 更新事件（布局更新或栅格元素的位置重新计算）
const layoutUpdatedEvent = (newLayout) => {
    for (let index = 0; index < newLayout.length; index++) {
        let height = newLayout[index].h;
        let y = newLayout[index].y;
        if (state.direction == "0") {
            if (height + y > 6) {
                for (let a = 0; a < state.selectModuleModel.length; a++) {
                    const ids = state.selectModuleModel[a];
                    if (newLayout[index].img == ids) {
                        state.selectModuleModel.splice(a, 1);
                    }
                }
                newLayout.splice(index, 1);
                message.destroy();
                message.error("尺寸超出当前展示区可布局范围！");
                layoutUpdatedEvent(newLayout);
            }
        } else {
            if (height + y > 8) {
                for (let a = 0; a < state.selectModuleModel.length; a++) {
                    const ids = state.selectModuleModel[a];
                    if (newLayout[index].img == ids) {
                        state.selectModuleModel.splice(a, 1);
                    }
                }
                newLayout.splice(index, 1);
                message.destroy();
                message.error("尺寸超出当前展示区可布局范围！");
                layoutUpdatedEvent(newLayout);
            }
        }
    }
};

const handleCheckedselectModuleChange = (i, e) => {
    window.dispatchEvent(new Event("resize")); // dispatch window resize event to force update layout
    let item, newId, newi;
    let nId = state.selectModuleList[i].images;
    if (e == false) {
        for (let w = 0; w < state.photoList.length; w++) {
            let arr = state.photoList[w].layoutclassmodellist;
            for (let r = 0; r < arr.length; r++) {
                let idi = arr[r].img;
                if (idi == nId) {
                    arr.splice(r, 1);
                }
            }
        }
    } else {
        for (let a = 0; a < state.selectModuleModel.length; a++) {
            const ids = state.selectModuleModel[a];
            if (nId == ids) {
                newId = nId;
                newi = a;
            }
        }
        if (newId != undefined) {
            for (
                let index = 0;
                index < state.selectModuleList.length;
                index++
            ) {
                const idnew = state.selectModuleList[index].images;
                if (idnew == newId) {
                    item = state.selectModuleList[index];
                }
            }
            let layout =
                state.photoList[state.paginationIndex].layoutclassmodellist;
            let newItem = {
                i: item.id,
                x: 0,
                y: 0,
                w: Number(item.width),
                h: Number(item.high),
                tag: item.tag,
                img: item.images,
                url: item.url,
                moduleId: item.id,
                imgType: item.imgType,
                descriptive: item.descriptive,
                secPageBackTime: item.secPageBackTime,
            };
            // 确定边界
            let Ys = [],
                maxX = 0,
                maxY = 0,
                edgeX = 0,
                edgeY = 0;
            layout.map((item) => {
                Ys.push(item.y + item.h);
            });
            if (state.direction == "0") {
                // 横板
                maxY = 6;
                edgeX = 8;
                edgeY = maxY;
            } else if (state.direction == "1") {
                // 竖版
                maxY = 8;
                edgeX = 6;
                edgeY = maxY;
            }

            // 使用二维数组生成地图
            let gridMap = new Array();
            for (let x = 0; x < edgeX; x++) {
                gridMap[x] = new Array();
                for (let y = 0; y < edgeY; y++) {
                    gridMap[x][y] = 0;
                }
            }
            // 标记占位
            layout.map((item) => {
                // 将layout中卡片所占区域标记为1
                for (let x = item.x; x < item.x + item.w; x++) {
                    for (let y = item.y; y < item.y + item.h; y++) {
                        gridMap[x][y] = 1;
                    }
                }
            });
            // 遍历地图，申请位置
            for (let y = 0; y < edgeY; y++) {
                for (let x = 0; x < edgeX; x++) {
                    // 申请所需空间
                    if (edgeX - x >= item.width && edgeY - y >= item.high) {
                        let itemSignArr = [];
                        for (let a = x; a < x + item.width; a++) {
                            for (let b = y; b < y + item.high; b++) {
                                itemSignArr.push(gridMap[x][y]);
                            }
                        }
                        if (itemSignArr.indexOf(1) < 0) {
                            newItem.x = x;
                            newItem.y = y;
                            if (state.direction == "0") {
                                // 横板
                                if (newItem.x < 8 && newItem.y < 6) {
                                    layout.push(newItem);
                                    window.dispatchEvent(new Event("resize")); // dispatch window resize event to force update layout
                                    layoutUpdatedEvent(layout);
                                }
                            } else if (state.direction == "1") {
                                // 竖版
                                if (newItem.x < 6 && newItem.y < 8) {
                                    layout.push(newItem);
                                    window.dispatchEvent(new Event("resize")); // dispatch window resize event to force update layout
                                    layoutUpdatedEvent(layout);
                                }
                            }
                            return false;
                        }
                    }
                }
            }
            for (let g = 0; g < layout.length; g++) {
                const element = layout[g].i;
                if (newItem.i != element) {
                    if (g == layout.length - 1) {
                        message.destroy();
                        message.error("尺寸超出当前展示区可布局范围！");
                        window.dispatchEvent(new Event("resize")); // dispatch window resize event to force update layout
                        state.selectModuleModel.splice(newi, 1);
                    }
                }
            }
        }
    }
};

// 减少走马灯展示区域
const reduceLayout = useThrottleFn(() => {
    let element = state.photoList[state.paginationIndex].layoutclassmodellist;
    if (element.length > 0) {
        Modal.confirm({
            title: "提示",
            type: "warning",
            content: "展示区有模板，请确定删除?",
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            onOk: () => {
                for (let index = 0; index < element.length; index++) {
                    const id = element[index].img;
                    for (let a = 0; a < state.selectModuleModel.length; a++) {
                        const newid = state.selectModuleModel[a];
                        if (id == newid) {
                            state.selectModuleModel.splice(a, 1);
                        }
                    }
                    const tag = element[index].tag;
                }
                state.photoList.splice(state.paginationIndex, 1);
                //   state.photoList[state.paginationIndex].layoutclassmodellist = []
                message.success("删除成功！");
            },
            onCancel() {
                message.warning("已取消删除!");
            },
        });
    } else {
        state.photoList.splice(state.paginationIndex, 1);
    }
}, 1000);

// 添加布局展示区域
const addLayout = () => {
    let obj = {
        layoutclassmodellist: [],
    };
    state.photoList.push(obj);

    state.initialIndex = state.photoList.length - 1;
    nextTick(() => {
        slideshow.value.setActiveItem(state.initialIndex);
    });
};

// 切换轮播图的回调获取当前的轮播图下标页码
function beCarouselChange(f, t) {
    state.paginationIndex = t;
}

const carouselChangeFn = (index) => {
    state.paginationIndex = index;
};

// 点击小组件上的X删除按钮
const newDeleteFn = (img, index) => {
    let newList = state.photoList[index].layoutclassmodellist;
    for (let a = 0; a < newList.length; a++) {
        const newid = newList[a].img;
        if (img == newid) {
            newList.splice(a, 1);
        }
    }
    for (let o = 0; o < state.selectModuleModel.length; o++) {
        const idn = state.selectModuleModel[o];
        if (img == idn) {
            state.selectModuleModel.splice(o, 1);
        }
    }
    window.dispatchEvent(new Event("resize")); // dispatch window resize event to force update layout
};

// 点击背景图切换事件
const backgroundTuiRadioChange = (item) => {
    // 赋值有状态栏的图片给请求参数
    state.establish.photo = item.image;
};

// 递归树形结构 item
// const getTreeItem = (data, id) => {
//   data.map(item => {
//     if (item.id === id) {
//       state.result.push(item);
//     } else {
//       if (item.children) {
//         getTreeItem(item.children, id);
//       }
//     }
//   });
// }

// 创建模板请求
const saveSubmissionFn = async () => {
    if (state.establish.name == "" || state.establish.name == null) {
        message.error("请输入模板名称！");
        return false;
    }
    // state.brandId.forEach((item) => {
    //   getTreeItem(state.equipment, item);
    // })
    // 遍历item获取brandId
    if (state.establish.isAllBrand) {
        state.establish.brandIds = [];
    } else {
        // state.establish.brandIds = state.result.map((item) => item.brandId)
        state.establish.brandIds = state.brandId;
    }
    // 处理details对象
    let list = state.photoList;
    let arr = [];
    for (let index = 0; index < list.length; index++) {
        let layoutList = list[index].layoutclassmodellist;
        for (let a = 0; a < layoutList.length; a++) {
            let obj = {
                url: layoutList[a].url,
                moduleId: layoutList[a].moduleId,
                imgType: layoutList[a].imgType,
                width: layoutList[a].w,
                secPageBackTime: layoutList[a].secPageBackTime,
                height: layoutList[a].h,
                x: layoutList[a].x,
                y: layoutList[a].y,
                images: layoutList[a].img,
                descriptive: layoutList[a].descriptive,
                tag: layoutList[a].tag,
                pagination: index,
            };
            arr.push(obj);
        }
    }
    state.establish.details = arr;
    // 如果有添加模块数据
    if (state.establish.details.length > 0) {
        // 编辑
        if (props.templateID) {
            state.establish.id = props.templateID.toString();
            const params = { ...state.establish, deviceType: state.deviceType };
            await updateClassTemplate(params);
            message.success("编辑成功!");
            showList();
        } else {
            // 创建
            const params = { ...state.establish, deviceType: state.deviceType };
            await creatLayout(params);
            message.success("创建成功!");
            showList();
        }
    } else {
        message.error("模板详情不能为空!");
    }
};

// 编辑模式获取到模板详情信息
const getClassLayoutInfo = async () => {
    const { data } = await classLayoutInfo(props.templateID);
    state.establish.name = data.name;
    state.establish.backgropPhoto = data.backgropPhoto;
    state.establish.photo = data.photo;
    const layoutLists = data.details;
    for (let i = 0; i < layoutLists.length; i++) {
        const pagination = Number(layoutLists[i].pagination);
        if (pagination != "" && pagination != null) {
            state.photoList = [];
            for (let w = 0; w < pagination + 1; w++) {
                let obj = { layoutclassmodellist: [] };
                state.photoList.push(obj);
            }
        }
    }
    for (let e = 0; e < layoutLists.length; e++) {
        const pagination = Number(layoutLists[e].pagination);
        let newsItem = {
            i: layoutLists[e].id,
            x: Number(layoutLists[e].x),
            y: Number(layoutLists[e].y),
            w: Number(layoutLists[e].width),
            h: Number(layoutLists[e].height),
            img: layoutLists[e].images,
            tag: layoutLists[e].tag,
            url: layoutLists[e].url,
            moduleId: layoutLists[e].moduleId,
            imgType: layoutLists[e].imgType,
            descriptive: layoutLists[e].descriptive,
            secPageBackTime: layoutLists[e].secPageBackTime,
        };
        state.photoList[pagination].layoutclassmodellist.push(newsItem);
    }
};

// 下拉会议机选择框获取设备信息
const dropdownVisibleChange = useThrottleFn(async () => {
    const { direction, deviceType } = state;
    let obj = { direction, deviceType };
    const { data } = await getBindingSiteTree(obj);
    state.equipment = data;
}, 5000);

const reqSignImageList = async () => {
    const { direction, deviceType } = state;
    const { data } = await getSignImageList({ direction, deviceType });
    state.backgroundList = data || [];
    state.establish.backgropPhoto = state.backgroundList[0].imagebg;
    state.establish.photo = state.backgroundList[0].image;
};
onMounted(() => {
    window.dispatchEvent(new Event("resize"));
    // 判断横竖屏默认图片
    reqSignImageList();
    // if (state.direction == '0') {
    //     state.backgroundList = [
    //         {
    //             image:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/horizontal%20background/hbtjbj1.png',
    //             imagebg:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/horizontal%20background/hbtjbj1-1.png'
    //         },
    //         {
    //             image:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/horizontal%20background/hbtjbj2.png',
    //             imagebg:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/horizontal%20background/hbtjbj2-2.png'
    //         },
    //         {
    //             image:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/horizontal%20background/hbtjbj3.png',
    //             imagebg:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/horizontal%20background/hbtjbj3-3.png'
    //         },
    //         {
    //             image:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/horizontal%20background/hbtjbj4.png',
    //             imagebg:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/horizontal%20background/hbtjbj4-4.png'
    //         },
    //         {
    //             image:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/horizontal%20background/hbtjbj5.png',
    //             imagebg:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/horizontal%20background/hbtjbj5-5.png'
    //         },
    //         {
    //             image:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/hbtjbj6.png',
    //             imagebg:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/hbtjbj6.png'
    //         }
    //     ]
    // } else {
    //     state.backgroundList = [
    //         {
    //             image:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/sbtjbj1.png',
    //             imagebg:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/sbtjbj1-1.png'
    //         },
    //         {
    //             image:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/sbtjbj2.png',
    //             imagebg:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/sbtjbj2-2.png'
    //         },
    //         {
    //             image:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/sbtjbj3.png',
    //             imagebg:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/sbtjbj3-3.png'
    //         },
    //         {
    //             image:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/sbtjbj4.png',
    //             imagebg:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/sbtjbj4-4.png'
    //         },
    //         {
    //             image:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/sbtjbj5.png',
    //             imagebg:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/sbtjbj5-5.png'
    //         },
    //         {
    //             image:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/sbtjbj6.png',
    //             imagebg:
    //       'https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/sbtjbj6.png'
    //         }
    //     ]
    // }
    gainClassifyList();
    // 如果是编辑模式
    if (props.templateID) {
        getClassLayoutInfo();
    }
});
</script>

<style lang="less">
// .homepage_container_main {
//   overflow: hidden;
// }

.container_main {
    width: 100%;
    // height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    background: #fff;
    border-bottom: 1px solid #ccc;
    transform: translateY(-57px);

    // position:absolute
    .header {
        // height: 57px;
        // line-height: 57px;
        // padding: 0 16px;
        height: 33px;
        // .tabs_back {
        //     .tabs_back__icon {
        //         color: @primary-color;
        //     }
        // }
    }
}

.homepageLeft_chlidmain {
    width: 23%;
    float: left;
    height: calc(87vh - 10px);
    border-right: 1px solid #e4e7ed;
    padding-right: 16px;
    padding-left: 16px;
    overflow: hidden auto;
    .moulde_index {
        height: 20px;
        font-size: 14px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        line-height: 20px;
        padding: 16px 0px 32px 0px;
    }
}

.homepageRight_chlidmain {
    width: 76%;
    float: right;
    height: calc(87vh - 10px);
    overflow-y: auto;
    padding-bottom: 40px;
}

.no_dataLayout {
    text-align: center;

    img {
        width: 180px;
    }

    p {
        color: rgba(0, 0, 0, 0.65);
    }
}

.tab_style {
    .tab_style_title {
        padding-right: 20px;
        color: #333;
        display: inline-block;
        vertical-align: top;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
        line-height: 30px;
    }

    .tab_style_list {
        display: inline-block;
        width: 85%;
        overflow: hidden;

        .tab_content {
            font-size: 14px;
            // color: #000000;
            display: block;
            float: left;
            line-height: 30px;
            cursor: pointer;
            padding-right: 15px;
        }

        .tab_content:hover {
            color: #00b781;
        }
    }
}

.actived {
    color: #00b781;
}

.manu_message {
    width: 90%;
    max-height: 50vh;
    overflow-y: auto;
    padding-bottom: 60px;

    .el-checkbox {
        display: inline-block;
        width: 100%;
        height: unset !important;
        margin: 0px;
        position: relative;
    }

    .el-checkbox__label {
        width: 100%;
        padding: 0px;
    }

    .el-checkbox__input {
        position: absolute !important;
        right: 10px;
        top: 10px;
    }

    .el-checkbox__inner {
        width: 20px;
        height: 20px;
        border-radius: 10px;
    }

    .el-checkbox__inner::after {
        height: 10px;
        left: 7px;
        top: 2px;
    }
}

.manu_img {
    position: relative;
    overflow: hidden;
    background-color: #dcdfe6;
    display: flex;
    align-items: center;

    img {
        width: 100%;
    }
}

.manu_h {
    padding: 50px;
}

.manu_v {
    padding: 25px;
}

.manu_success {
    width: 24px;
    height: 24px;
    position: absolute;
    background-color: #fff;
    border-radius: 12px;
    right: 10px;
    top: 10px;
}

.h_img {
    width: 100%;
}

.v_img {
    height: 100%;
    margin: 0px auto;
}

.manu_title span {
    line-height: 24px;
    margin: 0px;
    padding: 0px;
    font-weight: normal;
    font-size: 14px;
    color: #909399;
}

.background_box {
    display: flex;

    .ant-radio-wrapper {
        width: 100px;
        height: 56px;

        // margin-right : 10px;
        // margin-bottom: 10px;
        .ant-radio {
            position: absolute;
            top: 4px;
            right: -4px;
        }
    }
}

.background_main {
    width: 100px;
    height: 56px;
    border-radius: 4px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.screen_main {
    margin: 20px 80px 0px 80px;
}

.screen_title {
    display: block;
    margin-bottom: 20px;
    color: #303133;
    font-size: 14px;
    position: relative;
}

.show-img {
    position: relative;
    overflow: hidden;
    height: 100%;
    border-radius: 6px;

    .del-icon {
        cursor: pointer;
        display: none;
        width: 14px;
        height: 14px;
        line-height: 10px;
        top: 3px;
        right: 1px;
        position: absolute;
        color: #fff;
        text-align: center;
        background: #f5222d;
        border-radius: 50%;
    }
}

.show-img:hover {
    .del-icon {
        display: block;
    }
}

// .hengscreen_content {
//     width: 1037px;
//     height: 610px;
//     position: relative;
//     .ant-carousel {
//       .slick-slider {
//        z-index: 100;
//        height: 568px;
//         .slick-list{
//           height: 100%;
//         }
//        }
//     }
//     &::after {
//       content: "";
//       position: absolute;
//       left: 0;
//       right: 0;
//       top: 0;
//       bottom: 0;
//       background-image: url(https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/hbtmt1.png);
//       background-size: 100% 100%;
//       background-repeat: no-repeat;
//     }
// }

//   .shuscreen_content {
//     width: 367px;
//     height: 675px;
//     margin: 0px auto;
//     position: relative;
//     .ant-carousel {
//       .slick-slider {
//        z-index: 100;
//        height: 652px;
//         .slick-list{
//           height: 100%;
//         }
//        }
//     }
//     &::after{
//       content: "";
//       position: absolute;
//       left: 0;
//       right: 0;
//       top: 0;
//       bottom: 0;
//       background-image: url(https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/sbtmt1.png);
//       background-size: 100% 100%;
//       background-repeat: no-repeat;
//     }
//   }

.hengscreen_content {
    width: 1037px;
    height: 610px;
    position: relative;

    .el-carousel__container {
        height: 568px;
    }

    &::after {
        content: "";
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background-image: url(https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/hbtmt1.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}

.shuscreen_content {
    width: 367px;
    height: 675px;
    margin: 0px auto;
    position: relative;

    .el-carousel__container {
        height: 652px;
    }

    &::after {
        content: "";
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background-image: url(https://brand-yide.oss-cn-shenzhen.aliyuncs.com/202101/layout/vertical%20background/sbtmt1.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}

.screen_content {
    position: relative;

    .el-carousel {
        z-index: 100;
    }

    .el-carousel__item h3 {
        color: #475669;
        font-size: 1.8rem;
        opacity: 0.75;
        line-height: 460px;
        margin: 0;
    }

    .el-carousel__button {
        width: 12px;
        height: 12px;
        border-radius: 6px;
    }

    .el-carousel__indicators--outside {
        position: absolute;
        bottom: 0px;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%);
    }

    .el-carousel__indicator--horizontal {
        padding: 4px;
    }
}

.application_main {
    // margin-top: 20px;
    padding: 20px 0px 20px 0px;
}
</style>

<style scoped>
/* 走马灯内部样式 */
.ant-carousel {
    z-index: 100;
    height: 568px;
}

.ant-carousel :deep(.slick-arrow.custom-slick-arrow) {
    width: 25px;
    height: 25px;
    font-size: 25px;
    color: #fff;
    background-color: rgba(31, 45, 61, 0.11);
    opacity: 0.3;
    z-index: 1;
}

.ant-carousel :deep(.custom-slick-arrow:before) {
    display: none;
}

.ant-carousel :deep(.custom-slick-arrow:hover) {
    opacity: 0.5;
}

/* 模块小组件内部样式 */
.manu_message :deep(.ant-checkbox-wrapper) {
    width: 100%;
    margin: 0px;
    position: relative;
}

.manu_message :deep(.ant-checkbox) {
    position: absolute !important;
    z-index: 1;
    right: 20px;
    top: 10px;
}

.manu_message :deep(.ant-checkbox + span) {
    padding: 0px;
}

.manu_message :deep(.ant-checkbox .ant-checkbox-inner) {
    width: 20px;
    height: 20px;
    border-radius: 10px;
}

.manu_message :deep(.ant-checkbox-checked::after) {
    border-radius: 10px;
}

/* 模板加减号icon内部样式 */
.add_btn {
    z-index: 1000;
    position: absolute;
    bottom: 24px;
    width: 100%;
    font-size: 1.3rem;
    color: #ffffff;
    font-weight: bold;
}

.add_btn :deep(.anticon-plus) {
    position: absolute;
    right: 10px;
}

.add_btn :deep(.anticon-minus) {
    position: absolute;
    left: 10px;
}

/* 走马灯小圆点指示器内部样式 */
.ant-carousel :deep(.slick-dots li button) {
    width: 8px;
    height: 8px;
    border-radius: 6px;
}

/* .ant-carousel :deep(.slick-dots li.slick-active){
  width: none !important
} */
</style>
