<!-- 会议机布局 -->
<template>
    <div class="template">
        <!-- 会议机布局首页 -->
        <div v-if="!state.isEdit" class="homepage_container">
            <div class="homepage_head">
                <div>
                    <a-radio-group
                        v-model:value="state.direction"
                        button-style="solid"
                    >
                        <a-radio-button @click="directionFn" value="0"
                            >横版</a-radio-button
                        >
                        <a-radio-button @click="directionFn" value="1"
                            >竖版</a-radio-button
                        >
                    </a-radio-group>
                </div>
                <!-- <div>
                    <a-button
                        type="primary"
                        class="ediubutton"
                        @click="addHomePageLayout"
                        ><template #icon><plus-outlined /></template
                        >新建模板</a-button
                    >
                </div> -->
            </div>
            <a-button
                type="primary"
                class="homepage_head_add"
                @click="addHomePageLayout"
                ><template #icon><plus-outlined /></template>新建模板</a-button
            >
            <div class="homepage_main">
                <div class="no_dataLayout" v-if="state.noLayout == true">
                    <img src="/image/empty.png" alt="" />
                    <p>暂无数据</p>
                </div>

                <div v-else>
                    <div class="moulde_export">
                        <ul>
                            <li
                                v-for="(item, index) in state.mouldeList"
                                @mouseenter="btnEnterMouldeFn(index)"
                                @mouseleave="btnLeaveMouldeFn(index)"
                                :key="index"
                                :class="
                                    state.direction == '0'
                                        ? 'moulde_list_transverse'
                                        : 'moulde_list_vertical'
                                "
                            >
                                <div class="imgBox">
                                    <div
                                        :class="
                                            state.direction == '0'
                                                ? 'moulde_img'
                                                : 'moulde_img_vertical'
                                        "
                                        :style="{
                                            backgroundImage:
                                                'url(' + item.photo + ')',
                                        }"
                                    >
                                        <!-- 推荐模板下拉选项 查看/应用到会议机 -->
                                        <a-dropdown :trigger="['click']">
                                            <div class="moulde_btn">
                                                <img src="/image/cd3.png" alt />
                                            </div>
                                            <template #overlay>
                                                <a-menu
                                                    @click="handleMenuClick"
                                                >
                                                    <a-menu-item
                                                        :key="
                                                            beforeHandleCommand(
                                                                item,
                                                                'look'
                                                            )
                                                        "
                                                        style="
                                                            color: var(
                                                                --primary-color
                                                            );
                                                        "
                                                        >查看</a-menu-item
                                                    >
                                                    <a-menu-item
                                                        :key="
                                                            beforeHandleCommand(
                                                                item,
                                                                'adhibition'
                                                            )
                                                        "
                                                        >应用到会议机</a-menu-item
                                                    >
                                                </a-menu>
                                            </template>
                                        </a-dropdown>

                                        <!-- 推荐模板拖拽布局 -->
                                        <grid-layout
                                            :layout="state.Layout"
                                            :auto-size="false"
                                            :col-num="
                                                state.direction == '0' ? 8 : 6
                                            "
                                            :row-height="
                                                state.direction == '0'
                                                    ? state.transverseHeight
                                                    : state.verticalHeight
                                            "
                                            :max-rows="
                                                state.direction == '0' ? 6 : 8
                                            "
                                            :is-draggable="false"
                                            :is-resizable="false"
                                            :vertical-compact="false"
                                            :margin="[5, 5]"
                                            :use-css-transforms="true"
                                        >
                                            <grid-item
                                                v-for="(
                                                    items, indexs
                                                ) in item.details"
                                                :key="indexs"
                                                :x="items.x"
                                                :y="items.y"
                                                :w="items.width"
                                                :h="items.height"
                                                :i="items.id"
                                            >
                                                <img :src="items.images" alt />
                                            </grid-item>
                                        </grid-layout>
                                    </div>
                                    <div class="maskImg"></div>
                                </div>
                                <span
                                    style="
                                        color: rgba(0, 0, 0, 0.85);
                                        font-size: 14px;
                                        line-height: 36px;
                                    "
                                    >{{ item.name }}</span
                                >
                            </li>
                        </ul>

                        <ul>
                            <li
                                v-for="(item, index) in state.newmouldeList"
                                :key="index"
                                @mouseenter="newbtnEnterMouldeFn(index)"
                                @mouseleave="newbtnLeaveMouldeFn(index)"
                                :class="
                                    state.direction == '0'
                                        ? 'moulde_list_transverse'
                                        : 'moulde_list_vertical'
                                "
                            >
                                <div class="imgBox">
                                    <div
                                        :class="
                                            state.direction == '0'
                                                ? 'moulde_img'
                                                : 'moulde_img_vertical'
                                        "
                                        :style="{
                                            backgroundImage:
                                                'url(' + item.photo + ')',
                                        }"
                                    >
                                        <!-- 自定义模板下拉选项 查看/应用到会议机/编辑/删除 -->
                                        <a-dropdown :trigger="['click']">
                                            <div class="moulde_btn">
                                                <img src="/image/cd3.png" alt />
                                            </div>
                                            <template #overlay>
                                                <a-menu
                                                    @click="handleMenuClick"
                                                >
                                                    <a-menu-item
                                                        :key="
                                                            beforeHandleCommand(
                                                                item,
                                                                'look'
                                                            )
                                                        "
                                                        style="
                                                            color: var(
                                                                --primary-color
                                                            );
                                                        "
                                                        >查看</a-menu-item
                                                    >
                                                    <a-menu-item
                                                        :key="
                                                            beforeHandleCommand(
                                                                item,
                                                                'adhibition'
                                                            )
                                                        "
                                                        >应用到会议机</a-menu-item
                                                    >
                                                    <a-menu-item
                                                        :key="
                                                            beforeHandleCommand(
                                                                item,
                                                                'edit'
                                                            )
                                                        "
                                                        >编辑</a-menu-item
                                                    >
                                                    <a-menu-item
                                                        :key="
                                                            beforeHandleCommand(
                                                                item,
                                                                'del'
                                                            )
                                                        "
                                                        style="color: #f5222d"
                                                        >删除</a-menu-item
                                                    >
                                                </a-menu>
                                            </template>
                                        </a-dropdown>
                                        <!-- 自定义模板拖拽布局 -->
                                        <grid-layout
                                            :layout="state.Layout"
                                            :auto-size="false"
                                            :col-num="
                                                state.direction == '0' ? 8 : 6
                                            "
                                            :row-height="
                                                state.direction == '0'
                                                    ? state.transverseHeight
                                                    : state.verticalHeight
                                            "
                                            :max-rows="
                                                state.direction == '0' ? 6 : 8
                                            "
                                            :is-draggable="false"
                                            :is-resizable="false"
                                            :vertical-compact="false"
                                            :margin="[5, 5]"
                                            :use-css-transforms="true"
                                        >
                                            <grid-item
                                                v-for="(
                                                    items, indexs
                                                ) in item.reLayoutlis"
                                                :key="indexs"
                                                :x="items.x"
                                                :y="items.y"
                                                :w="items.width"
                                                :h="items.height"
                                                :i="items.id"
                                            >
                                                <img :src="items.images" alt />
                                            </grid-item>
                                        </grid-layout>
                                    </div>
                                    <div class="maskImg"></div>
                                </div>
                                <span
                                    style="
                                        color: rgba(0, 0, 0, 0.85);
                                        font-size: 14px;
                                        line-height: 36px;
                                    "
                                    >{{ item.name }}</span
                                >
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <!-- 查看 -->
            <div class="checkTemplate">
                <a-modal
                    :maskClosable="false"
                    class="lookModel"
                    style="width:928px;height:597px padding-bottom: 0px;"
                    :footer="null"
                    v-model:visible="state.seeDialogVisible"
                    title="查看模板"
                >
                    <div
                        :class="
                            state.direction == '0'
                                ? 'hengscreen_contents'
                                : 'shuscreen_contents'
                        "
                        :style="
                            state.direction == '0'
                                ? {
                                      backgroundImage:
                                          'url(' + state.backdropPhoto + ')',
                                      backgroundSize: '100% 100%',
                                      backgroundRepeat: 'no-repeat',
                                      paddingTop: '10px',
                                  }
                                : {
                                      backgroundImage:
                                          'url(' + state.backdropPhoto + ')',
                                      backgroundSize: '100% 100%',
                                      backgroundRepeat: 'no-repeat',
                                      paddingTop: '0px',
                                  }
                        "
                    >
                        <a-carousel arrows autoplay>
                            <!-- 左按钮切换 -->
                            <template #prevArrow>
                                <div
                                    class="custom-slick-arrow"
                                    style="left: 10px; z-index: 1"
                                >
                                    <left-circle-outlined />
                                </div>
                            </template>
                            <!-- 右按钮切换 -->
                            <template #nextArrow>
                                <div
                                    class="custom-slick-arrow"
                                    style="right: 10px"
                                >
                                    <right-circle-outlined />
                                </div>
                            </template>
                            <!-- 走马灯内容模板区域 遍历后生成多页 -->
                            <div
                                v-for="(item, a) in state.CarouselList"
                                :key="a"
                            >
                                <!-- 自定义模板拖拽布局 横竖屏动态调整 -->
                                <grid-layout
                                    v-model:layout="state.Layout"
                                    :col-num="state.direction == '0' ? 8 : 6"
                                    :row-height="
                                        state.direction == '0'
                                            ? state.seetransverseHeight
                                            : state.seeverticalHeight
                                    "
                                    :max-rows="state.direction == '0' ? 6 : 8"
                                    :is-draggable="false"
                                    :is-resizable="false"
                                    :vertical-compact="false"
                                    :auto-size="false"
                                    :margin="[5, 5]"
                                    :use-css-transforms="true"
                                >
                                    <!-- 模板拖拽item -->
                                    <grid-item
                                        v-for="(items, index) in item.details"
                                        :key="index"
                                        :x="items.x"
                                        :y="items.y"
                                        :w="items.w"
                                        :h="items.h"
                                        :i="items.i"
                                    >
                                        <div style="height: 100%; width: 100%">
                                            <img
                                                :src="items.img"
                                                style="
                                                    width: 100%;
                                                    height: 100%;
                                                "
                                                alt
                                            />
                                        </div>
                                    </grid-item>
                                </grid-layout>
                            </div>
                        </a-carousel>
                    </div>
                </a-modal>
            </div>

            <!-- 应用到会议机 -->
            <div>
                <a-drawer
                    v-model:visible="state.applyToClassVisible"
                    class="applyToClass"
                    destroyOnClose
                    title="应用到会议机"
                    :maskClosable="false"
                    @close="abrogate"
                >
                    <!-- 走马灯 -->
                    <div class="slideshow-main">
                        <div
                            :class="
                                state.direction == '0'
                                    ? 'class_transverse'
                                    : 'class_vertical'
                            "
                            :style="
                                state.direction == '0'
                                    ? {
                                          backgroundImage:
                                              'url(' +
                                              state.backdropPhoto +
                                              ')',
                                          backgroundSize: '100% 100%',
                                          backgroundRepeat: 'no-repeat',
                                          paddingTop: '10px',
                                      }
                                    : {
                                          backgroundImage:
                                              'url(' +
                                              state.backdropPhoto +
                                              ')',
                                          backgroundSize: '100% 100%',
                                          backgroundRepeat: 'no-repeat',
                                          paddingTop: '0px',
                                      }
                            "
                        >
                            <a-carousel arrows autoplay>
                                <!-- 左按钮切换 -->
                                <template #prevArrow>
                                    <div
                                        class="custom-slick-arrow"
                                        style="left: 10px; z-index: 1"
                                    >
                                        <left-circle-outlined />
                                    </div>
                                </template>
                                <!-- 右按钮切换 -->
                                <template #nextArrow>
                                    <div
                                        class="custom-slick-arrow"
                                        style="right: 10px"
                                    >
                                        <right-circle-outlined />
                                    </div>
                                </template>
                                <!-- 走马灯内容模板区域 遍历后生成多页 -->
                                <div
                                    v-for="(item, a) in state.CarouselList"
                                    :key="a"
                                >
                                    <!-- 自定义模板拖拽布局 横竖屏动态调整 -->
                                    <grid-layout
                                        v-model:layout="state.Layout"
                                        :col-num="
                                            state.direction == '0' ? 8 : 6
                                        "
                                        :row-height="
                                            state.direction == '0'
                                                ? state.applytransverseHeight
                                                : state.applyverticalHeight
                                        "
                                        :max-rows="
                                            state.direction == '0' ? 6 : 8
                                        "
                                        :is-draggable="false"
                                        :is-resizable="false"
                                        :vertical-compact="false"
                                        :auto-size="false"
                                        :margin="[5, 5]"
                                        :use-css-transforms="true"
                                    >
                                        <!-- 模板拖拽item -->
                                        <grid-item
                                            v-for="(
                                                items, index
                                            ) in item.details"
                                            :key="index"
                                            :x="items.x"
                                            :y="items.y"
                                            :w="items.w"
                                            :h="items.h"
                                            :i="items.i"
                                        >
                                            <div
                                                style="
                                                    height: 100%;
                                                    width: 100%;
                                                "
                                            >
                                                <img
                                                    :src="items.img"
                                                    style="
                                                        width: 100%;
                                                        height: 100%;
                                                    "
                                                    alt
                                                />
                                            </div>
                                        </grid-item>
                                    </grid-layout>
                                </div>
                            </a-carousel>
                        </div>
                    </div>
                    <div>
                        <div class="choice">
                            <a-space align="center">
                                <span>选择应用范围:</span>
                                <a-radio-group
                                    v-model:value="state.publish.isAllBrand"
                                >
                                    <a-radio :value="true">{{
                                        state.direction == "0"
                                            ? "全部横屏会议机"
                                            : "全部竖屏会议机"
                                    }}</a-radio>
                                    <a-radio :value="false">{{
                                        state.direction == "0"
                                            ? "指定横屏会议机"
                                            : "指定竖屏会议机"
                                    }}</a-radio>
                                </a-radio-group>
                            </a-space>
                        </div>

                        <div v-if="!state.publish.isAllBrand">
                            <a-tree-select
                                style="width: 100%"
                                tree-checkable
                                multiple
                                v-model:value="state.brandId"
                                placeholder="请选择会议机"
                                allow-clear
                                :tree-data="state.equipment"
                                :fieldNames="fieldNames"
                                @dropdownVisibleChange="dropdownVisibleChange"
                                :maxTagCount="3"
                            >
                                <template #title="title">
                                    <home-outlined class="icon-yellow" />
                                    {{ title.name }}
                                </template>
                            </a-tree-select>
                        </div>
                    </div>
                    <template #footer>
                        <div class="teFooter">
                            <a-button
                                style="margin-right: 8px"
                                @click="abrogate"
                                >取消</a-button
                            >
                            <a-button type="primary" @click="notarize"
                                >确认</a-button
                            >
                        </div>
                    </template>
                </a-drawer>
            </div>
        </div>
        <!-- 添加/编辑模板页面 -->
        <div :class="{add_edit: state.isEdit}">
            <LayoutAddOrEdit
                v-if="state.isEdit"
                @showList="handel"
                :direction="state.direction"
                :templateID="state.templateID"
            />
        </div>
    </div>
</template>

<script>
import { defineComponent } from "vue";
import { useRoute, useRouter } from "vue-router";
import LayoutAddOrEdit from "./components/layoutAddOrEdit.vue";
export default defineComponent({
    name: "classLayout",
    Components: {
        LayoutAddOrEdit,
    },
});
</script>

<script setup>
import { reactive, onMounted, ref, inject } from "vue";
import { message, Modal } from "ant-design-vue";
import { useThrottleFn } from "@vueuse/core";
import {
    getLayoutList,
    delClassTemplate,
    publishClassTemplate,
    getBindingSiteTree,
} from "@/api/classSign";
let stateShowNewPage = inject("showNewPage")();
const Router = useRouter();
const Route = useRoute();
const fieldNames = {
    label: "name",
    value: "id",
    children: "children",
};
const state = reactive({
    deviceType: 9,
    templateID: "",
    //操作按钮
    moreBtn: "",
    noLayout: false,
    seeDialogVisible: false,
    applyToClassVisible: false,
    backgropPhoto: "",
    CarouselList: [],
    verticalHeight: 41,
    transverseHeight: 28,
    seetransverseHeight: 60,
    seeverticalHeight: 53,
    applytransverseHeight: 47.5,
    applyverticalHeight: 66,
    isEdit: false,
    Layout: [],
    // 0横屏 1竖屏
    direction: "0",
    //推荐模板布局数据
    mouldeList: [],
    //自定义模板布局数据
    newmouldeList: [],
    equipment: [],
    brandId: [],
    result: [],
    publish: {
        isAllBrand: true,
        brandIds: [],
    },
});

// 横竖屏切换按钮
const directionFn = (e) => {
    state.direction = e.target.value;
    gainLayoutList();
};

// 新建模块
const addHomePageLayout = () => {
    Router.replace({
        query: {
            ...Route.query,
            edit: "edit",
        },
    });
    state.isEdit = true;
    stateShowNewPage.showNewPage = false;
};

// 获取布局首页数据
const gainLayoutList = async () => {
    const { direction, deviceType } = state;
    const obj = { direction, deviceType };
    const { data } = await getLayoutList(obj);
    if (data.defaultList.length == 0 && data.schoolList.length == 0) {
        state.noLayout = true;
    } else {
        state.noLayout = false;
    }
    state.mouldeList = data.defaultList;
    state.newmouldeList = data.schoolList;
    state.templateID = "";
    // 重置数据 默认展示第一屏的数据
    for (let index = 0; index < state.newmouldeList.length; index++) {
        const layoutlist = state.newmouldeList[index].details;
        state.newmouldeList[index].reLayoutlis = [];
        for (let a = 0; a < layoutlist.length; a++) {
            const pagination = layoutlist[a].pagination;
            if (pagination === 0) {
                state.newmouldeList[index].reLayoutlis.push(layoutlist[a]);
            }
        }
    }
};

// 推荐模板鼠标移入事件
const btnEnterMouldeFn = (i) => {};
// 推荐模板鼠标移出事件
const btnLeaveMouldeFn = (i) => {};
// 自定义模板鼠标移入事件
const newbtnEnterMouldeFn = (i) => {};
// 自定义模板鼠标移出事件
const newbtnLeaveMouldeFn = (i) => {};

// 包装一个对象
const beforeHandleCommand = (item, key) => {
    return {
        item: item,
        key: key,
    };
};

// 模板操作编辑查看删除按钮
const handleMenuClick = (val) => {
    const isBtn = val.key.key;
    const isItem = val.key.item;

    if (isBtn === "look") {
        state.seeDialogVisible = true;
        state.backdropPhoto = isItem.photo;
        const layoutLists = isItem.details;
        for (let i = 0; i < layoutLists.length; i++) {
            const pagination = layoutLists[i].pagination;
            if (pagination >= 0) {
                state.CarouselList = [];
                for (let w = 0; w < pagination + 1; w++) {
                    var obj = {
                        details: [],
                    };
                    state.CarouselList.push(obj);
                }
            }
        }
        for (let e = 0; e < layoutLists.length; e++) {
            const pagination = layoutLists[e].pagination;
            var newsItem = {
                i: layoutLists[e].id,
                x: layoutLists[e].x,
                y: layoutLists[e].y,
                w: layoutLists[e].width,
                h: layoutLists[e].height,
                img: layoutLists[e].images,
                tag: layoutLists[e].tag,
                descriptive: layoutLists[e].descriptive,
            };
            state.CarouselList[pagination].details.push(newsItem);
        }
    }
    if (isBtn === "edit") {
        addHomePageLayout();
        state.templateID = isItem.id;
    }
    if (isBtn === "del") {
        // 删除模板
        Modal.confirm({
            title: "提示",
            type: "warning",
            content: "此操作将永久删除模板, 是否继续?",
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            onOk: () => {
                delTemplate(isItem.id.toString());
                message.success("删除成功！");
            },
            onCancel() {
                message.warning("已取消删除!");
            },
        });
    }
    if (isBtn === "adhibition") {
        // 应用到会议机
        state.applyToClassVisible = true;
        // 清空/重置数据
        state.publish.isAllBrand = true;
        state.publish.brandIds = [];
        state.brandId = [];
        state.result = [];
        // 合并对象
        Object.assign(state.publish, isItem);
        state.backdropPhoto = isItem.photo;
        const layoutLists = isItem.details;
        for (let i = 0; i < layoutLists.length; i++) {
            const pagination = layoutLists[i].pagination;
            if (pagination >= 0) {
                state.CarouselList = [];
                for (let w = 0; w < pagination + 1; w++) {
                    var obj = {
                        details: [],
                    };
                    state.CarouselList.push(obj);
                }
            }
        }
        for (let e = 0; e < layoutLists.length; e++) {
            const pagination = layoutLists[e].pagination;
            var newsItem = {
                i: layoutLists[e].id,
                x: layoutLists[e].x,
                y: layoutLists[e].y,
                w: layoutLists[e].width,
                h: layoutLists[e].height,
                img: layoutLists[e].images,
                tag: layoutLists[e].tag,
                descriptive: layoutLists[e].descriptive,
            };
            state.CarouselList[pagination].details.push(newsItem);
        }
    }
};

const delTemplate = async (id) => {
    let delObj = { id: id };
    await delClassTemplate(delObj);
    gainLayoutList();
};
const handel = (val) => {
    state.isEdit = val;
    stateShowNewPage.showNewPage = true;
    gainLayoutList();
};

// 下拉会议机input获取设备
const dropdownVisibleChange = useThrottleFn(async () => {
    const { direction, deviceType } = state;
    let obj = { direction, deviceType };
    const { data } = await getBindingSiteTree(obj);
    state.equipment = data;
}, 5000);
// 确认按钮应用到会议机
const notarize = async () => {
    // state.brandId.forEach((item)=>{
    //      getTreeItem(state.equipment, item);
    // })

    // 遍历item获取brandId
    if (state.publish.isAllBrand) {
        state.publish.brandIds = [];
    } else {
        // state.publish.brandIds = state.result.map((item)=> item.brandId)
        state.publish.brandIds = state.brandId;
    }
    const { publish, deviceType } = state;
    const params = { ...publish, deviceType };
    await publishClassTemplate(params);
    message.success("应用成功！");
    state.applyToClassVisible = false;
    gainLayoutList();
};

// 递归树形结构 item
const getTreeItem = (data, id) => {
    data.map((item) => {
        if (item.id === id) {
            state.result.push(item);
        } else {
            if (item.children) {
                getTreeItem(item.children, id);
            }
        }
    });
};

// 取消按钮
const abrogate = () => {
    state.applyToClassVisible = false;
};
onMounted(() => {
    gainLayoutList();
});
</script>

<style lang="less" scoped>
.homepage_container {
    padding: 16px;
    position: relative;

    .homepage_head_add {
        position: absolute;
        top: -30px;
        right: 16px;
    }

    .homepage_head {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}

.homepage_main {
    width: 100%;
    margin: 0px auto;
}

.imgBox {
    position: relative;
    width: 100%;
    height: 100%;

    .maskImg {
        display: none;
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        right: 0;
        top: 0;
        border: 0;
        background: rgba(0, 0, 0, 0.5);
    }
}

.moulde_export {
    margin-top: 10px;
    height: calc(78vh - 20px);
    overflow-y: auto;
}

.moulde_list_transverse {
    width: 380px;
    margin-bottom: 20px;
    margin-right: 24px;
    float: left;

    &:hover {
        cursor: pointer;

        .moulde_btn {
            display: block;
        }

        .maskImg {
            display: block;
        }
    }
}

.moulde_list_vertical {
    width: 222px;
    margin-bottom: 20px;
    margin-right: 45px;
    float: left;

    &:hover {
        cursor: pointer;

        .moulde_btn {
            display: block;
        }

        .maskImg {
            display: block;
        }
    }
}

.moulde_img {
    width: 100%;
    background-color: #ebeef5;
    border-radius: 4px;
    height: 222px;
    overflow: hidden;
    position: relative;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding-top: 20px;
}

.moulde_img img {
    width: 100%;
    height: 100%;
}

.moulde_img_vertical {
    width: 100%;
    background-color: #ebeef5;
    // height: 578px;
    height: 395px;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding-top: 20px;
}

.moulde_img_vertical img {
    width: 100%;
    height: 100%;
}

.moulde_btn {
    position: absolute;
    width: 24px;
    height: 24px;
    z-index: 100;
    top: 5px;
    right: 5px;
    cursor: pointer;
    display: none;
}

.class_transverse {
    width: 569px;
    height: 370px;
    margin: 0px auto 20px;
    border-radius: 4px;
    overflow: hidden;

    :deep(.slick-slider) {
        height: 370px;
        z-index: 100;
    }

    :deep(.slick-slider .slick-list) {
        height: 100%;
    }

    :deep(.slick-arrow.custom-slick-arrow) {
        width: 25px;
        height: 25px;
        font-size: 25px;
        color: #fff;
        background-color: rgba(31, 45, 61, 0.11);
        opacity: 0.3;
        z-index: 1;
    }

    :deep(.custom-slick-arrow:before) {
        display: none;
    }

    :deep(.custom-slick-arrow:hover) {
        opacity: 0.5;
    }
}

.class_vertical {
    width: 360px;
    margin: 0px auto 20px;
    height: 600px;
    border-radius: 5px;
    overflow: hidden;

    :deep(.slick-slider) {
        height: 600px;
        z-index: 100;
    }

    :deep(.slick-slider .slick-list) {
        height: 100%;
    }

    :deep(.slick-arrow.custom-slick-arrow) {
        width: 25px;
        height: 25px;
        font-size: 25px;
        color: #fff;
        background-color: rgba(31, 45, 61, 0.11);
        opacity: 0.3;
        z-index: 1;
    }

    :deep(.custom-slick-arrow:before) {
        display: none;
    }

    :deep(.custom-slick-arrow:hover) {
        opacity: 0.5;
    }
}

.choice {
    padding: 16px 0px 16px 0px;
}

.teFooter {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    text-align: center;
    border-top: 1px solid #e9e9e9;
    padding: 10px 16px;
    background: #fff;
    z-index: 1;
}

.no_dataLayout {
    text-align: center;

    img {
        margin-top: 200px;
        width: 180px;
    }

    p {
        color: rgba(0, 0, 0, 0.65);
    }
}

:deep(.add_edit) {
    position: absolute;
    top: 56px;
    left: 0;
    right: 0;
    height: calc(100vh - 129px);

    .container_main {
        background: transparent;
    }

    .homepageLeft_chlidmain {
        overflow: hidden auto;
    }

    .homepageRight_chlidmain {
        height: calc(100vh - 132px);
        overflow: hidden auto;
    }
}
</style>

<style lang="less">
.lookModel {
    .ant-modal-content {
        width: 100%;
        height: 100%;
    }
}

.hengscreen_contents {
    width: 880px;
    height: 495px;
    margin: 0 auto;
    position: relative;

    .ant-carousel {
        .slick-slider {
            z-index: 100;
            height: 495px;

            .slick-list {
                height: 100%;
            }
        }

        .slick-arrow.custom-slick-arrow {
            width: 25px;
            height: 25px;
            font-size: 25px;
            color: #fff;
            background-color: rgba(31, 45, 61, 0.11);
            opacity: 0.3;
            z-index: 1;
        }

        .custom-slick-arrow::before {
            display: none;
        }

        .custom-slick-arrow:hover {
            opacity: 0.5;
        }
    }
}

.shuscreen_contents {
    width: 278px;
    height: 495px;
    margin: 0 auto;
    position: relative;

    .ant-carousel {
        .slick-slider {
            z-index: 100;
            height: 495px;

            .slick-list {
                height: 100%;
            }

            .slick-arrow.custom-slick-arrow {
                width: 25px;
                height: 25px;
                font-size: 25px;
                color: #fff;
                background-color: rgba(31, 45, 61, 0.11);
                opacity: 0.3;
                z-index: 1;
            }

            .custom-slick-arrow::before {
                display: none;
            }

            .custom-slick-arrow:hover {
                opacity: 0.5;
            }
        }
    }
}

.applyToClass {
    .ant-drawer-content-wrapper {
        width: 610px !important;
    }

    .ant-drawer-close {
        position: absolute;
        right: 0;
    }
}
</style>
