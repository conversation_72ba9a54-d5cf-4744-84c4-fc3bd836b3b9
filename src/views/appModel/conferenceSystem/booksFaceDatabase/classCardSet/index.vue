<!-- The copied piece of shit cannot be modified. If it can only be used, use it -->
<template>
    <div>
        <div class="searchHead">
            <a-form layout="inline">
                <a-form-item label="方向：">
                    <a-select ref="select" v-model:value="state.pagination.direction" style="width: 120px">
                        <a-select-option value="">全部</a-select-option>
                        <a-select-option :value="0">横屏</a-select-option>
                        <a-select-option :value="1">竖屏</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="会议机名称：">
                    <a-input class="searchName" placeholder="请输入" v-model:value="state.pagination.brandName"
                        @pressEnter="queryStatisticsBtn" />
                </a-form-item>

                <a-form-item>
                    <a-button type="primary" style="margin-right: 12px" @click="queryStatisticsBtn">
                        <template #icon>
                            <SearchOutlined style="margin-right: 12px" />
                        </template>
                        查询
                    </a-button>
                    <a-button @click="resetStatistics">
                        <template #icon>
                            <redo-outlined style="margin-right: 12px" />
                        </template>
                        重置
                    </a-button>
                </a-form-item>
            </a-form>
            <div>
                <a-space>
                    <a-button @click="handlerExportClassSign">导出</a-button>
                    <a-dropdown class="marl10">
                        <template #overlay>
                            <a-menu @click="handleMenuClick">
                                <a-menu-item key="timing">批量设置定时开关机</a-menu-item>
                                <a-menu-item key="restart">批量远程重启</a-menu-item>
                                <a-menu-item key="shutdown">批量远程关机</a-menu-item>
                            </a-menu>
                        </template>
                        <a-button>批量设置</a-button>
                    </a-dropdown>
                    <a-button @click="addClassSign" type="primary"><template #icon> <plus-outlined />
                        </template>新增会议机</a-button>
                </a-space>
            </div>
        </div>
        <div class="classSignTable">
            <YTable :totals="state.pagination" :columns="classSignTable" :dataSource="state.signDataSource"
                :slots="['operation']" :rowSelection="true" @onSelectedArry="handerSelectedArry"
                @onSelectedRowKeys="handerSelectedRowKeys">
                <template #bodyCell="{ column, record, index, text }">
                    <template v-if="column.dataIndex === 'index'">{{
                        index + 1
                    }}</template>
                    <template v-else-if="column.dataIndex === 'classSignName'">
                        <Tooltip :title="record.brandName" />
                    </template>
                    <template v-else-if="column.dataIndex === 'bindBuilding'">
                        <Tooltip :title="record.siteName" />
                    </template>
                    <template v-else-if="column.dataIndex === 'account'">
                        {{ record.loginName }}
                        <span>{{ record.plaintextPassword }}</span>
                    </template>
                    <template v-else-if="column.dataIndex === 'condition'">
                        <a-tag class="tagsReset" :color="record.isOnline ? '#00B781' : '#BFBFBF'" />
                        {{ record.isOnline == true ? "在线" : "离线" }}
                    </template>
                    <template v-else-if="column.dataIndex === 'version'">
                        {{ record.apkVersion || "--" }}
                    </template>
                    <template v-else-if="column.dataIndex === 'direction'">
                        <div>
                            {{ record.direction === 0 ? "横屏" : "竖屏" }}
                        </div>
                    </template>
                    <template v-else-if="column.dataIndex === 'messageEntrance'">
                        <div>{{ record.isMessage ? "开" : "关" }}</div>
                    </template>
                    <template v-else-if="column.dataIndex === 'secondWeb'">
                        <div>{{ record.isSecond ? "开" : "关" }}</div>
                    </template>
                    <template v-else-if="column.dataIndex === 'operation'">
                        <span>
                            <a style="color: #00b781" @click="setOperation(record)">设置</a>
                        </span>
                    </template>
                </template>
            </YTable>
        </div>
        <!-- 新增会议机按钮弹框 -->
        <a-drawer class="drawerButton" destroyOnClose v-model:visible="state.visible" :maskClosable="false"
            title="新增会议机">
            <div class="choiceSite">
                选择场地：
                <a-tree-select style="width: 100%" tree-checkable multiple v-model:value="state.createSign.siteIds"
                    placeholder="请选择场地" allow-clear :tree-data="state.siteOptions" :fieldNames="fieldNames"
                    @dropdownVisibleChange="dropdownVisibleChange">
                    <template #title="title">
                        <home-outlined class="icon-yellow" />
                        {{ title.name }}
                    </template>
                </a-tree-select>
            </div>
            <div class="choice">
                <a-space align="center">
                    <span>设置会议机方向：</span>
                    <a-radio-group v-model:value="state.createSign.direction" name="direction">
                        <a-radio :value="0">横屏</a-radio>
                        <a-radio :value="1">竖屏</a-radio>
                    </a-radio-group>
                </a-space>
            </div>
            <template #footer>
                <div class="footer">
                    <a-button style="margin-right: 8px" @click="closeAdd">取消</a-button>
                    <a-button type="primary" :loading="state.btnLoading" @click="submitAdd">确认</a-button>
                </div>
            </template>
        </a-drawer>
        <!-- 批量设置按钮弹框 -->
        <a-drawer class="drawerButton" :maskClosable="false" v-model:visible="state.visibles" @close="closeBatch"
            :title="showTitle()">
            <a-form>
                <a-form-item v-if="state.showDrawer === 'attendClass'">
                    <a-space align="center">
                        <span>上课模式：</span>
                        <a-switch v-model:checked="state.isClassMode" checked-children="开" un-checked-children="关" />
                    </a-space>
                </a-form-item>
                <a-form-item :style="state.range === 'assign'
                    ? 'margin-bottom: 20px;'
                    : 'margin-bottom: 10px;'
                    ">
                    <div :style="state.range === 'assign'
                        ? 'margin-bottom: 20px;'
                        : 'margin-bottom: 0px;'
                        ">
                        <a-space align="center">
                            <span class="qitseb">选择范围：</span>
                            <a-radio-group v-model:value="state.range" name="range">
                                <a-radio value="all">全部会议机</a-radio>
                                <a-radio value="assign">指定会议机</a-radio>
                            </a-radio-group>
                        </a-space>
                    </div>
                    <div v-if="state.range === 'assign'">
                        <a-tree-select style="width: 100%" tree-checkable multiple v-model:value="state.batchNos"
                            placeholder="请选择会议机" allow-clear :tree-data="state.signOptions"
                            :fieldNames="batchNosFieldNames">
                            <template #title="title">
                                <home-outlined class="icon-yellow" />
                                {{ title.name || title.showName }}
                            </template>
                        </a-tree-select>
                    </div>
                </a-form-item>
                <a-form-item v-if="state.showDrawer === 'timing'" style="margin-bottom: -14px">
                    <div class="timerElect">
                        定时开关机时间
                        <span class="timingHint">（按照每周为单位循环）</span>
                    </div>
                    <a-radio-group v-model:value="state.switchsSte.openType" @change="onChangeSwitchsSte"
                        style="margin-bottom: 14px">
                        <a-radio :value="1">按天设置</a-radio>
                        <a-radio :value="2">按周设置</a-radio>
                    </a-radio-group>
                </a-form-item>
                <!-- <a-form-item style="margin-bottom: 14px" v-if="state.range !== 'assign'">
                    <div style="display: flex">
                        <span v-if="state.switchsSte.openType === 2" style="
                                color: #ff4d4f;
                                font-size: 14px;
                                line-height: 48px;
                                margin-right: 6px;
                            ">*</span>
                        <a-time-range-picker class="timePicker" :placeholder="['开机时间', '关机时间']" valueFormat="HH:mm"
                            format="HH:mm" separator="至" style="width: 100%; margin-top: 16px"
                            v-model:value="state.timer" />
                    </div>
                    <div v-if="state.timerBatchTip" style="color: #f5222d">
                        请输入开关机时间
                    </div>
                </a-form-item> -->
                <a-checkbox v-if="
                    state.showDrawer === 'timing' &&
                    state.switchsSte.openType === 1
                " v-model:checked="state.useEach" @change="useDays">应用到每天
                </a-checkbox>
                <a-form-item v-if="
                    state.showDrawer === 'timing' &&
                    state.switchsSte.openType === 1
                ">
                    <YTable :columns="batchColumns" :dataSource="batchData" :rowSelection="false">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.dataIndex === 'week'">
                                <div>{{ record.name }}</div>
                            </template>
                            <template v-if="column.dataIndex === 'starting'">
                                <span>
                                    <a-time-picker v-model:value="state.switchsSte.switchs[record.key]
                                        .openTime
                                        " valueFormat="HH:mm" format="HH:mm" />
                                </span>
                            </template>
                            <template v-if="column.dataIndex === 'shutdown'">
                                <span>
                                    <a-time-picker v-model:value="state.switchsSte.switchs[record.key]
                                        .closeTime
                                        " valueFormat="HH:mm" format="HH:mm" />
                                </span>
                            </template>
                            <template v-if="column.dataIndex === 'eliminate'">
                                <span>
                                    <a style="color: #f5222d" @click="clearDate(record)">清除</a>
                                </span>
                            </template>
                        </template>
                    </YTable>
                </a-form-item>
                <a-form-item v-if="
                    state.showDrawer === 'timing' &&
                    state.switchsSte.openType === 2
                ">
                    <YTable :columns="weekColumns" :dataSource="batchData" :rowSelection="false">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.dataIndex === 'week'">
                                <div>{{ record.name }}</div>
                            </template>
                            <template v-if="column.dataIndex === 'openType'">
                                <a-switch v-model:checked="state.switchsSte.switchs[record.key]
                                    .openStatus
                                    " checked-children="开" un-checked-children="关" />
                            </template>
                        </template>
                    </YTable>
                </a-form-item>
                <div v-if="state.showDrawer === 'timing'">
                    <exclamation-circle-filled style="color: #faad14" />
                    提示：定时开关机设置成功后，需会议机在线后生效
                </div>
            </a-form>
            <template #footer>
                <div class="footer">
                    <a-button style="margin-right: 8px" @click="closeBatch">取消</a-button>
                    <a-button type="primary" @click="submitBatch">确认</a-button>
                </div>
            </template>
        </a-drawer>
        <!-- 点击设置按钮弹框 -->
        <a-drawer class="drawerButton" :maskClosable="false" v-model:visible="state.setVisible" @close="closeSet"
            title="设置">
            <a-form>
                <a-form-item>
                    <div>会议机名称：</div>
                    <a-input v-model:value="state.brandName" placeholder="请选择" disabled />
                </a-form-item>
                <a-form-item>
                    <div>会议机账号：</div>
                    <a-input v-model:value="state.loginName" placeholder="请选择" disabled />
                </a-form-item>
                <a-form-item>
                    <div>管理密码：</div>
                    <a-input v-model:value="state.setSign.plaintextPassword" @input="accountInput" :maxlength="6"
                        placeholder="请选择" />
                </a-form-item>
                <a-form-item>
                    <div>设置会议机方向：</div>
                    <a-select v-model:value="state.setSign.direction">
                        <a-select-option :value="0">横屏</a-select-option>
                        <a-select-option :value="1">竖屏</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item style="margin-bottom: 5px">
                    <div class="timerElect">
                        定时开关机时间
                        <span class="timingHint">（按照每周为单位循环）</span>
                    </div>
                    <a-radio-group style="margin-bottom: 14px" v-model:value="state.setSign.openType"
                        @change="changeOpenType">
                        <a-radio :value="1">按天设置</a-radio>
                        <a-radio :value="2">按周设置</a-radio>
                    </a-radio-group>
                </a-form-item>
                <a-form-item style="margin-bottom: 14px">
                    <div style="display: flex">
                        <span v-if="state.setSign.openType === 2" style="
                                color: #ff4d4f;
                                font-size: 14px;
                                line-height: 32px;
                                margin-right: 6px;
                            ">*</span>
                        <a-time-range-picker :placeholder="['开机时间', '关机时间']" valueFormat="HH:mm" format="HH:mm"
                            separator="至" style="width: 100%" v-model:value="state.timer" />
                    </div>
                    <div v-if="state.timerTip" style="color: #f5222d">
                        请输入开关机时间
                    </div>
                </a-form-item>
                <a-checkbox style="margin-bottom: 14px" v-if="state.setSign.openType === 1"
                    v-model:checked="state.useEvery" @change="useEveryday">应用到每天</a-checkbox>
                <a-form-item v-if="state.setSign.openType === 1">
                    <YTable :columns="batchColumns" :dataSource="batchData" :rowSelection="false">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.dataIndex === 'week'">
                                <div>{{ record.name }}</div>
                            </template>
                            <template v-if="column.dataIndex === 'starting'">
                                <span>
                                    <a-time-picker v-model:value="state.setSign.switchs[record.key]
                                        .openTime
                                        " valueFormat="HH:mm" format="HH:mm" />
                                </span>
                            </template>
                            <template v-if="column.dataIndex === 'shutdown'">
                                <span>
                                    <a-time-picker v-model:value="state.setSign.switchs[record.key]
                                        .closeTime
                                        " valueFormat="HH:mm" format="HH:mm" />
                                </span>
                            </template>
                            <template v-if="column.dataIndex === 'eliminate'">
                                <span>
                                    <a style="color: #f5222d" @click="sweepDate(record)">清除</a>
                                </span>
                            </template>
                        </template>
                    </YTable>
                </a-form-item>
                <a-form-item v-if="state.setSign.openType === 2">
                    <YTable :columns="setWeekColumns" :dataSource="batchData" :rowSelection="false">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.dataIndex === 'week'">
                                <div>{{ record.name }}</div>
                            </template>
                            <template v-if="column.dataIndex === 'openStatus'">
                                <a-switch v-model:checked="state.setSign.switchs[record.key]
                                    .openStatus
                                    " checked-children="开" un-checked-children="关" />
                            </template>
                        </template>
                    </YTable>
                </a-form-item>
                <div style="margin-bottom: 24px">
                    <exclamation-circle-filled style="color: #faad14" />
                    提示：“会议机方向、消息入口、二级页面”入口修改后,会议机重启后可生效."定时开关机设置成功后",需会议机在线后生效
                </div>
            </a-form>
            <template #footer>
                <div class="footer">
                    <a-button style="margin-right: 8px" @click="closeSet">取消</a-button>
                    <a-button type="primary" @click="submitSet">确认</a-button>
                </div>
            </template>
        </a-drawer>
        <!-- 霸屏管理 -->
        <a-drawer class="drawer_button" :width="500" destroyOnClose title="霸屏管理" :maskClosable="false"
            :visible="state.baVisible" @close="onClose">
            <a-spin :spinning="state.dspinning">
                <a-form ref="setRef" layout="vertical" :model="state.setForm" :rules="state.setRules">
                    <a-form-item label="上课:" style="
                            display: flex;
                            flex-direction: row;
                            align-items: baseline;
                        ">
                        <a-switch v-model:checked="state.checked1" checked-children="开" un-checked-children="关"
                            style="margin-left: 20px" />
                    </a-form-item>
                    <a-form-item label="优先级:" name="classes" v-if="state.checked1">
                        <a-select placeholder="请选择优先级" v-model:value="state.setForm.classes">
                            <a-select-option :value="item" v-for="(item, index) in state.classesLevel" :key="index">{{
                                item
                            }}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="考试:" style="
                            display: flex;
                            flex-direction: row;
                            align-items: baseline;
                        ">
                        <a-switch v-model:checked="state.checked2" checked-children="开" un-checked-children="关"
                            style="margin-left: 20px" />
                    </a-form-item>
                    <a-form-item label="优先级:" name="exam" v-if="state.checked2">
                        <a-select placeholder="请选择优先级" v-model:value="state.setForm.exam">
                            <a-select-option :value="item" v-for="(item, index) in state.examLevel" :key="index">{{ item
                                }}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="信息发布:" style="
                            display: flex;
                            flex-direction: row;
                            align-items: baseline;
                        ">
                        <a-switch v-model:checked="state.checked3" checked-children="开" un-checked-children="关"
                            style="margin-left: 20px" />
                    </a-form-item>
                    <a-form-item label="优先级:" name="mess" v-if="state.checked3">
                        <a-select placeholder="请选择优先级" v-model:value="state.setForm.mess">
                            <a-select-option :value="item" v-for="(item, index) in state.messLevel" :key="index">{{ item
                                }}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="自定义霸屏:" style="
                            display: flex;
                            flex-direction: row;
                            align-items: baseline;
                        ">
                        <a-switch v-model:checked="state.checked4" checked-children="开" un-checked-children="关"
                            style="margin-left: 20px" />
                    </a-form-item>
                    <a-form-item label="优先级:" name="custom" v-if="state.checked4">
                        <a-select placeholder="请选择优先级" v-model:value="state.setForm.custom">
                            <a-select-option :value="item" v-for="(item, index) in state.customLevel" :key="index">{{
                                item
                            }}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="设置轮播时长:" name="swiperTime">
                        <a-select placeholder="请选择轮播时长" v-model:value="state.setForm.swiperTime">
                            <a-select-option :value="10">10 s/张</a-select-option>
                            <a-select-option :value="20">20 s/张</a-select-option>
                            <a-select-option :value="30">30 s/张</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-form>
            </a-spin>
            <template #footer>
                <div class="footer" style="text-align: center">
                    <a-button style="margin-right: 8px" @click="onClose">取消</a-button>
                    <a-button type="primary" @click="onOk">确定</a-button>
                </div>
            </template>
        </a-drawer>
    </div>
</template>

<script>
import { defineComponent, reactive, onMounted, ref, toRaw, watch } from "vue";
import YTable from "@/components/YTable/index.vue";
import { getColumnCheckboxGroupFilterDropdown } from "@/components/FilterDropdown.jsx";
import { message } from "ant-design-vue";
import {
    ClassSignList,
    createClassSign,
    batchSet,
    updateClassSign,
    getBrandInfo,
    getUnSite,
    getSite,
    longRestart,
    longclose,
    classModeUpdate,
    setBrandDominateScreen,
    getBindingSiteTreeeV2,
    getBrandDominateScreen,
    brandClassSignExport
} from "@/api/classSign";
import { getDeviceTreeV3 } from "@/api/notice";
import { useRoute } from 'vue-router'
export default defineComponent({
    name: "classCardSet",
});
</script>

<script setup>
// table的列数据
const classSignTable = [
    {
        title: "序号",
        key: "index",
        dataIndex: "index",
        ellipsis: true,
        width: 100,
        fixed: "left",
    },
    {
        title: "会议机名称",
        dataIndex: "classSignName",
        width: 150,
    },
    {
        title: "绑定楼宇",
        dataIndex: "bindBuilding",
        width: 200,
    },
    {
        title: "账号密码",
        dataIndex: "account",
    },
    {
        title: "状态",
        dataIndex: "condition",
        filters: [
            {
                text: "全部",
                value: "",
            },
            {
                text: "在线",
                value: 1,
            },
            {
                text: "离线",
                value: 0,
            },
        ],
        // 超出宽度显示省略号
        // ellipsis: true
        filterDropdown: (vnode) => {
            if (!vnode.selectedKeys.length) {
                vnode.selectedKeys = "";
            } else {
                vnode.selectedKeys = vnode.selectedKeys[0];
            }
            return getColumnCheckboxGroupFilterDropdown(vnode);
        },
        customFilterDropdown: true,
        customFilterIcon: "caret-right-outlined",
    },
    {
        title: "版本号",
        dataIndex: "version",
    },
    {
        title: "方向",
        dataIndex: "direction",
    },
    {
        title: "操作",
        dataIndex: "operation",
        width: 80,
    },
];

const batchColumns = [
    { title: "星期", dataIndex: "week" },
    { title: "开机时间", dataIndex: "starting" },
    { title: "关机时间", dataIndex: "shutdown" },
    { title: "", dataIndex: "eliminate" },
];
const weekColumns = [
    { title: "星期", dataIndex: "week" },
    {
        title: "开关机设置",
        dataIndex: "openType",
        align: "right",
    },
];
const setWeekColumns = [
    { title: "星期", dataIndex: "week" },
    {
        title: "开关机设置",
        dataIndex: "openStatus",
        align: "right",
    },
];
const batchData = [
    {
        key: "0",
        name: "星期一",
        openStatus: false,
    },
    {
        key: "1",
        name: "星期二",
        openStatus: false,
    },
    {
        key: "2",
        name: "星期三",
        openStatus: false,
    },
    {
        key: "3",
        name: "星期四",
        openStatus: false,
    },
    {
        key: "4",
        name: "星期五",
        openStatus: false,
    },
    {
        key: "5",
        name: "星期六",
        openStatus: false,
    },
    {
        key: "6",
        name: "星期日",
        openStatus: false,
    },
];
const batchNosFieldNames = {
    label: "name",
    value: "id",
    children: "children",
};
const fieldNames = {
    label: "name",
    value: "id",
    children: "children",
};

const setRef = ref();
const state = reactive({
    btnLoading: false,
    deviceType: 9,
    dspinning: false,
    // 霸屏-----start
    bap: "",
    bapId: [],
    baVisible: false,
    classesLevel: [1, 2, 3, 4, 5, 6, 7, 8, 9],
    examLevel: [1, 2, 3, 4, 5, 6, 7, 8, 9],
    messLevel: [1, 2, 3, 4, 5, 6, 7, 8, 9],
    customLevel: [1, 2, 3, 4, 5, 6, 7, 8, 9],
    setForm: {
        swiperTime: 10,
    },
    setRules: {
        classes: [{ required: true, message: "请选择" }],
        exam: [{ required: true, message: "请选择" }],
        mess: [{ required: true, message: "请选择" }],
        custom: [{ required: true, message: "请选择" }],
        swiperTime: [{ required: true, message: "请选择" }],
    },
    checked1: false,
    checked2: false,
    checked3: true,
    checked4: true,
    // 霸屏-----end
    isMusts: true,
    isMust: true,
    timerBatchTip: false,
    timerTip: false,
    isClassMode: false,
    site: [],
    showDrawer: "",
    visible: false,
    visibles: false,
    setVisible: false,
    timedTask: false,
    timer: null,
    range: "all",
    useEach: false,
    useEvery: false,
    signDataSource: [],
    siteOptions: [],
    signOptions: [],
    brandName: "",
    loginName: "",
    // 新增会议机 参数
    createSign: {
        // 会议机方向 0:横 1：竖
        direction: 0,
        // 是否开启消息1：开 0不开
        isMessage: 1,
        // 是否开启二级页面1：开 0不开
        isSecond: 1,
        // 场地信息
        siteIds: [],
    },
    // 批量设置定时开关机
    switchsSte: {
        // 会议机ID，非必填，批量设置全部会议机设备不传此值
        brandIds: [],
        openType: 1,
        openTimeList: [],
        switchs: [
            {
                week: 1,
                openTime: "",
                closeTime: "",
                openStatus: false,
            },
            {
                week: 2,
                openTime: "",
                closeTime: "",
                openStatus: false,
            },
            {
                week: 3,
                openTime: "",
                closeTime: "",
                openStatus: false,
            },
            {
                week: 4,
                openTime: "",
                closeTime: "",
                openStatus: false,
            },
            {
                week: 5,
                openTime: "",
                closeTime: "",
                openStatus: false,
            },
            {
                week: 6,
                openTime: "",
                closeTime: "",
                openStatus: false,
            },
            {
                week: 7,
                openTime: "",
                closeTime: "",
                openStatus: false,
            },
        ],
    },
    brandIdArr: [],
    brandIdArrs: [],
    batchNos: [],
    result: [],
    Nos: [],
    pagination: {
        direction: "",
        brandName: "",
        pageNo: 1,
        pageSize: 10,
    },
    // 设置会议机
    setSign: {
        direction: null,
        plaintextPassword: "",
        isMessage: null,
        isSecond: null,
        id: "",
        openType: 1, // 1为按天、2为按周
        switchs: [
            {
                id: "",
                week: "1",
                openTime: "",
                closeTime: "",
                openStatus: false,
            },
            {
                id: "",
                week: "2",
                openTime: "",
                closeTime: "",
                openStatus: false,
            },
            {
                id: "",
                week: "3",
                openTime: "",
                closeTime: "",
                openStatus: false,
            },
            {
                id: "",
                week: "4",
                openTime: "",
                closeTime: "",
                openStatus: false,
            },
            {
                id: "",
                week: "5",
                openTime: "",
                closeTime: "",
                openStatus: false,
            },
            {
                id: "",
                week: "6",
                openTime: "",
                closeTime: "",
                openStatus: false,
            },
            {
                id: "",
                week: "7",
                openTime: "",
                closeTime: "",
                openStatus: false,
            },
        ],
    },
    saveParams: {}, // 保存批量霸屏保存的数据
});

/**
 * @description: 设置选择按天按月 （批量）
 * @return {*}
 */
const onChangeSwitchsSte = () => {
    state.timerBatchTip = false;
    state.timer = [];
    state.switchsSte.switchs = state.switchsSte.switchs.map((item, index) => {
        return {
            week: item.week,
            openStatus: false,
            closeTime: "",
            openTime: "",
            openType: item.openType,
        };
    });
};

/**
 * @description: 设置选择按天按月 （单个）
 * @param {*} val val.target.value  1为按天 2为按月
 * @return {*}
 */
const changeOpenType = (val) => {
    state.timerTip = false;
    state.timer = [];
    state.setSign.switchs = state.setSign.switchs.map((item, index) => {
        return {
            week: item.week,
            openStatus: false,
            closeTime: "",
            openTime: "",
            openType: item.openType,
            id: item.id,
        };
    });
};

// 抽屉标题
const showTitle = () => {
    if (state.showDrawer === "timing") {
        return "批量设置定时开关机";
    } else if (state.showDrawer === "restart") {
        return "批量远程重启";
    } else if (state.showDrawer === "shutdown") {
        return "批量远程关机";
    } else {
        return "上课模式";
    }
};
const findId = (V) => {
    V.forEach((item) => {
        if (item.type == 4) {
            state.brandIdArr.push(item.id);
        }
        if (item.children) {
            findId(item.children);
        }
    });
    return state.brandIdArr;
};

// 获取场地会议机列表
const getSignSiteNew = () => {
    const obj = { direction: "" };
    getBindingSiteTreeeV2(obj).then((res) => {
        state.brandIdArrs = findId(res.data);
    });
};
const _deviceNosObj = shallowRef({});

// 递归获取设备id
const findIdBatchNos = (data, nodeId) => {
    data.forEach((item) => {
        if (item?.no == nodeId) {
            if (!_deviceNosObj.value[item.id]) {
                state.batchNos.push(item.id);
                _deviceNosObj.value[item.id] = item.id
            }
        }
        if (item.children) {
            findIdBatchNos(item.children, nodeId);
        }
    });
};
// 列表的勾选
const handerSelectedArry = (bapIds, data) => {
    state.bapId = bapIds;
    state.batchNos = []
    let _deviceNos = []
    if (data?.length) {
        state.range = 'assign'
        data.forEach(item => {
            if (item.deviceNos.length) {
                _deviceNos = [..._deviceNos, ...item.deviceNos]
            }
        })
        _deviceNosObj.value = {}
        for (let i = 0; i < _deviceNos.length; i++) {
            findIdBatchNos(state.signOptions, _deviceNos[i])
        }
    }
};
watch(
    () => [state.checked1, state.checked2, state.checked3, state.checked4],
    (n, o) => {
        if (!n.includes(true)) {
            state.setRules.swiperTime[0].required = false;
        } else {
            state.setRules.swiperTime[0].required = true;
        }
    },
    { immediate: true, deep: true }
);
function getcode(arr) {
    const codearr = ["classes", "exam", "mess", "custom"];
    const ar = [];
    codearr.forEach((item) => {
        ar.push({
            code: item,
            level: null,
            duration: null,
            checked: false,
        });
    });
    ar.forEach((item, index) => {
        arr.forEach((e) => {
            if (item.code === e.code) {
                ar[index] = e;
                ar[index].checked = true;
            }
        });
    });
    return ar;
}

// 获取霸屏设置的信息
const getBrandDominateScreenFn = () => {
    state.dspinning = true;
    getBrandDominateScreen()
        .then((res) => {
            const bapData = res.data;
            // 全关 bapData = []
            if (bapData.length === 0) {
                state.checked1 = false;
                state.checked2 = false;
                state.checked3 = false;
                state.checked4 = false;
                state.setForm.classes = "";
                state.setForm.exam = "";
                state.setForm.mess = "";
                state.setForm.custom = "";
                if (window.localStorage.getItem("swiperTime")) {
                    const str =
                        window.localStorage.getItem("swiperTime") + " s/张";
                    const newStr = [...new Set(str)].join("");
                    state.setForm.swiperTime = newStr;
                } else {
                    state.setForm.swiperTime = 10;
                }
                return;
            }
            // 半关
            if (bapData.length !== 0) {
                const arrs = getcode(bapData);
                state.checked1 = arrs[0].checked;
                state.checked2 = arrs[1].checked;
                state.checked3 = arrs[2].checked;
                state.checked4 = arrs[3].checked;
                state.setForm.classes = arrs[0].level;
                state.setForm.exam = arrs[1].level;
                state.setForm.mess = arrs[2].level;
                state.setForm.custom = arrs[3].level;
                arrs.forEach((item, index) => {
                    if (item.duration) {
                        state.setForm.swiperTime = item.duration;
                    }
                });
            }
            state.dspinning = false;
        })
        .finally(() => {
            state.dspinning = false;
        });
};

// 霸屏管理
const baScreen = () => {
    state.baVisible = true;
    getBrandDominateScreenFn();
};

const getParams = () => {
    const newData = [];
    const arr = [
        state.checked1,
        state.checked2,
        state.checked3,
        state.checked4,
    ];
    const obj1 = {
        code: "classes",
        level: state.setForm.classes,
    };
    const obj2 = {
        code: "exam",
        level: state.setForm.exam,
    };
    const obj3 = {
        code: "mess",
        level: state.setForm.mess,
    };
    const obj4 = {
        code: "custom",
        level: state.setForm.custom,
    };
    const parArr = [obj1, obj2, obj3, obj4];

    arr.forEach((i, idx) => {
        if (i) {
            newData.push(parArr[idx]);
        }
    });
    return newData;
};

// 确定霸屏
const onOk = () => {
    setRef.value.validate().then(() => {
        const params = {
            deviceType: state.deviceType,
            duration: parseFloat(state.setForm.swiperTime),
            details: getParams(),
            brandIds: toRaw(state.brandIdArrs),
        };
        setBrandDominateScreen(params).then((res) => {
            state.baVisible = false;
            if (res.code === 0) {
                message.success(res.message);
                state.saveParams = params;
                window.localStorage.setItem(
                    "swiperTime",
                    state.setForm.swiperTime
                );
                queryStatistics();
            } else {
                message.warning(res.message);
            }
        });
    });
};
// 取消霸屏
const onClose = () => {
    setRef.value.resetFields();
    state.checked1 = false;
    state.checked2 = false;
    state.baVisible = false;
};
// 点击设置
const setOperation = (val) => {
    getClassBrandInfo(val.id);
    state.setVisible = true;
    state.useEvery = false;
};

// 获取会议机信息
const getClassBrandInfo = async (id) => {
    getBrandInfo({ id }).then((res) => {
        const { data } = res;
        state.brandName = data.brandName;
        state.loginName = data.loginName;
        state.setSign.plaintextPassword = data.plaintextPassword;
        state.setSign.direction = data.direction;
        state.setSign.isMessage = data.isMessage ? 1 : 0;
        state.setSign.isSecond = data.isSecond ? 1 : 0;
        state.setSign.id = data.id;
        state.setSign.switchs = data.switchs;
        state.setSign.openType = data.openType;
        state.timer = data.openType === 2 ? data.openTimeList : null;
    });
};

// 点击新增会议机
const addClassSign = () => {
    // 重置默认值
    state.createSign.direction = 0;
    state.createSign.isMessage = 1;
    state.createSign.isSecond = 1;
    state.createSign.siteIds = [];
    // 显示抽屉弹框
    state.visible = true;
};

// // 取消抽屉
// const closeVisible = () => {
//     state.visible = false
//     state.visibles = false
//     state.useEach = false
// }

// 批量按钮
const handleMenuClick = (val) => {
    state.showDrawer = val.key;
    state.visibles = true;
    if (state.showDrawer === "timing") {
        const weeks = ["1", "2", "3", "4", "5", "6", "7"];
        const weeksArr = weeks.map((item, index) => {
            return {
                id: "",
                week: item,
                openTime: "",
                closeTime: "",
                openStatus: false,
            };
        });
        state.switchsSte.switchs = weeksArr;
        state.switchsSte.openTimeList = [];
        state.timer = [];
    }
};

// 查询会议机列表
const queryStatistics = async () => {
    const params = {
        deviceType: state.deviceType,
        ...state.pagination,
    };
    const { data } = await ClassSignList(params);
    state.pagination.total = data.total;
    state.signDataSource = data.list;
};

// 查询按钮
const queryStatisticsBtn = async () => {
    state.pagination.pageNo = 1;
    queryStatistics();
};
// 重置会议机
const resetStatistics = async () => {
    state.pagination.direction = "";
    state.pagination.brandName = "";
    state.pagination.deviceType = state.deviceType;
    state.pagination.pageNo = 1;
    state.pagination.pageSize = 10;
    const { data } = await ClassSignList(state.pagination);
    state.signDataSource = data.list;
};

// 切换分页
const handerSelectedRowKeys = async (res) => {
    const { current, pageSize, condition } = res;
    state.pagination.pageNo = current;
    state.pagination.pageSize = pageSize;
    state.pagination.isOnline = condition ? condition[0] : "";
    queryStatistics();
};

// 新增会议机
const submitAdd = () => {
    const params = {
        ...state.createSign,
        deviceType: state.deviceType,
    };
    state.btnLoading = true;
    createClassSign(params)
        .then((res) => {
            message.success(res.message);
            state.visible = false;
            queryStatistics();
        })
        .finally(() => (state.btnLoading = false));
};

// 批量设置开关机日期
const submitBatch = async () => {
    // 遍历v-model选择场地id 数组 获取每一项item
    state.batchNos.forEach((item) => {
        getTreeItem(state.signOptions, item);
    });
    // 遍历item获取设备id
    let Nos = []
    let brandIdNos = []
    state.result.forEach((item) => {
        if (item.no) {
            Nos.push(item.no)
        }
        if (item.brandId) {
            brandIdNos.push(item.brandId)
        }
    });
    state.switchsSte.brandIds = brandIdNos;
    const batchData = {
        isAllBrand: state.range === "all",
        deviceNos: Nos,
    };

    const classMode = {
        isAllBrand: state.range === "all",
        deviceNos: Nos,
        brandIds: brandIdNos,
        isClassMode: state.isClassMode,
    };

    if (state.showDrawer === "timing") {
        if (state.switchsSte.openType === 2) {
            if (state.timer === null || state.timer.length === 0) {
                state.timerBatchTip = true;
            } else {
                state.timerBatchTip = false;
                state.switchsSte.switchs = state.switchsSte.switchs.map(
                    (item) => {
                        return {
                            closeTime:
                                item.openStatus === true ? state.timer[1] : "",
                            id: item.id,
                            openTime:
                                item.openStatus === true ? state.timer[0] : "",
                            openStatus: item.openStatus || false,
                            week: item.week,
                        };
                    }
                );
                await batchSet({
                    ...state.switchsSte,
                    openTimeList:
                        state.timer == null
                            ? []
                            : [state.timer[0], state.timer[1]],
                });
                message.success("批量设置开关机成功！");
            }
        } else {
            state.isMusts = true;
            state.switchsSte.switchs.forEach((item) => {
                if (
                    Boolean(item.openTime) !== false &&
                    Boolean(item.closeTime) === false
                ) {
                    state.isMusts = false;
                    message.destroy();
                    message.error("同一天的开机时间和关机时间需必填");
                }
                if (
                    Boolean(item.openTime) === false &&
                    Boolean(item.closeTime) !== false
                ) {
                    state.isMusts = false;
                    message.destroy();
                    message.error("同一天的开机时间和关机时间需必填");
                }
                state.visibles = true;
            });

            if (state.isMusts) {
                await batchSet({
                    deviceType: state.deviceType,
                    ...state.switchsSte,
                    openTimeList:
                        state.timer == null
                            ? []
                            : [state.timer[0], state.timer[1]],
                });
                message.success("批量设置开关机成功！");
            }
        }
    } else if (state.showDrawer === "restart") {
        const params = {
            deviceType: state.deviceType,
            ...batchData,
        };
        await longRestart(params);
        message.success("批量远程重启成功！");
    } else if (state.showDrawer === "shutdown") {
        const params1 = {
            deviceType: state.deviceType,
            ...batchData,
        };
        await longclose(params1);
        message.success("批量远程关机成功！");
    } else if (state.showDrawer === "attendClass") {
        const params2 = {
            deviceType: state.deviceType,
            ...classMode,
        };
        await classModeUpdate(params2);
        if (classMode.isClassMode) {
            message.success("上课模式已开启！");
        } else {
            message.success("上课模式已关闭！");
        }
    }
    if (state.timerBatchTip === false) {
        closeBatch();
        queryStatistics();
    }
    // state.visibles = false;
};

// 递归树形结构 item
const getTreeItem = (data, id) => {
    data.map((item) => {
        if (item.id === id) {
            state.result.push(item);
        } else {
            if (item.children) {
                getTreeItem(item.children, id);
            }
        }
    });
};

// 取消新增会议机对话框
const closeAdd = () => {
    state.visible = false;
};

// 取消批量开关机日期
const closeBatch = () => {
    state.range = "all";
    state.batchNos = [];
    state.result = [];
    state.switchsSte.brandIds = [];
    state.visibles = false;
    state.useEach = false;
};

// 取消设置弹窗按钮
const closeSet = () => {
    state.setVisible = false;
    state.useEvery = false;
};

// 确认设置按钮
const submitSet = async () => {
    if (state.setSign.openType === 2) {
        if (state.timer === null || state.timer.length === 0) {
            state.timerTip = true;
        } else {
            state.timerTip = false;
            state.setSign.switchs = state.setSign.switchs.map((item) => {
                return {
                    closeTime: item.openStatus === true ? state.timer[1] : "",
                    id: item.id,
                    openTime: item.openStatus === true ? state.timer[0] : "",
                    openStatus: item.openStatus || false,
                    week: item.week,
                };
            });
            state.setSign.isMessage = state.setSign.isMessage === 1;
            state.setSign.isSecond = state.setSign.isSecond === 1;
            await updateClassSign({
                deviceType: state.deviceType,
                ...state.setSign,
                openTimeList:
                    state.timer == null ? [] : [state.timer[0], state.timer[1]],
            });

            state.setVisible = false;
            message.success("设置会议机成功！");
            const params = {
                deviceType: state.deviceType,
                ...state.pagination,
            };
            const { data } = await ClassSignList(params);
            state.signDataSource = data.list;
        }
    } else {
        state.isMust = true;
        state.setSign.switchs.forEach((item) => {
            if (
                Boolean(item.openTime) !== false &&
                Boolean(item.closeTime) === false
            ) {
                state.isMust = false;
                message.destroy();
                message.error("同一天的开机时间和关机时间需必填");
            }
            if (
                Boolean(item.openTime) === false &&
                Boolean(item.closeTime) !== false
            ) {
                state.isMust = false;
                message.destroy();
                message.error("同一天的开机时间和关机时间需必填");
            }
        });
        if (state.isMust) {
            state.setSign.isMessage = state.setSign.isMessage === 1;
            state.setSign.isSecond = state.setSign.isSecond === 1;
            await updateClassSign({
                deviceType: state.deviceType,
                ...state.setSign,
                openTimeList:
                    state.timer == null ? [] : [state.timer[0], state.timer[1]],
            });
            state.setVisible = false;
            message.success("设置会议机成功！");
            const params = {
                deviceType: state.deviceType,
                ...state.pagination,
            };
            const { data } = await ClassSignList(params);
            state.signDataSource = data.list;
        }
    }
};

// 应用会议机到每一天
const useDays = (e) => {
    if (state.useEach) {
        state.switchsSte.switchs.forEach((item) => {
            item.openTime = state.timer[0];
            item.closeTime = state.timer[1];
        });
    } else {
        state.switchsSte.switchs.forEach((item) => {
            item.openTime = "";
            item.closeTime = "";
        });
    }
};
const useEveryday = () => {
    if (state.useEvery) {
        state.setSign.switchs.forEach((item) => {
            item.openTime = state.timer[0];
            item.closeTime = state.timer[1];
        });
    } else {
        state.setSign.switchs.forEach((item) => {
            item.openTime = "";
            item.closeTime = "";
        });
    }
};

// 清除日期
const clearDate = (val) => {
    state.switchsSte.switchs[val.key].openTime = "";
    state.switchsSte.switchs[val.key].closeTime = "";
};

const sweepDate = (val) => {
    state.setSign.switchs[val.key].openTime = "";
    state.setSign.switchs[val.key].closeTime = "";
};

// 获取未创建会议机账号的场地列表 新增时用
const getNoSignSite = async () => {
    const { data } = await getUnSite({ type: 9 });
    state.siteOptions = data;
};

// 获取场地会议机列表列表 设置的时候用

// 1-人脸机 2-班牌 3-考勤机 4-中性版班牌 5-一体机 6-自助借还书机 7-访客机 8-门禁机 9-会议机 10-鸿蒙班牌
// 20-馆员工作站 22-查询机 23-盘点车 24-安全门 25-德育兑换机 26-微型图书馆 27-手持盘点仪 28-摄像机 29-德育积分机 30-VMS


const route = useRoute()
const getSignSite = async () => {
    let params = { type: "9" };
    let Api = getDeviceTreeV3
    if (route.query.codes == 'meetingSystem') {
        Api = getSite
        params.deviceType = "9"
    }
    const { data } = await Api(params);
    state.signOptions = data;
};


const dropdownVisibleChange = () => {
    getNoSignSite();
};

// 密码限制
const accountInput = (e) => {
    const codeReg = new RegExp("[A-Za-z0-9]+");
    const len = e.target.value.length;
    let str = "";
    for (let i = 0; i < len; i++) {
        if (codeReg.test(e.target.value[i])) {
            str += e.target.value[i];
        }
    }
    state.setSign.plaintextPassword = str;
};

// 导出班牌
const handlerExportClassSign = async () => {
    await brandClassSignExport({ deviceType: 9 }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })
        )
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '会议机信息表.xlsx')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    })
}
onMounted(() => {
    queryStatistics();
    getSignSite();
    getSignSiteNew();
    // getBrandDominateScreenFn()
});
</script>

<style lang="less" scoped>
.classSignTable {
    margin: 0 16px;
}

.searchHead {
    /* padding-bottom: 16px; */
    display: flex;
    justify-content: space-between;
    margin: 16px;
}

.headContent {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .userInfoAtt {
        margin-left: 16px;
    }
}

.footer {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    text-align: center;
    border-top: 1px solid #e9e9e9;
    padding: 10px 16px;
    background: #fff;
    z-index: 1;
}

.attSign {
    padding-top: 4px;
}

.signResultLine {
    cursor: pointer;
    text-decoration: underline;
}

.searchName {
    width: 180px;
    height: 32px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
}

.choice {
    margin-bottom: 20px;
}

.choiceSite {
    margin-bottom: 20px;
}

.timerElect {
    padding-bottom: 12px;

    // font-size: 14px;
    // font-weight: 500;
    // line-height: 20px;
    // color:rgba(0, 0, 0, 0.85);
    .timingHint {
        color: rgba(0, 0, 0, 0.25);
    }
}
</style>
<style lang="less">
.timePicker {
    .ant-picker-input input {
        text-align: center;
    }
}

.drawerButton {
    .ant-drawer-content-wrapper {
        width: 570px !important;
    }

    .ant-drawer-close {
        position: absolute;
        right: 0;
    }

    // .editButton {
    //     width: 100%;
    //     border-top: 1px solid #d9d9d9;
    //     padding: 20px 0;
    //     display: flex;
    //     justify-content: center;
    //     align-items: center;
    // }
}

.drawer_button {
    .footer {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 100%;
        text-align: center !important;
        border-top: 1px solid #e9e9e9;
        padding: 10px 16px;
        background: #fff;
        z-index: 11;
    }
}
</style>
<style lang="less">
.ant-drawer-close {
    position: absolute !important;
    right: 0 !important;
}
</style>
