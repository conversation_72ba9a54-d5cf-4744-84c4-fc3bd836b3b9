<!-- introduce 场地介绍 -->
<template>
    <a-modal
        :maskClosable="false"
        :destroyOnClose="true"
        v-model:visible="props.visibleProps"
        :confirmLoading="state.siteFormLoading"
        :title="state.siteTitle"
        width="784px"
        @ok="siteOk"
        @cancel="cancelSite"
    >
        <a-form
            layout="vertical"
            name="base"
            ref="transitionFromRef"
            :model="state.introduceForm"
        >
            <a-form-item label="场地图片" name="buildingId">
                <div class="upload_wap">
                    <a-spin
                        :tip="`${state.progress}%`"
                        :indicator="indicator"
                        :spinning="state.spinning"
                    >
                        <div class="upload">
                            <a-upload
                                :multiple="true"
                                :showUploadList="false"
                                :file-list="state.fileList"
                                name="file"
                                action="/"
                                :before-upload="beforeUpload"
                                @change="handleChange"
                                class="upload_comp"
                                accept="image/png,image/jpg,image/jpeg"
                            >
                                <a-button
                                    type="primary"
                                    ghost
                                    style="margin-top: 75px"
                                    :disabled="
                                        state.imgPathsList.length >
                                        state.imgNumber - 1
                                    "
                                    @click="state.image1M = false"
                                >
                                    <template #icon><plus-outlined /></template>
                                    上传图片
                                </a-button>
                            </a-upload>
                            <p class="upload_format__hint upload_hint__rule">
                                最多只能上传{{
                                    state.imgNumber
                                }}张图片，大小不超过10M
                            </p>
                        </div>
                        <!-- 已上传图片 -->
                        <a-row :gutter="[24, 24]" class="upload_list">
                            <a-col
                                :span="4"
                                v-for="(item, index) in state.imgPathsList"
                                :key="index"
                            >
                                <div class="upload_item__warp">
                                    <div class="upload_item_image__warp">
                                        <a-image
                                            class="upload_item_image"
                                            :preview="false"
                                            width="86px"
                                            height="86px"
                                            :src="item"
                                        ></a-image>
                                        <!-- remove -->
                                        <close-circle-filled
                                            class="upload_item__remove"
                                            title="删除"
                                            @click="removeUpload(index)"
                                        />
                                    </div>
                                </div>
                            </a-col>
                        </a-row>
                    </a-spin>
                </div>
            </a-form-item>
            <a-form-item label="场地介绍" name="buildingId">
                <a-textarea
                    v-model:value="state.introduceForm.describe"
                    placeholder="请输入"
                    :maxlength="300"
                    showCount
                    :auto-size="{ minRows: 2, maxRows: 10 }"
                />
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<script setup>
import { reactive, watch } from "vue";
import { message } from "ant-design-vue";

import { siteDetails } from "@/api/site.js";

import { getFileUpload } from "@/api/notice";
const props = defineProps({
    visibleProps: {
        type: Boolean,
        default: true,
    },
    detailId: {
        type: String,
        default: "",
    },
});
const emit = defineEmits(["update:visibleProps", "introduceForm"]);
const state = reactive({
    image1M: false,
    imgNumber: 6,
    visible: true,
    siteTitle: "场地简介和图片",
    multiple: false,
    progress: 0,
    spinning: false,
    siteFormLoading: false,
    fileList: [],
    imgPathsList: [],
    introduceForm: {
        imgPaths: "",
        describe: null,
    },
});

const cancelSite = () => {
    emit("update:visibleProps", false);
};
const siteOk = () => {
    cancelSite();
    state.introduceForm.imgPaths = "";
    if (state.imgPathsList.length) {
        state.introduceForm.imgPaths = state.imgPathsList.toString();
    }
    emit("introduceForm", state.introduceForm);
};

const beforeUpload = (file, fileList) => {
    const total = state.imgPathsList.length + fileList.length;
    if (total > state.imgNumber) {
        if (!state.image1M) {
            message.warning(
                `当前限制选择 ${state.imgNumber} 个文件，本次选择了 ${
                    state.imgPathsList.length
                } 个文件，共选择了 ${
                    state.imgPathsList.length + fileList.length
                } 个文件`
            );
        }
        state.image1M = true;
        return true;
    }

    const isLt1M = file.size / 1024 / 1024 < 10;
    if (!isLt1M) {
        state.image1M = true;
        message.warning("上传图像须小于1MB！");
    } else {
        state.image1M = false;
    }
    return false;
};
const handleChange = async (info) => {
    if (state.image1M) {
        return;
    }
    const params = new FormData();
    params.append("file", info.file);
    await getFileUpload(params).then((res) => {
        const { data } = res;
        const { url } = data[0];
        state.imgPathsList.push(url);
    });
    state.fileList = info.fileList;
};

const removeUpload = (index) => {
    state.imgPathsList.splice(index, 1);
    state.fileList.splice(index, 1);
};
watch(
    () => props.visibleProps,
    (val) => {
        if (val) {
            state.introduceForm = {
                imgPaths: "",
                describe: null,
            };
            state.imgPathsList = [];
            state.fileList = [];
            if (props.detailId) {
                siteDetails({ id: props.detailId }).then(({ data }) => {
                    if (data.imgPaths) {
                        state.imgPathsList = data.imgPaths.split(",");
                    }
                    state.introduceForm = data;
                });
            }
        }
    }
);
</script>

<style scoped lang="less">
.upload_wap {
    min-height: 218px;
    padding: 0 16px 16px;
    border: 1px dashed @border-color-base;
    border-radius: 4px;
    box-sizing: content-box;
    overflow: hidden;

    .upload {
        text-align: center;
    }

    transition: all 0.25s;
}

.upload_format__hint {
    margin: 16px auto;
    color: @suggestive-color;
}

.upload_item__warp {
    &:hover {
        .upload_item__remove {
            display: block;
        }
    }
}

.upload_item_image__warp {
    position: relative;
    width: 86px;
    height: 86px;

    .upload_item__remove {
        display: none;
        position: absolute;
        right: -7px;
        top: -7px;
        color: @error-color;
        font-size: 14px;
        background: #fff;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.25s;
    }
}
</style>
