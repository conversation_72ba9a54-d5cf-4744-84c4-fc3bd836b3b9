<!-- 场地信息 siteInfo -->
<template>
    <div class="site_info">
        <a-button
            class="add_appointment"
            type="primary"
            :disabled="!state.venuesIndex"
            @click="state.visibleProps = true"
        >
            编辑
        </a-button>
        <div class="clazz_info">
            <a-avatar src="/image/field_build.png" :size="72"></a-avatar>
            <div class="clazz_user" v-if="state.venuesData.length">
                <div class="clazz">
                    <span>{{ state.venuesForm.name }}</span>
                    <a-dropdown :trigger="['click']">
                        <a-button
                            class="button-link"
                            type="link"
                            @click="state.isShowSite = true"
                            >［切换场地］</a-button
                        >
                        <template #overlay>
                            <a-menu>
                                <a-menu-item
                                    v-for="(item, idx) in state.venuesData"
                                    :key="item.id"
                                    @click="handerVenues(item)"
                                >
                                    <div class="checkbox">
                                        <span>{{ item.name }}</span>
                                        <div
                                            class="checkbox_chran"
                                            v-if="item.sites.length"
                                        >
                                            <a-radio-group
                                                class="checkbox_item"
                                                v-model:value="
                                                    state.venuesIndex
                                                "
                                            >
                                                <a-radio
                                                    v-for="it in item.sites"
                                                    :value="it.id"
                                                    @change="
                                                        handerVenues(item, it)
                                                    "
                                                >
                                                    {{ it.name }}
                                                </a-radio>
                                            </a-radio-group>
                                        </div>
                                    </div>
                                </a-menu-item>
                            </a-menu>
                        </template>
                    </a-dropdown>
                </div>
                <div class="address">
                    位置
                    <span class="item">{{ state.venuesForm.location }}</span>
                    楼层
                    <span class="item">{{ state.venuesForm.roomNum }}楼</span>
                    房间
                    <span class="item">{{ state.venuesForm.floor }}</span>
                    管理员
                    <span class="item">{{
                        state.venuesForm.administrator
                    }}</span>
                </div>
            </div>
        </div>
        <div class="site_img">
            <div class="titile">场地照片</div>
            <div class="imgs">
                <template v-if="state.venuesForm.imgPathss.length">
                    <img
                        class="img_item"
                        v-for="it in state.venuesForm.imgPathss"
                        :key="it"
                        :src="it"
                        alt=""
                    />
                </template>
                <a-empty
                    class="empty"
                    v-else
                    image="image/venues.png"
                    description="暂时还没有上传该场地的照片哦～"
                    :image-style="{ width: '100%', height: '146px' }"
                ></a-empty>
            </div>
        </div>
        <div class="site_introduce">
            <div class="titile">场地介绍</div>
            <div class="introduce" v-if="state.venuesForm.describe">
                {{ state.venuesForm.describe }}
            </div>
            <a-empty
                class="empty"
                :image-style="{ width: '100%', height: '146px' }"
                v-else
                image="image/venues.png"
                description="暂无场地介绍"
            ></a-empty>
        </div>

        <Introduce
            v-model:visibleProps="state.visibleProps"
            :detailId="state.venuesIndex"
            @introduceForm="editIntroduceForm"
        />
    </div>
</template>

<script setup>
import { reactive, onMounted } from "vue";
import Introduce from "./introduce.vue";
import { message } from "ant-design-vue";
import { updateSite } from "@/api/site.js";
import { getMeetingBuilding } from "@/api/conferenceSystem.js";
import { getfreeSiteInfo } from "@/api/siteBooking";

const state = reactive({
    isShowSite: false,
    venuesData: [],
    venuesForm: {
        id: "",
        name: "",
        floor: 0,
        roomNum: 0,
        imgPaths: "",
        administrator: "",
        describe: "",
        location: "",
        imgPathss: [],
    },
    venuesIndex: "",
    visibleProps: false,
    siteFormLoading: false,
});

// 场地详情
const getfreeSiteInfoFn = () => {
    getfreeSiteInfo({ id: state.venuesIndex }).then(({ data }) => {
        const { buildingName, name, roomNum, floor, imgPaths, describe, list } =
            data;
        const administrators = list?.map((v) => v.name);
        const params = {
            name: `${buildingName}${name}`,
            location: buildingName,
            roomNum,
            floor,
            imgPaths,
            describe,
            imgPathss: [],
        };
        if (imgPaths) {
            params.imgPathss = imgPaths.split(",");
        }
        if (administrators.length) {
            params.administrator = administrators.join("、");
        }

        Object.assign(state.venuesForm, params);
    });
};
const getMeetingBuildingInfo = async () => {
    await getMeetingBuilding().then(({ data }) => {
        state.venuesData = data;
        if (data?.length) {
            state.venuesIndex = data[0].sites[0].id;
        }
    });
};

// 场地切换
const handerVenues = (item, it) => {
    const { name, roomNum, describe, floor, imgPaths, buildingName, id } = it;
    state.name = `${item.name}${name}`;
    state.location = item.name;
    state.roomNum = roomNum;
    state.floor = floor;
    state.imgPaths = imgPaths;
    state.venuesIndex = id;
    getfreeSiteInfoFn();
};
// 编辑场地简介和图片
const editIntroduceForm = (item) => {
    state.siteFormLoading = true;
    updateSite(item)
        .then((res) => {
            message.success(res.message);
            state.visibleProps = false;
            getfreeSiteInfoFn();
        })
        .finally(() => {
            state.siteFormLoading = false;
        });
};
onMounted(async () => {
    await getMeetingBuildingInfo();
    if (state.venuesIndex) {
        await getfreeSiteInfoFn();
    }
});
</script>

<style scoped lang="less">
.site_info {
    margin: 16px;
    position: relative;

    .add_appointment {
        position: absolute;
        top: -48px;
        right: 0;
    }

    .clazz_info {
        background-color: @gray-background;
        padding: 16px;
        margin: 24px 0;
        border-radius: 4px;
        display: flex;

        .clazz_user {
            margin-left: 16px;
            display: flex;
            flex-direction: column;
            justify-content: space-around;

            .clazz {
                font-size: 18px;
                font-weight: 500;
            }

            .address {
                color: @disabled-color;

                .item {
                    color: @heading-color;
                }
            }
        }
    }

    .titile {
        font-size: 16px;
        font-weight: 500;
        margin: 10px 0;

        &::before {
            content: "";
            display: inline-block;
            width: 2px;
            height: 14px;
            background-color: @primary-color;
            margin-right: 10px;
        }
    }

    .imgs {
        .img_item {
            display: inline-block;
            width: 210px;
            height: 140px;
            margin-right: 16px;
        }
    }

    .introduce {
        font-size: 14px;
        line-height: 22px;
    }

    .empty {
        margin: 0;
    }
}
:deep(.ant-dropdown-menu-item:hover) {
    background: @body-background;
}
.checkbox {
    background: @body-background;
    width: 200px;
    // padding: 10px 10px 0;
    border-radius: 4px;
    max-height: 200px;
    overflow: hidden auto;
    .checkbox_chran {
        .checkbox_item {
            display: block;
            margin: 8px 0;
            :deep(.ant-radio-wrapper) {
                display: block;
                padding: 4px 5px;
                &.ant-radio-wrapper-checked {
                    display: flex;
                    background: @primary-color;
                    color: @body-background;
                    margin: 0;

                    // .ant-radio {
                    //     display: none;
                    // }
                }
            }
        }
    }
}
</style>
