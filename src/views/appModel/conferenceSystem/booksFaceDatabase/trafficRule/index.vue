<!--会议机通行规则设置-->
<template>
    <div class="trafficRuleDiv">
        <div class="left">
            <div class="title">
                会议机通行规则设置
                <a-button class="button-link" type="link" @click="updateRuleSetInfo">
                    <template #icon>
                        <FileAddFilled v-if="state.isShowEdit" />
                        <EditFilled v-else />
                    </template>
                    <span style="margin: 0 6px">
                        {{ state.isShowEdit ? "保存" : "编辑" }}
                    </span>
                </a-button>
            </div>
            <a-checkbox v-model:checked="state.ruleSet.status" :disabled="!state.isShowEdit">预约成功自动生成规则</a-checkbox>
            <ul class="item">
                <li>
                    1.通行时间段：会议的开始时间提前
                    <a-input-number style="width: 66px" v-model:value="state.ruleSet.beforeTime" placeholder="请输入"
                        :min="0" :max="120" :disabled="!state.isShowEdit" />
                    分钟 - 会议的结束时间延后
                    <a-input-number style="width: 66px" v-model:value="state.ruleSet.afterTime" placeholder="请输入"
                        :min="0" :max="120" :disabled="!state.isShowEdit" />
                    分钟
                </li>
                <li>
                    2.自动规则过期后删除：
                    <a-select style="width: 120px" v-model:value="state.ruleSet.autoDeleteRule"
                        :disabled="!state.isShowEdit" placeholder="请选择">
                        <a-select-option :value="true">是</a-select-option>
                        <a-select-option :value="false">否</a-select-option>
                    </a-select>
                </li>
            </ul>
        </div>
        <div class="right">
            <div class="title">会议机通行规则</div>
            <!-- 搜索表单 -->
            <div class="searchForm">
                <div class="searchHead">
                    <a-form :model="searchForm" layout="inline">
                        <a-form-item label="场地：" class="1" style="margin-bottom: 12px">
                            <a-cascader v-model:value="searchForm.fieid" :fieldNames="hometownFieldNames"
                                :options="fieldList1" placeholder="请选择" style="width: 200px" />
                        </a-form-item>
                        <a-form-item>
                            <a-input v-model:value="searchForm.name" placeholder="请输入人员姓名搜索" allowClear></a-input>
                        </a-form-item>
                        <a-form-item>
                            <a-button type="primary" style="margin-right: 10px" @click="query">
                                <template #icon>
                                    <SearchOutlined style="margin-right: 10px" />
                                </template>
                                查询
                            </a-button>
                            <a-button @click="reset">
                                <template #icon>
                                    <redo-outlined style="margin-right: 10px" />
                                </template>
                                重置
                            </a-button>
                        </a-form-item>
                    </a-form>
                    <a-button type="primary" @click="addGeneralRules">
                        <template #icon>
                            <plus-outlined />
                        </template>
                        添加规则
                    </a-button>
                </div>
            </div>
            <!-- 表格 -->
            <YTable class="worker-table tableBox" :columns="columns" :dataSource="dataSource" :rowSelection="false"
                :isScroll="true" :totals="state.pagination" @onSelectedRowKeys="handerSelectedRowKeys">
                <template #bodyCell="{ column, text, record }">
                    <template v-if="column.dataIndex === 'deviceType'">
                        <div v-if="record.deviceType == 9">会议机</div>
                    </template>

                    <template v-if="column.dataIndex === 'inOut'">
                        <div v-if="record.rulesType != 2">
                            {{
                                record.inOut == 1
                                    ? "出"
                                    : record.inOut == 2
                                        ? "入"
                                        : "-"
                            }}
                        </div>
                        <div v-else>
                            {{
                                record.inOut == 1
                                    ? "不允许出入"
                                    : record.inOut == 2
                                        ? "允许出入"
                                        : "-"
                            }}
                        </div>
                    </template>
                    <template v-if="column.dataIndex === 'isEnable'">
                        <div style="display: flex; align-items: center">
                            <div class="stateLi" :style="record.isEnable == 1
                                ? 'background: var(--primary-color)'
                                : record.isEnable == 2
                                    ? 'background: #F5222D'
                                    : 'background:#BFBFBF'
                                "></div>
                            <div>
                                {{
                                    record.isEnable == 1
                                        ? "已启用"
                                        : record.isEnable == 2
                                            ? "禁用"
                                            : "未启用"
                                }}
                            </div>
                        </div>
                    </template>
                    <template v-if="column.dataIndex === 'time'">
                        {{ record.startTime + "/" + record.endTime }}
                        <a-tooltip v-if="record.endTime == '2099-12-31'">
                            <template #title>该条规则永久有效</template>
                            <exclamation-circle-outlined style="font-size: 16px; color: #ff9800"
                                v-if="record.endTime == '2099-12-31'" />
                        </a-tooltip>
                        <a-tooltip v-if="
                            new Date(record.endTime).getTime() <
                            new Date(nowDate).getTime()
                        ">
                            <template #title>该条规则已过期</template>
                            <exclamation-circle-outlined style="font-size: 16px; color: #989595" v-if="
                                new Date(record.endTime).getTime() <
                                new Date(nowDate).getTime()
                            " />
                        </a-tooltip>
                    </template>
                    <div v-if="column.dataIndex === 'operation'">
                        <a-button type="link" style="color: var(--primary-color); padding-left: 0"
                            @click="edit(record)">修改</a-button>
                        <a-button type="link" @click="remove(record)" style="color: red">删除</a-button>
                        <a-button type="link" style="color: var(--primary-color)" @click="see(record)">查看规则</a-button>
                        <a-switch v-model:checked="record.isStatus" @change="changeStatus(record)" />
                        {{ record.isEnable == 1 ? "禁用" : "启用" }}
                    </div>
                    <template v-if="
                        column.dataIndex === 'ruleName' ||
                        column.dataIndex === 'siteName'
                    ">
                        <Tooltip :title="text"></Tooltip>
                    </template>
                </template>
            </YTable>
        </div>
        <!-- 规则弹窗 -->
        <a-drawer v-model:visible="rulesVisible" :title="title" width="502" @close="closeVisible">
            <a-form :model="rulesForm" layout="vertical" ref="rulesRefs">
                <a-form-item label="规则名称：" name="ruleName" :rules="[
                    {
                        required: true,
                        message: '请输入规则名称',
                    },
                ]">
                    <a-input v-model:value="rulesForm.ruleName" placeholder="请输入" allow-clear maxlength="20" />
                </a-form-item>
                <a-form-item label="选择场地：" name="siteId" :rules="[
                    {
                        required: true,
                        message: '请选择场地',
                    },
                ]">
                    <a-tree-select class="select_site_class" v-model:value="rulesForm.siteId" show-search
                        placeholder="请选择" allow-clear tree-default-expand-all :tree-data="fieldList" :field-names="{
                            children: 'children',
                            label: 'name',
                            key: 'id',
                            value: 'id',
                        }" @select="selectField" :disabled="disabled" :showSearch="false"></a-tree-select>
                </a-form-item>

                <a-form-item label="选择人员：" name="personnelName" :rules="[
                    {
                        required: true,
                        message: '请选择人员',
                    },
                    { validator: pwdCheck, trigger: 'blur' },
                ]">
                    <a-input v-model:value="rulesForm.personnelName" @click="openControlRef" allowClear
                        placeholder="请选择" readonly="readonly" />
                </a-form-item>

                <!-- <a-form-item label="选择人员：" name="personnelName" :rules="[
                    {
                        required: true,
                        message: '请选择人员',
                    },
                    { validator: pwdCheck, trigger: 'blur' },
                ]">
                    <y-select mode="personnel" :tabs="[
                        { tab: '教职工', key: 1, checked: true },
                        { tab: '学生组', key: 2, checked: false },
                    ]" v-model:visible="personnelvisible" v-model:checked="checkedList" :queryEltern="false"
                        @handleOk="handleOk">
                        <a-input v-model:value="rulesForm.personnelName" @click="addMember" allowClear placeholder="请选择"
                            readonly="readonly" />
                    </y-select>
                </a-form-item> -->
                <!-- <a-form-item label="通行方向：" class="form_inline" name="direction"
                    :rules="[{ required: true, message: '请选择通行方向' }]">
                    <a-radio-group v-model:value="rulesForm.direction">
                        <a-radio value="2">
                            <span>
                                {{
                                    title == '添加节假日规则' ||
                                    title == '修改节假日规则'
                                    ? '允许出入'
                                    : '入'
                                }}
                            </span>
                        </a-radio>
                        <a-radio value="1">
                            {{
                                title == '添加节假日规则' ||
                                title == '修改节假日规则'
                                ? '不允许出入'
                                : '出'
                            }}
                        </a-radio>
                    </a-radio-group>
                </a-form-item> -->

                <a-form-item label="有效期：" :name="title == '添加临时规则' ? 'time' : ''" v-if="
                    title != '添加节假日规则' && title != '修改节假日规则'
                " :rules="title == '添加临时规则'
                    ? [{ required: true, message: '请选择' }]
                    : []
                    ">
                    <a-range-picker v-model:value="rulesForm.time" value-format="YYYY-MM-DD" style="width: 100%"
                        separator="至" :disabled-date="disabledDate" @calendarChange="calendarPriceRangeChange" />
                </a-form-item>
                <!-- 节假日表格 -->
                <div v-if="
                    title == '添加节假日规则' || title == '修改节假日规则'
                ">
                    <a-button @click="addHoliday" style="
                            color: var(--primary-color);
                            border-color: var(--primary-color);
                            background: rgba(0, 183, 129, 0.08);
                        ">
                        <template #icon>
                            <plus-outlined />
                        </template>
                        添加节假日
                    </a-button>
                    <span class="holidayText">(同一时刻表内，节假日规则优先于常规规则)</span>
                    <YTable :columns="holidayColumns" :dataSource="holidayDataSource" :rowSelection="false"
                        style="margin-top: 16px">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.dataIndex === 'operation'">
                                <a-button type="link" @click="deleteholiday(record)" style="color: red">删除</a-button>
                            </template>
                            <template v-if="column.dataIndex === 'time'">
                                {{ record.startTime + "/" + record.endTime }}
                            </template>
                        </template>
                    </YTable>
                </div>
            </a-form>
            <a-form ref="formRef" layout="inline" :model="weekForm" v-if="title != '添加节假日规则' && title != '修改节假日规则'">
                <label>
                    <i style="color: red">*</i>
                    通行规则：
                    <span class="4" style="color: #00000033">(通行规则按照每周循环)</span>
                </label>
                <a-tabs v-model:activeKey="activeKey" type="card" :class="{ atab: everyDay }" id="atabDiv"
                    @change="state.noTime = false">
                    <a-tab-pane :tab="item.weekName" v-for="(item, index) in weekForm.weekList" :key="index">
                        <a-radio-group v-model:value="item.isAllow" name="radioGroup" @change="changeIsPassage(item)"
                            style="margin: 10px">
                            <a-radio value="0">自定义</a-radio>
                            <a-radio value="1">允许全天通行</a-radio>
                            <a-radio value="2" v-if="title == '添加临时规则'">不允许全天通行</a-radio>
                        </a-radio-group>
                        <a-space style="display: flex; margin-bottom: 8px" align="baseline"
                            v-for="(item1, i) in item.timelist" :key="i">
                            <a-form-item :label="item.isAllow === '2'
                                ? '非通行时间'
                                : '通行时间'
                                ">
                                <a-time-range-picker v-model:value="item1.time" @change="(v) => changetime(v, index, i)"
                                    value-format="HH:mm:ss" :disabled="item.isAllow === '1' ||
                                        item.isAllow === '2'
                                        " />
                            </a-form-item>
                            <i class="iconfont icon-shanchu" style="font-size: 22px" @click="removeUser(item, i)"
                                v-if="item.isAllow === '0'"></i>
                            <!-- <MinusCircleOutlined
                                @click="removeUser(item, i)"
                                v-if="item.isAllow == 0"
                            />-->
                        </a-space>
                        <div class="4" style="color: red; padding: 0px 0px 10px 70px" v-if="state.noTime">
                            <i class="iconfont icon-Shape6"></i>
                            通行时间没有填写完整
                        </div>
                        <a-form-item v-if="
                            item.isAllow === '0' && item.timelist.length < 6
                        ">
                            <a-button style="
                                    color: var(--primary-color);
                                    border-color: var(--primary-color);
                                    background: rgba(0, 183, 129, 0.08);
                                " @click="addUser(item)">
                                <template #icon> <plus-outlined /> </template>添加通行时间
                            </a-button>
                        </a-form-item>
                    </a-tab-pane>
                </a-tabs>
                <a-checkbox v-model:checked="everyDay" @change="changeEveryDay">应用到每天</a-checkbox>
            </a-form>
            <template #footer>
                <div class="footer">
                    <a-button style="margin-right: 8px" @click="cancel">取消</a-button>
                    <a-button type="primary" :loading="state.loading" @click="confirm">确认</a-button>
                </div>
            </template>
        </a-drawer>
        <!-- 节假日弹窗、 查看规则弹窗-->
        <a-modal v-model:visible="holidayVisible" :title="title1" @ok="handleOkHoliday"
            :width="title1 == '添加节假日' ? '408px' : '800px'" :zIndex="1049"
            :class="title1 == '添加节假日' ? '' : 'addRuleModal'">
            <!-- 添加节假日 -->
            <div v-if="title1 == '添加节假日'">
                <a-form :model="holidayForm" layout="vertical" ref="holidayRef">
                    <a-form-item label="节日名称：" name="name" :rules="[
                        {
                            required: true,
                            message: '请输入节日名称',
                        },
                    ]">
                        <a-input v-model:value="holidayForm.name" allow-clear maxlength="10" placeholder="请输入节日名称" />
                    </a-form-item>
                    <a-form-item label="日期" name="date" :rules="[
                        {
                            required: true,
                            message: '请选择日期',
                        },
                    ]">
                        <a-range-picker v-model:value="holidayForm.date" value-format="YYYY-MM-DD" style="width: 100%"
                            separator="至" />
                    </a-form-item>
                </a-form>
            </div>
            <!-- 查看常规、临时规则 -->
            <div v-if="state.seeType != 2 && title1 != '添加节假日'">
                有效期：
                <span>{{ times }}</span>
                <YTable :columns="seeColumns" :dataSource="seeDataSource" :rowSelection="false"
                    style="margin-top: 16px">
                </YTable>
            </div>
            <!-- 查看节假日规则 -->
            <div v-if="state.seeType == 2 && title1 != '添加节假日'">
                <!-- {{ isInOut }} -->
                <YTable :columns="seeHolidayColumns" :dataSource="seeHolidayDataSource" :rowSelection="false"
                    style="margin-top: 16px"></YTable>
            </div>
        </a-modal>
        <!-- 规则重复的异常弹框 -->
        <a-modal v-model:visible="abnormalVisible" title="异常" width="408px" class="abnormalModal">
            <exclamation-circle-filled id="itemTooltip" style="font-size: 18px; color: #ff9800" />
            <span>以下人员在该场地存在规则冲突，请调整后重新保存，同一场地同一人员同一时间内只能有一条同一类型的规则</span>
            <YTable :columns="abnormalColumns" :dataSource="abnormalDataSource" :rowSelection="false"
                style="margin-top: 16px">
            </YTable>
        </a-modal>

        <!-- 选人组件 -->
        <PersonSelectionControl ref="controlRef" :type="SELECT_TYPE.ALL" :selected="state.checkedList"
            :tabs="state.tabs" @search="searchSelects" @toggleLevel="toggleLevels" @toggleTabs="toggleTabss"
            @submit="submits" />
    </div>
</template>

<script setup>
import {
    reactive,
    toRefs,
    onMounted,
    computed,
    createVNode,
    nextTick,
    ref,
} from "vue";
import YTable from "comps/YTable/index.vue";
import {
    SearchOutlined,
    PlusOutlined,
    ExclamationCircleFilled,
} from "@ant-design/icons-vue";
import FormInstance, { Modal, message } from "ant-design-vue";
import YSelect from "@/components/YSelect/index";
import dayjs from "dayjs";
import { useRoute, useRouter } from "vue-router";
import {
    getSiteList,
    pageRuleInfo,
    createPasssceneRule,
    getHolidayInfo,
    getPassRuleDetailById,
    updatePassRuleInfo,
    deletePassRuleInfo,
    updateRuleIsEnableInfo,
} from "@/api/trafficRules.js";
import { useStore } from "@/store/index";

import {
    getDepartmentPersonnel,
    getSchoolRollPersonnel,
    getSchoolRollTree,
    getDepartmentTree,
} from "@/api/faceLibrary.js";
import { getBookingSiteNums } from "@/api/siteBooking";
import { updateRuleSet, getRuleSet } from "@/api/conferenceSystem.js";

import PersonSelectionControl from "@/components/PersonSelectionControl/index.vue";
import {
    SELECT_TYPE,
    DISPLAY_MODE,
} from "@/components/PersonSelectionControl/constants.js";
import { set } from "@vueuse/core";
const store = useStore();
const statusList = computed(
    () => store.state.selectSource.dictionary.emp_status_yes_id
);
const route = useRoute();
const router = useRouter();
const date = new Date();
const year = date.getFullYear();
let month = date.getMonth() + 1;
let day = date.getDate();
const deviceType = 9;
if (month < 10) {
    month = "0" + month;
}
if (day < 10) {
    day = "0" + day;
}
// 路由返回按钮
const handerFacultyForm = () => {
    router.back();
};
const nowDate = year + "-" + month + "-" + day;
const rulesRefs = ref();
const state = reactive({
    siteBookingTypeId: "",
    isShowEdit: false,
    ruleSet: {
        afterTime: 0,
        beforeTime: 0,
        id: "",
        autoDeleteRule: false,
        status: true,
    },
    loading: false,

    searchForm: {
        fieid: "",
        name: "",
        deviceType: deviceType || "",
    },
    dataSource: [],
    fieldList1: [],
    checked: true,
    flag: false,
    rulesVisible: false,
    title: "",
    rulesForm: {
        id: "",
        ruleName: "",
        siteId: null,
        personnelName: "",
        teacherList: [], // 教职工id
        studentList: [], // 学生id
        direction: "",
        time: "",
        deviceType: deviceType || 1,
    },
    activeKey: 0,
    rulesRef: FormInstance,
    fieldList: [],
    personnelvisible: false,
    checkedList: [],
    weekListLoca: {},
    weekForm: {
        weekList: [
            {
                weekName: "周一",
                week: 1,
                isAllow: deviceType === "9" ? "1" : "0",
                name: "1",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周二",
                week: 2,
                isAllow: deviceType === "9" ? "1" : "0",
                name: "2",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周三",
                week: 3,
                isAllow: deviceType === "9" ? "1" : "0",
                name: "3",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周四",
                week: 4,
                isAllow: deviceType === "9" ? "1" : "0",
                name: "4",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周五",
                week: 5,
                isAllow: deviceType === "9" ? "1" : "0",
                name: "5",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周六",
                week: 6,
                isAllow: deviceType === "9" ? "1" : "0",
                name: "6",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周日",
                week: 7,
                isAllow: deviceType === "9" ? "1" : "0",
                name: "7",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
        ],
    },
    // formRef: FormInstance,
    everyDay: true,
    holidayDataSource: [],
    holidayVisible: false,
    holidayForm: {
        name: "",
        date: "",
    },
    holidayRef: FormInstance,
    title1: "",
    times: "2022-3-8",
    seeDataSource: [],
    seeHolidayDataSource: [],
    disabled: false,
    pagination: {
        total: 0,
        pageNo: 1,
        pageSize: 10,
    },
    rulesType: 0,
    editType: 0,
    ruleId: "",
    seeType: "",
    isInOut: "",
    treeId: null,
    abnormalVisible: false,
    abnormalDataSource: [],
    treeTitle: "",
    selectPriceDate: "",
    noTime: false,
    offsetDays: 86400000 * 7, // 限制时间选择 超过7天禁选

    // 身份标识(0.学生 1.教职工 2.家长 3. 外部人员 4.自定义组)
    tabs: [
        {
            tab: "教职工",
            checked: true,
            id: 1,
            key: "teacher",
            // 有userId 则是人
            personField: { key: "userId", value: ["userId"] },
            // 单选 true 多选 false
            single: false,
            searchOption: {
                show: true,
                displayMode: DISPLAY_MODE.NEW,
            },
        },
        {
            tab: "学生组",
            checked: true,
            isClassify: true,
            id: 0,
            key: "student",
            // 有userId 则是人
            personField: { key: "studentCode", value: ["studentCode"] },
            // 单选 true 多选 false
            single: false,
            searchOption: {
                show: true,
                displayMode: DISPLAY_MODE.NEW,
            },
        },
    ],
    tableState: {
        deptId: "", // 部门ID
        status: "", // 状态
        id: "", // 教师id
        name: "",
        accommodation: "", // 就读类型  只有学生组有
    },
});
const formRef = ref();
const holidayRef = ref();

// 选择开始时间/结束时间
const calendarPriceRangeChange = (date) => {
    if (state.rulesType === 3) {
        state.selectPriceDate = date[0];
    }
};
// 根据选择的开始时间/结束时间，动态渲染要禁用的日期
const disabledDate = (current) => {
    if (state.rulesType === 3) {
        if (state.selectPriceDate) {
            const selectV = dayjs(
                state.selectPriceDate,
                "YYYY-MM-DD"
            ).valueOf();
            return (
                current >
                dayjs(new Date(selectV + state.offsetDays).getTime()) ||
                current < dayjs(new Date(selectV - state.offsetDays).getTime())
            );
        } else {
            return false;
        }
    }
};
const {
    searchForm,
    dataSource,
    fieldList,
    rulesVisible,
    title,
    rulesForm,
    weekListLoca,
    activeKey,
    rulesRef,
    personnelvisible,
    checkedList,
    // formRef,
    everyDay,
    holidayDataSource,
    holidayVisible,
    holidayForm,
    title1,
    times,
    seeDataSource,
    seeHolidayDataSource,
    fieldList1,
    disabled,
    isInOut,
    weekForm,
    abnormalVisible,
    abnormalDataSource,
    treeTitle,
} = toRefs(state);
const columns = [
    {
        title: "序号",
        width: 80,
        customRender: ({ text, record, index }) => `${index + 1}`, // 显示每一行的序号
    },
    {
        title: "规则名称",
        dataIndex: "ruleName",
        key: "id",
        width: 120,
        ellipsis: true,
        showSorterTooltip: true,
    },
    {
        title: "设备类型",
        dataIndex: "deviceType",
        key: "deviceType",
        width: 120,
    },
    {
        title: "场地",
        dataIndex: "siteName",
        key: "id",
        width: 120,
    },
    {
        title: "人员数量",
        dataIndex: "peopleNumber",
        key: "id",
        width: 120,
    },
    // {
    //     title: "通行方向",
    //     dataIndex: "inOut",
    //     key: "id",
    //     width: 120,
    // },
    {
        title: "有效期",
        dataIndex: "time",
        key: "id",
        width: 260,
    },
    {
        title: "状态",
        dataIndex: "isEnable",
        key: "id",
        width: 120,
    },
    {
        title: "操作",
        dataIndex: "operation",
        key: "id",
        width: 250,
        fixed: "right",
    },
];
const holidayColumns = [
    {
        title: "节假日",
        dataIndex: "name",
        key: "id",
        width: 100,
    },
    {
        title: "时间",
        dataIndex: "time",
        key: "id",
        width: 200,
    },
    {
        title: "操作",
        dataIndex: "operation",
        width: 50,
    },
];
const seeColumns = [
    {
        title: "",
        dataIndex: "time",
        key: "id",
    },
    {
        title: "周一",
        dataIndex: "date1",
        key: "id",
    },
    {
        title: "周二",
        dataIndex: "date2",
        key: "id",
    },
    {
        title: "周三",
        dataIndex: "date3",
        key: "id",
    },
    {
        title: "周四",
        dataIndex: "date4",
        key: "id",
    },
    {
        title: "周五",
        dataIndex: "date5",
        key: "id",
    },
    {
        title: "周六",
        dataIndex: "date6",
        key: "id",
    },
    {
        title: "周日",
        dataIndex: "date7",
        key: "id",
    },
];
const seeHolidayColumns = [
    {
        title: "节假日",
        dataIndex: "holiday",
        key: "id",
    },
    {
        title: "时间",
        dataIndex: "time",
        key: "id",
    },
];
const abnormalColumns = [
    {
        title: "规则重复人员",
        dataIndex: "userName",
        key: "id",
        align: "center",
    },
];

const pwdCheck = (rule, value, callback) => {
    if (!value) {
        return Promise.reject("");
    }
    return Promise.resolve();
};
const hometownFieldNames = reactive({
    label: "name",
    value: "id",
    children: "children",
});

const query = () => {
    state.pagination.pageNo = 1;
    getPageRuleInfo();
};
const reset = () => {
    state.searchForm.fieid = "";
    state.searchForm.name = "";
    state.searchForm.deviceType = "";
    getPageRuleInfo();
};
const changeStatus = (v) => {
    const obj = {
        ruleId: v.ruleId,
        ruleType: v.rulesType,
        isEnable: v.isStatus ? 1 : 2,
    };
    updateRuleIsEnable(obj, v);
};
const addGeneralRules = () => {
    state.noTime = false;
    state.everyDay = deviceType === "9";
    state.rulesVisible = true;
    state.title = "添加常规规则";
    state.rulesType = 1;
    rulesRefs.value?.resetFields();
    state.weekForm = {
        weekList: [
            {
                weekName: "周一",
                week: 1,
                isAllow: deviceType === "9" ? "1" : "0",
                name: "1",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周二",
                week: 2,
                isAllow: deviceType === "9" ? "1" : "0",
                name: "2",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周三",
                week: 3,
                isAllow: deviceType === "9" ? "1" : "0",
                name: "3",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周四",
                week: 4,
                isAllow: "0",
                name: "4",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周五",
                week: 5,
                isAllow: "0",
                name: "5",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周六",
                week: 6,
                isAllow: "0",
                name: "6",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周日",
                week: 7,
                isAllow: "0",
                name: "7",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
        ],
    };
    state.checkedList = [];
    state.rulesForm.time = "";
    state.rulesForm.id = "";
};
const cancel = () => {
    state.rulesVisible = false;
    rulesRefs.value?.resetFields();
};
const closeVisible = () => {
    rulesRefs.value?.resetFields();
};

// 列表
const getPageRuleInfo = () => {
    const { pagination, searchForm, siteBookingTypeId } = state;
    const params = {
        pageNo: pagination.pageNo,
        pageSize: pagination.pageSize,
        userName: searchForm.name,
        siteId: searchForm.fieid && searchForm.fieid[1],
        deviceType: searchForm.deviceType,
        siteBookingTypeId,
    };
    pageRuleInfo(params).then((res) => {
        const { data } = res;
        state.pagination.total = data.total;
        data.list.forEach((v) => {
            v.isStatus = v.isEnable === 1;
        });
        state.dataSource = data.list;
    });
};
const confirm = () => {
    const list = state.weekForm.weekList;
    if (state.title !== "添加节假日规则" && state.title !== "修改节假日规则") {
        state.noTime = !list[state.activeKey].timelist.every((v) => !!v.time);
    }
    if (!state.noTime) {
        state.weekForm.weekList.forEach((item) => {
            item.timelist = JSON.parse(
                JSON.stringify(
                    state.weekForm.weekList[state.activeKey].timelist
                )
            );
            item.isAllow = JSON.parse(
                JSON.stringify(state.weekForm.weekList[state.activeKey].isAllow)
            );
        });
    }
    rulesRefs.value.validateFields().then(() => {
        if (state.rulesForm.id) {
            if (state.noTime) return;
            Modal.confirm({
                title: "您确定要修改该条规则吗？",
                icon: createVNode(ExclamationCircleFilled),
                content: "修改后，旧的规则将失效，会议机将匹配新的人员和规则。",
                okText: "确认",
                cancelText: "取消",
                onCancel() {
                    message.info("已取消！");
                },
                onOk() {
                    updatepassRuleInfo();
                },
            });
        } else {
            CreatePasssceneRule();
        }
    });
};
// 选择场地
const selectField = (value, node, extra) => {
    state.rulesForm.siteId = value;
};
const handleOk = (data) => {
    const personnelName = [];
    if (!data.length) return;
    data.map((v) => {
        personnelName.push(v.name);
    });
    state.rulesForm.personnelName = personnelName.join("、");
    state.rulesForm.studentList = data
        .filter((item) => {
            // _source1为教师 2为学生  peopleType2为教师 1为学生
            return item._source === 2 || item.peopleType === "1";
        })
        .map((item) => {
            return {
                peopleId: item.id,
            };
        });
    state.rulesForm.teacherList = data
        .filter((item) => {
            // _source1为教师 2为学生  peopleType2为教师 1为学生
            return item._source === 1 || item.peopleType === "2";
        })
        .map((item) => {
            return { userId: item.userId, peopleId: item.id };
        });
};
const removeUser = (item, i) => {
    item.timelist.splice(i, 1);
};
const addUser = (item) => {
    item.timelist.push({ time: "" });
};
const changeIsPassage = (item) => {
    if (item.isAllow === "0") {
        item.timelist = [{ time: "" }];
    } else {
        item.timelist = [{ time: ["00:00:00", "23:59:59"] }];
    }
};
const addHoliday = () => {
    state.holidayVisible = true;
    state.title1 = "添加节假日";
};
const handleOkHoliday = () => {
    if (state.seeType == 1 || state.seeType == 2) {
        state.holidayVisible = false;
        return;
    }
    holidayRef.value.validateFields().then(() => {
        const obj = {
            name: state.holidayForm.name,
            startTime: state.holidayForm.date[0],
            endTime: state.holidayForm.date[1],
        };
        state.holidayDataSource.push(obj);
        state.holidayVisible = false;
        state.holidayForm = {
            name: "",
            date: "",
        };
    });
};
const deleteholiday = (data) => {
    Modal.confirm({
        title: "删除提示",
        icon: createVNode(ExclamationCircleFilled),
        content: "是否删除该条节假日?",
        okText: "确认",
        cancelText: "取消",
        onCancel() {
            message.info("已取消！");
        },
        onOk() {
            state.holidayDataSource = state.holidayDataSource.filter((item) => {
                return item !== data;
            });
            message.info("删除成功！");
        },
    });
};
const edit = (v) => {
    state.noTime = false;
    state.ruleId = v.ruleId;
    const obj = {
        id: v.ruleId,
        ruleType: v.rulesType,
    };
    if (v.rulesType == 1) {
        state.title = "修改常规规则";
        state.editType = 1;
    } else if (v.rulesType == 2) {
        state.title = "修改节假日规则";
        state.editType = 2;
    } else {
        state.title = "修改临时规则";
        state.editType = 3;
    }
    state.rulesVisible = true;
    PassRuleDetailById(obj);
};
const remove = (v) => {
    const obj = {
        id: v.ruleId,
        ruleType: v.rulesType,
    };
    Modal.confirm({
        title: "删除提示",
        icon: createVNode(ExclamationCircleFilled),
        content: "是否删除该条规则？",
        okText: "确认",
        cancelText: "取消",
        onCancel() {
            message.info("已取消！");
        },
        onOk() {
            deletePassRule(obj);
        },
    });
};
const see = (v) => {
    state.holidayVisible = true;
    state.title1 = v.ruleName;
    state.seeType = v.rulesType;
    state.times = v.startTime + "/" + v.endTime;
    const obj = {
        id: v.ruleId,
        ruleType: v.rulesType,
    };
    PassRuleDetailById(obj);
};
const changeEveryDay = (checkedValue) => {
    if (checkedValue.target.checked === true) {
        Modal.confirm({
            title: "应用到每天",
            icon: createVNode(ExclamationCircleFilled),
            content: "确定要将当前通行规则应用到每天吗？",
            okText: "确认",
            cancelText: "取消",
            onCancel() {
                message.info("已取消！");
                state.everyDay = false;
            },
            onOk() {
                state.everyDay = true;
                state.weekForm.weekList.forEach((item) => {
                    item.timelist = JSON.parse(
                        JSON.stringify(
                            state.weekForm.weekList[state.activeKey].timelist
                        )
                    );
                    item.isAllow = JSON.parse(
                        JSON.stringify(
                            state.weekForm.weekList[state.activeKey].isAllow
                        )
                    );
                });
            },
        });
    } else if (checkedValue.target.checked === false) {
        Modal.confirm({
            title: "应用到每天",
            icon: createVNode(ExclamationCircleFilled),
            content: "确定要取消应用到每天吗？",
            okText: "确认",
            cancelText: "取消",
            onCancel() {
                message.info("已取消！");
                state.everyDay = true;
            },
            onOk() {
                state.everyDay = false;
            },
        });
    }
};
const changetime = (v, index, cindex) => {
    if (!v) {
        nextTick(() => {
            state.weekForm.weekList[index].timelist[cindex].time = "";
        });
    }
    nextTick(() => {
        formRef.value.clearValidate();
    });
    if (cindex > 0) {
        const yearmonthday = "2021-12-02";
        const end =
            yearmonthday +
            " " +
            state.weekForm.weekList[index].timelist[cindex - 1].time[1];
        const start =
            yearmonthday +
            " " +
            state.weekForm.weekList[index].timelist[cindex].time[0];
        if (new Date(start).getTime() < new Date(end).getTime()) {
            message.error("当前通行时间小于上一时间");
            nextTick(() => {
                state.weekForm.weekList[index].timelist[cindex].time = "";
            });
        }
    }
};
// 分页
const handerSelectedRowKeys = (res) => {
    const { current, pageSize } = res;
    state.pagination.pageNo = current;
    state.pagination.pageSize = pageSize;
    getPageRuleInfo();
};

// 禁用选中父节点
const disabledP = (tree) => {
    return tree.map((item) => {
        if (item.children) {
            item.disabled = true;
            disabledP(item.children);
        }
        return item;
    });
};
// 场地接口
const SiteList = () => {
    // type 1为人脸机的场地  3为考勤机的场地 2为全部场地/会议机
    getSiteList({ type: 9 }).then(({ data }) => {
        state.fieldList1 = data;
        const newData = JSON.parse(JSON.stringify(data));
        state.fieldList = disabledP(newData);
    });
};

// 添加普通规则接口
const CreatePasssceneRule = () => {
    const obj = {
        type: state.rulesType,
        passsceneId: state.treeId || route.query.id,
        ...state.rulesForm,
        ruleHolTimeList: state.holidayDataSource,
        isDay: state.everyDay,
    };
    delete obj.time;
    if (state.rulesType == 2) {
        delete obj.ruleTimeList;
        obj.ruleHolTimeList = state.holidayDataSource.map((item) => {
            return {
                holName: item.name,
                holStartTime: item.startTime,
                holEndTime: item.endTime,
            };
        });
    } else {
        if (state.rulesForm.time && state.rulesForm.time.length > 0) {
            obj.startTime = state.rulesForm.time[0];
            obj.endTime = state.rulesForm.time[1];
        } else if (!state.rulesForm.time) {
            obj.isForever = 1;
        }
        obj.ruleTimeList = state.weekForm.weekList.map((item) => {
            return {
                allowType: item.isAllow,
                week: item.week || item.id,
                weekTimes: item.timelist.map((childrenItem) => {
                    return {
                        weekStartTime:
                            childrenItem.time && childrenItem.time[0]
                                ? childrenItem.time[0]
                                : "",
                        weekEndTime:
                            childrenItem.time && childrenItem.time[1]
                                ? childrenItem.time[1]
                                : "",
                    };
                }),
            };
        });
        delete obj.ruleHolTimeList;
    }
    state.loading = true;
    createPasssceneRule(obj)
        .then((res) => {
            message.success(res.message);
            getPageRuleInfo();
            state.rulesVisible = false;
            rulesRefs.value?.resetFields();
        })
        .catch((err) => {
            if (err.data.code === 1002013009) {
                state.abnormalVisible = true;
                state.abnormalDataSource = err.data.data;
            }
        })
        .finally(() => (state.loading = false));
};
// 获取法定节假日
const holidayInfo = () => {
    getHolidayInfo().then((res) => {
        const { data } = res;
        state.holidayDataSource = data;
    });
};
const getSeeDataSource = (v) => {
    let date1 = [];
    let date2 = [];
    let date3 = [];
    let date4 = [];
    let date5 = [];
    let date6 = [];
    let date7 = [];
    const list = [];
    v.weeksTimeRes.forEach((item) => {
        if (item.week == 1) {
            date1 = item.weeksReqList.map((val) => {
                if (item.passageType === 2) {
                    return "不允许通行";
                } else if (item.passageType === 1) {
                    return "全天通行";
                } else if (!val.startTime && !val.endTime) {
                    return "";
                } else {
                    return val.startTime + "-" + val.endTime;
                }
            });
        }
        if (item.week == 2) {
            date2 = item.weeksReqList.map((val) => {
                if (item.passageType === 2) {
                    return "不允许通行";
                } else if (item.passageType === 1) {
                    return "全天通行";
                } else if (!val.startTime && !val.endTime) {
                    return "";
                } else {
                    return val.startTime + "-" + val.endTime;
                }
            });
        }
        if (item.week == 3) {
            date3 = item.weeksReqList.map((val) => {
                if (item.passageType === 2) {
                    return "不允许通行";
                } else if (item.passageType === 1) {
                    return "全天通行";
                } else if (!val.startTime && !val.endTime) {
                    return "";
                } else {
                    return val.startTime + "-" + val.endTime;
                }
            });
        }
        if (item.week == 4) {
            date4 = item.weeksReqList.map((val) => {
                if (item.passageType === 2) {
                    return "不允许通行";
                } else if (item.passageType === 1) {
                    return "全天通行";
                } else if (!val.startTime && !val.endTime) {
                    return "";
                } else {
                    return val.startTime + "-" + val.endTime;
                }
            });
        }
        if (item.week == 5) {
            date5 = item.weeksReqList.map((val) => {
                if (item.passageType === 2) {
                    return "不允许通行";
                } else if (item.passageType === 1) {
                    return "全天通行";
                } else if (!val.startTime && !val.endTime) {
                    return "";
                } else {
                    return val.startTime + "-" + val.endTime;
                }
            });
        }
        if (item.week == 6) {
            date6 = item.weeksReqList.map((val) => {
                if (item.passageType === 2) {
                    return "不允许通行";
                } else if (item.passageType === 1) {
                    return "全天通行";
                } else if (!val.startTime && !val.endTime) {
                    return "";
                } else {
                    return val.startTime + "-" + val.endTime;
                }
            });
        }
        if (item.week == 7) {
            date7 = item.weeksReqList.map((val) => {
                if (item.passageType === 2) {
                    return "不允许通行";
                } else if (item.passageType === 1) {
                    return "全天通行";
                } else if (!val.startTime && !val.endTime) {
                    return "";
                } else {
                    return val.startTime + "-" + val.endTime;
                }
            });
        }
    });
    for (let i = 0; i < 6; i++) {
        const obj = {
            time: "通行时间",
            date1: date1[i] || "-",
            date2: date2[i] || "-",
            date3: date3[i] || "-",
            date4: date4[i] || "-",
            date5: date5[i] || "-",
            date6: date6[i] || "-",
            date7: date7[i] || "-",
        };
        let flag = false;
        // 循环对象判断
        for (const k in obj) {
            // 如果对象中只要有一个属性不为空 就push到数组中 反之就不push到数组中
            if (obj[k] && obj[k] !== "通行时间" && obj[k] !== "-") {
                flag = true;
            }
        }
        if (flag) {
            list.push(obj);
        }
    }
    return list;
};
// 获取规则详情
const PassRuleDetailById = (obj) => {
    // const { ruleType } = obj
    getPassRuleDetailById(obj).then(({ data }) => {
        const ruleTypeObj = {
            1: "ordinaryForm",
            2: "holidayForm",
            3: "temporaryForm",
        };
        const {
            id,
            ruleName,
            siteId,
            deviceType,
            inOut,
            startTime,
            endTime,
            weeksTimeRes,
            holidayReqList,
            isDay,
            peopleVoForms,
            selectedNodesInfo,
            whichWeek,
        } = data[ruleTypeObj[obj.ruleType]];
        state.rulesForm.id = id;
        state.rulesForm.ruleName = ruleName;
        state.rulesForm.siteId = siteId;
        state.rulesForm.deviceType = deviceType;
        state.rulesForm.direction = inOut;
        state.activeKey = whichWeek || 0;
        state.rulesForm.time = [startTime, endTime];
        weeksTimeRes?.forEach((v, idx) => {
            v.weeksReqList.forEach((k) => {
                if (k.endTime) {
                    state.activeKey = idx;
                }
            });
        });
        state.rulesForm.personListDTO = null
        let personnelName = peopleVoForms.map((v) => v.name);
        let list = peopleVoForms.map((v) => {
            return {
                ...v,
                id: v.id,
            };
        });
        if (selectedNodesInfo) {
            const selectedNodesInfo = JSON.parse(
                data[ruleTypeObj[obj.ruleType]].selectedNodesInfo
            );
            let _selectedNodesInfo = [];
            personnelName = selectedNodesInfo.map((v) => {
                let _params = { ...v, rollValue: v.typeValue };
                if (
                    [
                        "people_dept",
                        "student",
                        "eltern",
                        "people_role",
                        "people_external",
                    ].includes(v.typeValue)
                ) {
                    _params.userId = v.id;
                } else {
                    _params.pid = v.id;
                }
                _selectedNodesInfo.push(_params);
                return v.name;
            });
            list = _selectedNodesInfo;
            state.rulesForm.selectedNodesInfo =
                data[ruleTypeObj[obj.ruleType]].selectedNodesInfo;
            state.rulesForm.personListDTO = _selectedNodesInfo;
        } else {
            state.rulesForm.studentList = peopleVoForms
                .filter((i) => {
                    return !i.userId;
                })
                .map((i) => {
                    return {
                        peopleId: i.id,
                    };
                });
            state.rulesForm.teacherList = peopleVoForms
                .filter((i) => {
                    return i.userId;
                })
                .map((i) => {
                    return { userId: i.userId, peopleId: i.id };
                });
        }

        state.rulesForm.personnelName = personnelName.join("、");
        state.checkedList = list;
        if (obj.ruleType === "1") {
            weeksTimeRes?.forEach((item, index) => {
                state.weekForm.weekList[index].isAllow = item.passageType;
                state.weekForm.weekList[index].timelist = item.weeksReqList.map(
                    (v) => {
                        if (v.startTime) {
                            return { time: [v.startTime, v.endTime] };
                        } else {
                            return { time: "" };
                        }
                    }
                );
            });
            state.everyDay = isDay;
            state.weekListLoca = state.weekForm.weekList;
            state.seeDataSource = getSeeDataSource(
                data[ruleTypeObj[obj.ruleType]]
            );
        } else if (obj.ruleType === "2") {
            state.holidayDataSource = [];
            state.seeHolidayDataSource = [];
            holidayReqList.forEach((v, idx) => {
                state.holidayDataSource.push({
                    name: v.holidayName,
                    startTime: v.holidayStartTime,
                    endTime: v.holidayEndTime,
                });
                state.seeHolidayDataSource.push({
                    holiday: v.holidayName,
                    time: v.holidayStartTime + "/" + v.holidayEndTime,
                });
            });
            state.isInOut = inOut === "1" ? "允许出入" : "不允许出入";
        } else if (obj.ruleType === "3") {
            weeksTimeRes.forEach((item, index) => {
                state.weekForm.weekList[index].isAllow = item.passageType;
                state.weekForm.weekList[index].timelist = item.weeksReqList.map(
                    (v) => {
                        if (v.startTime) {
                            return { time: [v.startTime, v.endTime] };
                        } else {
                            return { time: "" };
                        }
                    }
                );
            });
            state.seeDataSource = getSeeDataSource(data.temporaryForm);
        }
        SiteList();
    });
};
// 更新规则
const updatepassRuleInfo = () => {
    const obj = {
        type: state.editType,
        ruleId: state.ruleId,
        passsceneId: state.treeId || route.query.id,
        ...state.rulesForm,
        ruleHolTimeList: state.holidayDataSource,
        isDay: state.everyDay,
    };
    delete obj.time;
    if (state.editType == 2) {
        delete obj.ruleTimeList;
        obj.ruleHolTimeList = state.holidayDataSource.map((item) => {
            return {
                holName: item.name,
                holStartTime: item.startTime,
                holEndTime: item.endTime,
            };
        });
    } else {
        if (state.rulesForm.time && state.rulesForm.time.length > 0) {
            obj.startTime = state.rulesForm.time[0];
            obj.endTime = state.rulesForm.time[1];
        } else if (!state.rulesForm.time) {
            obj.isForever = 1;
        }

        obj.ruleTimeList = state.weekForm.weekList.map((item) => {
            return {
                allowType: item.isAllow,
                week: item.week || item.id,
                weekTimes: item.timelist.map((childrenItem) => {
                    return {
                        weekStartTime:
                            childrenItem.time && childrenItem.time[0]
                                ? childrenItem.time[0]
                                : "",
                        weekEndTime:
                            childrenItem.time && childrenItem.time[1]
                                ? childrenItem.time[1]
                                : "",
                    };
                }),
            };
        });
        delete obj.ruleHolTimeList;
    }
    state.loading = true;
    updatePassRuleInfo(obj)
        .then((res) => {
            message.success(res.message);
            getPageRuleInfo();
            state.rulesVisible = false;
        })
        .finally(() => (state.loading = false));
};

// 删除规则
const deletePassRule = (obj) => {
    deletePassRuleInfo(obj).then((res) => {
        getPageRuleInfo();
    });
};
// 启用禁用规则
const updateRuleIsEnable = (obj, item) => {
    updateRuleIsEnableInfo(obj).then((res) => {
        message.success(res.message);
        getPageRuleInfo();
    }).catch(() => {
        setTimeout(() => {
            item.isStatus = !item.isStatus;
        }, 500)

    });
};
// 获取getRuleSet
const getRuleSetInfo = () => {
    getRuleSet().then(({ data }) => {
        Object.assign(state.ruleSet, data);
    });
};
// 提交会议机通行规则设置
const updateRuleSetInfo = () => {
    if (state.isShowEdit) {
        updateRuleSet(state.ruleSet).then((res) => {
            message.success(res.message);
        });
    }
    state.isShowEdit = !state.isShowEdit;
};

// // 选人控件
const controlRef = shallowRef();

// 教职工
let stateDataSource = shallowRef([]);
// 学籍
let schoolRollTree = shallowRef([]);
const querys = computed(() => route.query);
const codes = querys.value.codes || sessionStorage.getItem("codes");

// 获取部门 学籍
const getSchoolAggregate = async () => {
    // 学籍
    await getSchoolRollTree(codes).then(({ data }) => {
        schoolRollTree.value = data;
    });
    // // 部门
    await getDepartmentTree(codes).then(({ data }) => {
        stateDataSource.value = data;
    });
};
// // 选人控
// 开启选人弹框
const openControlRef = () => {
    controlRef.value.modelState.dataSource = stateDataSource.value;
    controlRef.value.modelState.open = true;
    state.toggleTabsType = "teacher";
};

// 获取教职工人员列表
const getStaffPages = (callback) => {
    const { pageNo, pageSize, total } = controlRef.value.modelState.searchTable;
    const { roll_status_yes_id = [], emp_status_yes_id = [] } =
        store.state.selectSource.dictionary;

    let params = {
        ...state.tableState,
        statusList: emp_status_yes_id,
        code: codes,
        pageNo,
        pageSize,
        total,
    };
    let Api = getDepartmentPersonnel;
    if (state.toggleTabsType === "student") {
        const { name, accommodation, deptId } = state.tableState;
        params = {
            filterNonEltern: false,
            queryEltern: false,
            code: codes || "",
            classesId: deptId,
            type: 4,
            name,
            accommodation,
            statusList: roll_status_yes_id,
            pageNo,
            pageSize,
            total,
        };
        Api = getSchoolRollPersonnel;
        // 那学籍取人的
        // Api = getStudentUserList
    }

    Api(params)
        .then(({ data }) => {
            if (state.toggleTabsType == "student") {
                callback(data);
                return;
            }
            const { pageNo, pageSize, total, list } = data;

            callback(list);
        })
        .finally(() => {
            controlRef.value.modelState.loading = false;
        });
};
// 搜人  查找教职工
function searchSelects(tabId, item) {
    const { name, accommodation } = item;
    if (name || accommodation) {
        // 清空搜索输入框
        // 清空搜索查询list数据
        controlRef.value.modelState.searchTable.list = [];
        // 选人组件 - 首次聚焦教职工
        state.tableState.name = name;
        state.tableState.accommodation = accommodation;
        controlRef.value.modelState.loading = true;
        // 如果是学生组搜人 则清空id
        if (!tabId) {
            state.tableState.deptId = "";
        }
        const callback = (data) => {
            controlRef.value.modelState.searchTable.list =
                controlRef.value.modelState.searchTable.list?.concat(data);
        };
        getStaffPages(callback);
    } else {
        // name为空时，不发送请求，恢复最原始数据
        // !tabId ? setDataSource(state.staffList) : setDataSource(state.studentList)
    }
}

// Tabs切换
const toggleTabss = (item) => {
    state.toggleTabsType = item.key;
    state.tableState.name = "";
    state.tableState.accommodation = "";
    // 重置
    controlRef.value.modelState.loading = false;
    controlRef.value.modelState.isPpresent = false;
    controlRef.value.modelState.dataSource =
        item.key == "teacher" ? stateDataSource.value : schoolRollTree.value;
};
// 下級
const toggleLevels = (tabId, item = {}, options) => {
    const { index, trigger } = options;
    // 清空输入框
    state.tableState.name = "";
    state.tableState.accommodation = "";
    state.tableState.deptId = item.id;
    state.tableState.rollValue = item.rollValue;
    // 重置分页及搜索
    controlRef.value.modelState.isPpresent = false;
    // 面包屑
    if (!index) {
        let newDataSource =
            state.toggleTabsType == "teacher"
                ? stateDataSource.value
                : schoolRollTree.value;
        // 第一层数据，恢复原本数据
        controlRef.value.modelState.dataSource = newDataSource;
    } else {
        const callback = (data) => {
            let children = item.children || [];
            controlRef.value.modelState.dataSource = children?.concat(data);
        };

        if (state.toggleTabsType === "teacher") {
            getStaffPages(callback);
        } else {
            // 如果是学生组
            // 如果是班级
            if (item.rollValue === "classes") {
                getStaffPages(callback);
            } else {
                callback([]);
            }
        }
    }
};

// 提交選人數據
const submits = (checked) => {
    const userName = [];
    state.rulesForm.studentList = [];
    state.rulesForm.teacherList = [];
    state.rulesForm.personListDTO = checked.map((item) => {
        let {
            userId,
            studentCode,
            id,
            name,
            showName,
            identity,
            rollValue,
            _type,
            peopleType,
        } = item;
        let parma = { id, name: showName || name, identity: _type || identity || 0 };
        userName.push(showName || name);
        if (_type == null || _type == undefined) {
            _type = identity;
        }
        // 1为教师  0为学生
        const isType = (Type) => item.hasOwnProperty(Type);
        // 类型说明:
        // 学籍时返回:班级 classes，学生:student，家长:eltern
        // 部门时返回:部门:dept，部门员工:people_dept
        // 角色时返回:角色:role，角色员工:people_role
        // 外部人员返回:组:external，组成员:people_external
        //identity 身份标识(0.学生 1.教职工 2.家长 3. 外部人员 4.自定义组)
        if (peopleType) {
            // peopleType  2为教师 1为学生
            if (peopleType == 1) {
                parma.identity = 0;
                parma.typeValue = "student";
                parma.userId = userId || id;
            } else {
                // 教职工人员
                parma.identity = 1;
                parma.typeValue = "people_dept";
                parma.userId = userId;
            }
        } else {
            if (_type === 1) {
                // 教职工
                if (isType("pid")) {
                    parma.typeValue = "dept";
                } else {
                    // 教职工人员
                    parma.typeValue = "people_dept";
                    parma.userId = userId;
                }
            } else if (_type === 2) {
                // 家长
                if (isType("pid")) {
                    parma.typeValue = rollValue;
                } else {
                    parma.typeValue = "eltern";
                    parma.userId = userId;
                }
            } else {
                // 学生
                if (isType("userId") || isType("studentCode")) {
                    parma.typeValue = "student";
                    parma.userId = userId || id;
                } else {
                    parma.typeValue = rollValue;
                }
            }
        }
        return parma;
    });
    state.rulesForm.personnelName = userName.join("、");
    state.checkedList = checked;
};
// 选人控件
onMounted(async () => {
    // await getTypeNum();
    getSchoolAggregate();
    getRuleSetInfo();
    getPageRuleInfo();
    holidayInfo();
    SiteList();
});
</script>

<style lang="less" scoped>
:deep(.ant-picker-range) {
    input {
        text-align: center;
    }

    input::-ms-input-placeholder {
        text-align: center;
    }

    input::-webkit-input-placeholder {
        text-align: center;
    }
}

.trafficRuleDiv {
    .title {
        font-size: 16px;
        font-weight: 600;
        margin: 16px 0;
    }

    .left {
        margin: 0 16px;

        .item {
            li {
                color: #000000a6;
                margin: 12px 0;
            }
        }
    }

    .right {
        width: 100%;
        overflow: hidden;
        padding: 0 16px;

        .tableBox {
            padding: 0 !important;
        }

        .searchHead {
            display: flex;
            justify-content: space-between;
        }
    }

    .ant-tabs {

        .ant-tabs-nav,
        .ant-tabs-nav-operations {
            display: flex !important;
        }
    }

    .addRuleModal .ant-modal-footer {
        display: none;
    }

    .abnormalModal .ant-modal-footer {
        display: none;
    }

    .ant-drawer-close {
        position: absolute;
        right: 0;
    }

    .atab .ant-tabs-content-holder {
        min-height: 100px;
        padding-left: 16px;
        padding-bottom: 20px;
    }

    .atab {
        min-width: 450px;
        border: 1px solid @border-color-base;
        margin-bottom: 12px;
    }

    #atabDiv {
        border: 1px solid @border-color-base;
        margin-bottom: 10px;

        .ant-tabs-tab {
            margin-left: 1px !important;
        }
    }

    // .addRuleModal {
    #atabDiv-more {
        display: none !important;
    }

    // }
    .addRuleModal #itemTooltip {
        position: absolute;
        top: 19px;
        left: 95px;
    }

    .atab .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: @body-background;
        background-color: @primary-color;
    }

    .abnormalModal #itemTooltip {
        position: absolute;
        top: 19px;
        left: 12px;
    }

    .abnormalModal .ant-modal-content .ant-modal-header .ant-modal-title {
        margin-left: 12px !important;
    }

    #atabDiv.ant-tabs-top>.ant-tabs-nav .ant-tabs-tab-active,
    #atabDiv.ant-tabs-top>div>.ant-tabs-nav .ant-tabs-tab-active {
        background-color: @primary-color;
        color: @body-background;
    }

    #atabDiv .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: @body-background;
    }

    #atabDiv .ant-tabs-content-holder {
        padding: 0 12px 21px;
    }

    .searchHead1 {
        display: flex;
        justify-content: space-between;
        margin-left: 15px;
    }
}

.text1 {
    font-size: 18px;
    font-weight: 500;
    margin: 20px 0 20px 0;
    margin-left: 14px;
}

.noData_empty {
    width: 180px;
    margin: 150px auto;
}

.threeButton {
    text-align: center;

    .btn {
        width: 200px;
        margin-bottom: 14px;
    }
}

.footer {
    text-align: center;
}

.rulesText {
    color: #909399;
}

.holidayText {
    margin-left: 12px;
    color: rgba(0, 0, 0, 0.45);
}

.atab {
    :deep(.ant-tabs-tab) {
        margin: 0 !important;
        background-color: @primary-color !important;
        color: @body-background;
        border-radius: 0 !important;
    }

    :deep(.ant-tabs-tab-active) {
        background-color: @primary-color !important;

        .ant-tabs-tab-btn {
            background-color: @primary-color !important;
            color: @body-background;
        }
    }
}

.open {
    display: flex;
    height: 100px;

    p {
        width: 60px;
        align-items: center;
    }
}

.stateLi {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.reset-layout-header {
    height: 57px;
    border-bottom: 1px solid @border-color-base;
}

.left-outlined {
    color: var(--primary-color);
    margin-right: 5px;
}

:deep(.ant-tabs-card) {
    border: 1px solid @border-color-base;
    border-radius: 5px;
    margin: 10px 0;
}

:deep(.ant-tabs-content) {
    padding: 0 20px 20px 20px;
}
</style>
