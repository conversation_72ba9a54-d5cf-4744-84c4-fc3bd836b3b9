<template>
    <a-drawer v-model:visible="props.visible" :title="state.title" width="500" @close="closeVisible">
        <a-form :model="state.rulesForm" layout="vertical" ref="rulesRef" :rules="rulesForm">
            <a-form-item label="规则名称：" name="ruleName">
                <a-input v-model:value="state.rulesForm.ruleName" placeholder="请输入" allow-clear maxlength="20" />
            </a-form-item>
            <a-form-item label="选择场地：" name="siteId">
                <a-tree-select class="select_site_class" v-model:value="state.rulesForm.siteId" show-search
                    placeholder="请选择" allow-clear tree-default-expand-all :tree-data="state.siteOption" :field-names="{
                        children: 'children',
                        label: 'name',
                        key: 'id',
                        value: 'id'
                    }" @select="selectField" :disabled="state.disabled" :showSearch="false"></a-tree-select>
            </a-form-item>
            <a-form-item label="选择通行设备：" name="deviceType">
                <a-select v-model:value="state.rulesForm.deviceType" style="width: 100%" placeholder="请选择">
                    <a-select-option :value="1">人脸机</a-select-option>
                    <a-select-option :value="3">考勤机</a-select-option>
                </a-select>
            </a-form-item>

            <a-form-item label="选择人员：" name="personnelName">
                <y-select mode="personnel" :tabs="[
                    { tab: '教职工', key: 1, checked: true },
                    { tab: '学生组', key: 2, checked: false }
                ]" v-model:visible="personnelvisible" v-model:checked="checkedList" :queryEltern="false"
                    @handleOk="handleOk">
                    <a-input v-model:value="state.rulesForm.personnelName" @click="addMember" allowClear placeholder="请选择"
                        readonly="readonly" />
                </y-select>
            </a-form-item>
            <a-form-item v-if="state.rulesForm.deviceType === 1" label="通行方向：" name="direction" class="labelAlign">
                <a-radio-group v-model:value="state.rulesForm.direction" :options="state.plainOptions"
                    @change="changeDirection">
                </a-radio-group>
            </a-form-item>
            <a-form-item label="有效期：" name="time">
                <a-range-picker v-model:value="state.rulesForm.time" value-format="YYYY-MM-DD" style="width: 100%"
                    separator="至" :disabled-date="disabledDate" @calendarChange="calendarPriceRangeChange" />
            </a-form-item>
            <label>
                <i style="color: red">*</i>
                通行规则：
                <span class="1" style="color: #00000033"> (通行规则按照每周循环) </span>
            </label>
            <div class="rules">
                <a-radio-group v-model:value="state.activeKey" button-style="solid" class="radio-group">
                    <a-radio-button v-for="(item, idx) in state.plainOptionsttt" :key="idx" :value="idx">
                        {{ item.weekName }}
                    </a-radio-button>
                </a-radio-group>
                <div class="content">
                    <a-radio-group name="radioGroup" v-model:value="state.plainOptionsttt[state.activeKey].isAllow
                        ">
                        <a-radio value="1">自定义</a-radio>
                        <a-radio value="2">允许全天通行</a-radio>
                    </a-radio-group>
                    <a-form-item class="labelAlign" v-for="(item, idx) in state.plainOptionsttt[
                        state.activeKey
                    ].ruleTimeList" :key="item.week" label="通行时间：" :name="['timelist', idx, 'time']">
                        <!-- :rules="[{ required: true, message: '请选择通行时间' }]" -->
                        <a-time-range-picker v-model:value="item.time" value-format="HH:mm:ss" />
                        <i v-if="state.plainOptionsttt[state.activeKey]
                            .ruleTimeList.length > 1
                            " class="iconfont icon-shouye-shanchu" @click="removeUser(idx)"></i>
                    </a-form-item>
                    <a-button @click="addUser" type="primary">
                        <template #icon> <plus-outlined /> </template>
                        添加通行时间
                    </a-button>
                    <br />
                    <br />
                    <a-checkbox v-model:checked="state.everyDay" @change="changeEveryDay">
                        应用到每天
                    </a-checkbox>
                </div>
            </div>
        </a-form>
        <template #footer>
            <div class="footer">
                <a-button style="margin-right: 8px" @click="closeVisible">
                    取消
                </a-button>
                <a-button type="primary" @click="saveConfirm">确认</a-button>
            </div>
        </template>
    </a-drawer>
</template>

<script setup>
import {
    createVNode,
    reactive,
    onMounted,
    shallowRef,
    watch,
    nextTick
} from 'vue'
import { Modal, message } from 'ant-design-vue'
import { PlusOutlined, ExclamationCircleFilled } from '@ant-design/icons-vue'
import { getHolidayInfo, createPasssceneRule } from '@/api/trafficRules.js'
import dayjs from 'dayjs'
const rulesRef = shallowRef()
const state = reactive({
    title: '添加常规规则',
    personnelvisible: false,
    siteOption: [],
    checkedList: [],
    fieldList: [],
    disabled: false,
    everyDay: false,
    activeKey: 1,
    params: {
        isDay: '',
        type: 1,
        passsceneId: 1,
        // holidayDataSource: [],
        ruleHolTimeList: []
    },
    rulesForm: {
        id: '',
        ruleName: '',
        siteId: null,
        personnelName: '',
        teacherList: [], // 教职工id
        studentList: [], // 学生id
        direction: '',
        time: '',
        deviceType: null
    },
    plainOptions: [
        { label: '出', value: 1 },
        { label: '入', value: 2 }
    ],
    plainOptionsttt: [
        {
            weekName: '周一',
            week: 1,
            isAllow: '0',
            name: '1',
            ruleTimeList: [
                {
                    time: ['06:00:00', '20:00:00']
                }
            ]
        },
        {
            weekName: '周二',
            week: 2,
            isAllow: '0',
            name: '2',
            ruleTimeList: [
                {
                    time: []
                }
            ]
        },
        {
            weekName: '周三',
            week: 3,
            isAllow: '0',
            name: '3',
            ruleTimeList: [
                {
                    time: []
                }
            ]
        },
        {
            weekName: '周四',
            week: 4,
            isAllow: '0',
            name: '4',
            ruleTimeList: [
                {
                    time: []
                }
            ]
        },
        {
            weekName: '周五',
            week: 5,
            isAllow: '0',
            name: '5',
            ruleTimeList: [
                {
                    time: []
                }
            ]
        },
        {
            weekName: '周六',
            week: 6,
            isAllow: '0',
            name: '6',
            ruleTimeList: [
                {
                    time: []
                }
            ]
        },
        {
            weekName: '周日',
            week: 7,
            isAllow: '0',
            name: '7',
            ruleTimeList: [
                {
                    time: []
                }
            ]
        }
    ],
    threeBtn: [
        { text: '添加常规规则', key: 'routine' },
        { text: '添加节假日规则', key: 'holidays' },
        { text: '添加临时规则', key: 'temporary' }
    ]
})
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    siteOption: {
        type: Array,
        default: () => []
    },
    rulesType: {
        type: Number,
        default: 1
    },
    passsceneId: {
        type: Number,
        default: 1
    }
})

const emit = defineEmits(['update:visible'])
const closeVisible = () => {
    emit('update:visible', false)
}

const pwdCheck = (rule, value, callback) => {
    if (!value) {
        return Promise.reject('请选择人员')
    }
    return Promise.resolve()
}
const rulesForm = {
    ruleName: [{ required: true, message: '请输入规则名称' }],
    siteId: [{ required: true, message: '请选择场地' }],
    deviceType: [{ required: true, message: '请选择通行设备' }],
    personnelName: [{ required: true, validator: pwdCheck }],
    direction: [{ required: true, message: '请选择通行方向' }]
    // time: [{ required: true, message: '请选择有效期' }]
}
const addHolidayRules = () => {
    rulesRef.value.resetFields()
}
// 选择开始时间/结束时间
const calendarPriceRangeChange = (date) => {
    if (state.rulesType === 3) {
        state.selectPriceDate = date[0]
    }
}
// 选择场地
const selectField = (value, node, extra) => {
    state.rulesForm.siteId = value
}
// 根据选择的开始时间/结束时间，动态渲染要禁用的日期
const disabledDate = (current) => {
    if (state.rulesType === 3) {
        if (state.selectPriceDate) {
            const selectV = dayjs(state.selectPriceDate, 'YYYY-MM-DD').valueOf()
            return (
                current >
                dayjs(new Date(selectV + state.offsetDays).getTime()) ||
                current < dayjs(new Date(selectV - state.offsetDays).getTime())
            )
        } else {
            return false
        }
    }
}
// 添加通行时间
const addUser = () => {
    state.plainOptionsttt[state.activeKey].ruleTimeList.push({ time: [] })
}
// 删除
const removeUser = (i) => {
    state.plainOptionsttt[state.activeKey].ruleTimeList.splice(i, 1)
}
const changetime = (event, i) => {
    if (event.length) {
        rulesRef.value.validateFields(i)
    }
}

// 添加普通规则接口
const CreatePasssceneRule = () => {
    const params = {
        ...state.rulesForm,
        ...state.params
    }
    createPasssceneRule(params)
        .then((res) => {
            message.success(res.message)
            // state.rulesVisible = false
            // rulesRef.value.resetFields()
        })
        .catch((err) => {
            if (err.data.code === 1002013009) {
                state.abnormalVisible = true
                state.abnormalDataSource = err.data.data
            }
        })
}
const saveConfirm = () => {
    rulesRef.value.validateFields().then(() => {
        CreatePasssceneRule()
        if (state.rulesForm.id) {
            Modal.confirm({
                title: '您确定要修改该条规则吗？',
                icon: createVNode(ExclamationCircleFilled),
                content: '修改后，旧的规则将失效，会议机将匹配新的人员和规则。',
                okText: '确认',
                cancelText: '取消',
                onCancel() { },
                onOk() { }
            })
        }
    })
}

const changeEveryDay = (event) => {
    Modal.confirm({
        title: '应用到每天',
        icon: createVNode(ExclamationCircleFilled),
        content: event.target.checked
            ? '确定要将当前通行规则应用到每天吗？'
            : '确定要取消应用到每天吗？',
        okText: '确认',
        cancelText: '取消',
        onCancel() {
            message.info('已取消！')
            state.everyDay = !event.target.checked
        },
        onOk() {
            state.everyDay = event.target.checked
            if (event.target.checked) {
                state.weekForm.weekList.forEach((item) => {
                    item.timelist = JSON.parse(
                        JSON.stringify(
                            state.weekForm.weekList[state.activeKey]
                                .ruleTimeList
                        )
                    )
                    item.isAllow = JSON.parse(
                        JSON.stringify(
                            state.weekForm.weekList[state.activeKey].isAllow
                        )
                    )
                })
            }
        }
    })
}
const handleOk = (data) => {
    const personnelName = []
    if (!data.length) return
    data.forEach((v) => {
        personnelName.push(v.name)
    })
    state.rulesForm.personnelName = personnelName.join('、')
    state.rulesForm.studentList = data
        .filter((item) => {
            // _source1为教师 2为学生  peopleType2为教师 1为学生
            return item._source === 2 || item.peopleType === '1'
        })
        .map((item) => {
            return {
                peopleId: item.id
            }
        })
    state.rulesForm.teacherList = data
        .filter((item) => {
            // _source1为教师 2为学生  peopleType2为教师 1为学生
            return item._source === 1 || item.peopleType === '2'
        })
        .map((item) => {
            return { userId: item.userId, peopleId: item.id }
        })
}
// 禁用选中父节点
const disabledP = (tree) => {
    tree.forEach((item) => {
        if (item.children) {
            item.disabled = true
            disabledP(item.children)
        }
    })
}

// 获取法定节假日
const holidayInfo = () => {
    getHolidayInfo().then((res) => {
        const { data } = res
        state.params.holidayDataSource = data
    })
}
watch(
    () => props.visible,
    (val) => {
        if (val) {
            state.params.type = props.rulesType
            state.params.passsceneId = props.passsceneId
            if (props.siteOption.length) {
                const newSiteOption = JSON.parse(
                    JSON.stringify(props.siteOption)
                )
                disabledP(newSiteOption)
                nextTick(() => {
                    state.siteOption = newSiteOption
                })
            }
        }
    },
    {
        immediate: true
    }
)
onMounted(() => {
    holidayInfo()
})
</script>

<style scoped lang="less">
.threeButton {
    margin: 0 50px;

    .btn {
        margin: 10px auto;
    }
}

.labelAlign {
    display: flex;
    flex-direction: initial;
    align-items: baseline;
    min-height: 45px;

    :deep(.ant-form-item-control) {
        position: relative;

        .ant-form-item-explain-connected {
            position: absolute;
            left: -80px;
            top: 23px;
        }
    }
}

.rules {
    overflow: hidden;
    margin-top: 10px;
    border: 1px solid @border-color-base;

    .radio-group {
        width: 450px;

        .ant-radio-button-wrapper {
            padding: 0 17.5px;
            border-top: none;

            &:first-child {
                border-left: none;
            }

            &:last-child {
                border-right: none;
            }
        }
    }

    .content {
        padding: 16px;

        .labelAlign {
            margin: 10px 0;
            min-height: 45px;

            .iconfont {
                margin: 0 10px;
                font-size: 18px;
            }

            :deep(.ant-form-item-explain-connected) {
                position: absolute;
                left: -80px;
                top: 32px;
            }
        }
    }
}
</style>
