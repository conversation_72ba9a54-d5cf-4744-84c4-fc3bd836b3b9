/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2023-05-04 14:11:49
 * @LastEditors: jingrou
 * @LastEditTime: 2023-06-01 15:32:19
 */
export const tableColumns = [
    {
        title: 'ID',
        // ellipsis: true,
        dataIndex: 'id',
        width: 200
    },
    {
        title: '姓名',
        dataIndex: 'userName',
        width: 100
    },
    {
        title: '人员类型',
        dataIndex: 'userType',
        width: 130
    },
    {
        title: '状态',
        dataIndex: 'status',
        width: 140
    },
    {
        title: '有效期（结束日期）',
        dataIndex: 'expirationEndTime',
        width: 200
    },
    {
        title: '更新时间',
        dataIndex: 'syncTime',
        width: 200
    },
    {
        title: '操作',
        ellipsis: true,
        dataIndex: 'operation',
        width: 100,
        fixed: 'right'
    }
]
export const deviceaceStatusText = [
    '已同步',
    '异常',
    '未同步',
    '同步中',
    '删除中'
]

export const deviceaceStatus = [
    'success',
    'error',
    'default',
    'warning',
    'error'
]

export const statusList = [
    { className: '全部', status: null },
    { className: '已同步', status: 0 },
    { className: '异常', status: 1 },
    { className: '未同步', status: 2 },
    { className: '同步中', status: 3 },
    { className: '删除中', status: 4 }
]
