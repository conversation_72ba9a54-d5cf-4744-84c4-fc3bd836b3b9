<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: zly
 * @Date: 2022-01-10 17:00:41
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-04-08 18:26:32
-->
<template>
    <div>
        <a-modal v-model:visible="visible" title="新增权限组" @ok="handleOk">
            <a-form
                :layout="formState.layout"
                :model="formState"
                v-bind="formItemLayout"
            >
                <a-form-item label="权限组名称：">
                    <a-input
                        v-model:value="formState.fieldA"
                        placeholder="请输入权限组名称"
                    />
                </a-form-item>
                <a-form-item label="权限成员：">
                    <a-input
                        v-model:value="formState.fieldB"
                        placeholder="请输入"
                    />
                </a-form-item>

                <a-form-item label="功能权限：">
                    <a-checkbox-group
                        v-model:value="value"
                        style="width: 100%"
                        :options="functionPlainOptions"
                    />
                </a-form-item>
                <!-- <a-form-item label="功能权限：">
                    <a-select
                        ref="select"
                        v-model:value="formState.fieldC"
                        style="width: 100%"
                        @focus="focus"
                        @change="handleChange"
                        placeholder="请选择功能权限"

                        :options="functionPlainOptions"
                    >
                        <a-select-option value="0">Jack</a-select-option>
                        <a-select-option value="1">Lucy</a-select-option>
                        <a-select-option value="2">yiminghe</a-select-option>
                    </a-select>
                </a-form-item> -->
                <a-form-item label="权限范围：">
                    <div class="scope-authority">部门范围：</div>
                    <a-radio-group
                        v-model:value="value1"
                        :options="plainOptions1"
                    />
                    <a-input
                        style="margin-top: 15px"
                        v-if="value1 == '自定义'"
                        v-model:value="formState.fieldD"
                        placeholder="请选择部门范围"
                    />
                    <div class="scope-authority">学籍范围：</div>
                    <a-radio-group
                        v-model:value="value2"
                        :options="plainOptions2"
                    />
                    <a-input
                        style="margin-top: 15px"
                        v-if="value2 == '自定义'"
                        v-model:value="formState.fieldE"
                        placeholder="请选择学籍范围"
                    />
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>

<script>
import { defineComponent, reactive, ref } from 'vue'
const plainOptions1 = ['全部', '所管辖部门及下级部门', '自定义']
const plainOptions2 = ['全部', '所负责学籍组织范围含下级组织', '自定义']

export default defineComponent({
    setup() {
        const formState = reactive({
            layout: 'vertical',
            fieldA: '',
            fieldB: '',
            fieldC: '',
            fieldD: '',
            fieldE: ''
        })
        const visible = ref(true)
        const functionPlainOptions = ref([
            { label: 'Apple', value: 'Apple' },
            { label: 'Pear', value: 'Pear' },
            { label: 'Orange', value: 'Orange' }
        ])
        const focus = () => {}
        const handleOk = (e) => {
            visible.value = false
        }
        const handleChange = (value) => {}
        const value1 = ref('全部')
        const value2 = ref('全部')
        return {
            formState,
            focus,
            handleChange,
            plainOptions1,
            plainOptions2,
            value1,
            value2,
            visible,
            handleOk,
            functionPlainOptions
        }
    }
})
</script>

<style lang="scss" scoped>
.scope-authority {
    width: 100%;
    background: #f6f6f6;
    height: 28px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 28px;
    padding-left: 8px;
    margin-bottom: 15px;
}
</style>
