<template>
    <div class="face_lib">
        <a-layout class="content">
            <a-layout-sider class="sider">
                <div class="fixed">
                    <div class="top text 2">
                        <div class="left">设备列表</div>
                        <div class="right">共{{ state.all }}台</div>
                    </div>
                    <a-input-search placeholder="请输入设备名称" allowClear v-model:value="state.nameDevice"
                        style="width: 240px; margin-right: 14px" @search="searchDevice" />
                </div>
                <div class="equipment_list">
                    <div class="item" v-for="(item, index) in state.list" :key="index" @click="hanbleShe(item, index)"
                        :class="state.active === index ? 'active' : ''">
                        <div class="left">
                            <div class="on_line" :class="item.isBinding == 2 ? 'act' : ''"></div>
                            <a-tooltip placement="top" v-if="item?.machineName.length > 10">
                                <template #title>
                                    <span>{{ item.machineName }}</span>
                                </template>
                                <div class="name" :style="state.active == index
                                        ? 'color: #00B781;'
                                        : ''
                                    ">
                                    {{ item.machineName }}
                                </div>
                            </a-tooltip>
                            <div class="name" v-else :style="state.active == index
                                    ? 'color: #00B781;'
                                    : ''
                                ">
                                {{ item.machineName }}
                            </div>
                        </div>
                        <div class="right" :style="state.active == index ? 'color: #00B781;' : ''
                            ">
                            <span :style="state.active == index
                                    ? 'color: #00B781;'
                                    : ''
                                ">{{
        item.synchronousNumber
        ? item.synchronousNumber
        : 0
    }}</span>
                            /{{ item.faceNumber ? item.faceNumber : 0 }}
                        </div>
                    </div>
                </div>
            </a-layout-sider>
            <a-layout-content class="contents">
                <div class="top_name">{{ state.topname }}</div>
                <div class="warp">
                    <div class="left">
                        <span>{{ state.siteName }}</span>
                    </div>
                    <div class="right">
                        <span class="ti">序列号：</span>
                        <span class="xu_num">{{ state.no }}</span>
                    </div>
                </div>
                <div class="searchHead">
                    <a-form layout="inline">
                        <a-form-item label="">
                            <a-input placeholder="请输入姓名" :maxlength="20" v-model:value="state.name"
                                style="width: 240px; margin-right: 14px" />
                        </a-form-item>
                        <a-form-item>
                            <a-button type="primary" style="margin-right: 12px" @click="hanbleSearch()">
                                <template #icon>
                                    <SearchOutlined style="margin-right: 12px" />
                                </template>
                                查询
                            </a-button>
                            <a-button @click="resetting()">
                                <template #icon>
                                    <redo-outlined style="margin-right: 12px" />
                                </template>
                                重置
                            </a-button>
                        </a-form-item>
                    </a-form>
                    <div>
                        <a-button @click="relevanceface()" type="primary" style="margin-right: 12px">关联人脸库
                        </a-button>
                        <a-button @click="Oneclicksynchronization()" type="primary" :disabled="state.faceTable && state.faceTable.length == 0
                            " style="margin-right: 12px">一键同步</a-button>
                        <a-button @click="clearall()" danger :disabled="state.faceTable && state.faceTable.length == 0
                            ">清空</a-button>
                    </div>
                </div>
                <YTable :columns="tableColumns" :dataSource="state.faceTable" :slots="['operation']" :rowSelection="false"
                    :totals="state.pagination" :spinLoad="state.spinLoad" @onSelectedRowKeys="handerSelectedRowKeys">
                    <template #headerCell="{ column }">
                        <template v-if="column.dataIndex === 'status'">
                            <a-dropdown :placement="bottomRight">
                                <a style="color: #000" @click.prevent>
                                    状态
                                    <caret-down-outlined style="color: var(--primary-color)" />
                                </a>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item v-for="(
                                                item, index
                                            ) in state.statusList" :key="index" @click="chooseStatus(item, index)">
                                            <span :class="state.statusIndex === index
                                                    ? 'act'
                                                    : ''
                                                ">{{ item.className }}</span>
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </template>
                        <template v-if="column.dataIndex === 'userType'">
                            <a-dropdown :placement="bottomRight">
                                <a style="color: #000" @click.prevent>
                                    人员类型
                                    <caret-down-outlined style="color: var(--primary-color)" />
                                </a>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item v-for="(
                                                item, index
                                            ) in state.peopleList" :key="index" @click="choosePeople(item, index)">
                                            <span :class="state.peopleIndex === index
                                                    ? 'act_peo'
                                                    : ''
                                                ">{{ item.label }}</span>
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </template>
                    </template>
                    <template #bodyCell="{ column, text, record }">
                        <template v-if="column.dataIndex === 'userType'">
                            <span>{{
                                record.userType == 1
                                ? '学生'
                                : record.userType == 2
                                    ? '教师工'
                                    : '非本校师生'
                            }}</span>
                        </template>
                        <template v-if="column.dataIndex === 'expirationStartTime'">
                            <span v-if="record.expirationEndTime">
                                {{ record.expirationStartTime }} -
                                {{ record.expirationEndTime }}
                            </span>
                            <span v-else>--</span>
                        </template>
                        <template v-if="column.dataIndex === 'status'">
                            <a-badge v-if="record?.status === 0" status="success" text="已同步" />
                            <a-badge v-if="record?.status === 1" status="error" text="异常" />
                            <a-badge v-if="record?.status === 2" status="default" text="未同步" />
                            <a-badge v-if="record?.status === 3" status="warning" text="同步中" />
                        </template>
                        <template v-if="column.dataIndex === 'operation'">
                            <a style="color: #00b781; margin-right: 10px" @click="syncFace(record)">同步</a>
                            <a style="color: var(--error-color)" @click="delFace(record)">删除</a>
                        </template>
                    </template>
                </YTable>
            </a-layout-content>
        </a-layout>
    </div>
    <!-- 选人组件 -->
    <faceSelect ref="select11" mode="personnel" @emitChangeTabs="emitChangeTabs" :treeData="state.treeData"
        :customTree="state.customTree" :deviceId="state.id" :tabs="[
            { tab: `教职工组`, key: 1, checked: true },
            { tab: `学生组`, key: 2, checked: false },
            { tab: `自定义组`, key: 4, checked: false }
        ]" v-model:visible="state.visible" v-model:checked="state.checkedList" @handleOk="handerCallbackParameter" />
    <FaceModel ref="faceRef" @syncOnePerson="syncOnePerson"></FaceModel>
</template>

<script setup>
import { onMounted, reactive, ref, watch, nextTick } from 'vue'
import YTable from 'comps/YTable/index.vue'
import faceSelect from '@/components/YSelect/indexFace.vue'
import { tableColumns } from './tableColumns.js'
import { modalConfirm } from '@/utils/util'
import { ExclamationCircleFilled } from '@ant-design/icons-vue'
import FaceModel from './FaceModel.vue'

import { useStore } from '@/store/index'
import {
    deviceList,
    getRecordPage,
    syncAll,
    clearFace,
    bindFaceInfo,
    machineList
} from '@/api/classSign'
import { getCustomGroupTree } from '@/api/faceLibrary'
import { message } from 'ant-design-vue'
const store = useStore()
const select11 = ref(null)
const { face_sync_status, face_user_type } = store.state.selectSource.dictionary
const faceRef = ref()

const props = defineProps({
    deviceType: {
        type: String,
        default: ''
    }
})

const state = reactive({
    treeData: [],
    customTree: [], // 自定义组的数据
    id: '', // 设备id
    schoolId: '',
    synchronousNumber: 0, // 已同步总数
    faceNumber: 0, // 总数
    all: 0, // 设备总数
    curObj: {}, // 当前选择的设备
    // 设备列表
    device: {
        pageNo: 1,
        pageSize: 100
    },
    visible: false,
    checkedList: [],
    name: '', // 搜索的人名
    nameDevice: '', // 设备名称
    siteName: '', // 场地名称
    no: '', // 设备编号
    List: [],
    firstCount: 0, // 第一个设备列表下的设备数
    active: 0,
    topname: '',
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
    },
    statusList: [
        { className: '全部', status: null },
        { className: '已同步', status: 0 },
        { className: '异常', status: 1 },
        { className: '未同步', status: 2 },
        { className: '同步中', status: 3 }
    ],
    statusIndex: 0,
    peopleList: [],
    peopleIndex: 0,
    faceTable: [],
    userId: '',
    // 弹框里面的在线状态和设备数量
    onLine: 0, // 在线状态
    onCount: 0, // 同步数
    onAllCount: 0 // 总的设备数
})

// 获取自定义组的数据(分组数据)
// let treeDatas = []
getCustomGroupTree().then((res) => {
    // treeDatas = res.data
    state.customTree = res.data
})

const emitChangeTabs = (key) => {
    const data = {
        1: [
            {
                id: '2121211',
                leader: '323232322',
                name: '全校',
                showName: '全校',
                sort: -1,
                type: -1,
                children: store.state.base.departmentTree
            }
        ],
        2: store.state.base.schoolRollTree,
        4: state.customTree
    }
    state.treeData = ref(data[key])
}
// 关联人脸库
const handerCallbackParameter = (e) => {
    // 学生1   教职工2  自定义人员3
    let arr = []
    arr = e.map((item) => {
        if (item._source === 1) {
            // 教职工
            return {
                peopleType: 2,
                schoolUserId: item.id
            }
        } else if (item._source === 2) {
            // 学生
            return {
                peopleType: 1,
                schoolUserId: item.id
            }
        } else {
            return {
                peopleType: item.faceType,
                schoolUserId: item.userId
            }
        }
    })
    window.localStorage.setItem('face_one', JSON.stringify(arr))
    const params = {
        // deviceIds: [],
        // userIds: [],
        userInfo: arr,
        deviceId: state.id
    }
    bindFaceInfo(params).then((res) => {
        message.success(res.message)
        getRecordPageFn()
        deviceListFn()
    })
}
// 搜索设备列表
const searchDevice = () => {
    deviceListFn(state.nameDevice)
}
// 查询
const hanbleSearch = () => {
    getRecordPageFn()
}
// 重置
const resetting = () => {
    state.name = ''
    state.pagination.pageNo = 1
    getRecordPageFn()
}
/* 分页事件 */
const handerSelectedRowKeys = (data) => {
    const { current, pageSize } = data
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    getRecordPageFn()
}
// 同步
const syncFace = (item) => {
    state.userId = item.userId
    faceRef.value.visible = true
    faceRef.value.checkAll = false
    // 获取相同设备的列表
    const params = {
        pageNo: 1,
        pageSize: 100,
        syncUserIds: [item.userId],
        // syncUserIds: item.userId,
        syncEquipmentId: state.id
    }
    deviceList(params).then((res) => {
        faceRef.value.status = state.list[state.active].status
        faceRef.value.name = state.list[state.active].name
        faceRef.value.address = state.list[state.active].siteName
        faceRef.value.count = state.list[state.active].synchronousNumber
        faceRef.value.all = state.list[state.active].faceNumber
        faceRef.value.plainOptions = res.data.list
    })
}
// 删除
const delFace = (item) => {
    modalConfirm('提示', '确定删除人员信息？', ExclamationCircleFilled).then(
        (res) => {
            if (res) {
                // 调接口
                clearFace({
                    deviceId: window.localStorage.getItem('device_id') || state.id,
                    userIds: [item.userId]
                }).then((res) => {
                    message.success(res.message)
                    getRecordPageFn()
                    deviceListFn()
                })
            }
        }
    )
}
// 切换状态
const chooseStatus = (item, index) => {
    state.pagination.pageNo = 1
    state.statusIndex = index
    getRecordPageFn(item.status, null)
}
// 选择人员类型
const choosePeople = (item, index) => {
    state.peopleIndex = index
    getRecordPageFn(null, item.value)
}
// 关联人脸库
const relevanceface = () => {
    select11.value.selectedList.length = 0
    state.visible = true
}
// 一键同步
const Oneclicksynchronization = () => {
    modalConfirm(
        '一键同步',
        '确定同步列表中所有人员的人脸信息？',
        ExclamationCircleFilled
    ).then((res) => {
        if (res) {
            // 调接口
            syncAll({
                deviceId: state.id
            }).then((res) => {
                message.success(res.message)
                getRecordPageFn()
            })
        }
    })
}
// 同步单个人
const syncOnePerson = () => {
    // if (faceRef.value.checkedList.length === 0) {
    //     message.error('请选择要同步的设备')
    //     return
    // }
    let ids = []
    ids = faceRef.value.checkedList.map((item) => {
        return item.id
    })
    const params = {
        deviceId: state.id,
        userIds: [state.userId],
        syncDeviceIds: ids
    }
    syncAll(params).then((res) => {
        message.success(res.message)
        faceRef.value.visible = false
        getRecordPageFn()
    })
}
// 清空
const clearall = () => {
    modalConfirm(
        '提示',
        '确定清空本机所有的人员信息？',
        ExclamationCircleFilled
    ).then((res) => {
        if (res) {
            // 调接口
            clearFace({
                deviceId: state.id
            }).then((res) => {
                message.success(res.message)
                getRecordPageFn()
                deviceListFn()
            })
        }
    })
}
const hanbleShe = (item, index) => {
    state.faceTable = []
    state.pagination.total = 0
    state.active = index
    state.topname = state.list[index].machineName
    state.siteName = state.list[index].siteName
    state.no = state.list[index].no
    state.id = state.list[index].id // 设备id
    window.localStorage.setItem('device_id', state.id)
    state.synchronousNumber = state.list[index].synchronousNumber
    state.faceNumber = state.list[index].faceNumber
    state.curObj = item
    getRecordPageFn()
}
watch(
    () => props.deviceType,
    (val) => { },
    { deep: true, immediate: true }
)
watch(
    () => state.list,
    (val) => {
        state.topname = val[0]?.machineName
        state.siteName = val[0]?.siteName
        state.no = val[0]?.no
        state.id = val[0]?.id
        // window.localStorage.setItem('device_id', state.id)
        state.schoolId = val[0]?.schoolId
    },
    { deep: true }
)

// 设备列表-----
// const deviceListFn = () => {
//     deviceList({
//         pageSize: state.device.pageSize,
//         pageNo: state.device.pageNo,
//         name: state.nameDevice
//     }).then((res) => {
//         state.list = res.data.list
//         state.all = res.data.list.length
//         if (state.list.length > 0) {
//             state.id = res.data.list[0].id
//             state.schoolId = res.data.list[0].schoolId
//         }
//         getRecordPageFn()
//         // 获取第一个数据
//     })
// }
// 教室考勤的设备列表

const machineListFn = () => {
    machineList({
        pageSize: state.device.pageSize,
        pageNo: state.device.pageNo,
        deviceType: 3,
        no: state.nameDevice
    }).then((res) => {
        state.list = res.data.list
        state.all = res.data.list.length
        if (state.list.length > 0) {
            state.id = res.data.list[0].id
            state.schoolId = res.data.list[0].schoolId
        }
        getRecordPageFn()
        // 获取第一个数据
    })
}

// 人员列表
const getRecordPageFn = (status, userType = null) => {
    const params = {
        pageSize: state.pagination.pageSize,
        pageNo: state.pagination.pageNo,
        userType,
        id: state.curObj.id || state.id,
        schoolId: state.curObj.schoolId || state.schoolId,
        name: state.name,
        status
    }
    getRecordPage(params).then((res) => {
        state.faceTable = res.data.list
        state.pagination.pageNo = res.data.pageNo
        state.pagination.pageSize = res.data.pageSize
        state.pagination.total = res.data.total
    })
}
onMounted(() => {
    getRecordPageFn()
    // 智慧班牌的设备列表
    // deviceListFn()

    // 教师考勤的设备列表
    machineListFn()

    // eslint-disable-next-line camelcase
    state.peopleList = face_user_type
})
</script>

<style lang="less" scoped>
.act {
    color: var(--primary-color);
}

.act_peo {
    color: var(--primary-color);
}

.face_lib {

    // height: calc(100% - 57px);
    .content {
        min-height: calc(100vh - 172px);

        .sider {
            min-width: 260px !important;
            border-right: 1px solid @border-color-base;
            padding: 20px 12px;
            max-height: 757px;
            overflow-y: scroll;

            :deep(.ant-layout-sider-children) {
                position: relative;
            }

            .fixed {
                position: sticky;
                background-color: #fff;

                .top {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 14px;
                    background-color: #fff;
                }
            }

            &::-webkit-scrollbar {
                display: none;
            }

            .text {
                font-size: 14px;
                font-weight: 500;
                color: rgba(0, 0, 0, 0.85);
            }

            .equipment_list {
                margin-top: 20px;

                .item {
                    height: 1px;
                    border-bottom: 1px solid #d9d9d9;
                    width: 100%;
                    height: 52px;
                    line-height: 52px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    cursor: pointer;

                    .left {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        .on_line {
                            width: 8px;
                            height: 8px;
                            border-radius: 50%;
                            background-color: #bfbfbf;
                            margin-right: 6px;
                        }

                        .name {
                            width: 157px;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }

                        .act {
                            background-color: #00b781;
                        }
                    }

                    .right {
                        color: #ccc;

                        span {
                            color: #000000;
                        }
                    }

                    &:last-child {
                        border-bottom: none;
                        // padding-bottom: 20px;
                    }
                }

                .active {
                    background: rgba(0, 183, 129, 0.08);
                    color: #00b781;
                }
            }
        }

        .contents {
            background: #fff;
            padding: 23px 14px;

            .top_name {
                margin-bottom: 20px;
            }

            .warp {
                margin-bottom: 22px;
                display: flex;

                .left {
                    margin-right: 24px;
                }

                .right {
                    .ti {
                        color: rgba(0, 0, 0, 0.45);
                    }
                }
            }

            .searchHead {
                display: flex;
                justify-content: space-between;
                margin: 24px 0 16px 0;
            }
        }
    }
}

:deep(.ant-layout-content) {
    max-height: 756px;
    overflow: scroll;
}
</style>
