
<template>
    <div class="apply_group__warp">
        <!-- 搜索框 -->
        <div class="search_box">
            <a-input-search
                v-model:value="state.searchValue"
                placeholder="搜索应用名称"
                style="width: 300px"
                allow-clear
                @change="handleSearch"
            />
            <div>
                <a-button type="primary" @click="handleCommonAppSetting">
                    常用应用设置
                </a-button>
            </div>
        </div>
        <!-- 应用分组 -->
        <div
            class="apply_group"
            v-for="(group, index) in filteredData"
            :key="`group_${index}`"
        >
            <div class="apply_group__title">{{ group.categoryName }}</div>
            <a-row class="apply_group__list" :gutter="16">
                <!-- eslint-disable vue/no-use-v-if-with-v-for,vue/no-confusing-v-for-v-if -->
                <a-col
                    :span="4"
                    class="apply_item__warp"
                    v-for="(apply, cindex) in group.apps"
                    :key="`apply_${cindex}`"
                    :xs="24"
                    :sm="24"
                    :md="12"
                    :lg="8"
                    :xl="6"
                    :xxl="4"
                >
                    <!-- 应用 -->
                    <div
                        class="apply_item"
                        @click="handleClick(apply)"
                        v-if="apply.status !== 1"
                    >
                        <div class="apply_item__info">
                            <div class="apply_logo">
                                <a-image
                                    :width="48"
                                    :height="48"
                                    :src="apply.logo"
                                    :preview="false"
                                    v-if="apply.logo"
                                />
                                <span
                                    class="icon_content"
                                    v-else
                                    v-html="iconFont(apply)"
                                >
                                </span>
                            </div>
                            <span class="apply_name">{{ apply.name }}</span>
                        </div>
                        <div class="apply_item__hanle">
                            <slot name="apply" :apply="apply"></slot>
                        </div>
                    </div>
                    <!-- 停用应用 -->
                    <div
                        class="apply_item"
                        @click="handleClick(apply)"
                        v-if="apply.status === 1 && state.openApp === true"
                    >
                        <div class="apply_item__info">
                            <div class="apply_logo">
                                <a-image
                                    :width="48"
                                    :height="48"
                                    :src="apply.logo"
                                    :preview="false"
                                />
                            </div>
                            <span class="apply_name">{{ apply.name }}</span>
                        </div>
                        <div class="apply_item__hanle">
                            <slot name="apply" :apply="apply"></slot>
                        </div>
                    </div>
                </a-col>
            </a-row>
            <div
                @click="appOpen"
                v-if="group.categoryName === '停用应用'"
                class="openClass"
            >
                <up-outlined v-if="state.openApp === true" />
                <down-outlined v-if="state.openApp === false" />
                {{ state.openApp === true ? '收起' : '展开' }}
            </div>
        </div>
    </div>
</template>

<script setup>
import { reactive, computed } from 'vue'

const props = defineProps({
    data: {
        type: Array,
        default: () => []
    }
})

const state = reactive({
    openApp: false,
    searchValue: ''
})

const emit = defineEmits(['handleClick', 'openCommonAppSetting'])

function handleClick(item) {
    emit('handleClick', item)
}

function handleCommonAppSetting() {
    emit('openCommonAppSetting')
}

const appOpen = () => {
    state.openApp = !state.openApp
}

const handleSearch = () => {
    // 搜索逻辑已通过 computed 实现
}

// 过滤数据 - 根据搜索关键词模糊匹配应用名称
const filteredData = computed(() => {
    if (!state.searchValue || state.searchValue.trim() === '') {
        return props.data
    }
    
    const keyword = state.searchValue.trim().toLowerCase()
    
    return props.data
        .map(group => {
            // 过滤每个分组中的应用
            const filteredApps = group.apps.filter(app => 
                app.name && app.name.toLowerCase().includes(keyword)
            )
            
            // 只返回有匹配应用的分组
            if (filteredApps.length > 0) {
                return {
                    ...group,
                    apps: filteredApps
                }
            }
            return null
        })
        .filter(group => group !== null) // 移除没有匹配应用的分组
})

const iconFont = computed(() => {
    return (apply) => {
        if (apply.extend) {
            const item = JSON.parse(apply.extend)
            return `<i class="table_icon iconfont ${item.icon}" style="background-color: ${item.background}"></i>`
        }
        return ''
    }
})
</script>
<style lang="less" scope>
.openClass {
    width: 50px;
    cursor: pointer;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: @primary-color;
    line-height: 20px;
    margin-top: 10px;
}
</style>
<style lang="less">
.apply_group__warp {
    padding: 24px 12px;
}

.search_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.apply_group {
    padding-bottom: 24px;

    &__title {
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
    }

    .apply_item {
        margin-top: 16px;
        border-radius: 8px;
        border: 1px solid #d9d9d9;
        padding: 16px 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        user-select: none;

        &:hover {
            transition: all 0.25s;
            background: @gray-background;

            .apply_name {
                color: @primary-color;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
            }

            .apply_item__hanle .apply_hanle__icon {
                display: block;
            }

            .enable {
                background: @body-background;
            }
        }

        .apply_item__info {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .apply_logo {
            width: 48px;
            height: 48px;
            background: #eee;
            border-radius: 8px;

            img {
                border-radius: 8px;
                width: 100% !important;
                height: 100% !important;
            }
        }

        .apply_name {
            font-size: 14px;
            padding-left: 12px;
            display: block;
            max-width: 107px;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
        }

        .apply_item__hanle {
            .apply_hanle__icon {
                display: none;
                padding: 4px 0 4px 4px;
                font-size: 34px;
                font-size: 18px;
            }
        }
    }
}
</style>
