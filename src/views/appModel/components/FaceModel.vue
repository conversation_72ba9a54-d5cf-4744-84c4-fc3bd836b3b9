<template>
    <a-modal
        v-model:visible="state.visible"
        :title="state.setFaceTitle ? '同步' : '刪除'"
        @ok="handleOk"
    >
        <a-spin :spinning="state.spinning">
            <div>{{ state.setFaceTitle ? "同步" : "刪除" }}人员信息？</div>
            <div class="text">当前设备</div>
            <a-checkbox v-model:checked="state.checked">
                <div class="curr">
                    <div class="right">
                        <div class="top">
                            <div
                                class="left"
                                :style="
                                    Number(state.machineStatus) === 1
                                        ? 'color: #00b781'
                                        : ''
                                "
                            >
                                <span
                                    class="cyl"
                                    :class="
                                        ['act_no', 'act'][
                                            Number(state.machineStatus)
                                        ]
                                    "
                                ></span>
                                <span>{{ state.machineName }}</span>
                            </div>
                            <div
                                :class="
                                    Number(state.machineStatus) === 1
                                        ? 'right right_class '
                                        : 'right'
                                "
                            >
                                <span> {{ state.synchronousNumber || 0 }}</span>
                                <span class="color_black division_line">/</span>
                                <span class="color_black">
                                    {{ state.faceNumber || 0 }}
                                </span>
                            </div>
                        </div>
                        <div
                            class="down"
                            :style="
                                Number(state.machineStatus) === 1
                                    ? 'color: #00b781'
                                    : ''
                            "
                        >
                            <span>{{ state.siteName }}</span>
                        </div>
                    </div>
                </div>
            </a-checkbox>
            <div v-if="state.plainOptions && state.plainOptions.length > 0">
                <div class="tip">以下设备存在相同人员，可选择同时更新</div>
                <div style="margin-bottom: 20px">
                    <a-checkbox
                        v-model:checked="state.checkAll"
                        :indeterminate="state.indeterminate"
                        @change="onCheckAllChange"
                        >全选</a-checkbox
                    >
                </div>
                <div class="check_box">
                    <a-checkbox-group v-model:value="state.checkedList">
                        <a-checkbox
                            :value="item"
                            v-for="(item, index) in state.plainOptions"
                            :key="index"
                        >
                            <div class="curr_item">
                                <div class="right">
                                    <div class="top">
                                        <div class="left">
                                            <span
                                                class="cyl"
                                                :class="
                                                    ['act_no', 'act'][
                                                        Number(
                                                            item.machineStatus
                                                        )
                                                    ]
                                                "
                                            ></span>
                                            <span>{{ item.machineName }}</span>
                                        </div>
                                        <div class="right">
                                            <span>
                                                {{
                                                    item.synchronousNumber || 0
                                                }}
                                            </span>
                                            <span
                                                class="color_black division_line"
                                                >/</span
                                            >
                                            <span class="color_black">
                                                {{ item.faceNumber || 0 }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="down">
                                        <span>{{ item.siteName }}</span>
                                    </div>
                                </div>
                            </div>
                        </a-checkbox>
                    </a-checkbox-group>
                </div>
            </div>
        </a-spin>
    </a-modal>
</template>
<script setup>
import { reactive, toRefs, watch } from "vue";
const state = reactive({
    spinning: false,
    machineName: "",
    machineStatus: 0,
    faceNumber: 0,
    synchronousNumber: 0,
    siteName: "",
    checked: true,
    // 多选----
    checkAll: false,
    checkedList: [],
    plainOptions: [],
    indeterminate: false,
    // 多选
    visible: false,
    setFaceTitle: true,
});
const emit = defineEmits(["syncOnePerson"]);

function onCheckAllChange(e) {
    Object.assign(state, {
        checkedList: e.target.checked ? state.plainOptions : [],
        indeterminate: false,
    });
}
watch(
    () => state.checkedList,
    (val) => {
        state.indeterminate =
            !!val.length && val.length < state.plainOptions.length;
        state.checkAll = val.length === state.plainOptions.length;
    }
);
watch(
    () => state.visible,
    (val) => {
        if (val) {
            state.indeterminate = false;
            state.checkAll = false;
        }
    }
);
// 同步单个人
function handleOk() {
    emit("syncOnePerson");
}
defineExpose({
    ...toRefs(state),
});
</script>
<style lang="less" scoped>
.text {
    font-size: 14px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    margin-top: 20px;
    margin-bottom: 12px;
}
.curr {
    display: flex;
    align-items: center;
    padding: 12px;
    color: #00b781;
    width: 100%;
    height: 72px;
    border-radius: 4px;
    background: rgba(0, 183, 129, 0.08);
    .left {
        margin-right: 14px;
    }
    .right {
        .top {
            width: 420px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            .left {
                max-width: 70%;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
                font-size: 14px;
                font-family: PingFangSC-Semibold, PingFang SC;
                font-weight: 600;
                line-height: 20px;
                color: rgba(0, 0, 0, 0.85);
                .cyl {
                    margin-right: 5px;
                    display: inline-block;
                    width: 8px;
                    height: 8px;
                    border-radius: 4px;
                }
                .act_no {
                    background-color: #bfbfbf;
                }
                .act {
                    background-color: #00b781;
                }
            }
            .right {
                color: rgba(0, 0, 0, 0.85);
                flex: 1;
                text-align: right;
                .division_line {
                    padding: 0px 2px;
                }
                .color_black,
                .division_line {
                    color: rgba(0, 0, 0, 0.45);
                }
            }
            .right_class {
                .division_line,
                .color_black,
                span {
                    color: #00b781 !important;
                }
            }
        }
        .down {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.45);
            line-height: 20px;
        }
    }
    :deep(.ant-checkbox) {
        top: 20px;
    }
}
:deep(.ant-checkbox-wrapper) {
    margin: 0 !important;
    width: 100% !important;
}
.curr_item {
    display: flex;
    align-items: center;
    padding: 12px;
    // color: #00b781;
    width: 440px;
    height: 72px;
    border-radius: 4px;
    background: #f6f6f6;
    margin-bottom: 10px;
    .right {
        width: 100%;
        .top {
            width: 100%;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            .left {
                max-width: 70%;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.85);
                font-family: PingFangSC-Semibold, PingFang SC;
                font-weight: 600;
                line-height: 20px;
                .cyl {
                    margin-right: 5px;
                    display: inline-block;
                    width: 8px;
                    height: 8px;
                    border-radius: 4px;
                    background-color: #00b781;
                }
                .act_no {
                    background-color: #bfbfbf;
                }
                .act {
                    background-color: #00b781;
                }
            }
            .right {
                flex: 1;
                text-align: right;
                color: rgba(0, 0, 0, 0.85);
                .division_line {
                    padding: 0px 2px;
                }
                .color_black,
                .division_line {
                    color: rgba(0, 0, 0, 0.45);
                }
            }
            .right_class {
                .division_line,
                .color_black,
                span {
                    color: #00b781 !important;
                }
            }
        }
        .down {
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.45);
            line-height: 20px;
        }
    }
    :deep(.ant-checkbox) {
        top: 20px;
    }
}
.tip {
    margin: 20px 0;
}
.check_box {
    max-height: 280px;
    overflow-y: auto;
    overflow-x: hidden;
}
</style>
