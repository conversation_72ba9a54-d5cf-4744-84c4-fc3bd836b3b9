<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-03-23 15:16:54
 * @LastEditors: jingrou
 * @LastEditTime: 2022-03-24 11:26:20
-->
<template>
    <div></div>
</template>
<script setup>
import { reactive, onMounted } from 'vue'
const state = reactive({
    toDoTable: [],
    current: 1,
    stepList: [],
    refuseData: '',
    openProcedure: true
})

onMounted(() => {
    // const appList = props.marketList.map((item) => {
    //         return item.apps
    //     })
})
</script>
<script>
export default {
    name: 'ApplyDetails',
    props: {
        infoData: {
            type: Object,
            required: true
        }
    }
}
</script>

<style lang="less" scoped></style>
<style lang="less"></style>
