<template>
    <div class="page-two">
        <a-layout-header
            class="reset-layout-header"
            style="position: relative; z-index: 999"
        >
            <i class="iconfont icon-xingzhuangjiehe19" @click="back()" />
            <span style="margin-left: 10px">查看报名表</span>
        </a-layout-header>
        <div class="content">
            <div class="title">走进敬老院，关爱老人</div>
            <a-form layout="inline">
                <a-form-item label="班级：">
                    <a-cascader
                        style="width: 300px"
                        :maxTagCount="1"
                        v-model:value="state.reportForm.class"
                        multiple
                        max-tag-count="responsive"
                        :options="state.calssOptions"
                        :fieldNames="fieldNames"
                        placeholder="请选择"
                    ></a-cascader>
                </a-form-item>
                <a-form-item label="部门：">
                    <a-cascader
                        style="width: 300px"
                        :maxTagCount="1"
                        v-model:value="state.reportForm.all"
                        multiple
                        max-tag-count="responsive"
                        :options="state.deptOptions"
                        :fieldNames="fieldNames"
                        placeholder="请选择"
                    ></a-cascader>
                </a-form-item>
            </a-form>
            <div class="echart">
                <div id="myChart" style="width: 100%; height: 300px"></div>
            </div>
            <a-form layout="inline">
                <div class="line-one">
                    <div class="right">
                        <a-form-item label="姓名：">
                            <a-input
                                v-model:value="state.reportForm.name"
                                allow-clear
                                placeholder="请输入"
                                style="width: 300px"
                            />
                        </a-form-item>
                        <a-form-item>
                            <a-button
                                type="primary"
                                style="margin-right: 12px"
                                @click="queryData()"
                            >
                                <template #icon>
                                    <SearchOutlined
                                        style="margin-right: 12px"
                                    />
                                </template>
                                查询
                            </a-button>
                            <a-button @click="resetForm()">
                                <template #icon>
                                    <redo-outlined style="margin-right: 12px" />
                                </template>
                                重置
                            </a-button>
                        </a-form-item>
                    </div>
                    <div class="left">
                        <a-popover placement="bottom" trigger="click">
                            <template #content>
                                <p style="cursor: pointer">导出名单</p>
                                <p style="cursor: pointer">导出资料</p>
                            </template>
                            <a-button>导出</a-button>
                        </a-popover>
                    </div>
                </div>
            </a-form>
            <div class="table">
                <YTable
                    :columns="columns"
                    :dataSource="state.reportTable"
                    :slots="['operation']"
                    :rowSelection="false"
                    :totals="state.pagination"
                    :spinLoad="state.spinLoad"
                    @onSelectedRowKeys="handerSelectedRowKeys"
                >
                    <template #headerCell="{ column }">
                        <template
                            v-if="
                                column.dataIndex === 'eventType' && state.divide
                            "
                        >
                            <a-dropdown>
                                <a style="color: #000" @click.prevent>
                                    活动分工
                                    <caret-down-outlined
                                        style="color: var(--primary-color)"
                                    />
                                </a>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item
                                            v-for="(
                                                item, index
                                            ) in state.eventTypeList"
                                            :key="index"
                                            @click="chooseType(item, index)"
                                        >
                                            <span
                                                :class="
                                                    state.typeIndex === index
                                                        ? 'act'
                                                        : ''
                                                "
                                                >{{ item.showName }}</span
                                            >
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </template>
                    </template>
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'eventType'">
                            <span v-if="!state.divide">-</span>
                            <span v-else>{{ record.eventType }}</span>
                        </template>
                        <template v-if="column.dataIndex === 'operation'">
                            <a
                                @click="lookDetail(record)"
                                style="
                                    color: var(--primary-color);
                                    margin-right: 10px;
                                "
                                >查看提交资料</a
                            >
                            <a
                                @click="cancelSingUp(record)"
                                style="
                                    color: var(--primary-color);
                                    margin-right: 10px;
                                "
                                >取消报名资格</a
                            >
                        </template>
                    </template>
                </YTable>
            </div>
            <div></div>
        </div>
    </div>
</template>

<script setup>
import { reactive, onMounted, onUnmounted, nextTick, watch } from "vue";
import YTable from "@/components/YTable/index.vue";
import { columns } from "./table.js";
import * as echarts from "echarts";
// import { Local } from "utils/storage.js"

// import echarts from "@/component/echarts"
// import { create } from '@/api/eventSignUp'
const fieldNames = {
    value: "value",
    label: "label",
    children: "children",
};
const teacher = {
    normal: {
        color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            {
                // 四个数字分别对应 数组中颜色的开始位置，分别为 右，下，左，上。例如（1,0,0,0 ）代表从右边开始渐
                // 变。offset取值为0~1，0代表开始时的颜色，1代表结束时的颜色，柱子表现为这两种颜色的渐变。
                offset: 0,
                color: "#09B97A",
            },
            {
                offset: 1,
                color: "#74EAD0",
            },
        ]),
    },
};
const student = {
    normal: {
        color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            {
                // 四个数字分别对应 数组中颜色的开始位置，分别为 右，下，左，上。例如（1,0,0,0 ）代表从右边开始渐
                // 变。offset取值为0~1，0代表开始时的颜色，1代表结束时的颜色，柱子表现为这两种颜色的渐变。
                offset: 0,
                color: "#99CC0E",
            },
            {
                offset: 1,
                color: "#CBED6F",
            },
        ]),
    },
};
const state = reactive({
    visible: false,
    divide: true, // 默认不分工
    reportForm: {},
    calssOptions: [
        {
            value: "1",
            label: "艾弗森",
            children: [
                {
                    value: "2",
                    label: "科比",
                    children: [
                        {
                            value: "3",
                            label: "阿斯蒂芬",
                        },
                        {
                            value: "4",
                            label: "阿斯蒂芬5",
                        },
                    ],
                },
            ],
        },
        {
            value: "5",
            label: "5555",
        },
        {
            value: "6",
            label: "6666",
        },
        {
            value: "7",
            label: "5555",
        },
        {
            value: "8",
            label: "6666",
        },
        {
            value: "9",
            label: "5555",
        },
        {
            value: "10",
            label: "6666",
        },
    ],
    deptOptions: [
        {
            value: "1",
            label: "艾弗森",
            children: [
                {
                    value: "2",
                    label: "科比",
                },
            ],
        },
    ],
    reportTable: [
        {
            eventType: "关爱老人",
            name: "小明",
            userType: "学生",
        },
    ],
    typeIndex: 0,
    eventTypeList: [
        { showName: "全部", status: null },
        { showName: "摄影师", status: 2 },
        { showName: "厨师", status: 0 },
        { showName: "司机", status: 1 },
    ],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },
    spinLoad: false,
    // 单项活动(xAxis.length=1)不分工(xAxis[0].data.length=1)
    eventOption: {
        tooltip: {
            trigger: "axis",
            formatter(a) {
                let res = "";
                let sum = 0;
                a.forEach((item, index) => {
                    if (index === 0) {
                        res += `${item.axisValue}<br/>`;
                    }
                    sum += item.value;
                    res += `${item.marker} ${item.seriesName} : ${item.value}<br/>`;
                    if (index === a.length - 1) {
                        res += `总人数 : ${sum}`;
                    }
                });
                return res;
            },
            axisPointer: {
                type: "shadow",
            },
        },
        legend: {
            x: "right",
        },
        grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
        },
        xAxis: [
            {
                type: "category",
                data: [""],
            },
        ],
        yAxis: [
            {
                type: "value",
                name: "人数",
                axisLine: {
                    show: true,
                },
            },
        ],
        series: [
            {
                name: "学生",
                type: "bar",
                stack: "Ad",
                barWidth: 100, // 宽度
                emphasis: {
                    focus: "series",
                },
                data: [320],
                itemStyle: student,
            },
            {
                name: "老师",
                type: "bar",
                stack: "Ad",
                barWidth: 100, // 宽度
                emphasis: {
                    focus: "series",
                },
                data: [120],
                itemStyle: teacher,
            },
        ],
    },
    // 单项活动分工
    // eventOption: {
    //     tooltip: {
    //         trigger: "axis",
    //         formatter(a) {
    //             let res = ""
    //             let sum = 0
    //             a.forEach((item, index) => {
    //                 if (index === 0) {
    //                     res += `${item.axisValue}<br/>`
    //                 }
    //                 sum += item.value
    //                 res += `${item.marker} ${item.seriesName} : ${item.value}<br/>`
    //                 if (index === a.length - 1) {
    //                     res += `总人数 : ${sum}`
    //                 }
    //             })
    //             return res
    //         },
    //         axisPointer: {
    //             type: "shadow"
    //         }
    //     },
    //     legend: {
    //         x: "right"
    //     },
    //     grid: {
    //         left: "3%",
    //         right: "4%",
    //         bottom: "3%",
    //         containLabel: true
    //     },
    //     xAxis: [
    //         {
    //             type: "category",
    //             data: ["摄影", "扫地", "111", "222"]
    //         }
    //     ],
    //     yAxis: [
    //         {
    //             type: "value",
    //             name: "人数",
    //             axisLine: {
    //                 show: true
    //             }
    //         }
    //     ],
    //     series: [
    //         {
    //             name: "学生",
    //             type: "bar",
    //             // stack: "overlap", // 堆叠效果(字符需要统一)
    //             barWidth: 60, // 宽度
    //             emphasis: {
    //                 focus: "series"
    //             },
    //             // z: "-1",
    //             // barGap: "-100%",
    //             data: [320, 500, 300, 400],
    //             itemStyle: student
    //         },
    //         {
    //             name: "老师",
    //             type: "bar",
    //             // stack: "ad",
    //             stack: "overlap", // 堆叠效果(字符需要统一)
    //             barWidth: 60, // 宽度
    //             emphasis: {
    //                 focus: "series"
    //             },
    //             // barGap: "60%",
    //             data: [120, 23, 600, 350],
    //             itemStyle: teacher
    //         },
    //         {
    //             name: "老师2",
    //             type: "bar",
    //             // stack: "Ad",
    //             stack: "overlap", // 堆叠效果(字符需要统一)
    //             barWidth: 60, // 宽度
    //             emphasis: {
    //                 focus: "series"
    //             },
    //             // barGap: "60%",
    //             data: [120, 23, 600, 100],
    //             itemStyle: teacher
    //         },
    //         {
    //             name: "老师3",
    //             type: "bar",
    //             // stack: "Ad",
    //             // stack: "overlap", // 堆叠效果(字符需要统一)
    //             barWidth: 60, // 宽度
    //             emphasis: {
    //                 focus: "series"
    //             },
    //             // barGap: "60%",
    //             data: [120, 23, 300, 190],
    //             itemStyle: teacher
    //         }
    //     ]
    // }
});
const emit = defineEmits(["showHomeAtForm"]);
const back = () => {
    emit("showHomeAtForm");
};
const lookDetail = () => {};
const cancel = () => {};
const chooseType = (item, index) => {
    state.typeIndex = index;
    // getList(item.id, null)
};
const handerSelectedRowKeys = () => {};
function getecharts() {
    const myChart = echarts.init(document.getElementById("myChart"));
    myChart.setOption(state.eventOption, true);
}
onMounted(() => {
    getecharts();
});
onUnmounted(() => {
    state.eventOption = {};
});
</script>

<style lang="less" scoped>
.act {
    color: var(--primary-color);
}

.page-two {
    position: absolute;
    top: -57px;
    left: 0;
    bottom: 0;
    right: 0;

    .reset-layout-header {
        height: 57px;
        line-height: 57px;
        border-bottom: 1px solid @border-color-base;
    }

    .content {
        padding: 20px 16px;

        .title {
            margin-bottom: 20px;
        }

        .echart {
            height: 380px;
            width: 100%;
        }
        .line-one {
            display: flex;
            justify-content: space-between;
            width: 100%;
            .right {
                display: flex;
            }
            .left {
                display: flex;
                justify-content: flex-end;
            }
        }

        .line {
            display: flex;
            align-items: center;
        }
        .table {
            margin-top: 20px;
        }
    }
}
.cneter-col {
    text-align: center !important;
}
</style>
