<template>
    <div class="warps">
        <!-- 活动报名 -->
        <div class="event" v-if="!state.homePage">
            <div class="bnt">
                <a-button type="primary" @click="createEvent()">
                    <template #icon>
                        <plus-outlined />
                    </template>
                    新增
                </a-button>
                <a-button
                    type="primary"
                    @click="delEvnet()"
                    danger
                    ghost
                    style="margin-left: 16px"
                    >删除</a-button
                >
            </div>
            <YTable
                :columns="columns"
                :dataSource="state.eventTable"
                :slots="['operation']"
                :rowSelection="true"
                :totals="state.pagination"
                :spinLoad="state.spinLoad"
                @onSelectedRowKeys="handerSelectedRowKeys"
            >
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'status'">
                        <a-badge
                            v-if="record.status === 0"
                            status="error"
                            text="未开始"
                        />
                        <a-badge
                            v-if="record.status === 1"
                            status="warning"
                            text="进行中"
                        />
                        <a-badge
                            v-if="record.status === 2"
                            status="success"
                            text="已结束   "
                        />
                        <a-badge
                            v-if="record.status === 4"
                            status="default"
                            style="color: #ccc"
                            text="已撤销"
                        />
                    </template>
                    <template v-if="column.dataIndex === 'operation'">
                        <a
                            @click="isEdit(record, false)"
                            style="
                                color: var(--primary-color);
                                margin-right: 10px;
                            "
                            >查看</a
                        >
                        <a
                            @click="isEdit(record, true)"
                            style="
                                color: var(--primary-color);
                                margin-right: 10px;
                            "
                            >修改</a
                        >
                        <a
                            @click="cancelSingUp(record)"
                            style="
                                color: var(--primary-color);
                                margin-right: 10px;
                            "
                            >撤销</a
                        >
                        <a
                            @click="viewReport(record)"
                            style="
                                color: var(--primary-color);
                                margin-right: 10px;
                            "
                            >查看报表</a
                        >
                    </template>
                </template>
            </YTable>
        </div>
        <!-- 新增 -->
        <addEvent @showHomePage="showHomePage" v-if="state.addPage"></addEvent>
        <!-- 查看报表 -->
        <reportForms
            @showHomeAtForm="showHomeAtForm"
            v-if="state.reportPage"
        ></reportForms>
        <!-- 查看&编辑 -->
        <editEvent
            @showHomeAtEdit="showHomeAtEdit"
            v-if="state.editPage"
        ></editEvent>
    </div>
</template>

<script setup>
import { reactive, onMounted, watch } from 'vue'
import { Local } from '@/utils/storage'
import { useRouter, useRoute } from 'vue-router'
import YTable from '@/components/YTable/index.vue'
import { columns } from './tableData'
import addEvent from '../addEvent'
import reportForms from '../reportForms'
import editEvent from '../editEvent'
import { modalConfirm } from '@/utils/util'
import { ExclamationCircleFilled } from '@ant-design/icons-vue'

// import { getTableList } from '@/api/eventSignUp'
import { message } from 'ant-design-vue'

const router = useRouter()
const route = useRoute()

// const column = [
//     {
//         title: "活动名称",
//         dataIndex: "name"
//     },
//     {
//         title: "活动类型",
//         dataIndex: "eventType"
//     },
//     {
//         title: "报名时间",
//         dataIndex: "signUpTime"
//     },
//     {
//         title: "状态",
//         dataIndex: "status"
//     },
//     {
//         title: "操作",
//         dataIndex: "operation"
//     }
// ]

const state = reactive({
    homePage: false, // 活动报名页面
    addPage: false, // 新增页面
    reportPage: false, // 查看报表页面
    editPage: false, // 查看&修改页面
    eventTable: [
        {
            name: '公益活动',
            eventType: 'asdf',
            signUpTime: '2023-05-01',
            status: 2
        },
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {}
    ],
    spinLoad: false,
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
    }
})

// const setStatus = computed(() => {
//     return (item) => {
//         switch (state.status) {
//         case 1:
//             return 99
//             break
//         case 2:
//             return 11
//             break
//         case 5:
//             return "222"
//             break
//         case 3:
//             return "333"
//         default:
//             break
//         }
//     }
// })
const delEvnet = () => {
    modalConfirm(
        '提示',
        '确定要删除该分类吗？删除后分类下的文件也都将被删除。',
        ExclamationCircleFilled
    ).then((res) => {
        if (res) {
            // classifyDel({ id: item.id }).then((re) => {
            //     message.success(re.message)
            //     getClassifyList()
            // })
        }
    })
}
// 新增活动报名
const createEvent = () => {
    state.homePage = true
    state.addPage = true
}
// 查看报表
const viewReport = () => {
    state.homePage = true
    state.reportPage = true
}
watch(
    () => [Local.get('homePage'), Local.get('reportPage')],
    (val) => {},
    { immediate: true }
)
// 回到活动报名首页
const showHomePage = () => {
    state.homePage = false
    state.addPage = false
}
const showHomeAtForm = () => {
    state.homePage = false
    state.reportPage = false
}
const showHomeAtEdit = () => {
    state.homePage = false
    state.editPage = false
}
// 撤销
const cancelSingUp = () => {}
const isEdit = (record, bol) => {
    state.homePage = true
    state.editPage = true
    if (bol) {
        // 编辑
    } else {
        // 查看
    }
}
// 获取活动报名列表
const getEventTableList = () => {}
/* 分页事件 */
const handerSelectedRowKeys = (data) => {
    const { current, pageSize } = data
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    getEventTableList()
}
// 0.拒绝、1.审批中、2.已通过、3.已取消、4.已过期
// 我的预约
onMounted(() => {
    getEventTableList()
})
</script>

<style lang="less" scoped>
.warps {
    position: relative;
    .event {
        padding: 23px 16px;
        .bnt {
            display: flex;
            justify-content: end;
            margin-bottom: 20px;
        }
    }
}
</style>
