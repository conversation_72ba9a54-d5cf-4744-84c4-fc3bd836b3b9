<template>
    <div class="page-two" style="position: relative">
        <a-layout-header
            class="reset-layout-header"
            style="position: relative; z-index: 99"
        >
            <i class="iconfont icon-xingzhuangjiehe19" @click="back()" />
            <span style="margin-left: 10px">新增</span>
        </a-layout-header>
        <div class="content">
            <a-form
                ref="eventRef"
                layout="vertical"
                :model="state.eventForm"
                name="base"
                :rules="state.rules"
            >
                <a-form-item label="活动名称: " name="name">
                    <a-input
                        v-model:value.trim="state.eventForm.name"
                        allowClear
                        show-count
                        :maxlength="50"
                        placeholder="请输入"
                    />
                </a-form-item>
                <a-form-item label="活动场次:" name="events">
                    <a-select
                        v-model:value="state.eventForm.events"
                        placeholder="请选择"
                    >
                        <a-select-option :value="1">单项活动</a-select-option>
                        <!-- <a-select-option :value="2">多次活动</a-select-option> -->
                    </a-select>
                </a-form-item>
                <a-form-item label="活动类型:" name="type">
                    <a-select
                        v-model:value="state.eventForm.type"
                        placeholder="请选择"
                    >
                        <a-select-option
                            v-for="(item, index) in state.typelist"
                            :key="index"
                            :value="item.value"
                            >{{ item.text }}</a-select-option
                        >
                    </a-select>
                </a-form-item>
                <!-- {{ state.eventForm.cont }} -->
                <a-form-item class="rich-text" label="活动内容：">
                    <Uedit v-model:modelValue="state.eventForm.cont" />
                </a-form-item>
                <a-form-item label="报名时间:" name="time">
                    <a-range-picker
                        style="width: 100%"
                        v-model:value="state.eventForm.time"
                        format="YYYY-MM-DD HH:mm"
                        :disabled-date="disabledDate"
                        :show-time="{ defaultValue: dayjs('00:00:', 'HH:mm') }"
                    />
                </a-form-item>
                <a-form-item
                    label="活动分工:"
                    name="fen"
                    style="position: relative"
                >
                    <span class="text"
                        >（如社会公益活动，活动人员分为采购人员，拍摄人员，记录人员等，则可选择“分工”的方式进行活动报名）</span
                    >
                    <a-select
                        v-model:value="state.eventForm.fen"
                        placeholder="请选择"
                    >
                        <a-select-option :value="2">不分工</a-select-option>
                        <a-select-option :value="1">分工</a-select-option>
                    </a-select>
                </a-form-item>
                <a-table
                    v-if="state.eventForm.fen === 1"
                    :columns="addCloumn"
                    size="small"
                    :data-source="state.eventTable"
                    bordered
                    :pagination="false"
                >
                    <template #bodyCell="{ column, record, index }">
                        <a-input
                            v-model:value="record.fenName"
                            allowClear
                            placeholder="请输入"
                            v-if="column.key === 'fenName'"
                        />
                        <a-input
                            v-model:value="record.num"
                            @input="
                                record.num = record.num.replace(/[^\d]/g, '')
                            "
                            allowClear
                            placeholder="请输入人数"
                            v-if="column.key === 'num'"
                        />
                        <span class="rens" v-if="column.key === 'num'">人</span>
                        <template v-if="column.dataIndex === 'operation'">
                            <a-button type="link" danger @click="delrow(index)"
                                >删除</a-button
                            >
                        </template>
                    </template>
                </a-table>
                <div v-if="state.eventForm.fen === 1" class="add-row">
                    <span @click="addrow()"
                        ><plus-outlined
                            color="#00B781"
                            style="margin-right: 10px"
                        />添加</span
                    >
                </div>
                <a-form-item label="参与人员范围：" name="peopleIds">
                    <a-form-item-rest>
                        <a-select
                            class="selectInputClass"
                            v-model:value="state.eventForm.peopleIds"
                            placeholder="请添加"
                            :options="state.peopleListOptions"
                            mode="multiple"
                            :open="false"
                            :field-names="{
                                label: 'showName',
                                value: 'id'
                            }"
                            @change="changeGroupList"
                        >
                        </a-select>
                        <div style="position: absolute; right: 0; top: 10px">
                            <Yselect
                                :tabs="state.yselectTabs"
                                v-model:visible="state.visible"
                                v-model:checked="state.checkedList"
                                @handleOk="handleSelectClassOk"
                            >
                                <a-button
                                    type="primary"
                                    size="small"
                                    class="yselect-tabs-btn"
                                >
                                    <template #icon>
                                        <plus-outlined />
                                    </template>
                                </a-button>
                            </Yselect>
                        </div>
                    </a-form-item-rest>
                </a-form-item>
                <a-form-item
                    label="参与活动方式:"
                    name="fang"
                    style="position: relative"
                >
                    <span class="text" style="left: 99px"
                        >（参与活动的方式，以个人名义或两人及两人以上组团，或以班级/部门报名参加）</span
                    >
                    <a-select
                        v-model:value="state.eventForm.fang"
                        placeholder="请选择"
                    >
                        <a-select-option :value="2">个人报名</a-select-option>
                        <a-select-option :value="1">班级报名</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="人数限制:" name="peoplexz">
                    <a-select
                        v-model:value="state.eventForm.peoplexz"
                        placeholder="请选择"
                    >
                        <a-select-option :value="2">不限制</a-select-option>
                        <a-select-option :value="1">限制</a-select-option>
                    </a-select>
                </a-form-item>
                <a-input
                    v-model:value="state.eventForm.peoplenum"
                    style="margin: -12px 0 20px 0"
                    @input="
                        state.eventForm.peoplenum =
                            state.eventForm.peoplenum.replace(/[^\d]/g, '')
                    "
                    allowClear
                    placeholder="请输入限制人数"
                    v-if="state.eventForm.peoplexz == 1"
                />
                <span class="ren" v-if="state.eventForm.peoplexz == 1">人</span>
                <a-form-item
                    label="是否需要提交材料:"
                    name="cai"
                    style="position: relative"
                >
                    <span class="text" style="left: 128px"
                        >（如需提交简历表/资料表，则需选择“需要”）</span
                    >
                    <a-select
                        v-model:value="state.eventForm.cai"
                        placeholder="请选择"
                    >
                        <a-select-option :value="2">不需要</a-select-option>
                        <a-select-option :value="1">需要</a-select-option>
                    </a-select>
                </a-form-item>
            </a-form>
        </div>
        <div class="footer">
            <a-button type="primary" :loading="state.btning" @click="submit()"
                >提交</a-button
            >
        </div>
    </div>
</template>

<script setup>
import { ref, shallowRef, reactive, onMounted, watch, nextTick } from 'vue'
import Uedit from '@/components/Uedit.vue'
import { addCloumn } from './table.js'
import dayjs from 'dayjs'
import Yselect from '@/components/YSelect'
import { message } from 'ant-design-vue'
// import { create } from '@/api/eventSignUp'
const eventRef = shallowRef()
const state = reactive({
    btning: false,
    spinLoad: false,
    eventForm: {
        events: 1
    },
    rules: {
        name: [{ required: true, message: '请输入活动名称' }],
        events: [{ required: true, message: '请选择活动场次' }],
        type: [{ required: true, message: '请选择类型' }],
        time: [{ required: true, message: '请选择时间' }],
        fen: [{ required: true, message: '请选择' }],
        fang: [{ required: true, message: '请选择' }],
        peoplexz: [{ required: true, message: '请选择' }],
        cai: [{ required: true, message: '请选择' }],
        peopleIds: [{ required: true, message: '请选择' }]
    },
    typelist: [
        { text: '文体活动', value: 0 },
        { text: '体育活动', value: 0 },
        { text: '社会公益活动', value: 0 },
        { text: '社团活动', value: 0 },
        { text: '宣传教育活动', value: 0 },
        { text: '心理咨询活动', value: 0 }
    ],
    eventTable: [{ fenName: '', num: null }],
    visible: false,
    peopleListOptions: [],
    checkedList: [],
    yselectTabs: [
        {
            tab: '教职工',
            key: 1,
            checked: true
            // disabled: commentJsonKey
        },
        {
            tab: '学生',
            key: 2,
            checked: true
            // disabled: commentJsonKey
        }
    ]
})
const emit = defineEmits(['showViews'])
const back = () => {
    emit('showHomePage')
}
function handleSelectClassOk(data) {
    state.peopleListOptions = [...data]
    state.eventForm.peopleIds = data.map((item) => item.id)
    // state.eventForm.people = data.map((item) => {
    //     return {
    //         classId: item.id || item.classId,
    //         gradeId: item.pid || item.gradeId
    //     }
    // })
    state.checkedList = [...data]
}
function changeGroupList(item) {
    state.checkedList = state.peopleListOptions.filter((i) =>
        item.includes(i.id)
    )
    // modelRef.classList = state.checkedList.map((item) => {
    //     return {
    //         classId: item.id || item.classId,
    //         gradeId: item.pid || item.gradeId
    //     }
    // })
}
const addrow = () => {
    state.eventTable.push({ fenName: '', num: null })
}
const delrow = (index) => {
    if (state.eventTable.length === 1) {
        return
    }

    state.eventTable.splice(index, 1)
}
const disabledDate = (current) => {
    // Can not select days before today and today
    return current && current < dayjs().endOf('day').add(-1, 'day')
}
function submit() {
    back()
    eventRef.value
        .validate()
        .then(async() => {
            state.btning = true
            // 重置表单
            eventRef.value.resetFields()
            state.eventForm.cont = ''
            delete state.eventForm.peopleIds
            try {
            } catch (error) {
                message.error('error___hou')
                state.submitBtnLoading = false
            }
        })
        .catch((err) => {})
        .finally(() => {
            state.btning = false
        })
}

onMounted(() => {})
</script>

<style lang="less" scoped>
.act {
    color: var(--primary-color);
}

.page-two {
    position: absolute;
    top: -57px;
    left: 0;
    bottom: 0;
    right: 0;
    .reset-layout-header {
        z-index: 9;
        height: 57px;
        line-height: 57px;
        border-bottom: 1px solid @border-color-base;
    }
    .content {
        padding: 20px 328px;
        margin-bottom: -57px;
        min-height: calc(100vh - 230px);
        .input_max {
            position: absolute;
            top: 6px;
            right: 10px;
        }
        .rich-text {
            min-height: 120px;
        }
        .ren {
            position: absolute;
            z-index: 9;
            right: 332px;
            color: rgba(0, 0, 0, 0.45);
        }
        .rens {
            position: absolute;
            z-index: 9;
            right: 12px;
            top: 12px;
            color: rgba(0, 0, 0, 0.45);
        }
        .add-row {
            height: 38px;
            width: 100%;
            line-height: 38px;
            padding-left: 14px;
            border: 1px solid #d9d9d9;
            color: #00b781;
            border-top: none;
            cursor: pointer;
            margin-bottom: 14px;
        }
        .text {
            position: absolute;
            top: -31px;
            z-index: 1;
            left: 72px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.45);
        }
        .input-group-addon {
            position: absolute;
            right: 14px;
            color: #ccc;
            top: 13px;
        }
    }
    .footer {
        z-index: 10;
        position: relative;
        bottom: -57px;
        height: 60px;
        line-height: 60px;
        padding-left: 328px;
        border-top: 1px solid #d9d9d9;
    }
}
.yselect-tabs-btn {
    position: relative;
    bottom: 6px;
    right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.selectInputClass {
    .ant-select-selection-overflow {
        margin-right: 24px !important;
    }
}
</style>
