<template>
    <div class="page-two" style="position: relative">
        <a-layout-header class="reset-layout-header" style="position: relative;z-index: 99;">
            <i class="iconfont icon-xingzhuangjiehe19" @click="back()" />
            <span style="margin-left: 10px;">新增</span>
        </a-layout-header>
        <div class="content">
            新增活动
            <div style="width:100%;height:299px;background-color: green;"></div>
            <div style="width:100%;height:299px;background-color: blue;"></div>
            <div style="width:100%;height:299px;background-color: yellowgreen;"></div>
            <div style="width:100%;height:299px;background-color: yellow;"></div>
            <div style="width:100%;height:299px;background-color: skyblue;"></div>
        </div>
        <div class="footer">
            <a-button type="primary">提交</a-button>
        </div>
    </div>
</template>

<script setup>
import { reactive, onMounted, watch, nextTick } from "vue"
// import { create } from '@/api/eventSignUp'

const state = reactive({
    visible: false,
    spinLoad: false
})
const emit = defineEmits(["showViews"])
const back = () => {
    emit("showHomePage")
}
onMounted(() => {})
</script>

<style lang="less" scoped>
.act {
    color: var(--primary-color);
}

.page-two {
    position: absolute;
    top: -57px;
    left: 0;
    bottom: 0;
    right: 0;
    .reset-layout-header {
        z-index: 9;
        height: 57px;
        line-height: 57px;
        border-bottom: 1px solid @border-color-base;
    }
    .content {
        padding: 20px 328px;
        margin-bottom: -57px;
        min-height: calc(100vh - 230px);
    }
    .footer{
        position: relative;
        bottom: -57px;
        height: 60px;
        line-height: 60px;
        padding-left: 328px;
        border-top: 1px solid #D9D9D9;
    }
}
</style>
