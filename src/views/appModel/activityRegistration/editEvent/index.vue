<template>
    <div class="page-two" style="position: relative">
        <a-layout-header
            class="reset-layout-header"
            style="position: relative; z-index: 99"
        >
            <i class="iconfont icon-xingzhuangjiehe19" @click="back()" />
            <span style="margin-left: 10px">查看</span>
        </a-layout-header>
        <div class="content">
            <div class="tile">{{ state.title }}</div>
            <div class="tit">{{ state.tit }}</div>
            <div class="top">
                <div class="tit-color2">单项活动</div>
                <div class="right" @click="lookReport()">
                    查看报表
                    <right-outlined style="color: #00b781" />
                </div>
            </div>
            <div class="fu-text" v-html="state.text"></div>
            <div class="dash"></div>
            <div class="table">
                <div
                    class="item"
                    v-for="(item, index) in state.tables"
                    :key="index"
                >
                    <div class="left tit-color1">
                        <span class="yuan"></span>
                        {{ item.left }}
                    </div>
                    <div class="right tit-color3">{{ item.right }}</div>
                </div>
            </div>
        </div>
        <div class="footer"></div>
    </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import { Local } from '@/utils/storage'
const state = reactive({
    tables: [
        {
            left: '报名时间',
            right: '2023-03-13 至 2023-03-30'
        },
        {
            left: '报名时间',
            right: '2023-03-13 至 2023-03-30'
        },
        {
            left: '报名时间',
            right: '2023-03-13 至 2023-03-30'
        },
        {
            left: '报名时间',
            right: '2023-03-13 至 2023-03-30'
        }
    ],
    visible: false,
    spinLoad: false,
    title: '阿斯顿发斯蒂芬',
    tit: '___________',
    text: '<div class=\'_3ygOc lg-fl \'> <p data-track=\'3\'>2023年3月10日上午，习近平同志全票当选中华人民共和国主席、中华人民共和国中央军事委员会主席。如潮的掌声在人民大会堂响起，习近平同志从主席台座席上起身，向全体代表鞠躬致意。这是2952位全国人大代表，肩负着亿万人民的重托，在民族复兴行进到关键一程，向第二个百年奋斗目标进军之际，作出的郑重选择</p> <p data-track=\'3\'><img src=\'https://alicdn.1d1j.cn/announcement/20230313/e1d780db51324a18b21fdf7bddbc4ae6.png\' alt=\'\' width=\'980\' height=\'620\' /></p> </div>'
})
const emit = defineEmits(['showHomeAtEdit'])
const back = () => {
    emit('showHomeAtEdit')
}
const lookReport = () => {
    Local.set('homePage', true)
    Local.set('reportPage', true)
}
onMounted(() => {})
</script>

<style lang="less" scoped>
.act {
    color: var(--primary-color);
}

.page-two {
    position: absolute;
    top: -57px;
    left: 0;
    bottom: 0;
    right: 0;
    .reset-layout-header {
        z-index: 9;
        height: 57px;
        line-height: 57px;
        border-bottom: 1px solid @border-color-base;
    }
    .content {
        text-align: center;
        padding: 20px 328px;
        margin-bottom: -57px;
        min-height: calc(100vh - 230px);
        margin: 0 auto;
        .tit-color1 {
            color: rgba(0, 0, 0, 0.45);
        }
        .tit-color2 {
            color: rgba(0, 0, 0, 0.65);
        }
        .tit-color3 {
            color: rgba(0, 0, 0, 0.85);
        }
        .tile {
            font-size: 18px;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
        }
        .tit {
            font-size: 14px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.45);
            margin: 20px 0;
        }
        .top {
            height: 46px;
            background: #f6f6f6;
            border-radius: 4px;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            margin-bottom: 24px;
            .right {
                color: #00b781;
                cursor: pointer;
            }
        }
        .fu-text {
            width: 100% !important;
        }
        .dash {
            width: 100%;
            height: 1px;
            border: 1px dashed #d9d9d9;
            margin: 20px 0;
        }
        .table {
            .item {
                padding: 0 16px;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                justify-content: space-between;
                min-height: 54px;
                line-height: 54px;
                border: 1px solid #ccc;
                margin-bottom: -1px;
                .left {
                    .yuan {
                        margin-bottom: 4px;
                        display: inline-block;
                        width: 4px;
                        height: 4px;
                        border-radius: 2px;
                        background: #8c8c8c;
                    }
                }
            }
        }
    }
    .footer {
        position: relative;
        bottom: -57px;
        height: 60px;
        line-height: 60px;
        padding-left: 328px;
    }
}
</style>
