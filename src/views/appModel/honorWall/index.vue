<template>
    <div>
        <a-layout-content class="reset-layout-content layout-header-radius">
            <a-layout-header class="reset-layout-header">
                <span @click="goBack">
                    <i class="iconfont icon-xingzhuangjiehe19" />
                    <!-- <arrow-left-outlined class="left-outlined" /> -->
                    返回
                </span>
            </a-layout-header>
            <div class="searchHead">
                <div>
                    <a-form layout="inline">
                        <a-form-item label="时间：" name="depts">
                            <a-date-picker
                                @change="onChangeData"
                                v-model:value="optionDate"
                                value-format="YYYY"
                                format="YYYY年"
                                picker="year"
                            >
                                <template #suffixIcon>
                                    <down-outlined />
                                </template>
                            </a-date-picker>
                        </a-form-item>
                        <a-form-item label="荣誉类型：" name="depts">
                            <a-select
                                ref="select"
                                v-model:value="state.honorpage.honorType"
                                style="width: 120px"
                                :options="honorTypes"
                                allowClear
                                placeholder="请选择荣誉类型"
                            >
                            </a-select>
                        </a-form-item>
                        <a-form-item>
                            <a-input-search
                                placeholder="请输入荣誉名称搜索"
                                v-model:value.trim="state.honorpage.name"
                                style="width: 336px; padding-bottom: 16px"
                                @search="queryStatistics"
                            />
                        </a-form-item>
                        <a-form-item>
                            <a-button
                                type="primary"
                                style="margin-right: 10px"
                                @click="queryStatistics"
                            >
                                <template #icon>
                                    <SearchOutlined
                                        style="margin-right: 10px"
                                    />
                                </template>
                                查询
                            </a-button>
                            <a-button @click="resetStatistics">
                                <template #icon>
                                    <redo-outlined style="margin-right: 10px" />
                                </template>
                                重置
                            </a-button>
                        </a-form-item>
                    </a-form>
                </div>
                <a-button type="primary" @click="addHonor">颁发荣誉</a-button>
            </div>
        </a-layout-content>
        <div>
            <a-list
                item-layout="horizontal"
                size="large"
                :pagination="pagination"
                :data-source="state.listData"
            >
                <template #renderItem="{ item }">
                    <a-list-item key="item.title">
                        <template #actions>
                            <a
                                key="list-loadmore-edit"
                                style="color: #00b781"
                                @click="editHonor(item.id)"
                                >编辑</a
                            >
                            <a-popconfirm
                                title="您确定要删除该条荣誉吗?"
                                ok-text="是"
                                cancel-text="否"
                                @confirm="delHonor(item.id)"
                            >
                                <a
                                    key="list-loadmore-more"
                                    style="color: #f5222d"
                                    >删除</a
                                >
                            </a-popconfirm>
                        </template>
                        <a-list-item-meta>
                            <template #description>
                                <div>{{ item.createTime }}</div>
                            </template>
                            <template #title>
                                <div class="titleTetx">
                                    荣誉名称: <span>{{ item.name }}</span>
                                </div>
                                <div class="titleTetx">
                                    荣誉类型:
                                    <span>{{
                                        honorType[item.honorType - 1]?.label
                                    }}</span>
                                </div>
                                <div class="titleTetx">
                                    荣誉级别:
                                    <span>{{
                                        honorLevel[item.level - 1]?.label ||
                                        "暂无"
                                    }}</span>
                                </div>
                                <div
                                    class="titleTetx"
                                    v-if="item.honorType !== 4"
                                >
                                    获奖{{
                                        item.honorType === 2
                                            ? "学生"
                                            : item.honorType === 3
                                            ? "教师"
                                            : "班级"
                                    }}: <span>{{ item.winnerNames }}</span>
                                </div>
                            </template>
                            <template #avatar>
                                <div style="overflow: hidden">
                                    <a-image
                                        :width="320"
                                        :height="185"
                                        alt="logo"
                                        :src="`${item.imgPaths}`"
                                    />
                                </div>
                            </template>
                        </a-list-item-meta>
                    </a-list-item>
                </template>
            </a-list>
        </div>
        <FormDrawer ref="formDrawerRef" @success="getHonorList" />
    </div>
</template>

<script setup>
import { honorType, honorTypes, honorLevel } from "./data";
import { honorList, delHonorWall } from "@/api/honorWall.js";
import { message } from "ant-design-vue";
import { reactive, onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "@/store/index";
import FormDrawer from "./components/formDrawer.vue";

const store = useStore();
const router = useRouter();
// 返回按钮
const goBack = () => {
    router.push("/app/list");
};
const state = reactive({
    honorpage: {
        pageNo: 1,
        pageSize: 10,
        name: null,
        yearName: null,
        honorType: null,
    },
    listData: [],
});

// 年份时间
const optionDate = ref();
const pagination = {
    onChange: (page, pageSize) => {
        state.honorpage.pageNo = page;
        state.honorpage.pageSize = pageSize;
        getHonorList();
    },
    // pageSize: 5,
    // current: 1,
    showQuickJumper: true,
    showSizeChanger: true,
    total: 0,
    pageSizeOptions: ["10", "20", "30", "40"],
    showTotal: (total, range) => {
        return `共 ${total} 条`;
    },
};

// 颁发荣誉按钮
const addHonor = () => {
    formDrawerRef.value && formDrawerRef.value.openAdd();
};
const onChangeData = (val) => {
    optionDate.value = val;
    state.honorpage.yearName = optionDate.value;
};

// 查询荣誉墙列表
const getHonorList = async () => {
    const { data } = await honorList(state.honorpage);
    state.listData = data.list;
    pagination.total = data.total;
};
// 查询按钮
const queryStatistics = () => {
    state.honorpage.pageNo = 1;
    getHonorList();
};
// 重置按钮
const resetStatistics = () => {
    state.honorpage.pageNo = 1;
    state.honorpage.pageSize = 10;
    state.honorpage.honorType = null;
    state.honorpage.name = null;
    state.honorpage.yearName = null;
    getHonorList();
};
const formDrawerRef = ref(null);
// 编辑荣誉
const editHonor = async (honorId) => {
    formDrawerRef.value && formDrawerRef.value.openEdit(honorId);
};

// 更改荣誉类型
const changeType = () => {
    state.winner = "";
};
// 删除荣誉
const delHonor = async (data) => {
    await delHonorWall({ id: data });
    getHonorList();
    message.success("删除成功");
};

onMounted(() => {
    getHonorList();
    store.dispatch("base/getSchoolRollTree");
    store.dispatch("base/getDepartmentTree");
});
</script>

<style lang="less" scoped>
.reset-layout-content {
    padding: 0;
    overflow: hidden;
    // border-radius: 20px;
}

.reset-layout-header {
    border-bottom: 1px solid @border-color-base;
    height: 57px;
    padding: 0 12px;

    .left-outlined {
        cursor: pointer;
        color: @primary-color;
        margin-right: 5px;
    }

    a {
        color: @text-color;
    }
}

.searchHead {
    /* padding-bottom: 16px; */
    display: flex;
    justify-content: space-between;
    // align-items: center;
    padding: 24px 16px 8px 16px;
    border-bottom: 1px solid @border-color-base;
}

// :deep(.ant-list-vertical .ant-list-item){
//     flex-direction: row-reverse;
// }

.titleTetx {
    color: rgba(0, 0, 0, 0.45);
    padding-bottom: 12px;

    span {
        color: rgba(0, 0, 0, 0.85);
    }
}

.footer {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    text-align: center;
    border-top: 1px solid #e9e9e9;
    padding: 10px 16px;
    background: #fff;
    z-index: 1;
}

.bg1 {
    background: url("/image/honor_bg1.png");
    background-size: 100% 100%;
}

.bg2 {
    background: url("/image/honor_bg2.png");
    background-size: 100% 100%;
}

.bg3 {
    background: url("/image/honor_bg3.png");
    background-size: 100% 100%;
}

.bg4 {
    background: url("/image/honor_bg4.png");
    background-size: 100% 100%;
}

.con {
    border-radius: 6px;
    width: 442px;
    margin: 20px 0px 10px 0px;
    min-height: 255px;
    text-align: center;
    border: 1px dashed #d9d9d9;

    .imgbox {
        padding-top: 50px;
    }

    .btn {
        height: 32px;
        color: #00b781;
        margin: 24px 0 24px;
        background: rgba(0, 183, 129, 0.08);
        border: 1px solid #00b781;
        border-radius: 4px;
    }

    .customImg {
        position: relative;
        height: 255px;

        img {
            border-radius: 6px;
            width: 100%;
            height: 100%;
        }

        .del {
            width: 20px;
            height: 20px;
            line-height: 20px;
            color: #fff;
            position: absolute;
            top: -10px;
            right: -10px;
            cursor: pointer;
            background: #f5222d;
            border-radius: 50%;
        }
    }

    // .list {
    //     display: flex;
    //     flex-wrap: wrap;
    //     margin-top: 20px;
    //     // justify-content: ;
    //     &_item {
    //         width: 100px;
    //         height: 100px;
    //         margin: 10px 10px;
    //         position: relative;
    //         img {
    //             width: 100%;
    //             height: 100%;
    //         }
    //         .del {
    //             width: 20px;
    //             height: 20px;
    //             line-height: 20px;
    //             color: #fff;
    //             position: absolute;
    //             top: -10px;
    //             right: -10px;
    //             cursor: pointer;
    //             background: #000;
    //             border-radius: 50%;
    //         }
    //     }
    // }
}

.TemInfo {
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    padding-bottom: 10px;
}

.add_count_down {
    // width: 90%;
    position: relative;
    margin: 20px 10px 10px 0px;
    height: 255px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    border: 1px solid #e1e1e1;
    border-radius: 6px;
    font-size: 20px;

    .count_text {
        position: absolute;
        top: 40px;
        text-align: center;
        left: 50%;
        transform: translate(-50%, 0);
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #fcf38c;
        line-height: 33px;
    }

    .count_text2 {
        position: absolute;
        top: 60px;
        width: 80px;
        white-space: pre-wrap;
        text-align: center;
        left: 50%;
        transform: translate(-50%, 0);
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #fcf38c;
        line-height: 33px;
    }
}

:deep(.addHonor .ant-form-item) {
    padding-bottom: 24px !important;
    margin-bottom: unset !important;
}

:deep(.ant-list-item-meta-description) {
    padding-top: 65px;
}

// :deep(.ant-list-item-meta-content){
//     position: relative;
// }
// :deep(.ant-list-item-meta-description){
//     position: absolute;
//     bottom: -114px;

// }
</style>

<style lang="less">
.drawerHonor {
    .ant-drawer-content-wrapper {
        width: 500px !important;
    }

    .ant-drawer-close {
        position: absolute;
        right: 0;
    }
}

.addHonor {
    .ant-form-item {
        padding-bottom: 24px !important;
        margin-bottom: unset !important;
    }
}

.ant-image img {
    width: 100%;
    height: 100%;
}
</style>
