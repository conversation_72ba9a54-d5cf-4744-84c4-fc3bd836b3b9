<template>
    <a-drawer
        class="drawerHonor"
        destroyOnClose
        v-model:visible="state.visible"
        :maskClosable="false"
        @close="closeVisible"
        :title="state.title"
    >
        <a-form
            class="addHonor"
            layout="vertical"
            ref="formRef"
            :model="state.honorContent"
            name="honor"
        >
            <a-form-item
                label="荣誉名称"
                name="name"
                :rules="[
                    {
                        required: true,
                        message: '请填写荣誉名称',
                    },
                ]"
            >
                <a-input
                    autoComplete="off"
                    v-model:value.trim="state.honorContent.name"
                    show-count
                    :maxlength="10"
                    placeholder="请输入荣誉名称"
                />
            </a-form-item>
            <a-form-item
                label="荣誉类型"
                name="honorType"
                :rules="[
                    {
                        required: true,
                        message: '请选择荣誉类型',
                        trigger: 'change',
                    },
                ]"
            >
                <a-select
                    v-model:value="state.honorContent.honorType"
                    @change="changeType"
                    :options="honorType"
                    :disabled="state.isShow"
                    placeholder="请选择荣誉类型"
                ></a-select>
            </a-form-item>
            <a-form-item label="荣誉级别" name="level">
                <a-select
                    v-model:value="state.honorContent.level"
                    :options="honorLevel"
                    :disabled="state.isShow"
                    placeholder="请选择荣誉类型"
                ></a-select>
            </a-form-item>
            <a-form-item
                :label="
                    state.honorContent.honorType === '2'
                        ? '获奖学生'
                        : state.honorContent.honorType === '3'
                        ? '获奖教师'
                        : '获奖班级'
                "
                name="winner"
                v-if="
                    state.honorContent.honorType &&
                    state.honorContent.honorType !== '4'
                "
                :rules="[
                    {
                        validator: validateardwinner,
                        required: true,
                        trigger: 'blur',
                    },
                ]"
            >
                <a-input
                    v-model:value="state.winner"
                    @click="addWinnerIds"
                    allowClear
                    placeholder="请选择"
                    readonly="readonly"
                />
            </a-form-item>
            <a-form-item label="荣誉描述" name="describe">
                <a-input
                    autoComplete="off"
                    v-model:value="state.honorContent.describe"
                    show-count
                    :maxlength="30"
                    placeholder="请输入荣誉描述"
                />
            </a-form-item>
            <a-form-item
                name="personnel"
                :rules="[
                    {
                        required: true,
                        message: '请选择查看人员',
                        trigger: 'change',
                    },
                ]"
            >
                <span style="color: #ff4d4f"> * </span><span>查看人员：</span>
                <a-radio-group
                    v-model:value="state.honorContent.personnel"
                    name="radioGroup"
                    :disabled="disabled"
                    :options="personnelOptions"
                    placeholder="请选择查看人员"
                >
                </a-radio-group>
            </a-form-item>
            <a-form-item name="brandIds" label="选择展示设备：">
                <div style="margin-bottom: 10px; display: flex">
                    <span>班牌：</span>
                    <a-tree-select
                        v-model:value="state.honorContent.brandIds"
                        style="width: 100%; flex: 1"
                        :tree-data="treeData"
                        tree-checkable
                        allow-clear
                        :show-checked-strategy="SHOW_ALL"
                        placeholder="请选择班牌"
                        tree-node-filter-prop="showName"
                        :field-names="{
                            children: 'children',
                            label: 'showName',
                            value: 'id',
                        }"
                    />
                </div>
            </a-form-item>
        </a-form>
        <div class="TemInfo">模板信息：</div>
        <div>
            <span>选择模板：</span>
            <a-radio-group
                v-model:value="state.honorContent.templateType"
                button-style="solid"
                :options="templateTypeOptions"
                option-type="button"
            >
            </a-radio-group>
        </div>
        <div>
            <div
                v-if="state.honorContent.templateType !== '99'"
                id="imageHonor"
                class="add_count_down"
                :class="
                    state.honorContent.templateType === '1'
                        ? 'bg1'
                        : state.honorContent.templateType === '2'
                        ? 'bg2'
                        : state.honorContent.templateType === '3'
                        ? 'bg3'
                        : 'bg4'
                "
            >
                <div>
                    <div
                        v-if="state.honorContent.templateType === '3'"
                        class="count_text2"
                    >
                        {{ state.honorContent.name }}
                    </div>
                    <div v-else class="count_text">
                        {{ state.honorContent.name }}
                    </div>
                </div>
            </div>
            <div v-else>
                <div class="con">
                    <div class="imgbox" v-if="!state.imageList.length">
                        <a-upload
                            :file-list="state.fileList"
                            :showUploadList="false"
                            accept="image/png,image/jpg,image/jpeg"
                            action="/"
                            :before-upload="beforeUpload"
                            @change="handelChange"
                        >
                            <a-button class="btn"> 选择图片 </a-button>
                        </a-upload>
                        <p>
                            仅支持上传jpeg/jpg/png格式图片，图片不能超过10M,最多可选1张
                        </p>
                    </div>
                    <div v-else class="customImg">
                        <img
                            :src="
                                state.imageList[state.imageList.length - 1]?.url
                            "
                            alt="img"
                        />
                        <div class="del" @click="removeUpload">×</div>
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="footer">
                <div>
                    <a-button style="margin-right: 8px" @click="closeVisible"
                        >取消</a-button
                    >
                    <a-button
                        type="primary"
                        @click="notarize"
                        :loading="state.loading"
                        >确认</a-button
                    >
                </div>
            </div>
        </template>
    </a-drawer>

    <y-select
        mode="personnel"
        :tabs="[{ tab: '教职工', key: 1, checked: true }]"
        v-model:visible="state.visible3"
        v-model:checked="state.checkedList3"
        @handleOk="handleOk3"
    >
    </y-select>

    <y-select
        mode="personnel"
        :tabs="[{ tab: '学生组', key: 2, checked: true }]"
        v-model:visible="state.visible2"
        v-model:checked="state.checkedList2"
        @handleOk="handleOk2"
    >
    </y-select>

    <y-select
        mode="depa"
        :tabs="[
            {
                tab: '班级',
                key: 2,
                checked: true,
                disabled: [0, 1, 2, 3],
            },
        ]"
        v-model:visible="state.visible1"
        v-model:checked="state.checkedList1"
        @handleOk="handleOk1"
    >
    </y-select>
</template>

<script setup>
import { reactive, ref } from "vue";
import { TreeSelect, message } from "ant-design-vue";
import YSelect from "@/components/YSelect/index";
import { getBindingSiteTreeeV2 } from "@/api/classSign";
import { generatorImage } from "@/utils/format";
import {
    honorType,
    honorLevel,
    templateTypeOptions,
    personnelOptions,
} from "../data";
import {
    addHonorWall,
    upHonorWall,
    honorWallInfo,
    getFileUpload,
} from "@/api/honorWall.js";

const emits = defineEmits(["success"]);

const SHOW_ALL = TreeSelect.SHOW_ALL;
const treeData = ref([]);
const formRef = ref();
const disabled = ref(false);

const validateardwinner = (_rule, value) => {
    if (state.winner === "") {
        return Promise.reject(new Error("请选择获奖人员"));
    }
    return Promise.resolve();
};

const state = reactive({
    loading: false,
    imageList: [],
    fileList: [],
    isedit: false,
    visible: false,
    title: "颁发荣誉",
    winner: "",
    visible1: false,
    visible2: false,
    visible3: false,
    checkedList1: [],
    checkedList2: [],
    checkedList3: [],
    honorContent: {
        brandIds: [],
        name: "",
        honorType: null,
        level: "",
        describe: "",
        personnel: 0,
        isShowBrand: false,
        templateType: "1",
        // imgPaths: '',
        honorWinnerIds: [],
    },
});

function getBindingSite() {
    getBindingSiteTreeeV2({ direction: "" }).then((res) => {
        const markDisable = (nodes) =>
            (nodes || []).map((item) => ({
                ...item,
                disableCheckbox: item.type !== 4,
                children: item.children ? markDisable(item.children) : [],
            }));
        treeData.value = markDisable(res.data);
    });
}

const addWinnerIds = () => {
    console.log(state.honorContent.honorType);
    switch (state.honorContent.honorType) {
        case "1":
            state.visible1 = true;
            break;
        case "2":
            state.visible2 = true;
            break;
        case "3":
            state.visible3 = true;
            break;
        default:
            break;
    }
};
const handleOk1 = (data) => {
    state.honorContent.honorWinnerIds = data.map((item) => item.id);
    state.winner = data.map((item) => item.name).join("、");
};
const handleOk2 = (data) => {
    state.honorContent.honorWinnerIds = data.map((item) => item.id);
    state.winner = data.map((item) => item.name).join("、");
};
const handleOk3 = (data) => {
    state.honorContent.honorWinnerIds = data.map((item) => item.id);
    state.winner = data.map((item) => item.name).join("、");
};

const getBase64 = (file) =>
    new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = (error) => reject(error);
    });

const handelChange = async (info) => {
    const arr = await Promise.all(
        info.fileList.map(async (file) => ({
            name: file.name,
            file: file.originFileObj,
            url: await getBase64(file.originFileObj),
            uid: file.uid,
        }))
    );
    state.imageList = arr;
    state.fileList = info.fileList;
};

const beforeUpload = (file) => {
    const validFileType = ["image/jpeg", "image/jpg", "image/png"];
    const isValidFileType = validFileType.indexOf(file.type) > -1;
    const isLt = file.size / 1024 / 1024 < 10;
    if (!isValidFileType) {
        message.error("上传头像图片只能是 JPG 或 PNG 格式!");
        return false;
    }
    if (!isLt) {
        message.error("上传头像图片大小不能超过 10MB!");
        return false;
    }
    return false;
};

const removeUpload = () => {
    state.imageList = [];
    state.fileList = [];
};

const changeType = () => {
    state.winner = "";
};

const fileUpload = (file) =>
    new Promise((resolve) => {
        const params = new FormData();
        params.append("file", file);
        getFileUpload(params).then((res) => {
            const { data } = res;
            resolve(data[0]?.url);
        });
    });

const notarize = async () => {
    await formRef.value.validate();
    state.loading = true;
    if (state.honorContent.templateType === "99") {
        const lastFile =
            state.fileList[state.fileList.length - 1]?.originFileObj;
        if (lastFile) {
            const customFileUrl = await fileUpload(lastFile);
            state.honorContent.imgPaths = customFileUrl;
        }
    } else {
        const imageHonor = document.getElementById("imageHonor");
        const honorFile = await generatorImage(imageHonor, "honor.png");
        const honorFileUrl = await fileUpload(honorFile);
        state.honorContent.imgPaths = honorFileUrl;
    }
    if (state.isedit) {
        try {
            await upHonorWall(state.honorContent);
            state.loading = false;
            closeVisible();
            message.success("编辑成功");
            emits("success");
        } catch (error) {
            state.loading = false;
        }
    } else {
        try {
            await addHonorWall(state.honorContent);
            state.loading = false;
            closeVisible();
            message.success("新增成功");
            emits("success");
        } catch (error) {
            state.loading = false;
        }
    }
    formRef.value.resetFields();
};

const resetInternalState = () => {
    state.winner = null;
    state.honorContent.isShowBrand = false;
    state.honorContent.templateType = "1";
    state.imageList = [];
    state.checkedList1 = [];
    state.checkedList2 = [];
    state.checkedList3 = [];
    state.honorContent.name = "";
    delete state.honorContent.honorType;
    delete state.honorContent.level;
    state.honorContent.describe = "";
    state.honorContent.personnel = 0;
};

const closeVisible = () => {
    resetInternalState();
    state.visible = false;
};

const openAdd = () => {
    disabled.value = false;
    resetInternalState();
    state.fileList = [];
    state.honorContent.brandIds = [];
    state.honorContent.honorWinnerIds = [];
    state.title = "颁发荣誉";
    getBindingSite();
    state.visible = true;
    state.isedit = false;
};

const openEdit = async (honorId) => {
    disabled.value = true;
    state.isedit = true;
    state.honorContent.id = honorId;
    const { data } = await honorWallInfo({ id: honorId });
    state.visible = true;
    state.title = "编辑荣誉";
    state.honorContent.name = data.name;
    state.honorContent.honorType = data.honorType.toString();
    state.honorContent.isShowBrand = data.isShowBrand;
    state.honorContent.level = data.level?.toString();
    state.honorContent.describe = data.describe;
    state.honorContent.personnel = data.personnel;
    state.honorContent.templateType = data.templateType.toString();
    state.honorContent.honorWinnerIds = data.winnerList.map((item) => item.id);
    state.honorContent.imgPaths = data?.imgPaths || "";
    state.honorContent.brandIds = data.brandIds || [];
    switch (state.honorContent.honorType) {
        case "1":
            state.checkedList1 = data.winnerList;
            break;
        case "2":
            state.checkedList2 = data.winnerList;
            break;
        case "3":
            state.checkedList3 = data.winnerList;
            break;
        default:
            break;
    }
    state.winner = data.winnerList.map((item) => item.name).join("、");
    if (state.honorContent.templateType === "99") {
        const imgArr = [];
        imgArr.push({
            url: data.imgPaths,
        });
        state.imageList = imgArr;
    } else {
        state.imageList = [];
        state.fileList = [];
    }
    getBindingSite();
};

defineExpose({ openAdd, openEdit });
</script>

<style lang="less" scoped>
.footer {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    text-align: center;
    border-top: 1px solid #e9e9e9;
    padding: 10px 16px;
    background: #fff;
    z-index: 1;
}

.bg1 {
    background: url("/image/honor_bg1.png");
    background-size: 100% 100%;
}

.bg2 {
    background: url("/image/honor_bg2.png");
    background-size: 100% 100%;
}

.bg3 {
    background: url("/image/honor_bg3.png");
    background-size: 100% 100%;
}

.bg4 {
    background: url("/image/honor_bg4.png");
    background-size: 100% 100%;
}

.con {
    border-radius: 6px;
    width: 442px;
    margin: 20px 0px 10px 0px;
    min-height: 255px;
    text-align: center;
    border: 1px dashed #d9d9d9;

    .imgbox {
        padding-top: 50px;
    }

    .btn {
        height: 32px;
        color: #00b781;
        margin: 24px 0 24px;
        background: rgba(0, 183, 129, 0.08);
        border: 1px solid #00b781;
        border-radius: 4px;
    }

    .customImg {
        position: relative;
        height: 255px;

        img {
            border-radius: 6px;
            width: 100%;
            height: 100%;
        }

        .del {
            width: 20px;
            height: 20px;
            line-height: 20px;
            color: #fff;
            position: absolute;
            top: -10px;
            right: -10px;
            cursor: pointer;
            background: #f5222d;
            border-radius: 50%;
        }
    }
}

.TemInfo {
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    padding-bottom: 10px;
}

.add_count_down {
    position: relative;
    margin: 20px 10px 10px 0px;
    height: 255px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    border: 1px solid #e1e1e1;
    border-radius: 6px;
    font-size: 20px;

    .count_text {
        position: absolute;
        top: 40px;
        text-align: center;
        left: 50%;
        transform: translate(-50%, 0);
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #fcf38c;
        line-height: 33px;
    }

    .count_text2 {
        position: absolute;
        top: 60px;
        width: 80px;
        white-space: pre-wrap;
        text-align: center;
        left: 50%;
        transform: translate(-50%, 0);
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #fcf38c;
        line-height: 33px;
    }
}

::deep(.addHonor .ant-form-item) {
    padding-bottom: 24px !important;
    margin-bottom: unset !important;
}
</style>

<style lang="less">
.drawerHonor {
    .ant-drawer-content-wrapper {
        width: 500px !important;
    }

    .ant-drawer-close {
        position: absolute;
        right: 0;
    }
}

.addHonor {
    .ant-form-item {
        padding-bottom: 24px !important;
        margin-bottom: unset !important;
    }
}

.ant-image img {
    width: 100%;
    height: 100%;
}

// 隐藏禁用节点的复选框（仅非 type=4 的层级被禁用）
.ant-select-tree-checkbox-disabled {
    display: none;
}
</style>
