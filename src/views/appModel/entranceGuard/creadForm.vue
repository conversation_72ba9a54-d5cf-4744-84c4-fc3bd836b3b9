<!-- CreadForm -->
<template>
    <div>
        <a-drawer v-model:visible="props.formVisible" :title="state.formTitle" width="500" :maskClosable="false"
            @close="handerCancel">
            <a-form :model="state.deviceForm" :rules="ruleList" name="basic" layout="vertical" ref="deviceFormRef">
                <a-form-item label="场地：" class="3" name="siteId">
                    <a-tree-select v-model:value="state.deviceForm.siteId" show-search placeholder="请选择" allow-clear
                        :treeDefaultExpandAll="true" :listHeight="320" :disabled="!!state.deviceForm.id"
                        :tree-data="state.fieldList" :field-names="state.fieldNames"
                        treeNodeFilterProp="name"></a-tree-select>
                </a-form-item>
                <a-form-item label="设备型号：" name="deviceManufacturer">
                    <a-select :field-names="{
                        label: 'deviceManufacturer',
                        value: 'deviceManufacturer',
                    }" :disabled="!!state.deviceForm.id" @change="deviceManufacturerChange"
                        v-model:value="state.deviceForm.deviceManufacturer" placeholder="请选择设备型号"
                        :options="state.schoolEquipmentTypeList">
                    </a-select>
                </a-form-item>
                <a-form-item label="门禁机名称：" name="machineName">
                    <a-input v-model:value="state.deviceForm.machineName" placeholder="请输入门禁机名称（不超过20字）" allow-clear
                        :maxlength="20" />
                </a-form-item>
                <a-form-item label="门禁机数量：" name="machineNumber" v-if="!state.deviceForm.id">
                    <a-input v-model:value="state.deviceForm.machineNumber" placeholder="请输入1-20的数字" allow-clear
                        :maxlength="20" />
                </a-form-item>
                <a-form-item class="administratorName" label="管理员：" name="administratorName">
                    <a-input v-model:value="state.deviceForm.administratorName" placeholder="请选择"
                        @click="handerManagerVisible" allowClear readonly allow-clear :maxlength="20" />

                    <CloseCircleFilled v-if="state.deviceForm.administratorName" class="administratorName_clos"
                        @click="handerAdministratorNameClos" />
                    <div style="margin-top: 8px">
                        <exclamation-circle-filled />
                        <span class="tip 1">最多选择三个管理员</span>
                    </div>
                </a-form-item>
            </a-form>
            <template #footer>
                <div class="footer">
                    <a-button style="margin-right: 8px" @click="handerCancel">取消</a-button>
                    <a-button type="primary" @click="formSubmit" :loading="state.submitLoading">确认</a-button>
                </div>
            </template>
        </a-drawer>

        <y-select mode="personnel" :tabs="[{ tab: '管理员', key: 1, checked: true }]"
            v-model:visible="state.managerVisible" v-model:checked="state.checkedList" :optionLength="3"
            @handleOk="managerOk">
        </y-select>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from "vue";
import { Local } from "@/utils/storage.ts";
import { message } from "ant-design-vue";
import {
    createMachine,
    detailMachine,
    updateMachine,
    siteList,
    getBrandlistBySchool
} from "@/api/machineApi.js";
import { useStore } from "vuex";

const store = useStore();

const props = defineProps({
    formVisible: {
        type: Boolean,
        defaulte: false,
    },
    detailId: {
        type: String,
        defaulte: "",
    },
});
const deviceFormRef = ref();
const state = reactive({
    formTitle: "新增门禁机",
    managerVisible: false,
    submitLoading: false,
    fieldNames: {
        children: "children",
        label: "name",
        key: "id",
        value: "id",
    },
    fieldList: [],
    checkedList: [],
    deviceForm: {
        id: "",
        siteId: null,
        machineName: "",
        machineNumber: "",
        administratorName: "",
        teacherId: [],
    },
    schoolEquipmentTypeList: []
});
const emit = defineEmits([
    "update:formVisible",
    "update:detailId",
    "emitPageList",
]);
const pwdCheck = (rule, value, callback) => {
    if (!value) return Promise.reject();
    return Promise.resolve();
};
const ruleList = {
    siteId: [{ required: true, message: "请选择场地" }],
    machineName: [{ required: true, message: "请输入人脸机名称" }],
    machineNumber: [
        { required: !state.deviceForm.id, message: "请输入人脸机数量" },
        {
            type: "number",
            message: "仅支持输入1-20之间的整数",
            trigger: "blur",
            transform(value) {
                if (value) {
                    const val = Number(value);
                    if (/^[1-9]$|^0[1-9]$|^1[0-9]$|^20$/.test(val)) {
                        return val;
                    }
                    return false;
                }
            },
        },
    ],
    administratorName: [
        { required: true, message: "请选择管理员" },
        { validator: pwdCheck, trigger: "blur" },
    ],
    deviceManufacturer: [{ required: true, message: '请选择设备型号：' }],

};

// 禁用选中父节点
const disabledP = (tree) => {
    tree.forEach((item) => {
        if (item.children) {
            item.disabled = true;
            disabledP(item.children);
        }
    });
};

// 场地接口
const getSiteList = () => {
    siteList().then((res) => {
        const { data } = res;
        state.fieldList = data;
        disabledP(data);
    });
};
// 取消
const handerCancel = () => {
    deviceFormRef.value.resetFields();
    emit("update:formVisible", false);
    emit("update:detailId", "");
    state.checkedList = [];
    state.deviceForm.id = "";
};
const handerManagerVisible = () => {
    if (!state.deviceForm.administratorName) {
        state.checkedList = []
    }
    store.dispatch("base/getDepartmentTree", '').then((res)=> {
        state.managerVisible = true
    })
}
const handerAdministratorNameClos = () => {
    state.deviceForm.administratorName = ''
    state.deviceForm.teacherId = []
    state.checkedList = []
}
// 确定
const formSubmit = () => {
    deviceFormRef.value.validateFields().then(() => {
        state.submitLoading = true;
        const params = {
            ...state.deviceForm,
            deviceType: 8,
        };
        let Api = createMachine;
        if (state.deviceForm.id) {
            Api = updateMachine;
        }
        Api(params)
            .then((res) => {
                message.success(res.message);
                emit("emitPageList");
                handerCancel();
            })
            .finally(() => {
                state.submitLoading = false;
            });
    });
};

// 选择的管理人员
const managerOk = (data) => {
    const administratorName = [];
    state.deviceForm.teacherId = [];
    if (!data.length) return;
    data.forEach((v) => {
        administratorName.push(v.name);
        state.deviceForm.teacherId.push(v.userId);
    });
    state.deviceForm.administratorName = administratorName.toString(",");
};
// 回显接口
const echoMachine = () => {
    detailMachine({ id: props.detailId }).then(({ data }) => {
        state.deviceForm.teacherId = [];
        state.checkedList = data.administratorList?.map((v) => {
            state.deviceForm?.teacherId.push(v.teacherId);
            return {
                ...v,
                id: v.teacherId,
            };
        });
        Object.assign(state.deviceForm, data);
    });
};
watch(
    () => props.detailId,
    (val) => {
        state.formTitle = "新增门禁机";
        if (val) {
            state.formTitle = "编辑门禁机";
            echoMachine();
        }
    }
);

const deviceManufacturerChange = (val, opt) => {
    state.deviceForm.deviceMode = opt.deviceMode
}
// 型号的列表
const reqBrandlistBySchool = () => {
    const schoolId = Local.get("schoolId");
    getBrandlistBySchool({
        schoolId,
        equipmentTypes: [8],
    }).then(({ data }) => {
        const allocatedArr =
            data.schoolEquipmentTypeList[0]?.deviceBrandDTOList || [];
        state.schoolEquipmentTypeList = allocatedArr.filter(
            (item) => item.allocated
        );
    });
};
onMounted(() => {
    getSiteList();
    reqBrandlistBySchool()
});
</script>

<style scoped lang="less">
.tip {
    color: #c0c4cc;
    font-size: 12px;
    margin-left: 6px;
}
</style>
