<!--
 * @Author: 舒志建 <EMAIL>
 * @Date: 2023-06-30 16:52:41
 * @LastEditors: 舒志建 <EMAIL>
 * @LastEditTime: 2023-07-04 15:12:22
 * @FilePath: \cloud-system-2.0c:\Users\<USER>\Desktop\ydproject\cloudSystem3.0\cloud-system-2.0\src\views\appModel\traffic\trafficRule\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="trafficRuleDiv">
        <div class="searchHead">
            <a-form
                :model="state.searchForm"
                layout="inline"
                ref="searchFormRef"
            >
                <a-form-item label="场地："  class="5"  name="siteId">
                    <a-cascader
                        v-model:value="state.searchForm.siteId"
                        :fieldNames="state.fieidNames"
                        :options="state.fieidOption"
                        placeholder="请选择"
                        style="width: 200px"
                    />
                </a-form-item>
                <a-form-item name="userName">
                    <a-input
                        v-model:value="state.searchForm.userName"
                        placeholder="请输入人员姓名搜索"
                        allowClear
                    ></a-input>
                </a-form-item>
                <a-form-item label="设备类型：" name="deviceType">
                    <a-select
                        style="width: 150px"
                        v-model:value="state.searchForm.deviceType"
                        :options="state.deviceTypeOptions"
                        placeholder="请选择设备类型"
                    >
                    </a-select>
                </a-form-item>
                <a-form-item>
                    <a-button
                        type="primary"
                        style="margin-right: 10px"
                        @click="query"
                    >
                        <template #icon>
                            <SearchOutlined style="margin-right: 10px" />
                        </template>
                        查询
                    </a-button>
                    <a-button @click="reset">
                        <template #icon>
                            <redo-outlined style="margin-right: 10px" />
                        </template>
                        重置
                    </a-button>
                </a-form-item>
            </a-form>
            <a-button type="primary" @click="addrule('')">
                <template #icon>
                    <plus-outlined />
                </template>
                添加规则
            </a-button>
        </div>
        <YTable
            class="worker-table tableBox"
            :columns="state.columns"
            :dataSource="state.dataSource"
            :rowSelection="false"
            :isScroll="true"
            :totals="state.pagination"
            @onSelectedRowKeys="handerSelectedRowKeys"
        >
            <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex === 'rulesType'">
                    {{ state.rulesTypeObj[record.rulesType] }}
                </template>
                <template v-if="column.dataIndex === 'deviceType'">
                    {{ state.deviceTypeObj[record.deviceType] }}
                </template>
                <template v-if="column.dataIndex === 'inOut'">
                    <template v-if="record.rulesType == 2">
                        {{ state.inOutNoObj[record.inOut] }}
                    </template>
                    <template v-else>
                        {{ state.inOutObj[record.inOut] }}
                    </template>
                </template>
                <template v-if="column.dataIndex === 'time'">
                    {{ record.startTime + '/' + record.endTime }}
                    <a-tooltip v-if="record.endTime == '2099-12-31'">
                        <template #title>该条规则永久有效</template>
                        <exclamation-circle-outlined
                            style="font-size: 16px; color: #ff9800"
                            v-if="record.endTime == '2099-12-31'"
                        />
                    </a-tooltip>
                    <a-tooltip
                        v-if="
                            new Date(record.endTime).getTime() <
                            new Date(nowDate).getTime()
                        "
                    >
                        <template #title>该条规则已过期</template>
                        <exclamation-circle-outlined
                            style="font-size: 16px; color: #989595"
                            v-if="
                                new Date(record.endTime).getTime() <
                                new Date(nowDate).getTime()
                            "
                        />
                    </a-tooltip>
                </template>
                <template v-if="column.dataIndex === 'isEnable'">
                    <a-badge
                        :color="state.isEnableObj[record.isEnable].color"
                        :text="state.isEnableObj[record.isEnable].text"
                    />
                </template>

                <template v-if="column.dataIndex === 'operation'">
                    <a-button
                        class="button-link"
                        type="link"
                        @click="edit(record)"
                        >修改</a-button
                    >
                    <a-button
                        class="button-link"
                        style="color: red"
                        type="link"
                        @click="remove(record)"
                        >删除</a-button
                    >
                    <a-button
                        class="button-link"
                        type="link"
                        @click="see(record)"
                        >查看规则</a-button
                    >
                    <a-switch
                        v-model:checked="record.isEnable"
                        :checkedValue="1"
                        :unCheckedValue="2"
                        @change="changeStatus(record)"
                    />
                    {{ record.isEnable === 1 ? '禁用' : '启用' }}
                </template>
            </template>
        </YTable>
        <component
            :is="state.componentName"
            :siteOption="state.fieidOption"
            v-model:visible="state.componentVisible"
            @emitAddRules="addrule"
        ></component>
    </div>
</template>

<script setup>
import { reactive, toRefs, onMounted, createVNode, nextTick } from 'vue'
import FormInstance, { Modal, message } from 'ant-design-vue'
import {
    SearchOutlined,
    PlusOutlined,
    ExclamationCircleFilled
} from '@ant-design/icons-vue'
import YTable from 'comps/YTable'
import {
    getSiteList,
    pageRuleInfo,
    updateRuleIsEnableInfo,
    deletePassRuleInfo
} from '@/api/trafficRules.js'
import { useRoute, useRouter } from 'vue-router'
import AddModifyRules from './AddModifyRules'
import AddModifyOption from './AddModifyOption'

const searchFormRef = shallowRef()
const router = useRouter()
const route = useRoute()
const state = reactive({
    componentVisible: false,
    componentName: null,
    components: {
        AddModifyRules,
        AddModifyOption
    },
    treeId: 1,
    roleTree: [],
    searchForm: {
        siteId: '',
        userName: '',
        deviceType: ''
    },
    dataSource: [],
    pagination: {
        total: 0,
        pageNo: 1,
        pageSize: 10
    },
    columns: [
        {
            title: '序号',
            width: 80,
            customRender: ({ text, record, index }) => `${index + 1}` // 显示每一行的序号
            // fixed: "left",
        },
        {
            title: '规则类型',
            dataIndex: 'rulesType',
            key: 'id',
            width: 120
        },
        {
            title: '设备类型',
            dataIndex: 'deviceType',
            key: 'deviceType',
            width: 120
        },
        {
            title: '场地',
            dataIndex: 'siteName',
            key: 'id',
            width: 120
        },
        {
            title: '人员数量',
            dataIndex: 'peopleNumber',
            key: 'id',
            width: 120
        },
        {
            title: '通行方向',
            dataIndex: 'inOut',
            key: 'id',
            width: 120
        },
        {
            title: '规则名称',
            dataIndex: 'ruleName',
            key: 'id',
            width: 120,
            ellipsis: true,
            showSorterTooltip: true
        },
        {
            title: '有效期',
            dataIndex: 'time',
            key: 'id',
            width: 260
        },
        {
            title: '状态',
            dataIndex: 'isEnable',
            key: 'id',
            width: 100
        },
        {
            title: '操作',
            dataIndex: 'operation',
            key: 'id',
            width: 200,
            fixed: 'right'
        }
    ],
    fieidOption: [],
    fieidNames: {
        label: 'name',
        value: 'id',
        children: 'children'
    },
    deviceTypeOptions: [
        {
            value: '',
            label: '全部'
        },
        {
            value: 1,
            label: '人脸机'
        },
        {
            value: 3,
            label: '考勤机'
        }
    ],
    rulesTypeObj: { 1: '常规规则', 2: '节假日规则', 3: '临时规则' },
    deviceTypeObj: { 1: '人脸机', 2: '班牌', 3: '考勤机' },
    inOutObj: { 1: '出', 2: '入' },
    inOutNoObj: { 1: '不允许出入', 2: '允许出入' },
    isEnableObj: [
        { color: '#BFBFBF', text: '未启用' },
        { color: '#00B781', text: '已启用' },
        { color: '#F5222D', text: '禁用' }
    ]
})

const date = new Date()
const year = date.getFullYear()
let month = date.getMonth() + 1
let day = date.getDate()
if (month < 10) {
    month = '0' + month
}
if (day < 10) {
    day = '0' + day
}
const nowDate = year + '-' + month + '-' + day

const addrule = (item) => {
    state.componentName =item?state.components[item]: AddModifyOption
    state.componentVisible = true
}
// 场地接口
const sitelist = () => {
    // type 1为人脸机的场地  3为考勤机的场地 2为全部场地/班牌
    const params = {
        type: state.searchForm.deviceType || 2
    }
    getSiteList(params).then(({ data }) => {
        state.fieidOption = data
    })
}

// 列表
const getPageRuleInfo = () => {
    const params = {
        passId: state.treeId || route.query.deptId,
        ...state.searchForm,
        ...state.pagination
    }
    if (params.siteId.length) {
        params.siteId = params.siteId[1]
    }
    pageRuleInfo(params).then((res) => {
        const { data } = res
        state.pagination.total = data.total
        state.dataSource = data.list
    })
}
// 分页
const handerSelectedRowKeys = (res) => {
    const { current, pageSize } = res
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    getPageRuleInfo()
}

const remove = (v) => {
    Modal.confirm({
        title: '删除提示',
        icon: createVNode(ExclamationCircleFilled),
        content: '是否删除该条规则？',
        okText: '确认',
        cancelText: '取消',
        onOk() {
            const { ruleId, rulesType } = v
            const params = {
                id: ruleId,
                ruleType: rulesType
            }
            // 删除规则
            deletePassRuleInfo(params).then(() => {
                getPageRuleInfo()
            })
        }
    })
}
// 查詢
const query = () => {
    state.pagination.pageNo = 1
    getPageRuleInfo()
}

const reset = () => {
    searchFormRef.value.resetFields()
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getPageRuleInfo()
}
const changeStatus = (v) => {
    const { ruleId, rulesType, isEnable } = v
    const params = {
        ruleId,
        isEnable,
        ruleType: rulesType
    }
    updateRuleIsEnableInfo(params).then((res) => {
        message.success(res.message)
        getPageRuleInfo()
    })
}
onMounted(() => {
    sitelist()
    getPageRuleInfo()
})
</script>

<style scoped lang="less">
.trafficRuleDiv {
    .searchHead {
        display: flex;
        justify-content: space-between;
        margin: 16px;
    }
}
</style>
