<!--
 * @Author: 通行规则
 * @Date: 2022-03-07 09:52:11
* @LastEditTime: 2023-07-13 14:06:14
 * @LastEditors: 舒志建 <EMAIL>
 * @Description: 不是我写的，我只是修改（太残忍了）
 * @FilePath: \cloud-system-2.0\src\views\appModel\traffic\index.vue Written garbage
-->

<template>
    <div class="trafficRuleDiv">
        <!-- 侧边栏 -->
        <div class="right">
            <!-- 搜索表单 -->
            <div class="searchForm">
                <div class="searchHead">
                    <a-form :model="searchForm" layout="inline">
                        <a-form-item label="场地：" class="4" style="margin-bottom: 12px">
                            <a-cascader v-model:value="searchForm.fieid" :fieldNames="hometownFieldNames"
                                :options="fieldList1" placeholder="请选择" style="width: 200px" />
                        </a-form-item>
                        <a-form-item>
                            <a-input v-model:value="searchForm.name" placeholder="请输入人员姓名搜索" allowClear></a-input>
                        </a-form-item>
                        <a-form-item>
                            <a-button type="primary" style="margin-right: 10px" @click="query">
                                <template #icon>
                                    <SearchOutlined style="margin-right: 10px" />
                                </template>
                                查询
                            </a-button>
                            <a-button @click="reset">
                                <template #icon>
                                    <redo-outlined style="margin-right: 10px" />
                                </template>
                                重置
                            </a-button>
                        </a-form-item>
                    </a-form>
                    <a-button type="primary" @click="addGeneralRules">
                        <template #icon>
                            <plus-outlined />
                        </template>
                        添加规则
                    </a-button>
                </div>
            </div>
            <!-- 表格 -->
            <YTable class="worker-table tableBox" :columns="columns" :dataSource="dataSource" :rowSelection="false"
                :isScroll="true" :totals="state.pagination" @onSelectedRowKeys="handerSelectedRowKeys">
                <template #bodyCell="{ column, text, record }">
                    <template v-if="column.dataIndex === 'rulesType'">
                        <div>
                            {{
                                record.rulesType == 1
                                ? "常规规则"
                                : record.rulesType == 2
                                    ? "节假日规则"
                                    : record.rulesType == 3
                                        ? "临时规则"
                                        : ""
                            }}
                        </div>
                    </template>
                    <template v-if="column.dataIndex === 'deviceType'">
                        <div v-if="record.deviceType == 8">门禁机</div>
                    </template>
                    <template v-if="column.dataIndex === 'inOut'">
                        <div v-if="record.rulesType != 2">
                            {{
                                record.inOut == 1
                                ? "出"
                                : record.inOut == 2
                                    ? "入"
                                    : "-"
                            }}
                        </div>
                        <div v-else>
                            {{
                                record.inOut == 1
                                ? "不允许出入"
                                : record.inOut == 2
                                    ? "允许出入"
                                    : "-"
                            }}
                        </div>
                    </template>
                    <template v-if="column.dataIndex === 'isEnable'">
                        <div style="display: flex; align-items: center">
                            <div class="stateLi" :style="record.isEnable == 1
                                ? 'background: var(--primary-color)'
                                : record.isEnable == 2
                                    ? 'background: #F5222D'
                                    : 'background:#BFBFBF'
                                "></div>
                            <div>
                                {{
                                    record.isEnable == 1
                                    ? "已启用"
                                    : record.isEnable == 2
                                        ? "禁用"
                                        : "未启用"
                                }}
                            </div>
                        </div>
                    </template>
                    <template v-if="column.dataIndex === 'time'">
                        {{ record.startTime + "/" + record.endTime }}
                        <a-tooltip v-if="record.endTime == '2099-12-31'">
                            <template #title>该条规则永久有效</template>
                            <exclamation-circle-outlined style="font-size: 16px; color: #ff9800"
                                v-if="record.endTime == '2099-12-31'" />
                        </a-tooltip>
                        <a-tooltip v-if="new Date(record.endTime).getTime() <
                            new Date(nowDate).getTime()
                            ">
                            <template #title>该条规则已过期</template>
                            <exclamation-circle-outlined style="font-size: 16px; color: #989595" v-if="new Date(record.endTime).getTime() <
                                new Date(nowDate).getTime()
                                " />
                        </a-tooltip>
                    </template>
                    <div v-if="column.dataIndex === 'operation'">
                        <a-button type="link" style="color: var(--primary-color); padding-left: 0"
                            @click="edit(record)">修改</a-button>
                        <a-button type="link" @click="remove(record)" style="color: red">删除</a-button>
                        <a-button type="link" style="color: var(--primary-color)" @click="see(record)">查看规则</a-button>
                        <a-switch v-model:checked="record.isStatus" @change="changeStatus(record)" />
                        {{ record.isEnable == 1 ? "禁用" : "启用" }}
                    </div>
                    <template v-if="column.dataIndex === 'ruleName' ||
                        column.dataIndex === 'siteName'
                        ">
                        <Tooltip :title="text"></Tooltip>
                    </template>
                </template>
            </YTable>
            <!-- 暂无数据 -->
            <!-- <div class="noData_empty" v-else>
                <a-empty
                    image="/image/empty.png"
                    :image-style="{
                        height: '180px',
                        width: '180px'
                    }"
                >
                    <template #description>
                        <span>暂无规则</span>
                    </template>
                </a-empty>
            </div> -->
        </div>
        <!-- 添加规则弹框 -->
        <a-modal v-model:visible="addRuleVisible" class="addRuleModal" width="408px" footer="">
            <template #title>
                <div>添加规则</div>
            </template>

            <div class="threeButton">
                <a-button type="primary" class="btn" @click="addGeneralRules">添加常规规则</a-button>
            </div>
        </a-modal>
        <!-- 规则弹窗 -->
        <a-drawer v-model:visible="rulesVisible" :title="title" width="502" @close="closeVisible">
            <a-form :model="rulesForm" layout="vertical" ref="rulesRefs">
                <a-form-item label="规则名称：" name="ruleName" :rules="[{ required: true, message: '请输入规则名称' }]">
                    <a-input v-model:value="rulesForm.ruleName" placeholder="请输入" allow-clear maxlength="20" />
                </a-form-item>
                <a-form-item label="选择场地：" name="siteId" :rules="[
                    {
                        required: true,
                        message: '请选择场地',
                    },
                ]">
                    <a-tree-select class="select_site_class" v-model:value="rulesForm.siteId" show-search placeholder="请选择"
                        allow-clear tree-default-expand-all :tree-data="fieldList" :field-names="{
                            children: 'children',
                            label: 'name',
                            key: 'id',
                            value: 'id',
                        }" @select="selectField" :disabled="disabled"></a-tree-select>
                </a-form-item>
                <a-form-item label="选择人员：" name="personnelName" :rules="[
                    {
                        required: true,
                        message: '请选择人员',
                    },
                    { validator: pwdCheck, trigger: 'blur' },
                ]">
                    <y-select mode="personnel" :tabs="[
                        { tab: '教职工', key: 1, checked: true },
                        { tab: '学生组', key: 2, checked: false },
                    ]" v-model:visible="personnelvisible" v-model:checked="checkedList" :queryEltern="false"
                        @handleOk="handleOk">
                        <a-input v-model:value="rulesForm.personnelName" allowClear placeholder="请选择" readonly="readonly" />
                    </y-select>
                </a-form-item>
                <a-form-item label="通行方向：" class="form_inline" name="direction" :rules="[
                    {
                        required: true,
                        message: '请选择通行方向',
                    },
                ]">
                    <a-radio-group v-model:value="rulesForm.direction">
                        <a-radio value="2">
                            <span>
                                {{
                                    title == "添加节假日规则" ||
                                    title == "修改节假日规则"
                                    ? "允许出入"
                                    : "入"
                                }}
                            </span>
                        </a-radio>
                        <a-radio value="1">
                            {{
                                title == "添加节假日规则" ||
                                title == "修改节假日规则"
                                ? "不允许出入"
                                : "出"
                            }}
                        </a-radio>
                    </a-radio-group>
                </a-form-item>
                <a-form-item label="有效期：" :name="title == '添加临时规则' ? 'time' : ''" v-if="title != '添加节假日规则' && title != '修改节假日规则'
                        " :rules="title == '添加临时规则'
            ? [{ required: true, message: '请选择' }]
            : []
            ">
                    <a-range-picker v-model:value="rulesForm.time" value-format="YYYY-MM-DD" style="width: 100%"
                        separator="至" :disabled-date="disabledDate" @calendarChange="calendarPriceRangeChange" />
                </a-form-item>
                <!-- 节假日表格 -->
                <div v-if="title == '添加节假日规则' || title == '修改节假日规则'
                    ">
                    <a-button @click="addHoliday" style="
                            color: var(--primary-color);
                            border-color: var(--primary-color);
                            background: rgba(0, 183, 129, 0.08);
                        ">
                        <template #icon>
                            <plus-outlined />
                        </template>
                        添加节假日
                    </a-button>
                    <span class="holidayText">(同一时刻表内，节假日规则优先于常规规则)</span>
                    <YTable :columns="holidayColumns" :dataSource="holidayDataSource" :rowSelection="false"
                        style="margin-top: 16px">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.dataIndex === 'operation'">
                                <a-button type="link" @click="deleteholiday(record)" style="color: red">删除</a-button>
                            </template>
                            <template v-if="column.dataIndex === 'time'">
                                {{ record.startTime + "/" + record.endTime }}
                            </template>
                        </template>
                    </YTable>
                </div>
            </a-form>
            <a-form ref="formRef" layout="inline" :model="weekForm" v-if="title != '添加节假日规则' && title != '修改节假日规则'">
                <label>
                    <i style="color: red">*</i>
                    通行规则：
                    <span class="5" style="color: #00000033">(通行规则按照每周循环)</span>
                </label>
                <a-tabs v-model:activeKey="activeKey" type="card" :class="{ atab: everyDay }" id="atabDiv">
                    <a-tab-pane :tab="item.weekName" v-for="(item, index) in weekForm.weekList" :key="index">
                        <a-radio-group v-model:value="item.isAllow" name="radioGroup" @change="changeIsPassage(item)"
                            style="margin: 10px">
                            <a-radio value="0">自定义</a-radio>
                            <a-radio value="1">允许全天通行</a-radio>
                            <a-radio value="2" v-if="title == '添加临时规则'">不允许全天通行</a-radio>
                        </a-radio-group>
                        <a-space style="display: flex; margin-bottom: 8px" align="baseline"
                            v-for="(item1, i) in item.timelist" :key="i">
                            <a-form-item :label="item.isAllow === '2'
                                ? '非通行时间'
                                : '通行时间'
                                ">
                                <a-time-range-picker v-model:value="item1.time" @change="(v) => changetime(v, index, i)"
                                    value-format="HH:mm:ss" :disabled="item.isAllow === '1' ||
                                        item.isAllow === '2'
                                        " />
                            </a-form-item>
                            <i class="iconfont icon-shanchu" style="font-size: 22px" @click="removeUser(item, i)"
                                v-if="item.isAllow === '0'"></i>
                            <!-- <MinusCircleOutlined
                                @click="removeUser(item, i)"
                                v-if="item.isAllow == 0"
                            />-->
                        </a-space>
                        <div style="color: red; padding: 0px 0px 10px 70px" v-if="state.noTime">
                            <i class="iconfont icon-Shape6"></i>
                            通行时间没有填写完整2
                        </div>
                        <a-form-item v-if="item.isAllow === '0' && item.timelist.length < 6
                            ">
                            <a-button style="
                                    color: var(--primary-color);
                                    border-color: var(--primary-color);
                                    background: rgba(0, 183, 129, 0.08);
                                " @click="addUser(item)">
                                <template #icon> <plus-outlined /> </template>添加通行时间
                            </a-button>
                        </a-form-item>
                    </a-tab-pane>
                </a-tabs>
                <a-checkbox v-model:checked="everyDay" @change="changeEveryDay">应用到每天</a-checkbox>
            </a-form>
            <template #footer>
                <div class="footer">
                    <a-button style="margin-right: 8px" @click="cancel">取消</a-button>
                    <a-button type="primary" :loading="state.loading" @click="confirm">确认</a-button>
                </div>
            </template>
        </a-drawer>
        <!-- 节假日弹窗、 查看规则弹窗-->
        <a-modal v-model:visible="holidayVisible" :title="title1" @ok="handleOkHoliday"
            :width="title1 == '添加节假日' ? '408px' : '800px'" :zIndex="1049" :class="title1 == '添加节假日' ? '' : 'addRuleModal'">
            <!-- 添加节假日 -->
            <div v-if="title1 == '添加节假日'">
                <a-form :model="holidayForm" layout="vertical" ref="holidayRef">
                    <a-form-item label="节日名称：" name="name" :rules="[
                        {
                            required: true,
                            message: '请输入节日名称',
                        },
                    ]">
                        <a-input v-model:value="holidayForm.name" allow-clear maxlength="10" placeholder="请输入节日名称" />
                    </a-form-item>
                    <a-form-item label="日期" name="date" :rules="[
                        {
                            required: true,
                            message: '请选择日期',
                        },
                    ]">
                        <a-range-picker v-model:value="holidayForm.date" value-format="YYYY-MM-DD" style="width: 100%"
                            separator="至" />
                    </a-form-item>
                </a-form>
            </div>
            <!-- 查看常规、临时规则 -->
            <div v-if="state.seeType != 2 && title1 != '添加节假日'">
                有效期：
                <span>{{ times }}</span>
                <YTable :columns="seeColumns" :dataSource="seeDataSource" :rowSelection="false" style="margin-top: 16px">
                </YTable>
            </div>
            <!-- 查看节假日规则 -->
            <div v-if="state.seeType == 2 && title1 != '添加节假日'">
                <!-- {{ isInOut }} -->
                <YTable :columns="seeHolidayColumns" :dataSource="seeHolidayDataSource" :rowSelection="false"
                    style="margin-top: 16px"></YTable>
            </div>
        </a-modal>
        <!-- 规则重复的异常弹框 -->
        <a-modal v-model:visible="abnormalVisible" title="异常" width="408px" class="abnormalModal">
            <exclamation-circle-filled id="itemTooltip" style="font-size: 18px; color: #ff9800" />
            <span>以下人员在该场地存在规则冲突，请调整后重新保存，同一场地同一人员同一时间内只能有一条同一类型的规则</span>
            <YTable :columns="abnormalColumns" :dataSource="abnormalDataSource" :rowSelection="false"
                style="margin-top: 16px"></YTable>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import {
    reactive,
    toRefs,
    onMounted,
    computed,
    createVNode,
    nextTick,
    ref,
    watch,
} from "vue";
import YTable from "comps/YTable/index.vue";
import {
    SearchOutlined,
    // MinusCircleOutlined,
    PlusOutlined,
    ExclamationCircleFilled,
} from "@ant-design/icons-vue";
import FormInstance, { Modal, message } from "ant-design-vue";
import YSelect from "@/components/YSelect/index";
import dayjs from "dayjs";
import { useRoute, useRouter } from "vue-router";
import {
    listPassSite,
    getSiteList,
    pageRuleInfo,
    createPasssceneRule,
    getHolidayInfo,
    getPassRuleDetailById,
    updatePassRuleInfo,
    deletePassRuleInfo,
    updateRuleIsEnableInfo,
} from "@/api/trafficRules.js";
import { getColumnCheckboxGroupFilterDropdown } from "@/components/FilterDropdown.jsx";

const route = useRoute();
const router = useRouter();
const date = new Date();
const year = date.getFullYear();
let month = date.getMonth() + 1;
let day = date.getDate();
const deviceType = 8;
if (month < 10) {
    month = "0" + month;
}
if (day < 10) {
    day = "0" + day;
}
// 路由返回按钮
const handerFacultyForm = () => {
    router.back();
};
const nowDate = year + "-" + month + "-" + day;
const rulesRefs = ref();
const state = reactive({
    loading: false,
    roleTree: [],
    fieldNames: {
        children: "children",
        title: "name",
        key: "id",
    },
    searchForm: {
        fieid: "",
        name: "",
        deviceType: 8,
    },
    dataSource: [],
    fieldList1: [],
    checked: true,
    flag: false,
    addRuleVisible: false,
    rulesVisible: false,
    title: "",
    rulesForm: {
        id: "",
        ruleName: "",
        siteId: null,
        personnelName: "",
        teacherList: [], // 教职工id
        studentList: [], // 学生id
        direction: "",
        time: "",
        deviceType: deviceType || 1,
    },
    activeKey: 0,
    rulesRef: FormInstance,
    fieldList: [],
    personnelvisible: false,
    checkedList: [],
    weekListLoca: {},
    weekForm: {
        weekList: [
            {
                weekName: "周一",
                week: 1,
                isAllow: "1",
                name: "1",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周二",
                week: 2,
                isAllow: "1",
                name: "2",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周三",
                week: 3,
                isAllow: "1",
                name: "3",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周四",
                week: 4,
                isAllow: "1",
                name: "4",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周五",
                week: 5,
                isAllow: "1",
                name: "5",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周六",
                week: 6,
                isAllow: "1",
                name: "6",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
            {
                weekName: "周日",
                week: 7,
                isAllow: "1",
                name: "7",
                timelist: [
                    {
                        time: "",
                    },
                ],
            },
        ],
    },
    // formRef: FormInstance,
    everyDay: true,
    holidayDataSource: [],
    holidayVisible: false,
    holidayForm: {
        name: "",
        date: "",
    },
    holidayRef: FormInstance,
    title1: "",
    times: "2022-3-8",
    seeDataSource: [],
    seeHolidayDataSource: [],
    disabled: false,
    pagination: {
        total: 0,
        pageNo: 1,
        pageSize: 10,
    },
    rulesType: 0,
    editType: 0,
    ruleId: "",
    seeType: "",
    isInOut: "",
    treeId: null,
    abnormalVisible: false,
    abnormalDataSource: [],
    treeTitle: "",
    selectPriceDate: "",
    noTime: false,
    offsetDays: 86400000 * 7, // 限制时间选择 超过7天禁选
});
const formRef = ref();
const holidayRef = ref();

// 选择开始时间/结束时间
const calendarPriceRangeChange = (date) => {
    if (state.rulesType === 3) {
        state.selectPriceDate = date[0];
    }
};
// 根据选择的开始时间/结束时间，动态渲染要禁用的日期
const disabledDate = (current) => {
    if (state.rulesType === 3) {
        if (state.selectPriceDate) {
            const selectV = dayjs(
                state.selectPriceDate,
                "YYYY-MM-DD"
            ).valueOf();
            return (
                current >
                dayjs(new Date(selectV + state.offsetDays).getTime()) ||
                current < dayjs(new Date(selectV - state.offsetDays).getTime())
            );
        } else {
            return false;
        }
    }
};
const {
    roleTree,
    fieldNames,
    searchForm,
    dataSource,
    fieldList,
    addRuleVisible,
    rulesVisible,
    title,
    rulesForm,
    weekListLoca,
    activeKey,
    rulesRef,
    personnelvisible,
    checkedList,
    // formRef,
    everyDay,
    holidayDataSource,
    holidayVisible,
    holidayForm,
    title1,
    times,
    seeDataSource,
    seeHolidayDataSource,
    fieldList1,
    disabled,
    isInOut,
    weekForm,
    abnormalVisible,
    abnormalDataSource,
    treeTitle,
} = toRefs(state);
const columns = [
    {
        title: "序号",
        width: 80,
        customRender: ({ text, record, index }) => `${index + 1}`, // 显示每一行的序号
    },
    {
        title: "规则名称",
        dataIndex: "ruleName",
        key: "id",
        width: 120,
        ellipsis: true,
        showSorterTooltip: true,
    },
    {
        title: "设备类型",
        dataIndex: "deviceType",
        key: "deviceType",
        width: 120,
    },
    {
        title: "场地",
        dataIndex: "siteName",
        key: "id",
        width: 120,
    },
    {
        title: "人员数量",
        dataIndex: "peopleNumber",
        key: "id",
        width: 120,
    },
    {
        title: "通行方向",
        dataIndex: "inOut",
        key: "inOut",
        width: 120,
    },
    {
        title: "有效期",
        dataIndex: "time",
        key: "id",
        width: 260,
    },
    {
        title: "状态",
        dataIndex: "isEnable",
        key: "id",
        width: 120,
    },
    {
        title: "操作",
        dataIndex: "operation",
        key: "id",
        width: 280,
        fixed: "right",
    },
];
const holidayColumns = [
    {
        title: "节假日",
        dataIndex: "name",
        key: "id",
        width: 100,
    },
    {
        title: "时间",
        dataIndex: "time",
        key: "id",
        width: 200,
    },
    {
        title: "操作",
        dataIndex: "operation",
        width: 50,
    },
];
const seeColumns = [
    {
        title: "",
        dataIndex: "time",
        key: "id",
    },
    {
        title: "周一",
        dataIndex: "date1",
        key: "id",
    },
    {
        title: "周二",
        dataIndex: "date2",
        key: "id",
    },
    {
        title: "周三",
        dataIndex: "date3",
        key: "id",
    },
    {
        title: "周四",
        dataIndex: "date4",
        key: "id",
    },
    {
        title: "周五",
        dataIndex: "date5",
        key: "id",
    },
    {
        title: "周六",
        dataIndex: "date6",
        key: "id",
    },
    {
        title: "周日",
        dataIndex: "date7",
        key: "id",
    },
];
const seeHolidayColumns = [
    {
        title: "节假日",
        dataIndex: "holiday",
        key: "id",
    },
    {
        title: "时间",
        dataIndex: "time",
        key: "id",
    },
];
const abnormalColumns = [
    {
        title: "规则重复人员",
        dataIndex: "userName",
        key: "id",
        align: "center",
    },
];

const pwdCheck = (rule, value, callback) => {
    if (!value) {
        return Promise.reject("");
    }
    return Promise.resolve();
};
const hometownFieldNames = reactive({
    label: "name",
    value: "id",
    children: "children",
});
// 当第一次进入并且路由上面没有参数 默认选择展开行的第一项 拿到id 和type
const getTreeTypeIder = (item) => {
    state.treeId = item.id;
    state.treeTitle = item.name;
};
const selectSemester = (v, n) => {
    state.treeId = v;
    state.treeTitle = n.name;
    state.searchForm.name = "";
    state.searchForm.fieid = "";
    state.searchForm.deviceType = "";
    getPageRuleInfo();
};
const query = () => {
    state.pagination.pageNo = 1;
    getPageRuleInfo();
};
const reset = () => {
    state.searchForm.fieid = "";
    state.searchForm.name = "";
    state.searchForm.deviceType = "";
    getPageRuleInfo();
};
const changeStatus = (v) => {
    const obj = {
        ruleId: v.ruleId,
        ruleType: v.rulesType,
        isEnable: v.isStatus ? 1 : 2,
    };
    updateRuleIsEnable(obj);
};
// const addRuleFrame = () => {
//     state.addRuleVisible = true
// }
const addGeneralRules = () => {
    state.noTime = false;
    state.everyDay = true;
    state.rulesVisible = true;
    state.addRuleVisible = false;
    state.title = "添加常规规则";
    state.rulesType = 1;
    rulesRefs.value?.resetFields();
    // closeVisible();
    let time = ["00:00:00", "23:59:59"];
    state.weekForm = {
        weekList: [
            {
                weekName: "周一",
                week: 1,
                isAllow: "1",
                name: "1",
                timelist: [{ time }],
            },
            {
                weekName: "周二",
                week: 2,
                isAllow: "1",
                name: "2",
                timelist: [{ time }],
            },
            {
                weekName: "周三",
                week: 3,
                isAllow: "1",
                name: "3",
                timelist: [{ time }],
            },
            {
                weekName: "周四",
                week: 4,
                isAllow: "1",
                name: "4",
                timelist: [{ time }],
            },
            {
                weekName: "周五",
                week: 5,
                isAllow: "1",
                name: "5",
                timelist: [{ time }],
            },
            {
                weekName: "周六",
                week: 6,
                isAllow: "1",
                name: "6",
                timelist: [{ time }],
            },
            {
                weekName: "周日",
                week: 7,
                isAllow: "1",
                name: "7",
                timelist: [{ time }],
            },
        ],
    };
    state.checkedList = [];
    state.rulesForm.time = "";
    state.rulesForm.id = "";
};
const cancel = () => {
    state.rulesVisible = false;
    rulesRefs.value?.resetFields();
};
const closeVisible = () => {
    rulesRefs.value?.resetFields();
};
const confirm = () => {
    const list = state.weekForm.weekList;
    if (state.title !== "添加节假日规则" && state.title !== "修改节假日规则") {
        state.noTime = !list[state.activeKey].timelist.every((v) => !!v.time);
    }
    if (!state.noTime) {
        state.weekForm.weekList.forEach((item) => {
            item.timelist = JSON.parse(
                JSON.stringify(
                    state.weekForm.weekList[state.activeKey].timelist
                )
            );
            item.isAllow = JSON.parse(
                JSON.stringify(state.weekForm.weekList[state.activeKey].isAllow)
            );
        });
    }
    rulesRefs.value.validateFields().then(() => {
        if (state.rulesForm.id) {
            if (state.noTime) return;
            Modal.confirm({
                title: "您确定要修改该条规则吗？",
                icon: createVNode(ExclamationCircleFilled),
                content: "修改后，旧的规则将失效，闸机将匹配新的人员和规则。",
                okText: "确认",
                cancelText: "取消",
                onCancel() {
                    message.info("已取消！");
                },
                onOk() {
                    updatepassRuleInfo();
                },
            });
        } else {
            CreatePasssceneRule();
        }
    });
};
// 选择场地
const selectField = (value, node, extra) => {
    state.rulesForm.siteId = value;
};
const handleOk = (data) => {
    const personnelName = [];
    if (!data.length) return;
    data.map((v) => {
        personnelName.push(v.name);
    });
    state.rulesForm.personnelName = personnelName.join("、");
    state.rulesForm.studentList = data
        .filter((item) => {
            // _source1为教师 2为学生  peopleType2为教师 1为学生
            return item._source === 2 || item.peopleType === "1";
        })
        .map((item) => {
            return {
                peopleId: item.id,
            };
        });
    state.rulesForm.teacherList = data
        .filter((item) => {
            // _source1为教师 2为学生  peopleType2为教师 1为学生
            return item._source === 1 || item.peopleType === "2";
        })
        .map((item) => {
            return { userId: item.userId, peopleId: item.id };
        });
};
const removeUser = (item, i) => {
    item.timelist.splice(i, 1);
};
const addUser = (item) => {
    item.timelist.push({ time: "" });
};
const changeIsPassage = (item) => {
    if (item.isAllow === "0") {
        item.timelist = [{ time: "" }];
    } else {
        item.timelist = [{ time: ["00:00:00", "23:59:59"] }];
    }
};
const addHoliday = () => {
    state.holidayVisible = true;
    state.title1 = "添加节假日";
};
const handleOkHoliday = () => {
    if (state.seeType == 1 || state.seeType == 2) {
        state.holidayVisible = false;
        return;
    }
    holidayRef.value.validateFields().then(() => {
        const obj = {
            name: state.holidayForm.name,
            startTime: state.holidayForm.date[0],
            endTime: state.holidayForm.date[1],
        };
        state.holidayDataSource.push(obj);
        state.holidayVisible = false;
        state.holidayForm = {
            name: "",
            date: "",
        };
    });
};
const deleteholiday = (data) => {
    Modal.confirm({
        title: "删除提示",
        icon: createVNode(ExclamationCircleFilled),
        content: "是否删除该条节假日?",
        okText: "确认",
        cancelText: "取消",
        onCancel() {
            message.info("已取消！");
        },
        onOk() {
            state.holidayDataSource = state.holidayDataSource.filter((item) => {
                return item !== data;
            });
            message.info("删除成功！");
        },
    });
};
const addrule = () => {
    state.addRuleVisible = true;
    SiteList();
};
const edit = (v) => {
    state.noTime = false;
    state.ruleId = v.ruleId;
    const obj = {
        id: v.ruleId,
        ruleType: v.rulesType,
    };
    if (v.rulesType == 1) {
        state.title = "修改常规规则";
        state.editType = 1;
    } else if (v.rulesType == 2) {
        state.title = "修改节假日规则";
        state.editType = 2;
    } else {
        state.title = "修改临时规则";
        state.editType = 3;
    }
    state.rulesVisible = true;
    PassRuleDetailById(obj);
};
const remove = (v) => {
    const obj = {
        id: v.ruleId,
        ruleType: v.rulesType,
    };
    Modal.confirm({
        title: "删除提示",
        icon: createVNode(ExclamationCircleFilled),
        content: "是否删除该条规则？",
        okText: "确认",
        cancelText: "取消",
        onCancel() {
            message.info("已取消！");
        },
        onOk() {
            deletePassRule(obj);
        },
    });
};
const see = (v) => {
    state.holidayVisible = true;
    state.title1 = v.ruleName;
    state.seeType = v.rulesType;
    state.times = v.startTime + "/" + v.endTime;
    const obj = {
        id: v.ruleId,
        ruleType: v.rulesType,
    };
    PassRuleDetailById(obj);
};
const changeEveryDay = (checkedValue) => {
    if (checkedValue.target.checked === true) {
        Modal.confirm({
            title: "应用到每天",
            icon: createVNode(ExclamationCircleFilled),
            content: "确定要将当前通行规则应用到每天吗？",
            okText: "确认",
            cancelText: "取消",
            onCancel() {
                message.info("已取消！");
                state.everyDay = false;
            },
            onOk() {
                state.everyDay = true;
                state.weekForm.weekList.forEach((item) => {
                    item.timelist = JSON.parse(
                        JSON.stringify(
                            state.weekForm.weekList[state.activeKey].timelist
                        )
                    );
                    item.isAllow = JSON.parse(
                        JSON.stringify(
                            state.weekForm.weekList[state.activeKey].isAllow
                        )
                    );
                });
            },
        });
    } else if (checkedValue.target.checked === false) {
        Modal.confirm({
            title: "应用到每天",
            icon: createVNode(ExclamationCircleFilled),
            content: "确定要取消应用到每天吗？",
            okText: "确认",
            cancelText: "取消",
            onCancel() {
                message.info("已取消！");
                state.everyDay = true;
            },
            onOk() {
                state.everyDay = false;
            },
        });
    }
};
const changetime = (v, index, cindex) => {
    if (!v) {
        nextTick(() => {
            state.weekForm.weekList[index].timelist[cindex].time = "";
        });
    }
    nextTick(() => {
        formRef.value.clearValidate();
    });
    if (cindex > 0) {
        const yearmonthday = "2021-12-02";
        const end =
            yearmonthday +
            " " +
            state.weekForm.weekList[index].timelist[cindex - 1].time[1];
        const start =
            yearmonthday +
            " " +
            state.weekForm.weekList[index].timelist[cindex].time[0];
        if (new Date(start).getTime() < new Date(end).getTime()) {
            message.error("当前通行时间小于上一时间");
            nextTick(() => {
                state.weekForm.weekList[index].timelist[cindex].time = "";
            });
        }
    }
};
// 分页
const handerSelectedRowKeys = (res) => {
    const { current, pageSize } = res;
    state.pagination.pageNo = current;
    state.pagination.pageSize = pageSize;
    getPageRuleInfo();
};
// 侧边栏接口
// const getListPassSite = () => {
// listPassSite().then((res) => {
//     const { data } = res
//     state.roleTree = data
// })
// }
// 选择通行设备
const deviceTypeChange = () => {
    state.rulesForm.siteId = null;
    SiteList();
};

// 禁用选中父节点
const disabledP = (tree) => {
    return tree.map((item) => {
        if (item.children) {
            item.disabled = true;
            disabledP(item.children);
        }
        return item;
    });
};
// 场地接口
const SiteList = (type?: number) => {
    // type 1为人脸机的场地  3为考勤机的场地 2为全部场地/班牌
    getSiteList({ type: 8 }).then(({ data }) => {
        state.fieldList1 = data;
        const datas = JSON.parse(JSON.stringify(data));
        state.fieldList = disabledP(datas);
    });
};

// 列表
const getPageRuleInfo = () => {
    const obj = {
        pageNo: state.pagination.pageNo,
        pageSize: state.pagination.pageSize,
        userName: state.searchForm.name,
        siteId: state.searchForm.fieid && state.searchForm.fieid[1],
        passId: state.treeId || route.query.id || route.query.deptId,
        deviceType: state.searchForm.deviceType,
        // passId: deptId
    };
    pageRuleInfo(obj).then((res) => {
        const { data } = res;
        state.pagination.total = data.total;
        data.list.forEach((v) => {
            v.isStatus = v.isEnable === 1;
            // v.isEnable == 1 ? (v.isEnable = true) : (v.isEnable = false)
        });
        state.dataSource = data.list;
    });
};
// 添加普通规则接口
const CreatePasssceneRule = () => {
    const obj = {
        type: state.rulesType,
        passsceneId: state.treeId || route.query.id,
        ...state.rulesForm,
        ruleHolTimeList: state.holidayDataSource,
        isDay: state.everyDay,
    };
    delete obj.time;
    if (state.rulesType == 2) {
        delete obj.ruleTimeList;
        obj.ruleHolTimeList = state.holidayDataSource.map((item) => {
            return {
                holName: item.name,
                holStartTime: item.startTime,
                holEndTime: item.endTime,
            };
        });
    } else {
        if (state.rulesForm.time && state.rulesForm.time.length > 0) {
            obj.startTime = state.rulesForm.time[0];
            obj.endTime = state.rulesForm.time[1];
        } else if (!state.rulesForm.time) {
            obj.isForever = 1;
        }
        obj.ruleTimeList = state.weekForm.weekList.map((item) => {
            return {
                allowType: item.isAllow,
                week: item.week || item.id,
                weekTimes: item.timelist.map((childrenItem) => {
                    return {
                        weekStartTime:
                            childrenItem.time && childrenItem.time[0]
                                ? childrenItem.time[0]
                                : "",
                        weekEndTime:
                            childrenItem.time && childrenItem.time[1]
                                ? childrenItem.time[1]
                                : "",
                    };
                }),
            };
        });
        delete obj.ruleHolTimeList;
    }
    state.loading = true;
    createPasssceneRule(obj)
        .then((res) => {
            message.success(res.message);
            getPageRuleInfo();
            state.rulesVisible = false;
            rulesRefs.value?.resetFields();
        })
        .catch((err) => {
            if (err.data.code === 1002013009) {
                state.abnormalVisible = true;
                state.abnormalDataSource = err.data.data;
            }
        })
        .finally(() => (state.loading = false));
};
// 获取法定节假日
const holidayInfo = () => {
    getHolidayInfo().then((res) => {
        const { data } = res;
        state.holidayDataSource = data;
    });
};
const getSeeDataSource = (v) => {
    let date1 = [];
    let date2 = [];
    let date3 = [];
    let date4 = [];
    let date5 = [];
    let date6 = [];
    let date7 = [];
    const list = [];
    v.weeksTimeRes.forEach((item) => {
        if (item.week == 1) {
            date1 = item.weeksReqList.map((val) => {
                if (item.passageType === 2) {
                    return "不允许通行";
                } else if (item.passageType === 1) {
                    return "全天通行";
                } else if (!val.startTime && !val.endTime) {
                    return "";
                } else {
                    return val.startTime + "-" + val.endTime;
                }
            });
        }
        if (item.week == 2) {
            date2 = item.weeksReqList.map((val) => {
                if (item.passageType === 2) {
                    return "不允许通行";
                } else if (item.passageType === 1) {
                    return "全天通行";
                } else if (!val.startTime && !val.endTime) {
                    return "";
                } else {
                    return val.startTime + "-" + val.endTime;
                }
            });
        }
        if (item.week == 3) {
            date3 = item.weeksReqList.map((val) => {
                if (item.passageType === 2) {
                    return "不允许通行";
                } else if (item.passageType === 1) {
                    return "全天通行";
                } else if (!val.startTime && !val.endTime) {
                    return "";
                } else {
                    return val.startTime + "-" + val.endTime;
                }
            });
        }
        if (item.week == 4) {
            date4 = item.weeksReqList.map((val) => {
                if (item.passageType === 2) {
                    return "不允许通行";
                } else if (item.passageType === 1) {
                    return "全天通行";
                } else if (!val.startTime && !val.endTime) {
                    return "";
                } else {
                    return val.startTime + "-" + val.endTime;
                }
            });
        }
        if (item.week == 5) {
            date5 = item.weeksReqList.map((val) => {
                if (item.passageType === 2) {
                    return "不允许通行";
                } else if (item.passageType === 1) {
                    return "全天通行";
                } else if (!val.startTime && !val.endTime) {
                    return "";
                } else {
                    return val.startTime + "-" + val.endTime;
                }
            });
        }
        if (item.week == 6) {
            date6 = item.weeksReqList.map((val) => {
                if (item.passageType === 2) {
                    return "不允许通行";
                } else if (item.passageType === 1) {
                    return "全天通行";
                } else if (!val.startTime && !val.endTime) {
                    return "";
                } else {
                    return val.startTime + "-" + val.endTime;
                }
            });
        }
        if (item.week == 7) {
            date7 = item.weeksReqList.map((val) => {
                if (item.passageType === 2) {
                    return "不允许通行";
                } else if (item.passageType === 1) {
                    return "全天通行";
                } else if (!val.startTime && !val.endTime) {
                    return "";
                } else {
                    return val.startTime + "-" + val.endTime;
                }
            });
        }
    });
    for (let i = 0; i < 6; i++) {
        const obj = {
            time: "通行时间",
            date1: date1[i] || "-",
            date2: date2[i] || "-",
            date3: date3[i] || "-",
            date4: date4[i] || "-",
            date5: date5[i] || "-",
            date6: date6[i] || "-",
            date7: date7[i] || "-",
        };
        let flag = false;
        // 循环对象判断
        for (const k in obj) {
            // 如果对象中只要有一个属性不为空 就push到数组中 反之就不push到数组中
            if (obj[k] && obj[k] !== "通行时间" && obj[k] !== "-") {
                flag = true;
            }
        }
        if (flag) {
            list.push(obj);
        }
    }
    return list;
};
// 获取规则详情
const PassRuleDetailById = (obj) => {
    // const { ruleType } = obj
    getPassRuleDetailById(obj).then((res) => {
        const { data } = res;
        if (obj.ruleType === "1") {
            state.rulesForm.id = data.ordinaryForm.id;
            state.rulesForm.ruleName = data.ordinaryForm.ruleName;
            state.rulesForm.siteId = data.ordinaryForm.siteId;
            state.rulesForm.direction = data.ordinaryForm.inOut;
            state.rulesForm.deviceType = data.ordinaryForm.deviceType;
            state.rulesForm.time = [
                data.ordinaryForm.startTime,
                data.ordinaryForm.endTime,
            ];

            state.everyDay = data.ordinaryForm.isDay;
            const personnelName = [];
            data.ordinaryForm.peopleVoForms.map((v) => {
                personnelName.push(v.name);
            });
            state.rulesForm.personnelName = personnelName.join("、");
            state.rulesForm.studentList = data.ordinaryForm.peopleVoForms
                .filter((i) => {
                    return !i.userId;
                })
                .map((i) => {
                    return {
                        peopleId: i.id,
                    };
                });
            state.rulesForm.teacherList = data.ordinaryForm.peopleVoForms
                .filter((i) => {
                    return i.userId;
                })
                .map((i) => {
                    return { userId: i.userId, peopleId: i.id };
                });

            const list = data.ordinaryForm.peopleVoForms.map((v) => {
                return {
                    ...v,
                    id: v.id,
                };
            });
            state.checkedList = list;

            data.ordinaryForm.weeksTimeRes.forEach((item, index) => {
                state.weekForm.weekList[index].isAllow = item.passageType;
                state.weekForm.weekList[index].timelist = item.weeksReqList.map(
                    (v) => {
                        if (v.startTime) {
                            return { time: [v.startTime, v.endTime] };
                        } else {
                            return { time: "" };
                        }
                    }
                );
            });

            state.weekListLoca = state.weekForm.weekList;
            state.seeDataSource = getSeeDataSource(data.ordinaryForm);
            SiteList();
        }
        if (obj.ruleType === "2") {
            state.rulesForm.id = data.holidayForm.id;
            state.rulesForm.ruleName = data.holidayForm.ruleName;
            state.rulesForm.siteId = data.holidayForm.siteId;
            state.rulesForm.deviceType = data.holidayForm.deviceType;
            state.rulesForm.direction = data.holidayForm.inOut;
            state.rulesForm.time = [
                data.holidayForm.startTime,
                data.holidayForm.endTime,
            ];
            state.holidayDataSource = data.holidayForm.holidayReqList.map(
                (v) => {
                    return {
                        name: v.holidayName,
                        startTime: v.holidayStartTime,
                        endTime: v.holidayEndTime,
                    };
                }
            );
            state.seeHolidayDataSource = data.holidayForm.holidayReqList.map(
                (v) => {
                    return {
                        holiday: v.holidayName,
                        time: v.holidayStartTime + "/" + v.holidayEndTime,
                    };
                }
            );
            state.isInOut =
                data.holidayForm.inOut === "1" ? "允许出入" : "不允许出入";

            const personnelName = [];
            data.holidayForm.peopleVoForms.map((v) => {
                personnelName.push(v.name);
            });
            state.rulesForm.personnelName = personnelName.join("、");
            state.rulesForm.studentList = data.holidayForm.peopleVoForms
                .filter((i) => {
                    return !i.userId;
                })
                .map((i) => {
                    return {
                        peopleId: i.id,
                    };
                });
            state.rulesForm.teacherList = data.holidayForm.peopleVoForms
                .filter((i) => {
                    return i.userId;
                })
                .map((i) => {
                    return { userId: i.userId, peopleId: i.id };
                });

            const list = data.holidayForm.peopleVoForms.map((v) => {
                return {
                    ...v,
                    id: v.id,
                };
            });
            state.checkedList = list;
            SiteList();
        }
        if (obj.ruleType === "3") {
            state.rulesForm.id = data.temporaryForm.id;
            state.rulesForm.deviceType = data.temporaryForm.deviceType;
            state.rulesForm.ruleName = data.temporaryForm.ruleName;
            state.rulesForm.siteId = data.temporaryForm.siteId;
            state.rulesForm.direction = data.temporaryForm.inOut;
            state.rulesForm.time = [
                data.temporaryForm.startTime,
                data.temporaryForm.endTime,
            ];

            state.everyDay = data.temporaryForm.isDay;
            const personnelName = [];
            data.temporaryForm.peopleVoForms.map((v) => {
                personnelName.push(v.name);
            });
            state.rulesForm.personnelName = personnelName.join("、");
            state.rulesForm.studentList = data.temporaryForm.peopleVoForms
                .filter((i) => {
                    return !i.userId;
                })
                .map((i) => {
                    return {
                        peopleId: i.id,
                    };
                });
            state.rulesForm.teacherList = data.temporaryForm.peopleVoForms
                .filter((i) => {
                    return i.userId;
                })
                .map((i) => {
                    return { userId: i.userId, peopleId: i.id };
                });

            const list = data.temporaryForm.peopleVoForms.map((v) => {
                return {
                    ...v,
                    id: v.id,
                };
            });
            state.checkedList = list;
            data.temporaryForm.weeksTimeRes.forEach((item, index) => {
                state.weekForm.weekList[index].isAllow = item.passageType;
                state.weekForm.weekList[index].timelist = item.weeksReqList.map(
                    (v) => {
                        if (v.startTime) {
                            return { time: [v.startTime, v.endTime] };
                        } else {
                            return { time: "" };
                        }
                    }
                );
            });
            state.seeDataSource = getSeeDataSource(data.temporaryForm);
            SiteList();
        }
    });
};
// 更新规则
const updatepassRuleInfo = () => {
    const obj = {
        type: state.editType,
        ruleId: state.ruleId,
        passsceneId: state.treeId || route.query.id,
        ...state.rulesForm,
        ruleHolTimeList: state.holidayDataSource,
        isDay: state.everyDay,
    };
    delete obj.time;
    if (state.editType == 2) {
        delete obj.ruleTimeList;
        obj.ruleHolTimeList = state.holidayDataSource.map((item) => {
            return {
                holName: item.name,
                holStartTime: item.startTime,
                holEndTime: item.endTime,
            };
        });
    } else {
        if (state.rulesForm.time && state.rulesForm.time.length > 0) {
            obj.startTime = state.rulesForm.time[0];
            obj.endTime = state.rulesForm.time[1];
        } else if (!state.rulesForm.time) {
            obj.isForever = 1;
        }

        obj.ruleTimeList = state.weekForm.weekList.map((item) => {
            return {
                allowType: item.isAllow,
                week: item.week || item.id,
                weekTimes: item.timelist.map((childrenItem) => {
                    return {
                        weekStartTime:
                            childrenItem.time && childrenItem.time[0]
                                ? childrenItem.time[0]
                                : "",
                        weekEndTime:
                            childrenItem.time && childrenItem.time[1]
                                ? childrenItem.time[1]
                                : "",
                    };
                }),
            };
        });
        delete obj.ruleHolTimeList;
    }
    state.loading = true;
    updatePassRuleInfo(obj)
        .then((res) => {
            message.success(res.message);
            getPageRuleInfo();
            state.rulesVisible = false;
        })
        .finally(() => (state.loading = false));
};

// 删除规则
const deletePassRule = (obj) => {
    deletePassRuleInfo(obj).then((res) => {
        getPageRuleInfo();
    });
};
// 启用禁用规则
const updateRuleIsEnable = (obj) => {
    updateRuleIsEnableInfo(obj).then((res) => {
        message.success(res.message);
        getPageRuleInfo();
    });
};
// watch(() => deviceType, (val) => {
//     state.everyDay = val === '8'
// }, {
//     immediate: true
// })
onMounted(() => {
    // getListPassSite()
    getPageRuleInfo();
    holidayInfo();
    SiteList(2);
});
</script>

<style lang="less" scoped>
:deep(.ant-picker-range) {
    input {
        text-align: center;
    }

    input::-ms-input-placeholder {
        text-align: center;
    }

    input::-webkit-input-placeholder {
        text-align: center;
    }
}

.trafficRuleDiv {
    .right {
        width: 100%;
        overflow: hidden;
        padding: 16px;

        .tableBox {
            padding: 0 !important;
        }

        .searchHead {
            display: flex;
            justify-content: space-between;
        }
    }
}

.text1 {
    font-size: 18px;
    font-weight: 500;
    margin: 20px 0 20px 0;
    margin-left: 14px;
}

.noData_empty {
    width: 180px;
    margin: 150px auto;
}

.threeButton {
    text-align: center;

    .btn {
        width: 200px;
        margin-bottom: 14px;
    }
}

.footer {
    text-align: center;
}

.rulesText {
    color: #909399;
}

.holidayText {
    margin-left: 12px;
    color: rgba(0, 0, 0, 0.45);
}

.atab {
    :deep(.ant-tabs-tab) {
        margin: 0 !important;
        background-color: @primary-color !important;
        color: @body-background;
        border-radius: 0 !important;
    }

    :deep(.ant-tabs-tab-active) {
        background-color: #009166 !important;

        .ant-tabs-tab-btn {
            background-color: #009166 !important;
            color: #fff;
        }
    }
}

.open {
    display: flex;
    height: 100px;

    p {
        width: 60px;
        align-items: center;
    }
}

.stateLi {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.reset-layout-header {
    height: 57px;
    border-bottom: 1px solid @border-color-base;
}

.left-outlined {
    color: var(--primary-color);
    margin-right: 5px;
}

:deep(.ant-tabs-card) {
    border: 1px solid @border-color-base;
    border-radius: 5px;
    margin: 10px 0;
}

:deep(.ant-tabs-content) {
    padding: 0 20px 20px 20px;
}
</style>
<style lang="less">
.trafficRuleDiv {
    .ant-tabs {

        .ant-tabs-nav,
        .ant-tabs-nav-operations {
            display: flex !important;
        }
    }

    .addRuleModal .ant-modal-footer {
        display: none;
    }

    .abnormalModal .ant-modal-footer {
        display: none;
    }

    .ant-drawer-close {
        position: absolute;
        right: 0;
    }

    .atab .ant-tabs-content-holder {
        min-height: 100px;
        padding-left: 16px;
        padding-bottom: 20px;
    }

    .atab {
        min-width: 450px;
        border: 1px solid @border-color-base;
        margin-bottom: 12px;
    }

    #atabDiv {
        border: 1px solid @border-color-base;
        margin-bottom: 10px;

        .ant-tabs-tab {
            margin-left: 1px !important;
        }
    }

    // .addRuleModal {
    #atabDiv-more {
        display: none !important;
    }

    // }
    .addRuleModal #itemTooltip {
        position: absolute;
        top: 19px;
        left: 95px;
    }

    .atab .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: #fff;
        background-color: @primary-color;
    }

    .abnormalModal #itemTooltip {
        position: absolute;
        top: 19px;
        left: 12px;
    }

    .abnormalModal .ant-modal-content .ant-modal-header .ant-modal-title {
        margin-left: 12px !important;
    }

    #atabDiv.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab-active,
    #atabDiv.ant-tabs-top > div > .ant-tabs-nav .ant-tabs-tab-active {
        background-color: @primary-color;
        color: #fff;
    }

    #atabDiv .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: #fff;
    }

    #atabDiv .ant-tabs-content-holder {
        padding: 0 12px 21px;
    }

    .searchHead1 {
        display: flex;
        justify-content: space-between;
        margin-left: 15px;
    }
}
</style>
