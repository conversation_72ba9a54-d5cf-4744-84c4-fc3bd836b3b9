<template>
    <a-modal
        v-model:visible="props.visible"
        class="addRuleModal"
        width="408px"
        footer=""
    >
        <template #title>
            <div>
                添加规则
                <a-tooltip
                    title="临时出入规则的优先级最高，节假日出入规则的
                        优先级高于常规出入规则。"
                >
                    <exclamation-circle-outlined
                        style="font-size: 18px; color: #ff9800"
                    />
                </a-tooltip>
            </div>
        </template>

        <div class="threeButton">
            <a-button
                type="primary"
                class="btn"
                size="large"
                block
                v-for="item in state.threeBtn"
                :key="item.key"
                @click="addRules(item)"
            >
                {{ item.text }}
            </a-button>
        </div>
    </a-modal>
</template>

<script setup>
import { reactive } from 'vue'

const emit = defineEmits(['emitAddRules'])
const state = reactive({
    threeBtn: [
        { text: '添加常规规则', key: 'AddModifyRules', rulesType: 1 },
        { text: '添加节假日规则', key: 'holidays', rulesType: 2 },
        { text: '添加临时规则', key: 'temporary', rulesType: 3 }
    ]
})
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    }
})
// 添加常规规则
const addRules = (item) => {
    emit('emitAddRules', item)
}
// 添加节假日规则
// 添加临时规则
</script>

<style scoped lang="less">
.threeButton {
    margin: 0 50px;
    .btn {
        margin: 10px auto;
    }
}
</style>
