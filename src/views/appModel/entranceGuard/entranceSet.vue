<!-- entranceSet 门禁设置 -->
<template>
    <div class="entrance_set">
        <a-spin :spinning="state.tableSpinning">
            <YTable
                :columns="attendanceColumns"
                :dataSource="state.dataSource"
                style="flex: 1"
                :isScroll="true"
                rowKey="id"
                :totals="state.pagination"
                @onSelectedArry="(val) => (state.selectedrowkeys = val)"
                @onSelectedRowKeys="handerSelectedRowKeys"
            >
                <template #handleItem>
                    <div class="table_info">
                        <div class="table_title">
                            <a-form layout="inline">
                                <a-form-item>
                                    <!-- <a-input
                                        v-model:value.trim="state.searchKey"
                                        allow-clear
                                        placeholder="请输入门禁机序列号/门禁机名称"
                                        style="width: 300px"
                                    /> -->
                                    <a-input-search
                                        allowClear
                                        v-model:value.trim="state.searchKey"
                                        placeholder="请输入"
                                    >
                                        <template #addonBefore>
                                            <a-select
                                                class="input-search"
                                                v-model:value.trim="
                                                    state.searchType
                                                "
                                            >
                                                <a-select-option value="no"
                                                    >序列号</a-select-option
                                                >
                                                <a-select-option value="name"
                                                    >设备名称</a-select-option
                                                >
                                            </a-select>
                                        </template>
                                    </a-input-search>
                                </a-form-item>
                                <a-form-item>
                                    <a-button
                                        type="primary"
                                        style="margin-right: 12px"
                                        @click="queryData"
                                    >
                                        <template #icon>
                                            <SearchOutlined />
                                        </template>
                                        查询
                                    </a-button>
                                    <a-button @click="resetForm">
                                        <template #icon
                                            ><redo-outlined
                                        /></template>
                                        重置
                                    </a-button>
                                </a-form-item>
                            </a-form>
                        </div>
                        <a-space>
                            <a-button
                                type="primary"
                                @click="addEntranceGuard(null)"
                            >
                                <template #icon><plus-outlined /></template>
                                新增门禁机
                            </a-button>
                            <a-button
                                :disabled="!state.selectedrowkeys.length"
                                @click="handlerBatchRestart"
                            >
                                批量重启
                            </a-button>
                            <a-button
                                :disabled="!state.selectedrowkeys.length"
                                @click="handlerBatchUpdate"
                            >
                                批量更新
                            </a-button>
                        </a-space>
                    </div>
                </template>
                <template #bodyCell="{ column, text, record, index }">
                    <templage v-if="column.dataIndex === 'index'">
                        {{ index + 1 }}
                    </templage>
                    <a-badge
                        v-else-if="column.dataIndex === 'machineStatus'"
                        :status="
                            ['default', 'success'][record.machineStatus] || ''
                        "
                        :text="['离线', '在线'][record.machineStatus] || '--'"
                    />
                    <span v-else-if="column.dataIndex === 'no'">
                        {{ record?.no || "--" }}
                    </span>

                    <span v-else-if="column.dataIndex === 'softVersion'">
                        {{ record?.softVersion || "--" }}
                    </span>
                    <div
                        v-else-if="column.dataIndex === 'operation'"
                        style="display: flex"
                    >
                        <a-button
                            type="link"
                            @click="addEntranceGuard(record.id)"
                            style="color: var(--primary-color)"
                            >编辑</a-button
                        >
                        <a-button
                            v-if="record.machineStatus"
                            style="color: var(--primary-color)"
                            type="link"
                            @click="resetPassword(record)"
                            >重置验证密码</a-button
                        >
                        <a-button v-else style="color: #999999" type="link">
                            重置验证密码
                            <a-tooltip placement="top">
                                <template #title>
                                    <span>该设备还未激活</span>
                                </template>
                                <i
                                    class="iconfont icon-outlinebeifen iconClass"
                                ></i>
                            </a-tooltip>
                        </a-button>

                        <a-button
                            type="link"
                            style="color: red"
                            @click="deleteFaceMachine(record)"
                            >删除</a-button
                        >
                    </div>
                    <div v-else>
                        <Tooltip :title="text"></Tooltip>
                    </div>
                </template>
            </YTable>
        </a-spin>

        <!-- 重置密码弹框 -->
        <a-modal
            v-model:visible="state.passwordvisible"
            title="重置密码"
            @ok="handleOk"
            :maskClosable="false"
        >
            <a-form
                :model="state.passwordForm"
                name="passwordForm"
                layout="vertical"
                ref="passwordRef"
            >
                <a-form-item
                    label="请输入新的验证密码"
                    name="newPassword"
                    :rules="[
                        {
                            required: true,
                            message: '请输入新的验证密码',
                        },
                    ]"
                >
                    <a-input
                        v-model:value="state.passwordForm.newPassword"
                        placeholder="请输入"
                        :maxlength="4"
                    />
                    <exclamation-circle-filled />
                    <span class="tips">
                        验证密码用于操作人脸机设置的验证，请记住你的密码</span
                    >
                </a-form-item>
            </a-form>
        </a-modal>
        <!-- 新增/编辑 -->
        <CreadForm
            v-model:formVisible="state.formVisible"
            v-model:detailId="state.detailId"
            @emitPageList="getPageList"
        />
    </div>
</template>

<script setup>
import { reactive, shallowRef, onMounted, h, createVNode } from "vue";
import { ExclamationCircleFilled } from "@ant-design/icons-vue";
import { message, Modal } from "ant-design-vue";
import YTable from "comps/YTable/index.vue";
import CreadForm from "./creadForm.vue";
import { getColumnCheckboxGroupFilterDropdown } from "@/components/FilterDropdown.jsx";
import {
    resetBarrierPassword,
    machineUpdateApp,
    machineRestart,
} from "@/api/trafficRules.js";
import { pageList, deleteMachine } from "@/api/machineApi.js";
const passwordRef = shallowRef();

const state = reactive({
    tableSpinning: false,
    detailId: "",
    searchKey: "",
    dataSource: [],
    formVisible: false,
    deviceType: 8, // 1 闸机 2班牌 3考勤机 4中性版班牌 5一体机
    pagination: {
        total: 0,
        pageNo: 1,
        pageSize: 10,
    },
    fieldList: [], // 场地列表数据
    no: "",
    passwordvisible: false,
    passwordForm: {
        newPassword: "",
    },
    passwordId: "",
    machineStatus: "",
    selectedrowkeys: [],
    searchType: "name",
});

// 考勤表格
const attendanceColumns = [
    {
        title: "序号",
        dataIndex: "index",
        width: 80,
    },
    {
        title: "场地",
        dataIndex: "siteName",
        ellipsis: true,
    },
    {
        title: "门禁机名称",
        dataIndex: "machineName",
        ellipsis: true,
    },
    {
        title: "门禁机序列号",
        dataIndex: "no",
        ellipsis: true,
    },

    {
        title: "状态",
        dataIndex: "machineStatus",
        key: "machineStatus",
        ellipsis: true,
        showSorterTooltip: true,
        filterMultiple: false,
        pathColor: false,
        customFilterDropdown: true,
        customFilterIcon: "caret-right-outlined",
        filters: [
            {
                text: "全部",
                value: "",
            },
            {
                text: "在线",
                value: "1",
            },
            {
                text: "离线",
                value: "0",
            },
        ],
        filterDropdown: (vnode) => {
            if (!vnode.selectedKeys.length) {
                vnode.selectedKeys = "";
            } else {
                vnode.selectedKeys = vnode.selectedKeys[0];
            }
            return getColumnCheckboxGroupFilterDropdown(vnode);
        },
    },
    {
        title: "版本号",
        dataIndex: "softVersion",
        ellipsis: true,
    },
    {
        title: "创建时间",
        dataIndex: "updateTime",
        ellipsis: true,
    },
    {
        title: "操作",
        key: "operation",
        dataIndex: "operation",
        width: 280,
        align: "left",
    },
];
// 列表
const getPageList = () => {
    state.tableSpinning = true;
    const params = {
        ...state.pagination,
        machineStatus: state.machineStatus,
        deviceType: state.deviceType,
        [state.searchType]: state.searchKey,
    };
    pageList(params)
        .then((res) => {
            const { data } = res;
            state.pagination.total = data.total;
            state.dataSource = data.list;
        })
        .finally(() => {
            state.tableSpinning = false;
        });
};

// 分页
const handerSelectedRowKeys = (res) => {
    const { current, pageSize, machineStatus } = res;
    state.machineStatus = machineStatus ? machineStatus[0] : "";
    state.pagination.pageNo = current;
    state.pagination.pageSize = pageSize;
    getPageList();
};

// 查询
const queryData = () => {
    state.pagination.pageNo = 1;
    state.pagination.pageSize = 10;
    getPageList();
};
// 重置
const resetForm = () => {
    state.pagination.pageNo = 1;
    state.pagination.pageSize = 10;
    state.searchKey = "";
    getPageList();
};

// 新增编辑
const addEntranceGuard = (id) => {
    state.formVisible = true;
    state.detailId = id;
};

// 打开重置
const resetPassword = (item) => {
    state.passwordForm.newPassword = "";
    state.passwordvisible = true;
    state.passwordId = item.id;
};
// 重置密码接口
const handleOk = () => {
    passwordRef.value.validateFields().then(() => {
        const params = {
            id: state.passwordId,
            password: state.passwordForm.newPassword,
        };
        resetBarrierPassword(params).then((res) => {
            message.success(res.message);
            state.passwordvisible = false;
            state.passwordForm.newPassword = "";
            getPageList();
        });
    });
};

// 删除接口
const deleteMach = (item) => {
    const params = { id: item.id };
    deleteMachine(params).then((res) => {
        getPageList();
        message.success(res.message);
    });
};
// 删除
const deleteFaceMachine = (item) => {
    Modal.confirm({
        title: "删除提示",
        icon: createVNode(ExclamationCircleFilled),
        content: "删除后不可恢复，确认删除已选设备？",
        okText: "确认",
        cancelText: "取消",
        onCancel() {
            message.info("已取消！");
        },
        onOk() {
            deleteMach(item);
        },
    });
};

// 批量重启
const handlerBatchRestart = () => {
    Modal.confirm({
        title: "提示",
        icon: createVNode(ExclamationCircleFilled),
        content: "确认对勾选设备进行重启？",
        okText: "确认",
        cancelText: "取消",
        onCancel() {
            message.info("已取消！");
        },
        onOk() {
            const params = { machineIds: state.selectedrowkeys };
            machineRestart(params).then((res) => {
                message.success(res.message);
                getPageList();
            });
        },
    });
};
// 批量更新
const handlerBatchUpdate = () => {
    Modal.confirm({
        title: "提示",
        icon: createVNode(ExclamationCircleFilled),
        content: "确认对勾选设备进行更新？",
        okText: "确认",
        cancelText: "取消",
        onCancel() {
            message.info("已取消！");
        },
        onOk() {
            const params = { machineIds: state.selectedrowkeys };
            machineUpdateApp(params).then((res) => {
                message.success(res.message);
                getPageList();
            });
        },
    });
};
onMounted(() => {
    getPageList();
});
</script>

<style scoped lang="less">
.entrance_set {
    padding: 0 20px;

    .iconClass {
        margin-left: 2px;
        color: #efc689;
    }
}
</style>
