<!-- doorOpenAdmin 开门管理 -->
<template>
    <div class="door-open-admin" v-if="!state.isShowPswdAdmin">
        <a-spin :spinning="state.tableSpinning">
            <YTable
                :columns="attendanceColumns"
                :dataSource="state.dataSource"
                rowKey="id"
                style="flex: 1"
                :rowSelection="false"
                :isScroll="true"
                :totals="state.pagination"
                @onSelectedRowKeys="handerSelectedRowKeys"
            >
                <template #handleItem>
                    <div class="table_info">
                        <a-form
                            layout="inline"
                            ref="searchFormRef"
                            :model="state.form"
                        >
                            <a-form-item label="设备名称：" name="deviceName">
                                <a-input
                                    v-model:value="state.form.deviceName"
                                    allow-clear
                                    placeholder="请输入"
                                />
                            </a-form-item>
                            <a-form-item label="所在场地：" name="siteName">
                                <a-input
                                    v-model:value="state.form.siteName"
                                    allow-clear
                                    placeholder="请输入"
                                />
                            </a-form-item>
                            <a-form-item label="开门方式：" name="type">
                                <a-select
                                    style="width: 160px"
                                    v-model:value="state.form.type"
                                    show-search
                                    placeholder="请选择"
                                    :options="doorOpenType"
                                    :fieldNames="{
                                        label: 'name',
                                        value: 'id',
                                    }"
                                    :filter-option="filterOption"
                                ></a-select>
                            </a-form-item>
                            <a-form-item label="开门时间：" name="starEndtTime">
                                <a-range-picker
                                    style="width: 350px"
                                    v-model:value="state.form.starEndtTime"
                                    format="YYYY-MM-DD HH:mm:ss"
                                    valueFormat="YYYY-MM-DD HH:mm:ss"
                                    show-time
                                    :placeholder="['开始时间', '结束时间']"
                                />
                            </a-form-item>

                            <a-form-item>
                                <a-button
                                    type="primary"
                                    style="margin-right: 12px"
                                    @click="queryData"
                                >
                                    <template #icon>
                                        <SearchOutlined />
                                    </template>
                                    查询
                                </a-button>
                                <a-button @click="resetForm">
                                    <template #icon><redo-outlined /></template>
                                    重置
                                </a-button>
                            </a-form-item>
                        </a-form>
                        <a-button type="primary" @click="handlerOpenDoorAdmin">
                            开门密码管理
                        </a-button>
                    </div>
                </template>
                <template #bodyCell="{ column, text, record, index }">
                    <templage v-if="column.dataIndex === 'index'">
                        {{ index + 1 }}
                    </templage>
                    <templage v-else-if="column.dataIndex === 'type'">
                        {{ doorOpenType.find((item) => item.id == text)?.name }}
                    </templage>
                    <div v-else>
                        <Tooltip :title="text"></Tooltip>
                    </div>
                </template>
            </YTable>
        </a-spin>
    </div>
    <DoorOpenPswdAdmin v-else v-model:isShowPswdAdmin="state.isShowPswdAdmin" />
</template>

<script setup>
import { reactive, onMounted } from "vue";
import YTable from "comps/YTable/index.vue";
import DoorOpenPswdAdmin from "./doorOpenPswdAdmin/index.vue";
import { getOpenDoorList } from "@/api/doorOpenAdmin";

const searchFormRef = shallowRef();
const doorOpenType = [
    { name: "全部", id: null },
    { name: "人脸", id: 1 },
    { name: "刷卡", id: 2 },
    { name: "密码", id: 3 },
];

const state = reactive({
    tableSpinning: false,
    isShowPswdAdmin: false,
    dataSource: [
        {
            openDoorTime: "2021-01-01 00:00:00",
        },
    ],
    pagination: {
        total: 0,
        pageNo: 1,
        pageSize: 10,
    },
    form: {
        starEndtTime: [],
        deviceType: 8, //8门禁、9会议
        deviceName: "",
        siteName: "",
        startTime: "",
        endTime: "",
        type: null,
        orderField: "",
        orderSort: "",
    },
});

// 考勤表格
const attendanceColumns = [
    {
        title: "序号",
        dataIndex: "index",
        width: 80,
    },
    {
        title: "设备名称",
        dataIndex: "deviceName",
    },
    {
        title: "所在场地",
        dataIndex: "siteName",
    },
    {
        title: "开门方式",
        dataIndex: "type",
    },
    {
        title: "开门指令",
        dataIndex: "command",
    },
    {
        title: "开门时间",
        dataIndex: "openDoorTime",
        sorter: true,
        width: 180,
    },
];
// 开门管理列表
const getPageList = () => {
    const params = {
        ...state.pagination,
        ...state.form,
    };
    state.tableSpinning = true;
    getOpenDoorList(params)
        .then(({ data }) => {
            const { list, pageNo, pageSize, total } = data;
            state.dataSource = list || [];
            state.pagination.pageNo = pageNo;
            state.pagination.pageSize = pageSize;
            state.pagination.total = total;
        })
        .finally(() => {
            state.tableSpinning = false;
        });
};
const filterOption = (input, option) => {
    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 重置
const resetForm = () => {
    searchFormRef.value.resetFields();
    state.pagination.pageNo = 1;
    state.pagination.pageSize = 10;
    state.form.startTime = "";
    state.form.endTime = "";
    state.form.starEndtTime = []
    state.form.deviceName = "";
    state.form.siteName = "";
    state.form.type = null;
    getPageList();
};

// 查询
const queryData = () => {
    state.form.startTime = "";
    state.form.endTime = "";
    if (state.form.starEndtTime.length) {
        state.form.startTime = state.form.starEndtTime[0];
        state.form.endTime = state.form.starEndtTime[1];
    }
    state.pagination.pageNo = 1;
    state.pagination.pageSize = 10;
    getPageList();
};
// 分页
const handerSelectedRowKeys = ({ current, pageSize }, filter) => {
    state.form.orderField = "";
    state.form.orderSort = "";
    if (filter.order) {
        state.form.orderField = "createTime";
        state.form.orderSort = filter.order == "ascend" ? "asc" : "desc"; // asc | desc
    }
    state.pagination.pageNo = current;
    state.pagination.pageSize = pageSize;
    getPageList();
};

// 开门密码管理
const handlerOpenDoorAdmin = () => {
    state.isShowPswdAdmin = true;
};
onMounted(() => {
    getPageList();
});
</script>

<style scoped lang="less">
.door-open-admin {
    padding: 0 20px;
    :deep(.ant-table-column-has-sorters) {
        .ant-table-column-sorters {
            justify-content: flex-start;
            .ant-table-column-title {
                flex: none;
            }
        }
    }
}
</style>
