<!-- viewDevices 查看设备 -->
<template>
    <div class="view-devices" v-if="!state.visibleIssuanceRecord">
        <div class="view-devices_hander">
            <a-button type="text" class="tabs_back" @click="handerBack">
                <i class="iconfont icon-xingzhuangjiehe19"></i>
            </a-button>
            <span>查看设备</span>
        </div>
        <div class="view-devices_body">
            <a-spin :spinning="state.tableSpinning">
                <YTable
                    :columns="attendanceColumns"
                    :dataSource="state.dataSource"
                    rowKeyId="deviceId"
                    style="flex: 1"
                    :rowSelection="rowSelection"
                    :isScroll="false"
                    :isSrollY="true"
                    :isSrollYH="clientHeight - 310"
                    :totals="state.pagination"
                    @onSelectedArry="(val) => (state.selectedrowkeys = val)"
                    @onSelectedRowKeys="handerSelectedRowKeys"
                >
                    <template #handleItem>
                        <div class="table_info">
                            <div></div>
                            <a-space>
                                <a-button
                                    type="primary"
                                    :disabled="!state.selectedrowkeys.length"
                                    @click="handlerBatchPasswordIssuance"
                                >
                                    批量下发密码
                                </a-button>
                                <a-button
                                    @click="state.visibleIssuanceRecord = true"
                                    >下发记录
                                </a-button>
                            </a-space>
                        </div>
                    </template>
                    <template #bodyCell="{ column, text, record, index }">
                        <templage v-if="column.dataIndex === 'index'">
                            {{ index + 1 }}
                        </templage>

                        <div v-else>
                            <Tooltip :title="text"></Tooltip>
                        </div>
                    </template>
                </YTable>
            </a-spin>
        </div>
    </div>
    <IssuanceRecord
        v-else
        v-model:visibleIssuanceRecord="state.visibleIssuanceRecord"
        :viewDevicesId="props.viewDevicesId"
    />
</template>

<script setup>
import { reactive, onMounted } from "vue";
import { message, Modal } from "ant-design-vue";
import YTable from "comps/YTable/index.vue";
import IssuanceRecord from "./issuanceRecord.vue";
import {
    postPasswordDeviceList,
    postBatchPasswordIssuance,
} from "@/api/doorOpenAdmin.js";

const props = defineProps({
    visibleViewDevices: {
        type: Boolean,
        default: false,
    },
    viewDevicesId: {
        type: String,
        default: "",
    },
    rowSelection: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(["update:visibleViewDevices"]);
const state = reactive({
    selectedrowkeys: [],
    visibleIssuanceRecord: false,
    tableSpinning: false,
    dataSource: [],
    pagination: {
        total: 0,
        pageNo: 1,
        pageSize: 10,
    },
});

// 考勤表格
const attendanceColumns = [
    {
        title: "序号",
        dataIndex: "index",
    },
    {
        title: "设备名称",
        dataIndex: "deviceName",
    },
    {
        title: "设备状态",
        dataIndex: "deviceStatus",
    },
    {
        title: "密码下发状态",
        dataIndex: "deviceStatusName",
    },
    {
        title: "最近下发时间",
        dataIndex: "lastIssuanceTime",
        width: 200,
    },
];
// 列表
const getPageList = () => {
    const params = {
        ...state.pagination,
        desc: true,
        deviceType: 8, // 8门禁、9会议
        pwdId: props.viewDevicesId,
    };
    postPasswordDeviceList(params)
        .then(({ data }) => {
            const { list, pageNo, pageSize, total } = data;
            state.dataSource = list || [];
            state.pagination.pageNo = pageNo;
            state.pagination.pageSize = pageSize;
            state.pagination.total = total;
        })
        .finally(() => {
            state.tableSpinning = false;
        });
};
// 批量下发密码
const handlerBatchPasswordIssuance = (id) => {
    Modal.confirm({
        title: "提示",
        content: "确认是否下发密码至勾选设备",
        okText: "确认",
        cancelText: "取消",
        onOk() {
            const params = {
                deviceIds: state.selectedrowkeys,
                deviceType: 8, // 8门禁、9会议
                pwdId: props.viewDevicesId,
            };
            postBatchPasswordIssuance(params).then((res) => {
                message.success(res.message);
                state.selectedrowkeys.length = 0;
                getPageList();
            });
        },
    });
};
// 分页
const handerSelectedRowKeys = ({ current, pageSize }) => {
    state.pagination.pageNo = current;
    state.pagination.pageSize = pageSize;
    getPageList();
};

// 返回
const handerBack = () => {
    emit("update:visibleViewDevices", false);
};

// 获取窗口高度
const clientHeight = window.document.body.clientHeight;
onMounted(() => {
    getPageList();
});
</script>

<style scoped lang="less">
.view-devices {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;

    .view-devices_hander {
        height: 46px;
        line-height: 46px;
        background: @body-background;
        border-radius: 12px 12px 0 0;
        border-bottom: 1px solid @border-color-base;
    }

    .view-devices_body {
        padding: 20px;
        .table_info {
            padding-top: 0;
        }
    }
}
</style>
