<!-- doorOpenPswdAdmin 开门密码管理 -->
<template>
    <div class="door-open-pswd-admin" v-if="!state.visibleViewDevices">
        <div class="door-open-pswd-admin_hander">
            <a-button type="text" class="tabs_back" @click="handerBack">
                <i class="iconfont icon-xingzhuangjiehe19"></i>
            </a-button>
            <span>开门密码管理</span>
        </div>
        <div class="door-open-pswd-admin_body">
            <a-spin :spinning="state.tableSpinning">
                <YTable :columns="attendanceColumns" :dataSource="state.dataSource" rowKey="id" style="flex: 1"
                    :rowSelection="false" :isScroll="false" :isSrollY="true" :isSrollYH="clientHeight - 310"
                    :totals="state.pagination" @onSelectedRowKeys="handerSelectedRowKeys">
                    <template #handleItem>
                        <div class="table_info">
                            <div class="table_title">
                                <a-form layout="inline" ref="searchFormRef" :model="state.form">
                                    <a-form-item label="创建人：" name="createBy">
                                        <a-input v-model:value="state.form.createBy" allow-clear placeholder="请输入" />
                                    </a-form-item>

                                    <a-form-item label="密码状态：" name="pwdStatus">
                                        <a-select style="width: 160px" v-model:value="state.form.pwdStatus" show-search
                                            placeholder="请选择" :options="doorOpenType" :fieldNames="{
                                                label: 'name',
                                                value: 'id',
                                            }" :filter-option="filterOption"></a-select>
                                    </a-form-item>

                                    <a-form-item>
                                        <a-button type="primary" style="margin-right: 12px" @click="queryData">
                                            <template #icon>
                                                <SearchOutlined />
                                            </template>
                                            查询
                                        </a-button>
                                        <a-button @click="resetForm">
                                            <template #icon><redo-outlined /></template>
                                            重置
                                        </a-button>
                                    </a-form-item>
                                </a-form>
                            </div>
                            <a-button type="primary" @click="state.visiblePaswrd = true">
                                新建密码
                            </a-button>
                        </div>
                    </template>
                    <template #bodyCell="{ column, text, record, index }">
                        <templage v-if="column.dataIndex === 'index'">
                            {{ index + 1 }}
                        </templage>
                        <div v-else-if="column.dataIndex === 'password'" style="color: #00b781">
                            {{ text }}
                            <CopyOutlined @click="handlerCopy(text)" />
                        </div>
                        <templage v-else-if="column.dataIndex === 'deviceIssuedNum'">
                            {{ text }} /
                            {{ record.deviceTotal }}
                        </templage>

                        <templage v-else-if="column.dataIndex === 'pswdValidity'">
                            {{ record.startTime }} ~
                            {{ record.endTime }}
                        </templage>
                        <templage v-else-if="column.dataIndex === 'pwdStatus'">
                            {{
                                doorOpenType.find((item) => item.id == text)
                                    ?.name
                            }}
                        </templage>

                        <template v-else-if="column.dataIndex === 'operation'">
                            <a-button type="link" @click="handlerViewDevices(record)">查看设备</a-button>
                            <a-button v-if="!record.isCancel" type="link" style="color: red"
                                @click="passwordRemove(record)">
                                作废
                            </a-button>
                        </template>
                        <div v-else>
                            <Tooltip :title="text"></Tooltip>
                        </div>
                    </template>
                </YTable>
            </a-spin>
        </div>
        <AddPassword v-model:visiblePaswrd="state.visiblePaswrd" @emitPageList="resetForm" />
    </div>
    <ViewDevices v-else v-model:visibleViewDevices="state.visibleViewDevices" :viewDevicesId="state.viewDevicesId"
        :rowSelection="state.rowSelection" @emitPageList="getPageList" />
</template>

<script setup>
import { reactive, onMounted } from "vue";
import { message, Modal } from "ant-design-vue";
import YTable from "comps/YTable/index.vue";
import { copyLink } from "@/utils/util";
import AddPassword from "./addPassword.vue";
import ViewDevices from "./viewDevices.vue";
import {
    getopenDoorAdminList,
    doorPasswordRemove,
} from "@/api/doorOpenAdmin.js";
const searchFormRef = shallowRef();
const doorOpenType = [
    { name: "全部", id: null },
    { name: "未过期", id: 1 },
    { name: "已过期", id: 2 },
    { name: "已作废", id: 3 },
];
const props = defineProps({
    isShowPswdAdmin: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(["update:isShowPswdAdmin"]);
const state = reactive({
    visiblePaswrd: false,
    tableSpinning: false,
    visibleViewDevices: false,
    rowSelection: false,
    viewDevicesId: "",
    dataSource: [],
    pagination: {
        total: 0,
        pageNo: 1,
        pageSize: 10,
    },
    form: {
        deviceType: 8, // 8门禁、9会议
        createBy: "",
        pwdStatus: null,
        orderField: "",
        orderSort: "",
    },
});

// 考勤表格
const attendanceColumns = [
    {
        title: "序号",
        dataIndex: "index",
        width: 80,
    },
    {
        title: "开门密码",
        dataIndex: "password",
    },
    {
        title: "下发设备数",
        dataIndex: "deviceIssuedNum",
    },
    {
        title: "密码有效期",
        dataIndex: "pswdValidity",
        width: 340,
    },
    {
        title: "密码状态",
        dataIndex: "pwdStatus",
    },
    {
        title: "创建人",
        dataIndex: "createBy",
    },
    {
        title: "创建时间",
        dataIndex: "createTime",
        sorter: true,
    },
    {
        title: "操作",
        key: "operation",
        dataIndex: "operation",
        width: 190,
        align: "left",
    },
];
// 开门密码管理列表
const getPageList = () => {
    const params = {
        ...state.pagination,
        ...state.form,
    };
    state.tableSpinning = true;
    getopenDoorAdminList(params)
        .then(({ data }) => {
            const { list, pageNo, pageSize, total } = data;
            state.dataSource = list || [];
            state.pagination.pageNo = pageNo;
            state.pagination.pageSize = pageSize;
            state.pagination.total = total;
        })
        .finally(() => {
            state.tableSpinning = false;
        });
};

const filterOption = (input, option) => {
    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 复制
const handlerCopy = (text) => {
    copyLink(text);
    message.success("复制成功");
};
// 删除
const passwordRemove = (item) => {
    Modal.confirm({
        title: "提示",
        content: "确认作废该密码吗？",
        okText: "确认",
        cancelText: "取消",
        onOk() {
            const { id, isCancel } = item;
            doorPasswordRemove({ id, isCancel: !isCancel }).then((res) => {
                message.success(res.message);
                getPageList();
            });
        },
    });
};

// 查询
const queryData = () => {
    state.pagination.pageNo = 1;
    state.pagination.pageSize = 10;
    getPageList();
};

// 重置
const resetForm = () => {
    state.form.createBy = "";
    state.form.pwdStatus = null;
    queryData();
};

// 查看设备
const handlerViewDevices = (item) => {
    state.visibleViewDevices = true;
    state.viewDevicesId = item.id;
    state.rowSelection = item.pwdStatus == 1;
};
// 分页
const handerSelectedRowKeys = ({ current, pageSize }, filter) => {
    state.form.orderField = "";
    state.form.orderSort = "";
    if (filter.order) {
        state.form.orderField = "createTime";
        state.form.orderSort = filter.order == "ascend" ? "asc" : "desc"; // asc | desc
    }
    state.pagination.pageNo = current;
    state.pagination.pageSize = pageSize;
    getPageList();
};
// 返回
const handerBack = () => {
    emit("update:isShowPswdAdmin", false);
};
// 获取窗口高度
const clientHeight = window.document.body.clientHeight;
onMounted(() => {
    getPageList();
});
</script>

<style scoped lang="less">
.door-open-pswd-admin {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;

    .door-open-pswd-admin_hander {
        height: 46px;
        line-height: 46px;
        background: @body-background;
        border-radius: 12px 12px 0 0;
        border-bottom: 1px solid @border-color-base;
    }

    .door-open-pswd-admin_body {
        padding: 20px;

        .table_info {
            padding-top: 0;

        }
    }

    :deep(.ant-table-column-has-sorters) {
        .ant-table-column-sorters {
            justify-content: flex-start;

            .ant-table-column-title {
                flex: none;
            }
        }
    }
}
</style>
