<!-- entranceGuard 门禁系统 --> 
<template>
  <div class="entrance_guard">
    <a-tabs v-model:activeKey="state.activeKey" v-bind="$attrs">
      <template #leftExtra>
        <slot name="leftExtra">
          <a-button type="text" class="tabs_back" @click="router.push({ name: 'appModelList' })">
            <i class="iconfont icon-xingzhuangjiehe19"></i>
          </a-button>
        </slot>
      </template>
      <a-tab-pane v-for="item in permissionList" :key="item.component" :tab="item.name" />
    </a-tabs>
    <component :is="state.components[state.activeKey]" :passId="state.passId" />
  </div>
</template>

<script setup>
import { provide, computed, reactive, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import deviceManagements from './entranceSet.vue'
import trafficRule from './trafficRule/index.vue'
import trafficFaceLibrary from './trafficFaceLibrary/index.vue'
import doorOpenAdmin from './doorOpenAdmin/index.vue'

const store = useStore()
const route = useRoute()
const router = useRouter()

const state = reactive({
  passId: '',
  activeKey: 'deviceManagements',
  showBack: true,
  tabsList: [],
  components: { deviceManagements, trafficRule, trafficFaceLibrary,doorOpenAdmin },
})
// const authRoutes = computed(() => store.state.base.allJurisdiction)

provide("integrationParams", state)
const permissionList = computed(() => {
  const data = store.state.base.permissionList[0]?.children
  state.activeKey = data[0]?.component
  return data
})

const getTabsListFn = () => {
  state.tabsList = []
  const newData = toRaw(
    authRoutes.value.filter(
      (item) => item.code === "entranceGuardMachine"
    )
  )

  if (newData.length && newData[0].list.length) {
    newData[0].list.forEach((v) => {
      if (v.children.length) {
        const newChidren = JSON.parse(JSON.stringify(v.children))
        state.tabsList = newChidren.map((item, idx) => {
          if (!idx) {
            state.activeKey = item.path
            //   router.push({ query: { ...route.query, deviceType: 8 } })
          }
          return {
            ...item,
            meta: {
              title: item.name
            }
          }
        })
      }
    })
  }
  provide("integrationParams", state)
}

// watch(
//   () => authRoutes.value,
//   (val) => {
//     val.length && getTabsListFn()

//   },
//   {
//     immediate: true
//   }
// )
</script>

<style lang="less" scoped>
:deep(.ant-tabs-nav) {
  margin: 0;
}
</style>