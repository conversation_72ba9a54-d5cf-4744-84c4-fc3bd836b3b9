<template>
    <div class="face_lib">
        <a-layout class="content">
            <a-layout-sider class="sider" width="290">
                <div class="fixed">
                    <div class="top text 4">
                        <div class="left">设备列表</div>
                        <div class="right">共{{ state.all }}台</div>
                    </div>
                    <!-- <a-input-search
                        placeholder="请输入设备名称/序列号"
                        allowClear
                        v-model:value="state.nameDevice"
                        @search="searchDevice"
                    /> -->
                    <a-input-search
                        allowClear
                        v-model:value.trim="state.nameDevice"
                        placeholder="请输入"
                        @search="searchDevice"
                    >
                        <template #addonBefore>
                            <a-select
                                class="input-search"
                                v-model:value.trim="state.searchType"
                            >
                                <a-select-option value="no"
                                    >序列号</a-select-option
                                >
                                <a-select-option value="name"
                                    >设备名称</a-select-option
                                >
                            </a-select>
                        </template>
                    </a-input-search>
                </div>
                <a-space style="margin-top: 10px">
                    <a-button
                        type="primary"
                        :disabled="!state.checkedDeviceList.length"
                        @click="openControlRef"
                    >
                        批量关联人脸库
                    </a-button>
                    <a-button
                        type="primary"
                        :disabled="!state.checkedDeviceList.length"
                        @click="batchSynchronization"
                    >
                        批量同步人脸库
                    </a-button>
                </a-space>
                <a-spin
                    :spinning="state.deviceLoading"
                    tip="数据加载中..."
                    style="height: 500px"
                >
                    <div
                        class="equipment_list"
                        id="equipment_list"
                        @scroll="scroll"
                        v-if="state.list && state.list.length > 0"
                    >
                        <a-checkbox
                            style="margin: 0 0 10px 15px"
                            :disabled="!state.online_list.length"
                            v-model:checked="state.isCheckAll"
                            :indeterminate="state.indeterminate"
                            @change="onCheckAllChange"
                        >
                            全选
                        </a-checkbox>
                        <a-checkbox-group
                            v-model:value="state.checkedDeviceList"
                            style="width: 100%"
                        >
                            <div
                                class="item"
                                v-for="(item, index) in state.list"
                                :key="index"
                                @click="hanbleShe(item, index)"
                                :class="state.active === index ? 'active' : ''"
                            >
                                <div class="left">
                                    <a-checkbox
                                        :value="item"
                                        :disabled="item.machineStatus != 1"
                                    ></a-checkbox>
                                    <div
                                        class="on_line"
                                        :class="
                                            item.machineStatus == 1 ? 'act' : ''
                                        "
                                    ></div>
                                    <a-tooltip
                                        placement="top"
                                        v-if="item.machineName?.length > 10"
                                    >
                                        <template #title>
                                            <span>{{ item.machineName }}</span>
                                        </template>
                                        <div
                                            class="name"
                                            :style="
                                                state.active == index
                                                    ? 'color: #00B781;'
                                                    : ''
                                            "
                                        >
                                            {{ item.machineName }}
                                        </div>
                                    </a-tooltip>
                                    <div
                                        class="name"
                                        v-else
                                        :style="
                                            state.active == index
                                                ? 'color: #00B781;'
                                                : ''
                                        "
                                    >
                                        {{ item.machineName }}
                                    </div>
                                </div>
                                <div
                                    :class="
                                        state.active == index
                                            ? 'right right_class '
                                            : 'right'
                                    "
                                >
                                    <span>
                                        {{ item.synchronousNumber || 0 }}</span
                                    >
                                    <span class="color_black division_line"
                                        >/</span
                                    >
                                    <span class="color_black">
                                        {{ item.faceNumber || 0 }}
                                    </span>
                                </div>
                            </div>
                        </a-checkbox-group>
                    </div>
                    <div v-else class="no_data">
                        <img src="/image/faceEmpty.png" alt="" />
                        <span class="text">暂无设备数据</span>
                    </div>
                </a-spin>
            </a-layout-sider>
            <a-layout-content class="contents">
                <div class="top_name">{{ state.topname }}</div>
                <div class="warp">
                    <div class="left">
                        <span>{{
                            `${
                                state.buildingName
                                    ? state.buildingName + "-"
                                    : ""
                            }${state.siteName}`
                        }}</span>
                    </div>
                    <div class="right">
                        <span class="ti">序列号：</span>
                        <span class="xu_num">{{ state.no }}</span>
                    </div>
                </div>
                <div class="searchHead">
                    <a-form layout="inline">
                        <a-form-item label="">
                            <a-input
                                placeholder="请输入姓名"
                                :maxlength="20"
                                v-model:value="state.name"
                                style="width: 240px; margin: 0px 14px 14px 0px"
                            >
                                <template #suffix>
                                    <search-outlined
                                        @click="hanbleSearch"
                                        style="font-size: 20px; color: #8f8f8f"
                                    />
                                </template>
                            </a-input>
                        </a-form-item>
                        <div>
                            <a-button
                                style="margin-right: 12px"
                                type="primary"
                                @click="hanbleSearch"
                            >
                                <template #icon>
                                    <SearchOutlined
                                        style="margin-right: 12px"
                                    />
                                </template>
                                查询
                            </a-button>
                            <a-button @click="resetting">
                                <template #icon>
                                    <redo-outlined style="margin-right: 12px" />
                                </template>
                                重置
                            </a-button>
                        </div>
                    </a-form>
                    <div>
                        <a-button
                            @click="openControlRef"
                            type="primary"
                            :disabled="!state.curObj.id || !state.id"
                            style="margin: 0px 12px 12px 0px"
                        >
                            关联人脸库
                        </a-button>
                        <a-button
                            @click="Oneclicksynchronization"
                            type="primary"
                            :disabled="
                                state.faceTable && state.faceTable.length == 0
                            "
                            style="margin: 0px 12px 12px 0px"
                        >
                            一键同步
                        </a-button>
                        <a-button
                            @click="clearall"
                            danger
                            :disabled="
                                state.faceTable && state.faceTable.length == 0
                            "
                        >
                            清空
                        </a-button>
                    </div>
                </div>
                <a-spin :spinning="state.tableLoading">
                    <YTable
                        :scroll="{ x: 1200 }"
                        :columns="tableColumns"
                        :dataSource="state.faceTable"
                        :slots="['operation']"
                        :rowSelection="false"
                        :totals="state.pagination"
                        :spinLoad="state.spinLoad"
                        @onSelectedRowKeys="handerSelectedRowKeys"
                    >
                        <template #headerCell="{ column }">
                            <template v-if="column.dataIndex === 'status'">
                                <a-dropdown placement="bottomRight">
                                    <a style="color: #000" @click.prevent>
                                        状态
                                        <caret-down-outlined
                                            style="color: var(--primary-color)"
                                        />
                                    </a>
                                    <template #overlay>
                                        <a-menu>
                                            <a-menu-item
                                                v-for="(
                                                    item, index
                                                ) in statusList"
                                                :key="index"
                                                @click="
                                                    chooseStatus(item, index)
                                                "
                                            >
                                                <span
                                                    :class="
                                                        state.statusIndex ===
                                                        index
                                                            ? 'act'
                                                            : ''
                                                    "
                                                    >{{ item.className }}</span
                                                >
                                            </a-menu-item>
                                        </a-menu>
                                    </template>
                                </a-dropdown>
                            </template>
                            <template v-if="column.dataIndex === 'userType'">
                                <a-dropdown placement="bottomRight">
                                    <a style="color: #000" @click.prevent>
                                        人员类型
                                        <caret-down-outlined
                                            style="color: var(--primary-color)"
                                        />
                                    </a>
                                    <template #overlay>
                                        <a-menu>
                                            <a-menu-item
                                                v-for="(
                                                    item, index
                                                ) in faceUserTypeList"
                                                :key="index"
                                                @click="
                                                    choosePeople(item, index)
                                                "
                                            >
                                                <span
                                                    :class="
                                                        state.peopleIndex ===
                                                        index
                                                            ? 'act_peo'
                                                            : ''
                                                    "
                                                    >{{ item.label }}</span
                                                >
                                            </a-menu-item>
                                        </a-menu>
                                    </template>
                                </a-dropdown>
                            </template>
                        </template>
                        <template #bodyCell="{ column, record, text }">
                            <template v-if="column.dataIndex === 'userType'">
                                <span>{{ userTypeList[record.userType] }}</span>
                            </template>
                            <template v-else-if="column.dataIndex === 'status'">
                                <a-badge
                                    :status="deviceaceStatus[record.status]"
                                    :text="deviceaceStatusText[record.status]"
                                />
                                <a-tooltip placement="top" color="#fff">
                                    <template #title>
                                        <span style="color: #000">
                                            同步异常：{{ record.renson }}</span
                                        >
                                    </template>
                                    <i
                                        v-if="record.status === 1"
                                        style="
                                            color: red;
                                            cursor: pointer;
                                            margin-left: 6px;
                                        "
                                        class="iconfont icon-outlinebeifen"
                                    ></i>
                                </a-tooltip>
                            </template>
                            <template
                                v-else-if="column.dataIndex === 'operation'"
                            >
                                <a
                                    style="color: #00b781; margin-right: 10px"
                                    @click="syncFace(record, true)"
                                    >同步</a
                                >
                                <a
                                    style="color: var(--error-color)"
                                    @click="syncFace(record, false)"
                                    >删除</a
                                >
                            </template>
                            <template v-else>
                                {{ text || "-" }}
                            </template>
                        </template>
                    </YTable>
                </a-spin>
            </a-layout-content>
        </a-layout>
    </div>
    <!-- 选人组件 -->
    <faceSelect
        ref="selectPersonnelRef"
        mode="personnel"
        @emitChangeTabs="emitChangeTabs"
        :treeData="state.treeData"
        :customTree="state.customTree"
        :deviceId="state.id"
        :tabs="[
            { tab: `教职工组`, key: 1, checked: true },
            { tab: `学生组`, key: 2, checked: false },
            {
                tab: `外部人员`,
                key: 3,
                checked: false,
                selectType: 'outsiders',
            },
            { tab: `自定义组`, key: 4, checked: false },
        ]"
        v-model:visible="state.visible"
        v-model:checked="state.checkedList"
        @handleOk="handerCallbackParameter"
    />
    <FaceModel ref="faceRef" @syncOnePerson="syncOnePerson"></FaceModel>

    <!-- 选人组件 -->
    <PersonSelectionControl
        ref="controlRef"
        :type="SELECT_TYPE.ALL"
        :selected="state.checkedList"
        :tabs="tabsSelection"
        @search="searchSelect"
        @toggleLevel="toggleLevel"
        @toggleTabs="toggleTabs"
        @submit="submit"
    />
</template>

<script setup>
import { onMounted, computed, reactive, ref } from "vue";
import YTable from "comps/YTable/index.vue";
import faceSelect from "@/components/YSelect/indexFace.vue";
import {
    tableColumns,
    deviceaceStatusText,
    deviceaceStatus,
    statusList,
} from "./tableColumns.js";
import { ExclamationCircleFilled } from "@ant-design/icons-vue";
import FaceModel from "./FaceModel.vue";
import { useStore } from "@/store/index";
import {
    machineList,
    getRecordPage,
    syncAll,
    clearFace,
    bindFaceInfo,
} from "@/api/classSign";
import { message } from "ant-design-vue";
import { externalGroupNumberList } from "@/api/outsiders.js";
import { useRoute } from "vue-router";

import PersonSelectionControl from "@/components/PersonSelectionControl/index.vue";
import {
    SELECT_TYPE,
    DISPLAY_MODE,
} from "@/components/PersonSelectionControl/constants.js";
import { debounce, modalConfirm } from "@/utils/util";

import {
    getFacePageList,
    getCustomGroupTree,
    getSchoolRollStudentPage,
    getDepartmentPersonnel,
} from "@/api/faceLibrary.js";
const route = useRoute();
const store = useStore();
const faceRef = ref();
const selectPersonnelRef = ref(null);

const state = reactive({
    online_list: [],
    isCheckAll: false,
    indeterminate: false,
    checkedDeviceList: [],
    deviceType: 8,
    tableLoading: false,
    deviceLoading: false,
    treeData: [],
    customTree: [], // 自定义组的数据
    outsidersTree: [], // 外部人员
    id: "", // 设备id
    schoolId: "",
    synchronousNumber: 0, // 已同步总数
    faceNumber: 0, // 总数
    all: 0, // 设备总数
    curObj: {}, // 当前选择的设备
    // 设备列表
    device: {
        pageNo: 1,
        pageSize: 100,
    },
    visible: false,
    checkedList: [],
    name: "", // 搜索的人名
    nameDevice: "", // 设备名称
    siteName: "", // 场地名称
    no: "", // 设备编号
    List: [],
    firstCount: 0, // 第一个设备列表下的设备数
    active: 0,
    topname: "",
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
    },

    statusIndex: 0,
    peopleIndex: 0,
    faceTable: [],
    userId: "",
    // 弹框里面的在线状态和设备数量
    onLine: 0, // 在线状态
    onCount: 0, // 同步数
    onAllCount: 0, // 总的设备数
    isSync: true,
    queryUserType: null,
    queryStatus: null,
    toggleTabsType: "",
    toggleTabsKey: "",
    tableState: {
        deptId: "", // 部门ID
        status: "", // 状态
        id: "", // 教师id
        name: "",
        accommodation: "", // 就读类型  只有学生组有
    },
    searchType: "name",
});
const baseDepPersonnel = () => {
    store.dispatch("base/getDepartmentTree", "cloud");
    store.dispatch("base/getSchoolRollTree", "cloud");
};
// ----------------- start 选年级班级框逻辑 ---------------
const tabsSelection = [
    {
        tab: "教职工组",
        checked: true,
        id: 1,
        key: "staff",
        // 有userId 则是人
        personField: { key: "userId", value: ["userId"] },
        // 单选 true 多选 false
        single: false,
        // 输入框是否显示
        searchOption: {
            show: true,
            displayMode: DISPLAY_MODE.NEW,
        },
    },
    {
        tab: "学生组",
        checked: true,
        isClassify: true,
        id: 2,
        key: "student",
        // 有userId 则是人
        personField: { key: "studentCode", value: ["studentCode"] },
        // 单选 true 多选 false
        single: false,
        // 输入框是否显示
        searchOption: {
            show: true,
            displayMode: DISPLAY_MODE.NEW,
        },
    },
    {
        tab: "外部人员",
        checked: true,
        id: 3,
        key: "outsiders",
        // 有userId 则是人
        personField: { key: "userId", value: ["userId"] },
        // 单选 true 多选 false
        single: false,
        // 输入框是否显示
        searchOption: {
            show: true,
            displayMode: DISPLAY_MODE.NEW,
        },
    },
    {
        tab: "自定义组",
        checked: true,
        id: 4,
        key: "custom",
        // 有userId 则是人
        personField: { key: "userId", value: ["userId"] },
        // 单选 true 多选 false
        single: false,
        // 输入框是否显示
        searchOption: {
            show: true,
            displayMode: DISPLAY_MODE.NEW,
        },
    },
];
const controlRef = shallowRef();

// 开启选人弹框
const openControlRef = () => {
    emitChangeTabs(1);
    state.toggleTabsKey = "staff";
    controlRef.value.modelState.dataSource = state.treeData;
    state.tableState.deptId = state.treeData[0]?.id || "";
    controlRef.value.modelState.open = true;
    state.checkedList = [];
};

// 获取教职工人员列表
const getStaffPages = (callback) => {
    const { pageNo, pageSize, total } = controlRef.value.modelState.searchTable;
    const { roll_status_yes_id = [], emp_status_yes_id = [] } =
        store.state.selectSource.dictionary;
    let Api = getDepartmentPersonnel;
    const { name, accommodation, deptId, _type = 0 } = state.tableState;
    const codes = route.query.codes;
    let params = {
        id: deptId,
        name,
        accommodation,
        code: codes,
        pageNo,
        pageSize,
        total,
    };

    if (state.toggleTabsKey === "student") {
        Api = getSchoolRollStudentPage;
        params = {
            ...params,
            filterNonEltern: false,
            queryEltern: false,
            // classesId: deptId,
            type: _type,
            statusList: roll_status_yes_id,
        };
    } else if (state.toggleTabsKey === "outsiders") {
        Api = getFacePageList;
        params = {
            ...params,
            status: 1,
            type: 4,
        };
    } else if (state.toggleTabsKey == "custom") {
        Api = getFacePageList;
        params = {
            ...params,
            status: 1,
            type: 3,
        };
    } else {
        params = {
            ...params,
            ...state.tableState,
            statusList: emp_status_yes_id,
        };
    }
    Api(params)
        .then(({ data }) => {
            let { list } = data;
            // 外部人员 自定义组特需处理
            if (["custom", "outsiders"].includes(state.toggleTabsKey)) {
                list = data.page.list;
            }
            // 学生组
            if (state.toggleTabsKey == "student") {
                list = data?.studentPageListVO?.list || [];
            }
            callback(list);
        })
        .finally(() => {
            controlRef.value.modelState.loading = false;
        });
};

// 下級
const toggleLevel = (tabId, item = {}, options) => {
    const { index, trigger } = options;
    // 清空输入框
    state.tableState.name = "";
    state.tableState.accommodation = "";
    // 重置分页及搜索
    controlRef.value.modelState.isPpresent = false;
    // 面包屑
    if (!index) {
        // 第一层数据，恢复原本数据
        controlRef.value.modelState.dataSource = state.treeData;
    } else {
        state.tableState.deptId = item.id;
        state.tableState._type = item.type;
        const callback = (data) => {
            let children = item.children || [];
            controlRef.value.modelState.dataSource = children?.concat(
                data || []
            );
        };
        getStaffPages(callback);
    }
};

// Tabs切换
const toggleTabs = (item) => {
    state.toggleTabsType = item.id;
    state.toggleTabsKey = item.key;
    state.tableState.name = "";
    state.tableState.accommodation = "";
    // 重置
    controlRef.value.modelState.loading = false;
    controlRef.value.modelState.isPpresent = false;
    emitChangeTabs(item.id);
    state.tableState.deptId = state.treeData[0]?.id || "";
    controlRef.value.modelState.dataSource = state.treeData;
};

// 搜人  查找教职工
function searchSelect(tabId, item) {
    const { name, accommodation } = item;
    if (name || accommodation) {
        // 清空搜索输入框
        // 清空搜索查询list数据
        controlRef.value.modelState.searchTable.list = [];
        // 选人组件 - 首次聚焦教职工
        state.tableState.name = name;
        state.tableState.accommodation = accommodation;
        controlRef.value.modelState.loading = true;
        const callback = (data) => {
            controlRef.value.modelState.searchTable.list =
                controlRef.value.modelState.searchTable.list?.concat(data);
        };
        getStaffPages(callback);
    } else {
        // name为空时，不发送请求，恢复最原始数据
        // !tabId ? setDataSource(state.staffList) : setDataSource(state.studentList)
    }
}

// 提交選人數據
const submit = (checked) => {
    // 学生1   教职工2  自定义人员3
    // 类型说明:
    // 学籍时返回:班级 classes，学生:student，家长:eltern
    // 部门时返回:部门:dept，部门员工:people_dept
    // 角色时返回:角色:role，角色员工:people_role
    // 外部人员返回:组:external，组成员:people_external
    let arr = [];
    arr = checked.map((item) => {
        const { rollValue, id, userId, name, showName, _type } = item;
        let parma = { id, name: showName || name, identity: _type || 0 };
        const isType = (Type) => item.hasOwnProperty(Type);
        //identity 身份标识(0.学生 1.教职工 2.家长 3. 外部人员 4.自定义组)
        if (_type === 1) {
            // 教职工
            if (isType("pid")) {
                parma.typeValue = "dept";
            } else {
                parma.typeValue = "people_dept";
                parma.userId = userId;
            }
        } else if (_type === 2) {
            // 学生
            parma.identity = 0;
            if (isType("pid")) {
                parma.typeValue = rollValue;
            } else {
                parma.typeValue = "student";
                parma.userId = userId;
            }
        } else if (_type === 3) {
            // 外部人员
            if (isType("userId")) {
                parma.typeValue = "people_external";
                parma.userId = userId;
            } else {
                parma.typeValue = "external";
            }
        } else {
            // 自定义组
            parma.schoolUserType = 4;
            if (isType("userId")) {
                parma.typeValue = "people_external";
                parma.userId = userId;
            } else {
                parma.typeValue = "external";
            }
        }
        return parma;
    });
    const params = {
        personListDTO: arr,
        deviceId: state.id,
    };
    // 如果有数据则是批量关联人脸库
    if (state.checkedDeviceList.length) {
        const deviceIds = state.checkedDeviceList.map((v) => v.id);
        params.deviceId = "";
        params.deviceIds = deviceIds;
    }
    bindFaceInfo(params).then((res) => {
        message.success(res.message);
        state.list = [];
        state.device.pageNo = 1;
        state.device.pageSize = 20;
        machineListFn();
        // baseDepPersonnel()
        state.checkedDeviceList = [];
    });
    state.checkedList = checked;
};

// ----------------- end 选年级班级框逻辑 ---------------

const resetOptions = () => {
    state.isCheckAll = false;
    state.indeterminate = false;
    state.checkedDeviceList = [];
};
const onCheckAllChange = (e) => {
    Object.assign(state, {
        checkedDeviceList: e.target.checked ? state.online_list : [],
        indeterminate: false,
    });
};
watch(
    () => state.checkedDeviceList,
    (val) => {
        state.indeterminate =
            !!val.length && val.length < state.online_list.length;
        state.isCheckAll = val.length === state.online_list.length;
    }
);
// 批量同步关联人脸库end
const faceUserTypeList = computed(() => {
    const userTypeList = store.state?.selectSource?.dictionary.face_user_type;
    const arr = [
        {
            label: "全部",
            status: 0,
            machineStatus: 0,
            value: null,
        },
    ];
    return [...arr, ...userTypeList];
});

const userTypeList = computed(() => {
    const list = store.state?.selectSource?.dictionary.face_user_type;
    const userTypeList = ["-"];
    list.forEach((i) => {
        userTypeList.push(i.label);
    });
    return userTypeList || ["-", "学生", "教师工", "外部人员"];
});

// 获取设备列表
function machineListFn() {
    state.queryStatus = null;
    state.queryUserType = null;
    const params = {
        pageSize: state.device.pageSize,
        pageNo: state.device.pageNo,
        deviceType: state.deviceType,
        deviceTypeList: [state.deviceType],
        [state.searchType]: state.nameDevice,
    };
    state.deviceLoading = true;
    // deviceType: 1 闸机 2 班牌 3 考勤机 4 中性版班牌 5 一体机
    machineList(params)
        .then((res) => {
            const { list } = res.data;
            state.list = list;
            state.online_list = list.filter((item) => item.machineStatus == 1);
            state.all = list.length;
            if (list && list.length > 0) {
                if (state.id === "" || !state.id) {
                    state.topname = list[0]?.machineName || "";
                    state.siteName = list[0]?.siteName || "";
                    state.buildingName = list[0]?.buildingName || "";
                    state.no = list[0]?.no || "";
                    state.id = list[0]?.id || "";
                    state.curObj = list[0] || {};
                    state.schoolId = list[0]?.schoolId || "";
                }
                //     // 获取第一个数据
                getRecordPageFn();
            }
        })
        .finally(() => {
            state.deviceLoading = false;
        });
}

// 获取自定义组的数据(分组数据)
getCustomGroupTree().then((res) => {
    state.customTree = res.data;
});
// 获取外部人员组列表 左侧菜单
const getExternalGroupList = async () => {
    return await externalGroupNumberList().then(({ data }) => {
        state.outsidersTree = data;
    });
};
const emitChangeTabs = async (key) => {
    const data = {
        // 1: [
        //     {
        //         id: "2121211",
        //         leader: "323232322",
        //         name: "全校",
        //         machineName: "全校",
        //         showName: "全校",
        //         sort: -1,
        //         type: -1,
        //         children: store.state.base.departmentTree,
        //     },
        // ],
        1: store.state.base.departmentTree,
        2: store.state.base.schoolRollTree,
        3: state.outsidersTree,
        4: state.customTree,
    };
    state.treeData = ref(data[key]);
};
// 关联人脸库
const handerCallbackParameter = (e) => {
    // 学生1   教职工2  自定义人员3
    let arr = [];
    arr = e.map((item) => {
        if (item._source === 1) {
            // 教职工
            return {
                peopleType: 2,
                schoolUserId: item.id,
            };
        } else if (item._source === 2) {
            // 学生
            return {
                peopleType: 1,
                schoolUserId: item.id,
            };
        } else if (item._source === 3) {
            // 外部人员
            return {
                peopleType: 3,
                schoolUserId: item.id,
            };
        } else {
            return {
                peopleType: item.faceType,
                schoolUserId: item.userId,
            };
        }
    });
    window.localStorage.setItem("face_one", JSON.stringify(arr));
    const params = {
        userInfo: arr,
        deviceId: state.id,
    };
    bindFaceInfo(params).then((res) => {
        message.success(res.message);
        machineListFn();
    });
};
// 搜索设备列表
const searchDevice = () => {
    if (state.nameDevice) {
        state.list = [];
    }
    state.device.pageSize = 20;
    state.device.pageNo = 1;
    resetOptions();
    state.checkedDeviceList = [];
    machineListFn();
};
// 查询
const hanbleSearch = () => {
    getRecordPageFn();
};
// 重置
const resetting = () => {
    state.queryStatus = null;
    state.queryUserType = null;
    state.name = "";
    state.pagination.pageNo = 1;
    getRecordPageFn();
};
/* 分页事件 */
const handerSelectedRowKeys = (data) => {
    const { current, pageSize } = data;
    state.pagination.pageNo = current;
    state.pagination.pageSize = pageSize;
    getRecordPageFn();
};
// 同步
const syncFace = (item, isSync) => {
    state.isSync = isSync;
    state.userId = item.userId;
    faceRef.value.spinning = true;
    faceRef.value.visible = true;
    faceRef.value.setFaceTitle = isSync;
    faceRef.value.checkAll = false;
    // 获取相同设备的列表
    const params = {
        pageNo: 1,
        pageSize: 100,
        syncUserIds: [item.userId],
        deviceType: state.deviceType,
        // syncUserIds: item.userId,
        syncEquipmentId: state.id,
    };
    machineList(params)
        .then((res) => {
            const { list } = res?.data;
            if (state.list && state.list.length > 0) {
                const {
                    siteName,
                    machineName,
                    synchronousNumber,
                    faceNumber,
                    machineStatus,
                } = state.list[state.active];
                faceRef.value.machineStatus = Number(machineStatus) || "";
                faceRef.value.machineName = machineName || "";
                faceRef.value.siteName = siteName || "";
                faceRef.value.synchronousNumber = synchronousNumber || "";
                faceRef.value.faceNumber = faceNumber || "";
            }
            faceRef.value.plainOptions = list || [];
        })
        .finally(() => {
            faceRef.value.spinning = false;
        });
};
// 切换状态
const chooseStatus = (item, index) => {
    state.pagination.pageNo = 1;
    state.statusIndex = index;
    state.queryStatus = item.status;
    getRecordPageFn();
};
// 选择人员类型
const choosePeople = (item, index) => {
    state.queryUserType = item.value;
    state.peopleIndex = index;
    state.pagination.pageNo = 1;
    getRecordPageFn();
};
// 关联人脸库
const relevanceface = () => {
    selectPersonnelRef.value.selectedList.length = 0;
    state.visible = true;
};
// 一键同步
const Oneclicksynchronization = () => {
    modalConfirm(
        "一键同步",
        "确定同步列表中所有人员的人脸信息？",
        ExclamationCircleFilled
    ).then((res) => {
        if (res) {
            // 调接口
            syncAll({
                deviceId: state.id,
                deviceType: state.deviceType,
            }).then((res) => {
                message.success(res.message);
                getRecordPageFn();
            });
        }
    });
};
// 同步单个人
const syncOnePerson = () => {
    let ids = [];
    ids = faceRef.value.checkedList.map((item) => {
        return item.id;
    });
    const params = {
        deviceId: state.id,
        userIds: [state.userId],
        deviceIds: ids,
        deviceType: state.deviceType,
    };
    if (state.isSync) {
        syncAll(params).then((res) => {
            message.success(res.message);
            faceRef.value.visible = false;
            resetOptions();
            machineListFn();
        });
    } else {
        clearFace({
            deviceId: window.localStorage.getItem("device_id") || state.id,
            userIds: [state.userId],
            deviceIds: ids,
            deviceType: state.deviceType,
        }).then((res) => {
            message.success(res.message);
            faceRef.value.visible = false;
            resetOptions();
            machineListFn();
        });
    }
};

// 批量同步人脸
const batchSynchronization = () => {
    // 批量同步人脸库
    modalConfirm(
        "批量同步人脸库",
        "确定同步所有人员的人脸信息？",
        ExclamationCircleFilled
    ).then((res) => {
        if (res) {
            // 调接口
            const deviceIds = state.checkedDeviceList.map((v) => v.id);
            syncAll({ deviceIds }).then((res) => {
                message.success(res.message);
                resetOptions();
                getRecordPageFn();
                state.list = [];
                machineListFn();
                state.checkedDeviceList = [];
            });
        }
    });
};
// 清空
const clearall = () => {
    modalConfirm(
        "提示",
        "确定清空本机所有的人员信息？",
        ExclamationCircleFilled
    ).then((res) => {
        if (res) {
            // 调接口
            clearFace({
                deviceId: state.id,
                deviceType: state.deviceType,
            }).then((res) => {
                message.success(res.message);
                machineListFn();
                resetOptions();
            });
        }
    });
};
const hanbleShe = (item, index) => {
    state.faceTable = [];
    state.queryStatus = null;
    state.queryUserType = null;
    state.pagination.total = 0;
    state.active = index;
    state.topname = state.list[index].machineName;
    state.siteName = state.list[index].siteName;
    state.buildingName = state.list[index].buildingName;
    state.no = state.list[index].no;
    state.id = state.list[index].id; // 设备id
    window.localStorage.setItem("device_id", state.id);
    state.synchronousNumber = state.list[index].synchronousNumber;
    state.faceNumber = state.list[index].faceNumber;
    state.curObj = item;
    state.pagination.pageNo = 1;
    getRecordPageFn();
};

// 人员列表
const getRecordPageFn = () => {
    state.tableLoading = true;
    const params = {
        pageSize: state.pagination.pageSize,
        pageNo: state.pagination.pageNo,
        userType: state.queryUserType,
        id: state.curObj.id || state.id,
        schoolId: state.curObj.schoolId || state.schoolId,
        name: state.name,
        status: state.queryStatus,
        deviceType: state.deviceType,
    };
    if (state.curObj.id || state.id) {
        getRecordPage(params)
            .then((res) => {
                state.faceTable = res.data.list;
                state.pagination.pageNo = res.data.pageNo;
                state.pagination.pageSize = res.data.pageSize;
                state.pagination.total = res.data.total;
            })
            .finally(() => {
                state.tableLoading = false;
            });
    } else {
        state.tableLoading = false;
    }
};

async function scroll() {
    let isLoading = true; // 是否有数据可以加载
    const dom = document.getElementById("equipment_list");
    const bottomOfWindow =
        dom.scrollHeight - dom.scrollTop - dom.clientHeight <= 100;
    if (bottomOfWindow && isLoading && state.list.length < state.all) {
        state.device.pageNo = state.device.pageNo + 1; // 每次分页+1
        const data = {
            pageNo: state.device.pageNo,
            pageSize: state.device.pageSize,
            name: state.nameDevice,
            deviceType: route.query.deviceType || typeList[faceType.value],
        };
        const res = await machineList(data); // 自己封装的请求数据的方法
        // 有数据的时候加载
        const { list } = res.data;
        if (list && list.length > 0) {
            state.list.push(...list); // 追加数据 使用 ...语法
            isLoading = true;
        } else {
            // message.warning('温馨提示：暂无更多数据信息！')
            isLoading = false; // 无数据可以加载
        }
    }
}
onMounted(() => {
    baseDepPersonnel();
    machineListFn();
    getExternalGroupList();
});
</script>

<style lang="less" scoped>
.act {
    color: var(--primary-color);
}

.act_peo {
    color: var(--primary-color);
}

.ant-tabs-tabpane,
:deep(.ant-tabs-tabpane) {
    border-radius: 20px;
}

.face_lib {
    overflow: hidden;
    height: calc(100vh - 132px);
    border-radius: 20px;

    .content {
        overflow: hidden;
        height: 100%;

        .sider {
            max-height: 100%;
            min-height: 100%;
            overflow: hidden;
            border-right: 1px solid @border-color-base;
            padding: 20px 9px 20px 12px;

            :deep(.ant-layout-sider-children) {
                position: relative;
            }

            .fixed {
                position: sticky;
                background-color: #fff;

                .top {
                    font-size: 14px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 600;
                    color: rgba(0, 0, 0, 0.85);
                    line-height: 20px;
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 14px;
                }
            }

            &::-webkit-scrollbar {
                display: none;
            }

            .text {
                font-size: 14px;
                font-weight: 500;
                color: rgba(0, 0, 0, 0.85);
            }

            :deep(.ant-spin-container) {
                height: calc(100vh - 296px);
            }

            .equipment_list {
                margin-top: 10px;
                max-height: 100%;
                overflow-y: auto;

                .item {
                    padding: 12px 16px;
                    border-bottom: 1px solid #d9d9d9;
                    width: 100%;
                    height: 52px;
                    line-height: 52px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    cursor: pointer;

                    .left {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        max-width: 60%;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        word-break: break-all;

                        .on_line {
                            width: 10px;
                            height: 8px;
                            border-radius: 50%;
                            background-color: #bfbfbf;
                            margin: 0 6px;
                        }

                        .name {
                            font-size: 14px;
                            font-family: PingFangSC-Semibold, PingFang SC;
                            font-weight: 600;
                            line-height: 20px;
                            width: 157px;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }

                        .act {
                            background-color: #00b781;
                        }
                    }

                    .right {
                        color: #ccc;
                        font-size: 14px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        display: flex;
                        line-height: 20px;
                        color: rgba(0, 0, 0, 0.85);

                        .division_line {
                            padding: 0px 2px;
                        }

                        .color_black,
                        .division_line {
                            color: rgba(0, 0, 0, 0.45);
                        }
                    }

                    .right_class {
                        .division_line,
                        .color_black,
                        span {
                            color: #00b781 !important;
                        }
                    }
                }

                .active {
                    background: rgba(0, 183, 129, 0.08);
                    color: #00b781;
                }
            }

            .no_data {
                height: 500px;
                width: 100%;
                display: flex;
                justify-content: center;
                flex-direction: column;
                align-items: center;

                img {
                    width: 180px;
                    height: 180px;
                }

                .text {
                    font-size: 14px;
                    font-family: PingFangSC-Regular, PingFang SC;
                    font-weight: 400;
                    color: rgba(0, 0, 0, 0.65);
                    line-height: 20px;
                }
            }
        }

        .contents {
            max-height: 100%;
            overflow-y: auto;
            background: #fff;
            padding: 16px;
            font-family: PingFangSC-Medium, PingFang SC;

            .top_name {
                font-size: 18px;
                font-weight: 600;
                color: rgba(0, 0, 0, 0.85);
                line-height: 25px;
                margin-bottom: 8px;
            }

            .warp {
                margin-bottom: 16px;
                display: flex;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                line-height: 20px;
                font-size: 14px;

                .left {
                    color: rgba(0, 0, 0, 0.85);
                    margin-right: 24px;
                }

                .right {
                    color: rgba(0, 0, 0, 0.85);

                    .ti {
                        color: rgba(0, 0, 0, 0.45);
                    }
                }
            }

            .searchHead {
                display: flex;
                justify-content: space-between;
                margin-bottom: 2px;
            }
        }
    }
}

:deep(.table_wrapper) {
    width: 100% !important;
    overflow-x: auto;
}
</style>
