<!-- eslint-disable -->
<template>
    <a-layout>
        <a-layout-header
            class="layout_header header header_fixed"
            :style="isAnnounceTop == true ? { top: '46px' } : {}"
        >
            <Logo :title="title" :icon="icon" />
            <User />
        </a-layout-header>
        <a-layout-content class="layout_content page_container__warp">
            <a-layout class="layout">
                <a-layout-sider
                    :style="isAnnounceTop == true ? { marginTop: '44px' } : {}"
                    class="layout_sider sider_one_level"
                    :collapsedWidth="60"
                    v-model:openKeys="state.openKeys"
                    v-model:collapsed="state.collapsed"
                    @mouseenter="state.collapsed = false"
                    @mouseleave="state.collapsed = true"
                >
                    <a-menu
                        v-model:selectedKeys="state.selectedKeys"
                        v-model:openKeys="state.openKeys"
                        mode="inline"
                    >
                        <a-menu-item
                            v-for="item in state.siderOneLevelList"
                            :key="item.name"
                            @click="handerSelect(item)"
                        >
                            <template #icon>
                                <i class="sider" :class="item.meta.icon"></i>
                            </template>
                            {{ item.meta.title }}
                        </a-menu-item>
                    </a-menu>
                </a-layout-sider>
                <div class="layout_right">
                    <a-layout-sider
                        class="layout_sider sider_two_level 2"
                        :style="
                            isAnnounceTop == true ? { marginTop: '44px' } : {}
                        "
                        v-if="state.integration"
                    >
                        <ul class="yd_menu">
                            <li
                                class="yd_menu_item"
                                v-for="item in state.siderTwoLevelList"
                                :key="item.name"
                                @click="handerRouter(item)"
                                :class="{ active: state.twoLevelId == item.id }"
                            >
                                <i
                                    style="font-size: 24px"
                                    class="sider"
                                    :class="item.icon || 'icon-zhirisheng'"
                                ></i>
                                <span>{{ item.name }}</span>
                            </li>
                        </ul>
                    </a-layout-sider>
                    <a-layout
                        :style="{
                            marginLeft: state.integration ? '200px' : '',
                        }"
                    >
                        <a-layout-content class="layout_content_view">
                            <RouterTabs
                                backRout="appModelList"
                                v-if="state.integration"
                                :twoLevelId="state.twoLevelId"
                                :customParam="state.customParam"
                                :_types="state._types"
                            />
                            <template v-else>
                                <router-view
                                    class="router-view"
                                    v-slot="{ Component }"
                                >
                                    <transition
                                        enter-active-class="animate__animated animate__fadeIn"
                                    >
                                        <div
                                            class="view_content"
                                            :class="{
                                                active:
                                                    route.name === 'dashboard',
                                            }"
                                        >
                                            <component
                                                :is="Component"
                                                :key="comKey"
                                            />
                                        </div>
                                    </transition>
                                </router-view>
                            </template>
                        </a-layout-content>
                    </a-layout>
                </div>
            </a-layout>
        </a-layout-content>
    </a-layout>
</template>

<script setup>
import { watch, computed, reactive, onBeforeMount } from "vue";
import { useRouter, useRoute } from "vue-router";
import { Local } from "@/utils/storage";
import axios from "@/utils/request";
import { useStore } from "vuex";
import Logo from "./Logo";
import User from "./User";
import RouterTabs from "@/components/IntegratedAggr/index.vue";
import { getNewRouters } from "@/api/user.js";
const store = useStore();
const router = useRouter();
const route = useRoute();

const isAnnounceTop = computed(() => {
    return store.state.base.isAnnounceTop;
});
const title = computed(() => {
    return store.state.user.currentSchool?.schoolName;
});
const icon = computed(() => {
    return store.state.user.currentSchool?.schoolLogo;
});
const comKey = computed(() => {
    return route.path;
});
const state = reactive({
    attendanceSystem: {},
    _types: null,
    integration: false,
    passId: "",
    activeKey: "",
    showBack: false,
    tabsList: [],
    twoLevelId: "",
    customParam: "",
    systemCode: "",
    isSwitchDorm: false,
    collapsed: true,
    switchclazz: false,
    openKeys: [],
    selectedKeys: [],
    openKeysTwo: [],
    selectedKeysTwo: [],
    siderOneLevelList: [],
    siderTwoLevelList: [],
    enablefunc: "",
});

const customParams = computed(() => {
    return route.meta.customParams?.split(".") || [];
});
provide("integrationParams", state);
// 校园考勤系统 设置type 类型
const getType = () => {
    axios.get("/attweb/eventStatistical/getType").then(({ data }) => {
        data?.forEach((v) => (state.attendanceSystem[v.code] = v));
    });
};
const schoolAttendanceSystem = async (Platform) => {
    state.attendanceSystem = {};
    if (Platform === "schoolAttendanceSystem") {
        await getType();
    }
    // 获取新的路由
    await getNewRouters({ Platform })
        .then(({ data }) => {
            // IntegratedAggr.vue中的菜单
            const { children, id, component, appCode, customParams } = data[0];
            if (Object.keys(state.attendanceSystem).length) {
                state._types = state.attendanceSystem[appCode].type;
            }
            state.customParam = customParams;
            state.twoLevelId = id;
            state.siderTwoLevelList = data;
            state.tabsList = children;
            state.systemCode = component;
            state.activeKey = children[0].component;
        })
        .catch((error) => {
            // 无权限访问
            if (error.data.code === 1002003014) {
                router.push({ path: "/401", query: { redirect: "app/list" } });
            }
        });
};

// 集成系统菜单切换
const handerRouter = (item) => {
    const { id, children, component, customParams, appCode } = item;
    state.customParam = customParams;
    state.twoLevelId = id;
    state.tabsList = children;
    state.systemCode = component;
    state.activeKey = children[0].component;
    if (Object.keys(state.attendanceSystem).length) {
        state._types = state.attendanceSystem[appCode].type;
    }
};
watch(
    () => route.path,
    async (val, olVal) => {
        // 判断是否是集成系统： 校园管理系统  学校门禁管理系统
        if (
            val !== olVal &&
            (val.indexOf("schoolAttendanceSystem") !== -1 ||
                val.indexOf("schoolGateManagement") !== -1)
        ) {
            const { pathMatch } = route.params;
            state.integration = true;
            await schoolAttendanceSystem(pathMatch[0]);
        }
        state.selectedKeys = customParams.value;
        state.selectedKeysTwo = [customParams.value[1]];
    },
    { immediate: true, deep: true }
);
const permission = computed(() => {
    return store.state.base.permission;
});
// 一级菜单点击事件
const handerSelect = (item, bol) => {
    router.push(item.redirect || item.path);
};

onBeforeMount(() => {
    state.siderOneLevelList = permission.value.filter((i) => !i.hideInMenu);
});
</script>

<style scoped lang="less">
.layout {
    min-height: calc(100vh - 48px);
}

.header {
    height: 48px;
    display: flex;
    justify-content: space-between;
    padding: 0 24px;
}

.header_fixed {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    z-index: 999;
}

.page_container__warp {
    padding-top: 48px;
    min-height: 100%;
    box-sizing: border-box;
}

.layout_right {
    display: flex;
    flex: 1;
    margin-left: 60px;
}

:deep(.layout_sider) {
    .ant-menu {
        background: @gray-background;

        &:after,
        &:before {
            display: none;
        }

        .ant-menu-item {
            margin: 0;
            width: auto;
        }
    }

    &.sider_one_level {
        background: @gray-background;
        border-right: 1px solid @border-color-base;
        padding-top: 18px;
        position: fixed;
        top: 48px;
        left: 0;
        bottom: 0px;
        // 如果不想做整体移动 菜单栏又是固定fixed 定位 的那就直接把z-index调高
        z-index: 999; //要多大有多大算了

        .sider {
            font-size: 24px;
        }

        .ant-menu-item {
            border-radius: 4px;
            margin: 0 8px;
            padding: 0 9px;
        }

        .ant-menu-item-active {
            background-color: @border-color-base;
            color: @primary-color;
        }

        .ant-menu-item-selected {
            background-color: @primary-color;
            color: @body-background;

            .sider {
                color: @body-background;
            }
        }
    }

    &.sider_two_level {
        border-top: 1px solid @border-color-base;
        position: fixed;
        top: 48px;
        bottom: 0px;
        // 如果不想做整体移动 菜单栏又是固定fixed 定位 的那就直接把z-index调高
        z-index: 2;

        .sider {
            font-size: 26px;
        }

        .layout_sider_header {
            margin-bottom: 18px;
        }

        .ant-menu-item-selected {
            background-color: @gray-background;
            color: @primary-color;
            border-radius: 4px 0 0 4px;
        }

        .ant-menu {
            background: @body-background;

            &.ant-menu-inline {
                border: none;
            }

            .ant-menu-item {
                margin: 0;
                padding: 0;
                margin-left: 12px;

                &:after,
                &:before {
                    display: none;
                }
            }
        }

        .yd_menu {
            margin: 20px 0 0 20px;

            .yd_menu_item {
                height: 40px;
                line-height: 40px;
                border-radius: 4px 0 0 4px;
                text-indent: 22px;
                cursor: pointer;
                font-size: 14px;
                display: flex;

                .sider {
                    width: 26px;
                }

                &.active {
                    background: @gray-background;
                    color: @primary-color;
                }
            }
        }
    }

    .ant-menu-item-selected::after {
        display: none;
    }
}

.layout_content_view {
    .view_content {
        margin: 12px;
        border-radius: 12px;
        background: @body-background;
        min-height: calc(100vh - 74px);
        min-width: 1194px;
        position: relative;
    }
}

.yd_menu {
    margin-left: 20px;

    .yd_menu_item {
        height: 40px;
        line-height: 40px;
        border-radius: 4px 0 0 4px;
        text-indent: 22px;
        cursor: pointer;
        font-size: 14px;
        display: flex;

        .sider {
            width: 26px;
        }

        &.active {
            background: @gray-background;
            color: @primary-color;
        }
    }
}
</style>
