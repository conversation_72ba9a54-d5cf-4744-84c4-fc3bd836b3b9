<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-03-21 09:03:58
 * @LastEditors: 舒志建 <EMAIL>
 * @LastEditTime: 2023-07-10 21:01:54
-->
<template>
    <a-breadcrumb style="padding-top: 16px" v-show="!ishome && !isnoauth">
        <a-breadcrumb-item v-for="item in breadcrumbList" :key="item.path">
            <router-link :to="{
                path: item.path,
                query: {
                    ...$route.query
                }
            }">
                {{ item.breadcrumbName }}
            </router-link>
        </a-breadcrumb-item>
    </a-breadcrumb>
</template>

<script lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
export default {
    setup() {
        const route = useRoute()
        const router = useRouter()
        const breadcrumbList = ref([])
        const getRouterName = () => {
            const matched = route.matched.map((item) => {
                return {
                    path: item.path,
                    icon: item.meta && item.meta.icon,
                    breadcrumbName: item.meta && item.meta.title
                }
            })
            // const first = matched[1]
            // if (first && first.path == '/dashboard') {
            matched.splice(0, 1)
            // 如果是设备管理祛除最后一项
            const len = matched.length - 1
            if (matched[len].path.includes('basicManagement')) {
                matched.splice(len, 1)
            }
            // }
            breadcrumbList.value = matched
        }
        watch(
            () => {
                return router.currentRoute.value.path
            },
            () => {
                getRouterName()
            }
        )
        const ishome = computed(() => {
            return route.name === 'dashboard'
        })
        const isnoauth = computed(() => {
            return route.name === 'noauth401'
        })

        onMounted(() => {
            getRouterName()
        })
        return {
            breadcrumbList,
            ishome,
            isnoauth
        }
    }
}
</script>
