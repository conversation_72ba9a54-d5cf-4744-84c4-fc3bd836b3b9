<template>
    <div class="head">
        <a-avatar :size="36" src="/image/dorm_avatar.png"></a-avatar>

        <div class="head_name">{{ activeDorm.name }}</div>

        <a-dropdown v-model:visible="state.visible">
            <div class="head_switch">
                <i class="iconfont icon-qiehuan" style="font-size: 26px;"></i>
                <span class="label">切换宿舍</span>
            </div>
            <template #overlay>
                <a-menu @click="handleMenuClick" v-if="dorms.length">
                    <a-menu-item 
                        v-for="item in dorms" 
                        :key="item"
                    >{{ item.name }}</a-menu-item>
                </a-menu>
                <div class="empty" v-else>暂无宿舍信息</div>
            </template>
        </a-dropdown>
    </div>
</template>

<script setup>
import { reactive, computed } from "vue";
import { useStore } from "vuex";

const store = useStore();
const state = reactive({
    visible: false,
});

const dorms = computed(() => {
    return store.state.dorm.dorms
})
const activeDorm = computed(() => {
    return store.state.dorm.activeDorm
})

const handleMenuClick = (row) => {
    state.visible = false
    store.commit('dorm/setActiveDorm', row.key)
}

</script>

<style lang="less" scoped>
.head {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #FFFFFF;
    text-align: center;
    padding: 24px 0 17px;
    background-color: #00B781;
}
.head_name {
    font-size: 16px;
    line-height: 22px;
    padding: 0 12px;
    margin-top: 8px;
}
.head_switch {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 120px;
    height: 36px;
    font-size: 14px;
    border: 1px solid #FFFFFF;
    margin-top: 16px;
    border-radius: 50px;
    cursor: pointer;
    user-select: none;
    .label {
        margin-left: 4px;
    }
}
.empty {
    background: #fff;
    text-align: center;
    border-radius: 2px;
    padding: 12px 0;
    box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
}
</style>
