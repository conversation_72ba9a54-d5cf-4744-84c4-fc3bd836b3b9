<!--
 * @Descripttion: 登录后布局组件
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-23 13:35:18
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-03-15 12:21:39
-->
<template>
    <a-layout>
        <a-layout-header
            class="header header_fixed"
            :style="isAnnounceTop == true ? { top: '46px' } : {}"
        >
            <Logo :title="title" :icon="icon" />
            <User />
        </a-layout-header>
        <a-layout class="page_container__warp">
            <a-layout class="page_container">
                <a-layout-content class="layout_content">
                    <router-view class="router-view" v-slot="{ Component }">
                        <transition
                            enter-active-class="animate__animated animate__fadeIn"
                        >
                            <component :is="Component" :key="$route.path" />
                        </transition>
                    </router-view>
                </a-layout-content>
            </a-layout>
        </a-layout>
    </a-layout>
</template>
<script lang="ts">
import { defineComponent, toRefs, reactive, computed } from "vue";
import { Local } from "@/utils/storage";
import Logo from "./Logo/index.vue";
import User from "./User/index.vue";

import { useStore } from "@/store/index";
export default defineComponent({
    components: {
        User,
        Logo,
    },
    setup() {
        const store = useStore();
        const state = reactive({
            collapsed: false,
        });
        const title = computed(() => {
            return store.state.user.currentSchool?.schoolName;
        });

        const icon = computed(() => {
            return store.state.user.currentSchool?.schoolLogo;
        });
        const isAnnounceTop = computed(() => {
            return store.state.base.isAnnounceTop;
        });
        return {
            isAnnounceTop,
            ...toRefs(state),
            title,
            icon,
        };
    },
});
</script>
<style lang="less" scoped>
.ant-layout {
    min-height: 100%;
}

.layout {
    min-height: 100vh;
}

.header {
    background: @primary-color;
    height: 48px;
    display: flex;
    justify-content: space-between;
    padding: 0 24px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.42);
    margin-bottom: 2px;

    .user_warp {
        display: flex;
        align-items: center;
        color: @body-background;
    }
}

.header_fixed {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    // z-index: 2000;
    z-index: 999;
}

.page_container__warp {
    padding-top: 50px;
    min-height: 100%;
    box-sizing: border-box;
}

.page_container {
    min-height: 100%;
}

.router-view {
    height: 100%;
}

.hander_nav {
    padding-top: 4px;
    background: @body-background;
    height: calc(100vh - 48px);
    overflow: scroll auto;

    &::-webkit-scrollbar {
        display: none;
    }
}

.layout_content {
    background: #fff;
    margin: 0;
}
</style>
