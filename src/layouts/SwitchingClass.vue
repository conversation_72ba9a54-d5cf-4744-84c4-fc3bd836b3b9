<template>
    <!-- eslint-disable -->
    <div class="head">
        <div class="head_box">
            <div class="head_left">
                <div class="avatar_box">
                    <div class="avatar_outline">
                        <div class="pendant" v-if="state.ranking === '1'">
                            <img src="/image/pendant1.png" alt="" />
                        </div>
                        <div class="pendant" v-if="state.ranking === '2'">
                            <img src="/image/pendant2.png" alt="" />
                        </div>
                        <div class="pendant" v-if="state.ranking === '3'">
                            <img src="/image/pendant3.png" alt="" />
                        </div>
                        <a-avatar
                            :size="100"
                            :src="
                                state.classesIcon || '/image/default_class.png'
                            "
                            :style="{
                                backgroundColor: '#ffffff',
                                border: ranking[state.ranking],
                            }"
                        >
                            <template #icon>
                                <UserOutlined />
                            </template>
                        </a-avatar>
                        <div class="round">修改</div>
                    </div>
                    <div class="upimg">
                        <a-upload
                            accept="image/jpeg,image/png"
                            class="avatar-uploader"
                            list-type="picture-card"
                            :show-upload-list="false"
                            :action="state.actionUrl"
                            :before-upload="beforeUpload"
                            @change="handleChange"
                        >
                        </a-upload>
                    </div>
                </div>
                <div
                    class="name_class"
                    @mouseenter="disableMouseScroll"
                    @mouseleave="enableMouseScroll"
                >
                    <a-tooltip placement="top">
                        <template #title>
                            <span>
                                {{ state.classesName || "暂未分配班级" }}
                            </span>
                        </template>
                        <span class="name_class_text">
                            {{ state.classesName || "暂未分配班级" }}
                        </span>
                    </a-tooltip>
                    <!-- <i class="iconfont icon-qiehuan"></i> -->
                    <a-tooltip placement="right">
                        <template #title>切换班级</template>
                        <a-dropdown placement="bottomRight">
                            <!-- 这里加一点东西 如果班级列表数据只有一个那就不显示了,2个以上才显示切换 -->
                            <i
                                class="iconfont icon-qiehuan1 touchIcon"
                                v-if="classInfos.length > 1"
                            ></i>
                            <template #overlay>
                                <a-menu @click="handleMenuClick">
                                    <a-menu-item
                                        v-for="item in classInfos"
                                        :key="item.id"
                                    >
                                        {{ item.showName || item.name }}
                                    </a-menu-item>
                                </a-menu>
                            </template>
                        </a-dropdown>
                    </a-tooltip>
                </div>
                <div class="info_class">
                    <span>{{ state.studentNums }}人</span>
                    <span>｜</span>
                    <!-- <span class="teach_name">班主任：{{ state.master }}</span> -->
                    <span
                        class="teach_name"
                        :title="store.state.user?.userInfo?.name"
                        >班主任：{{ store.state.user?.userInfo?.name }}</span
                    >
                </div>
            </div>
        </div>
    </div>
</template>

<script setup name="headCom">
/* eslint-disable */
import { reactive, onMounted, ref } from "vue";
import {
    getqueryClassMaterList,
    getgetClassesDetail,
    targetComponent,
} from "@/api/3.0.js";
// import { getFileUpload } from '@/api/notice'
import api from "@/api";
import { setClassesIcon } from "@/api/classSign";
import { Local } from "@/utils/storage.ts";
import { useStore } from "vuex";
import { message } from "ant-design-vue";

const store = useStore();
const router = useRouter();
const state = reactive({
    classInfos: [],
    noticeList: [],
    actionUrl: `${import.meta.env.VITE_BASE_API}${api.fileUpload}`,
    classesIcon: "",
    mainId: "",
});

function disableScrollEvent(event) {
    // 阻止鼠标滚动事件的默认行为
    event.preventDefault();
}

function disableMouseScroll() {
    // 添加事件监听器，捕获鼠标滚动事件并阻止默认行为
    window.addEventListener("mousewheel", disableScrollEvent, {
        passive: false,
    });
}

function enableMouseScroll() {
    // 移除事件监听器，恢复鼠标滚动事件
    window.removeEventListener("mousewheel", disableScrollEvent, {
        passive: false,
    });
}

const ranking = {
    1: "4px solid #FF6826",
    2: "4px solid #FF8E59",
    3: "4px solid #FFB11E",
};

// const res = await store.dispatch("cutClass/reqqueryClassMaterList");
// state.classID = res.id;
// reqTargetComponent();
// reqgetClassesDetail();

const classesId = computed(() => {
    return store.state.cutClass.director_classId;
});

const classInfos = computed(() => {
    return store.state.cutClass.classInfo;
});

const reqTargetComponent = async () => {
    if (!Local.get("director_classId")) return;
    let { data } = await targetComponent({
        type: 1, // 1-班级德育 2-宿舍德育
        classId: Local.get("director_classId") || "",
    });
    if (!data) return;
    state.classMoral = data;
    state.ranking = data?.overviewObj?.ownObject?.lastCycleRankUp;
    // state.ranking = '4'
};

// 请求班级信息
const reqgetClassesDetail = async () => {
    const classesId = Local.get("director_classId") || "";
    if (classesId) {
        const { data } = await getgetClassesDetail({ classesId });
        const { classesName, studentNums, master, classesIcon } = data;
        store.commit("cutClass/CLASSES_OBJ", data);
        Local.set("classesObj", data);
        state.classesName = classesName;
        state.studentNums = studentNums;
        state.master = master;
        state.classesIcon = classesIcon;
    } else {
        state.classesName = "";
        state.studentNums = "";
        state.master = "";
        state.classesIcon = "";
        Local.set("classesObj", {});
        store.commit("cutClass/CLASSES_OBJ", {});
    }
};

const handleMenuClick = (e) => {
    window.localStorage.removeItem("winPrize");
    state.mainId = e.key;
    window.localStorage.setItem(
        "director_classId",
        JSON.stringify(state.mainId)
    );
    reqgetClassesDetail();
    location.reload();
};
// 头像上传之前的回调
const beforeUpload = (file) => {
    // file 上传的图片文件信息
    // file.type 文件类型
    const validFileType = ["image/jpeg", "image/jpg", "image/png"];
    const isValidFileType = validFileType.indexOf(file.type) > -1;
    // file.size 文件大小
    const isLt = file.size / 1024 < 500;
    if (!isValidFileType) {
        message.error("上传头像图片只能是 JPG 或 PNG 格式!");
    }
    if (!isLt) {
        message.error("上传头像图片大小不能超过 500KB!");
    }
    // 返回值 true 可以上传
    // 返回值 false 不会上传
    return isValidFileType && isLt;
};
// 默认头像
const imgurl = () => {
    if (state.icon.classesIcon) {
        return state.icon.classesIcon;
    } else {
        return "/image/classde1.png";
    }
};
function getBase64(img, callback) {
    const reader = new FileReader();
    reader.addEventListener("load", () => callback(reader.result));
    reader.readAsDataURL(img);
}
// 头像上传
const handleChange = (info) => {
    if (info.file.status === "uploading") {
    }
    if (info.file.status === "done") {
        getBase64(info.file.originFileObj, (base64Url) => {
            state.classesIcon = info.file.response.data[0].url;
            setClassesIcon({
                classesIcon: state.classesIcon,
                id: Local.get("director_classId") || "",
            });
        });
    }
    if (info.file.status === "error") {
        message.error("上传错误！");
    }
};

onMounted(() => {
    Local.set("classesObj", {});
    reqgetClassesDetail();
    reqTargetComponent();
});
</script>

<style lang="less" scoped>
.head {
    .head_box {
        display: flex;
        padding: 20px 12px 0px 12px;
        height: 260px;
        background: #00b781;
    }
}

.head_left {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
}

:deep(.ant-avatar) {
    border: 4px solid #fff;
}

.avatar_box {
    padding-top: 46px;
    position: relative;

    .upimg {
        position: absolute;
        bottom: 0;
        width: 100px;
        height: 100px;
        opacity: 0;
        border-radius: 100px;
        overflow: hidden;
    }

    .avatar-uploader {
        display: block;
        width: 100px;
        height: 100px;
    }

    &&:hover .upimg {
        display: block;
    }

    .avatar_outline {
        // padding-bottom: 20px;
    }

    .round {
        position: absolute;
        left: 15%;
        bottom: 0;
        width: 70%;
        cursor: pointer;
        border-radius: 12px;
        padding: 2px 18px;
        background-color: #ffffff;
        color: #00b781;
        text-align: center;
        opacity: 0;
        transition: opacity 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

    &:hover {
        .round {
            opacity: 1;
        }
    }
}

.pendant {
    position: absolute;
    right: -8px;
    top: 18px;
}

.name_class {
    position: relative;
    left: 8px;
    padding-top: 20px;
    padding-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    width: 180px;
    text-align: center;

    .name_class_text {
        max-width: 150px;
        display: inline-block;
        overflow: hidden;
        cursor: pointer;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding-right: 4px;
        vertical-align: bottom;
    }
}

.info_class {
    // padding-bottom: 26px;
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
    color: #ffffff;
    display: flex;
    align-items: center;

    .teach_name {
        display: inline-block;
        /* 将span转换为块级元素，以便设置宽度 */
        width: 120px;
        white-space: nowrap;
        /* 防止文字换行 */
        overflow: hidden;
        /* 隐藏超出部分的文字 */
        text-overflow: ellipsis;
        /* 显示省略号 */
    }
}

.head_right {
    // width: 900px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    .inform_title {
        padding: 0px 12px 0px 8px;
        font-size: 24px;
        font-weight: normal;
        color: #ffffff;
    }

    .bar_box {
        padding-left: 14px;
        width: 600px;
    }
}

.notice_item {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

:deep(.ant-carousel) {
    color: #ffffff;
}

.touchIcon {
    font-size: 16px !important;
    cursor: pointer;
}
</style>
