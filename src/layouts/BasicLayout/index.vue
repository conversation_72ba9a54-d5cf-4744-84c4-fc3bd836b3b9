<!--
 * @Descripttion: 登录后布局组件
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-23 13:35:18
* @LastEditors: 舒志建 <EMAIL> * @LastEditTime: 2023-07-08 09:39:10

-->
<template>
    <a-layout>
        <a-layout-header
            class="header header_fixed"
            :style="isAnnounceTop == true ? { top: '46px' } : {}"
        >
            <Logo :title="title" :icon="icon" />
            <User />
        </a-layout-header>
        <a-layout class="page_container__warp">
            <a-layout-sider
                class="layout_sider"
                width="216"
                v-model:collapsed="state.collapsed"
                :style="isAnnounceTop == true ? { marginTop: '44px' } : {}"
            >
                <div class="hander_nav">
                    <Menu v-model:collapsed="state.collapsed" />
                </div>
                <div
                    :class="{
                        collapsed_warp: true,
                        isCollapsed: state.collapsed,
                    }"
                    @click="handleChangeCollapsed"
                ></div>
            </a-layout-sider>
            <a-layout
                class="page_container"
                :style="`${
                    !state.collapsed
                        ? 'padding:0 12px 12px 228px'
                        : 'padding:0 24px 24px 92px;'
                }`"
            >
                <Breadcrumb />
                <a-layout-content class="layout_content">
                    <router-view class="router-view" v-slot="{ Component }">
                        <!--  :key="$route.path" -->
                        <transition
                            enter-active-class="animate__animated animate__fadeIn"
                        >
                            <component :is="Component" :key="$route.path" />
                        </transition>
                    </router-view>
                </a-layout-content>
                <!-- <a-layout-footer style="text-align: center">
                  Yede web ©2021
                  <a-button type="link">v1.0.0</a-button>
              </a-layout-footer>-->
            </a-layout>
        </a-layout>
    </a-layout>
</template>
<script lang="ts" setup>
import { reactive, computed } from "vue";
import { Local } from "@/utils/storage";
import Logo from "../Logo/index.vue";
import User from "../User/index.vue";
import Breadcrumb from "../Breadcrumb/index.vue";
import Menu from "@/components/Menu/index.vue";

import { useStore } from "@/store/index";
const store = useStore();
const state = reactive({
    collapsed: false,
});

const isAnnounceTop = computed(() => {
    return store.state.base.isAnnounceTop;
});
const title = computed(() => {
    return store.state.user.currentSchool?.schoolName;
});

const icon = computed(() => {
    return store.state.user.currentSchool?.schoolLogo;
});
const handleChangeCollapsed = () => {
    state.collapsed = !state.collapsed;
};
</script>
<style lang="less" scoped>
.ant-layout {
    min-height: 100%;
}

.layout {
    min-height: 100vh;
}

.header {
    background: @primary-color;
    height: 48px;
    display: flex;
    justify-content: space-between;
    padding: 0 24px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.42);
    margin-bottom: 2px;

    .user_warp {
        display: flex;
        align-items: center;
        color: @body-background;
    }
}

.header_fixed {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    // z-index: 2000;
    z-index: 999;
}

.layout_sider {
    position: fixed;
    top: 49px;
    left: 0;
    height: 100%;
    box-shadow: 2px 0 6px rgba(0, 21, 41, 0.04);

    .collapsed_warp {
        // position: absolute;
        // right: -12px;
        // top: 55px;
        // width: 12px;
        // height: 32px;
        // cursor: pointer;
        // background: url('data:/image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAABACAYAAAAAqrdiAAAAAXNSR0IArs4c6QAAAhNJREFUWEdjZNjW6MtACWBk+svw9993Bob/rxkEOB8y2JR/RjaOkWIL0B33n/EBg9e/qwyMDf9AUtS3AGTqv/9vGbwZToAsoY0FIEtAPvGuu0w7C0CW8HEcoK0F//7fp7EFDF9oawEj01/aWkCzZIqUN0Z9QLAUGw2i0SAiGAIEFYymItoEUa2Knboat5BY1rXtRz///gmu3HEBkuPguEVitIWgbDjIwL1vH8xzObVoA1Us4GVlZzplmZStwS3qCjNw39sH851PLVpPsQXyHPxsRywTy2U4+Exhhj358em05fE5nU9+fPlFkQVmfNK828yiaoVZOTVgBt34+nq32fF5UwmFP8GGV6CEhugiPf9GHmZ2GZjhJ94/XmV5Yv4SgskHqgBnJOfJmyp0arg2cDCxCEHV/tv65vZMn9PLtxNrOE4fdKg66xYrW1azMDJxQVqC/3/Nf3KhN+XK5uOkGI7VApDL+zU9epgYGdlACv78//el8+6R1prbB66SajhWC67bZuVp8Ii4gCS///3ztvjGrvrpj848IsfwgbGA5kEE8hZNIxkW1jRNpjBLaJrRYJbQtKiAWQIt7MpkOPjMqF7YwQzEXlzfW+B8ask6ikpTdM00q3CQLaJplUlqkUFynTxqAakhQFD9aByMBhHBECCoYDQVDXAQ0XxA6h+th9RoPihI02FNmg7M0nRomeqD4wSG9wHsX2Z4jwk7xAAAAABJRU5ErkJggg==') no-repeat;
        // background-size: 12px 32px;
        // transition: all 0.25s;

        position: absolute;
        right: -14px;
        top: 55px;
        width: 17px;
        height: 32px;
        cursor: pointer;
        background: url(/image/leftAdsorption.png) no-repeat center;
        background-size: cover;
        transition: all 0.25s;
    }

    .isCollapsed {
        background: url("/image/rightAdsorption.png") no-repeat center;
        background-size: cover;
        // transform: rotateY(180deg);
        right: -14px;
    }

    //
    .ant-menu-vertical > .ant-menu-item,
    .ant-menu-vertical-left > .ant-menu-item,
    .ant-menu-vertical-right > .ant-menu-item,
    .ant-menu-inline > .ant-menu-item,
    .ant-menu-vertical > .ant-menu-submenu > .ant-menu-submenu-title,
    .ant-menu-vertical-left > .ant-menu-submenu > .ant-menu-submenu-title,
    .ant-menu-vertical-right > .ant-menu-submenu > .ant-menu-submenu-title,
    .ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
        height: 48px;
        line-height: 48px;
    }

    .ant-menu-sub.ant-menu-inline > .ant-menu-item,
    .ant-menu-sub.ant-menu-inline
        > .ant-menu-submenu
        > .ant-menu-submenu-title {
        height: 48px;
        line-height: 48px;
        // margin-left:12px;
        // margin-right:12px;
        border-radius: 4px;
    }

    .ant-menu-inline .ant-menu-selected::after,
    .ant-menu-inline .ant-menu-item-selected::after {
        content: none;
    }

    .ant-menu-sub.ant-menu-inline {
        padding-left: 12px;
        padding-right: 12px;
        background: #fff;
    }

    .ant-menu-sub.ant-menu-inline {
        background: #fff;
    }

    .ant-menu-item:hover {
        background: @gray-background;
    }

    .ant-menu-item-selected.ant-menu-item:hover {
        background: @acitve-background;
    }
}

.page_container__warp {
    padding-top: 50px;
    min-height: 100%;
    box-sizing: border-box;
}

.page_container {
    min-height: 100%;
    padding: 0 24px 24px 240px;
}

.router-view {
    height: 100%;
}

.hander_nav {
    padding-top: 4px;
    background: @body-background;
    height: calc(100vh - 48px);
    overflow: scroll auto;

    &::-webkit-scrollbar {
        display: none;
    }
}

.layout_content {
    background: #fff;
    margin: 0;
    margin-top: 16px;
}
</style>
