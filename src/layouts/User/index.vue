<template>
    <div class="user_wrapper">
        <!-- <div class="content_wrapper" title="帮助中心">
            <router-link to="/help/doc" class="help_icon">
                <question-circle-outlined />
            </router-link>
            <a-divider class="help_line" type="vertical" />
        </div> -->

        <div class="content_wrapper">
            <a-dropdown @visibleChange="userVisibleChange">
                <span class="action">
                    <a-image
                        class="avatar"
                        size="small"
                        :src="userInfo.avatar"
                        fallback="/image/tephoto1.png"
                        :preview="false"
                    />

                    <span class="nickname">
                        {{ userInfo.name }}
                        <caret-down-outlined
                            :class="`caretup_${userVisible}`"
                        />
                    </span>
                </span>
                <template #overlay>
                    <a-menu class="user-dropdown-menu-wrapper">
                        <a-menu-item key="0">
                            <router-link to="/account/center">
                                <user-outlined />个人中心
                            </router-link>
                        </a-menu-item>
                        <a-menu-item key="1">
                            <a href="javascript:;" @click="changePassword">
                                <key-outlined />修改密码
                            </a>
                            <!-- <key-outlined />修改密码 -->
                        </a-menu-item>
                        <!-- <a-menu-item key="2" disabled>
                            <setting-outlined />测试
                        </a-menu-item>-->
                        <a-menu-divider />
                        <a-menu-item key="3">
                            <a href="javascript:;" @click="handleLogout">
                                <poweroff-outlined />退出登录
                            </a>
                        </a-menu-item>
                    </a-menu>
                </template>
            </a-dropdown>
        </div>
        <a-divider style="background-color: #fff" type="vertical" />
        <a-button @click="openFn" shape="round" v-if="!isCloud">
            <span style="color: #00b781">前往开放平台</span>
        </a-button>

        <!-- 修改密码对话框 -->
        <a-modal
            v-model:visible="changePasswordVisible"
            title="修改密码"
            width="400px"
            style="top: 150px"
            :maskClosable="false"
            @cancel="cancel"
            @ok="changePasswordOk"
        >
            <template #footer>
                <a-button key="back" @click="cancel">取消</a-button>
                <a-button
                    key="submit"
                    type="primary"
                    @click="changePasswordOk"
                    :loading="submitLoading"
                    >确定</a-button
                >
            </template>
            <a-form
                :model="changePasswordForm"
                layout="vertical"
                ref="changePasswordRef"
            >
                <a-form-item
                    label="旧密码："
                    name="oldPassword"
                    :rules="[
                        { required: true, message: '请输入旧密码！' },
                        { validator: pwdCheck, trigger: 'blur' },
                    ]"
                >
                    <a-input-password
                        v-model:value="changePasswordForm.oldPassword"
                        placeholder="请输入旧密码"
                        allow-clear
                    />
                </a-form-item>
                <a-form-item
                    label="新密码："
                    name="newPassword"
                    :rules="[
                        { required: true, message: '请输入新密码！' },
                        { validator: verifyNewPassword, trigger: 'blur' },
                    ]"
                >
                    <a-input-password
                        v-model:value="changePasswordForm.newPassword"
                        placeholder="请输入新密码"
                        allow-clear
                    />
                </a-form-item>
                <a-form-item
                    label="确认密码："
                    name="confirmPassword"
                    :rules="[
                        { required: true, message: '请再次输入新密码！' },
                        { validator: pwdAgainCheck, trigger: 'blur' },
                    ]"
                >
                    <a-input-password
                        v-model:value="changePasswordForm.confirmPassword"
                        placeholder="请输入包含字母、数字、特殊字符，长度为8-20个字符的密码。"
                        allow-clear
                    />
                    <exclamation-circle-filled />&nbsp;
                    <span
                        >需包含字母、数字、特殊字符，长度为8-20个字符的密码</span
                    >
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>

<script>
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { message, Modal } from "ant-design-vue";
import {
    defineComponent,
    toRefs,
    ref,
    getCurrentInstance,
    computed,
    reactive,
    createVNode,
} from "vue";
import { useRouter } from "vue-router";
// import type { FormInstance } from 'ant-design-vue'
import { ls, regularSpecialCharacters } from "@/utils/util";
import config from "@/config/config";
import api from "@/api";
import { useStore } from "@/store/index";
import RSA from "@/utils/rsa.js";
import { verifyPhone, checkField } from "@/utils/toolsValidate.ts";

import { useStorage } from "@/utils/index";
const { ACCESS_TOKEN, ACCESS_REFRESHTOKEN, ACCESS_DATA } = config;
export default defineComponent({
    setup() {
        const store = useStore();
        const router = useRouter();
        const { proxy } = getCurrentInstance();
        const { getItem } = useStorage();
        const state = reactive({
            userVisible: false,
            changePasswordVisible: false,
            changePasswordForm: {
                oldPassword: "",
                newPassword: "",
                confirmPassword: "",
            },
            submitLoading: false,
            isCloud: getItem("isCloud") || false,
        });

        const userInfo = computed(() => {
            return store.state.user.userInfo;
        });
        const userVisibleChange = (v) => {
            state.userVisible = v;
        };

        function clearLocalStorage() {
            for (let i = 0; i < localStorage.length; i++) {
                let key = localStorage.key(i) || "";
                if (key.includes("Comp")) {
                    continue; // 跳过包含"Comp"字符串的key
                }
                localStorage.removeItem(key);
            }
        }
        // 清空缓存并退出
        const clearExit = () => {
            ls.remove(ACCESS_TOKEN);
            ls.remove(ACCESS_REFRESHTOKEN);
            ls.remove(ACCESS_DATA);
            ls.remove("schoolId");
            ls.remove("dictionary");
            ls.remove("columnsCheckedList");
            message.success("安全退出");
            window.localStorage.removeItem("director_classId");
            clearLocalStorage();
            // window.location.reload()
            localStorage.clear();
            router.push({ path: "/login" });
        };

        const handleLogout = () => {
            Modal.confirm({
                title: "提示",
                icon: createVNode(ExclamationCircleOutlined),
                content: "真的要注销登录吗？",
                okText: "确定",
                cancelText: "取消",
                onOk() {
                    clearExit();
                },
                class: "test",
            });
        };
        const changePassword = () => {
            state.changePasswordVisible = true;
        };
        const cancel = () => {
            changePasswordRef.value.resetFields();
            state.changePasswordVisible = false;
        };
        const changePasswordOk = () => {
            changePasswordRef.value.validateFields().then(() => {
                updatePassword();
            });
        };
        const changePasswordRef = ref();

        // 修改密码验证
        const pwdCheck = (rule, value, callback) => {
            if (value) {
                if (/[\u4E00-\u9FA5]/g.test(value)) {
                    return Promise.reject(
                        "只可输入字母、数字、特殊字符、不能输入汉字!"
                    );
                }
                if (value.length < 6) {
                    return Promise.reject("密码不能少于6位！");
                }
                if (value.length > 20) {
                    return Promise.reject("密码最长不能超过20位！");
                }
            }
            return Promise.resolve();
        };
        const verifyNewPassword = (rule, value, callback) => {
            if (!regularSpecialCharacters.test(value)) {
                return Promise.reject(
                    new Error(
                        "请输入包含字母、数字、特殊字符，长度为8-20个字符的密码！"
                    )
                );
            }
            return Promise.resolve();
        };
        // 重复密码验证
        const pwdAgainCheck = (rule, value, callback) => {
            if (value) {
                if (state.changePasswordForm.newPassword !== value) {
                    return Promise.reject("两次输入密码不一致！");
                }
            }
            return Promise.resolve();
        };
        // 修改密码的接口
        const updatePassword = () => {
            state.submitLoading = true;
            const params = {
                oldPwd: state.changePasswordForm.oldPassword,
                newPwd: state.changePasswordForm.newPassword,
                confirmPwd: state.changePasswordForm.confirmPassword,
            };
            const passWadData = {
                paramEncipher: RSA.encrypt(JSON.stringify(params)),
            };
            proxy.$defHttp
                .post(api.updatePassword, passWadData)
                .then((res) => {
                    state.changePasswordVisible = false;
                    changePasswordRef.value.resetFields();
                    // message.success(res.message)
                    clearExit();
                })
                .finally(() => {
                    state.submitLoading = false;
                });
        };
        const openFn = () => {
            const openSystem =
                window.open(import.meta.env.VITE_BASE_API_OPEN) || "";
            setTimeout(() => {
                if (openSystem !== "") {
                    const { currentSchool, accessToken } = store.state.user;
                    const sArr = [];
                    sArr.push(currentSchool);
                    const params = {
                        accessToken,
                        schoolInfo: sArr,
                    };
                    const obj = JSON.parse(JSON.stringify(params));
                    openSystem.postMessage(
                        obj,
                        import.meta.env.VITE_BASE_API_OPEN
                    );
                }
            }, 100);
        };
        return {
            ...toRefs(state),
            openFn,
            userInfo,
            handleLogout,
            userVisibleChange,
            changePassword,
            changePasswordOk,
            changePasswordRef,
            cancel,
            pwdCheck,
            verifyNewPassword,
            pwdAgainCheck,
            updatePassword,
        };
    },
});
</script>

<style scoped lang="less">
.user_wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.vertical {
    margin: 0px 12px;
}

.help_icon {
    padding: 8px;
    color: @body-background;
    font-size: 18px;
}

.help_line {
    background: @body-background;
    height: 18px;
    margin-right: 16px;
}

.content_wrapper {
    height: 100%;
    line-height: 48px;

    .action {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        // padding: 0 16px;
        transition: all 0.3s;
        height: 100%;
    }

    :deep(.avatar) {
        width: 24px;
        height: 24px;
        margin-right: 8px;
        border-radius: 50%;
    }

    .nickname {
        // color: @body-background;
        font-family: PingFangSC-Medium, PingFang SC;
        font-size: 14px;
    }

    .caretup_false {
        transition: all 0.25s;
        transform: rotate(0);
    }

    .caretup_true {
        transition: all 0.25s;
        transform: rotate(180deg);
    }
}
</style>
