<!-- eslint-disable -->

<template>
    <a-layout>
        <a-layout-header class="layout_header header header_fixed"
            :style="isAnnounceTop == true ? { top: '46px' } : {}">
            <Logo :title="title" :icon="icon" />
            <User />
        </a-layout-header>
        <a-layout-content class="layout_content page_container__warp">
            <a-layout class="layout">
                <a-layout-sider class="layout_sider sider_one_level"
                    :style="isAnnounceTop == true ? { marginTop: '44px' } : {}" :collapsedWidth="60"
                    v-model:openKeys="state.openKeys" v-model:collapsed="state.collapsed"
                    @mouseenter="state.collapsed = false" @mouseleave="state.collapsed = true">
                    <a-menu v-model:selectedKeys="state.selectedKeys" v-model:openKeys="state.openKeys" mode="inline">
                        <a-menu-item v-for="item in state.siderOneLevelList" :key="item.name"
                            @click="handerSelect(item)">
                            <template #icon>
                                <i
                                    style="font-size: 24px"
                                    v-if="item.meta.icon == 'icon-xiaoyiyun'"
                                    class="iconfont icon-xiaoyiyun"
                                ></i>
                                <i v-else class="sider" :class="item.meta.icon"></i>
                            </template>
                            {{ item.meta.title }}
                        </a-menu-item>
                    </a-menu>
                </a-layout-sider>
                <div class="layout_right">
                    <a-layout-sider class="layout_sider sider_two_level 1" :style="isAnnounceTop == true ? { marginTop: '44px' } : {}
                        " v-if="
                            customParams[1] && customParams[1] !== 'dashboard'
                        ">
                        <div class="layout_sider_header">
                            <SwitchingClass v-if="isClassManager" />
                        </div>
                        <ul v-if="route.query.isSystemApp === 'true'" class="yd_menu">
                            <li class="yd_menu_item" v-for="item in siderTwoLevelList" :key="item.name"
                                :class="{ active: state.twoLevelId == item.id }" @click="handerRouter(item)">
                                <i style="font-size: 24px" class="sider"
                                    :class="item.meta.icon || 'icon-zhirisheng'"></i>
                                <span> {{ item.meta.title }}</span>
                            </li>
                        </ul>

                        <a-menu v-else v-model:selectedKeys="state.selectedKeysTwo" v-model:openKeys="state.openKeysTwo"
                            mode="inline" :inlineIndent="30" :style="isclassManager ? menuStype : null">
                            <!--   :-->
                            <a-menu-item v-for="item in siderTwoLevelList" :key="item.name"
                                @click="handerTwoSelect(item)">
                                <template #icon>
                                    <i style="font-size: 24px; margin: 0" class="sider" :class="item.meta.icon"></i>
                                </template>
                                <span> {{ item.meta.title }}</span>
                            </a-menu-item>
                        </a-menu>
                    </a-layout-sider>

                    <a-layout :style="{
                        marginLeft:
                            customParams[1] &&
                                customParams[1] !== 'dashboard'
                                ? '200px'
                                : '',
                    }">
                        <a-layout-content class="layout_content_view">
                            <a-spin :spinning="$f">
                                <router-view class="router-view" v-slot="{ Component, route }">
                                    <transition enter-active-class="animate__animated animate__fadeIn">
                                        <!-- 
                                     :style="
                                                isAnnounceTop == true
                                                    ? { marginTop: '58px' }
                                                    : {}
                                            " -->
                                        <div class="view_content" :class="{
                                            active:
                                                route.name === 'dashboard',
                                        }">
                                            <component :is="Component" :key="comKey" />
                                        </div>
                                    </transition>
                                </router-view>
                            </a-spin>
                        </a-layout-content>
                    </a-layout>
                </div>
            </a-layout>
        </a-layout-content>
    </a-layout>
</template>

<script setup>
import { watch, computed, reactive, onBeforeMount, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useStore } from "vuex";
import { Local } from "@/utils/storage";
import Logo from "../Logo/index.vue";
import User from "../User/index.vue";
import SwitchingClass from "../SwitchingClass"; // 班主任的特殊选择班级菜单
import { ls } from "@/utils/util";
import config from "@/config/config";

const store = useStore();
const router = useRouter();
const route = useRoute();
const isClassManager = computed(() => {
    return route.path.indexOf("classManager") != -1;
});
const comKey = computed(() => {
    return route.path;
});
const isAnnounceTop = computed(() => {
    return store.state.base.isAnnounceTop;
});

const $f = !!window.$f;
const title = computed(() => {
    return store.state.user.currentSchool?.schoolName;
});
const customParams = computed(() => {
    if (route.meta.customParams) {
        return route.meta.customParams.split(".");
    }
    return [];
});
const icon = computed(() => {
    return store.state.user.currentSchool?.schoolLogo;
});
const state = reactive({
    tabsList: [],
    twoLevelId: "",
    isSwitchDorm: false,
    collapsed: true,
    switchclazz: false,
    openKeys: [],
    selectedKeys: [],
    openKeysTwo: [],
    selectedKeysTwo: [],
    siderOneLevelList: [],
    siderTwoLevelList: [],
});
const menuStype = shallowRef({
    overflow: 'hidden auto',
    height: 'calc(100% - 278px)'
});
const isclassManager = shallowRef(false);
watch(
    () => route.path,
    (val) => {
        if (customParams.value.length) {
            state.selectedKeys = customParams.value || [val.name];
            state.selectedKeysTwo = [customParams.value[1]];
        } else {
            const selectedKeys = val.split("/")[1];
            if (selectedKeys == "dashboard") {
                state.selectedKeys = ["index"];
            } else {
                if (selectedKeys == "app") {
                    state.selectedKeys = ["appModel", "app"];
                } else {
                    state.selectedKeys = [selectedKeys];
                }
            }
        }
        if (val.indexOf('/classManager') != -1) {
            isclassManager.value = true;
        } else {
            isclassManager.value = false;
        }
    },
    { immediate: true, deep: true }
);

const permission = computed(() => {
    return store.state.base.permission;
});
const siderTwoLevelList = computed(() => {
    let list = [];
    permission.value.forEach((i) => {
        if (i.name == customParams.value[0]) {
            list = i.children;
        }
    });
    return list.filter((i) => !i.hideInMenu);
});

// 集成系统菜单切换
const handerRouter = (item) => {
    const { id, name, systemCode, passId } = item;
    state.twoLevelId = id;
    router.push({
        name,
        query: {
            ...route.query,
            systemCode,
            passId,
        },
    });
};

// 一级菜单点击事件
const handerSelect = (item, bol) => {
    if (item.name == "chatAi") {
        const token = `Bearer ${ls.get(config.ACCESS_TOKEN)}`;
        window.open(`${import.meta.env.VITE_BASE_API_XIAOYIAI}/#/home?token=${token}`, "_blank");
    } else {
        router.push(item.redirect || item.path);
    }
};
// 二级菜单点击事件
const handerTwoSelect = (item) => {
    router.push(item.redirect || item.path);
};

onBeforeMount(() => {
    state.siderOneLevelList = permission.value.filter((i) => !i.hideInMenu);
});
</script>

<style scoped lang="less">
.layout {
    min-height: calc(100vh - 48px);
}

.header {
    height: 48px;
    display: flex;
    justify-content: space-between;
    padding: 0 24px;
}

.header_fixed {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    z-index: 999;
}

.page_container__warp {
    padding-top: 48px;
    min-height: 100%;
    box-sizing: border-box;
}

.layout_right {
    display: flex;
    flex: 1;
    margin-left: 60px;


}

:deep(.layout_sider) {
    .ant-menu {
        background: @gray-background;

        &:after,
        &:before {
            display: none;
        }

        .ant-menu-item {
            margin: 0;
            width: auto;
        }
    }

    &.sider_one_level {
        background: @gray-background;
        border-right: 1px solid @border-color-base;
        padding-top: 18px;
        position: fixed;
        top: 48px;
        left: 0;
        bottom: 0px;
        // 如果不想做整体移动 菜单栏又是固定fixed 定位 的那就直接把z-index调高
        z-index: 999; //要多大有多大算了

        .sider {
            font-size: 24px;
        }

        .ant-menu-item {
            border-radius: 4px;
            margin: 0 8px;
            padding: 0 9px;
        }

        .ant-menu-item-active {
            background-color: @acitve-background;
            color: @primary-color;
        }

        .ant-menu-item-selected {
            background-color: @primary-color;
            color: @body-background;

            .sider,
            .iconfont {
                color: @body-background;
            }
        }
    }

    &.sider_two_level {
        border-top: 1px solid @border-color-base;
        position: fixed;
        top: 48px;
        bottom: 0px;
        // 如果不想做整体移动 菜单栏又是固定fixed 定位 的那就直接把z-index调高
        z-index: 2;

        .sider {
            font-size: 26px;
        }

        .layout_sider_header {
            margin-bottom: 18px;
        }

        .ant-menu-item-selected {
            background-color: @gray-background;
            color: @primary-color;
            border-radius: 4px 0 0 4px;
        }

        .ant-menu {
            background: @body-background;

            &.ant-menu-inline {
                border: none;
            }

            .ant-menu-item {
                margin: 0;
                padding: 0;
                margin-left: 12px;

                &:after,
                &:before {
                    display: none;
                }
            }
        }

        .yd_menu {
            margin-left: 20px;

            .yd_menu_item {
                height: 40px;
                line-height: 40px;
                border-radius: 4px 0 0 4px;
                text-indent: 22px;
                cursor: pointer;
                font-size: 14px;
                display: flex;

                .iconfont {
                    width: 26px;
                }

                &.active {
                    background: @gray-background;
                    color: @primary-color;
                }
            }
        }
    }

    .ant-menu-item-selected::after {
        display: none;
    }


}

.layout_content_view {
    .view_content {
        margin: 12px;
        border-radius: 12px;
        background: @body-background;
        min-height: calc(100vh - 74px);
        min-width: 1194px;
        position: relative;
    }
}
</style>
