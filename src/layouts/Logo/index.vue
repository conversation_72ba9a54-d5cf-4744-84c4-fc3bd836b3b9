<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-24 11:02:14
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-06-28 18:00:58
-->
<template>
    <div class="logo_warp">
        <a-image class="logo_img" :src="icon" fallback="/image/uploadImg.png" :preview="false" />
        <a-dropdown :trigger="['click']" v-if="schoolList?.school?.length > 1">
            <a class="ant-dropdown-link" @click.prevent>
                <span class="name">{{ title }}</span>
                <DownOutlined style="color: var(--suggestive-color);font-size: 13px;" />
            </a>
            <template #overlay>
                <a-menu @click="openChange">
                    <a-menu-item class="menu-item" :class="{ active: schoolList.schoolId === item.id }"
                        v-for="item in schoolList?.school" :key="item">
                        <a-image class="logo_img" :src="item.schoolLogo" fallback="/image/uploadImg.png" :preview="false" />
                        <a href="javascript:;">{{ item.schoolName }}</a>
                    </a-menu-item>
                </a-menu>
            </template>
        </a-dropdown>
        <span class="name" v-else>{{ title }}</span>
    </div>
</template>

<script setup lang="ts" name='Logo'>
import { computed } from 'vue'
import { getCurrentUser, checkUserLogin } from '@/api/user'
import { useStore } from '@/store/index'
import { message } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import { Local } from '@/utils/storage.ts'
const store = useStore()
const route = useRoute()
const router = useRouter()

defineProps({
    title: {
        type: String
    },
    icon: {
        type: String
    }
})

const openChange = (event) => {
    const { id, isEnabled, children } = event.key
    if (schoolList.value.schoolId == id) { return }
    if (isEnabled !== undefined && !isEnabled) {
        return message.error('该学校被禁用')
    }
    if (children.length) {
        const { isEnabled } = children[0]
        if (!isEnabled) {
            return message.error('该教职工账号已停用')
        }
    }
    checkUserLogin({ schoolId: id }).then(async () => {
        store.commit('user/setCurrentSchool', event.key)
        Local.set('schoolId', id)
        if (route.params.error === 'Error: **********') {
        } else {
            window.sessionStorage.removeItem('accountFrom')
        }
        // 因为登录前的用户名拿的旧数据名称，跟登录后的用户名不相同，所以要重新调用个人用户信息接口获取最新的用户信息
        await getCurrentUser().then(({ data }) => {
            store.commit('user/setUser', data)
        })
        setTimeout(() => {
            location.href = '/'
            // router.push({ path: '/switchSchools' })
        }, 100)
    })
}

const schoolList = computed(() => store.state.user.userInfo);
</script>

<style scoped lang="less">
.logo_warp {
    display: flex;
    align-items: center;

    :deep(.logo_img) {
        margin-right: 16px;
        width: 38px;
        height: 38px;
        border-radius: 50%;
    }

    .name {
        margin-right: 10px;
        color: @heading-color;
        font-size: 16px;
        font-weight: 600;
    }

}
</style>


<style lang="less">
.menu-item {
    &.active {
        background-color: @primary-color;
        color: @body-background;
    }

    .logo_img {
        width: 25px;
        height: 25px;
        margin-right: 16px;
        border-radius: 50%;
    }
}
</style>

