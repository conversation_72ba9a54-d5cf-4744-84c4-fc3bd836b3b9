/* eslint-disable */
import { createRouter, createWebHashHistory } from "vue-router";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import axios from "axios";
import * as dd from "dingtalk-jsapi"; // 此方式为整体加载，也可按需进行加载
import config from "@/config/config";
import { ls, debounce } from "@/utils/util";
import { useStorage } from "@/utils/index";
import { store } from "../store";
import { constantRoutes, asyncRouterMap } from "@/config/router.configs.js";
import { Modal } from "ant-design-vue";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { createVNode } from "vue";

const { ACCESS_TOKEN, ACCESS_REFRESHTOKEN, ACCESS_DATA } = config;
const router = createRouter({
    history: createWebHashHistory(),
    routes: constantRoutes,
    // 每次切换路由的时候滚动到页面顶部
    scrollBehavior() {
        return { top: 0, left: 0 };
    },
});
const getRouterOne = (path) => {
    if (path.indexOf("appModel") > -1) {
        return path;
    }
    const get = (arr) => {
        let item = null;
        arr.forEach((i) => {
            if (i.path == path) {
                item = i;
            } else {
                if (i.children && !item) {
                    item = get(i.children);
                }
            }
        });
        return item;
    };
    const item = get(store.state.base.permission);
    if (item) {
        return item.path;
    } else {
        return store.state.base.permission[0].path;
    }
};
async function initRouters(Platform) {
    return new Promise<void>(async (resolve, reject) => {
        try {
            let permission: any = [];
            if (store.state.user.currentSchool.id) {
                permission = await store.dispatch("base/getUserRouters", {
                    schoolId: store.state.user.currentSchool.id,
                    Platform,
                });
            }
            // permission.push(appRoute)
            const map = asyncRouterMap[0];
            map.children = permission;
            router.addRoute(asyncRouterMap[1]);
            router.addRoute(map);
            setTimeout(() => {
                resolve();
            }, 1);
        } catch (e: any) {
            console.error(
                e?.data?.message || e?.data?.detailMessage || "获取用户菜单失败"
            );
            ls.remove(ACCESS_TOKEN);
            ls.remove(ACCESS_REFRESHTOKEN);
            ls.remove(ACCESS_DATA);
            throw new Error(e?.data?.code);
        }
    });
}
// 初始获取学校模板组件
async function initGetCommentKeys() {
    await store.dispatch(
        "base/getCommentKeys",
        store.state.user.currentSchool.id
    );
}
function initWebSocket() {
    store.dispatch("base/initWebSocket", {});
}

function verifyVersion() {
    return new Promise((relove, reject) => {
        axios
            .get("/version.json?v=" + new Date().getTime(), {
                headers: { "Cache-Control": "no-cache" },
                baseURL: window.location.origin,
            })
            .then((res) => {
                const clientVersion = document.querySelector(
                    "meta[name*='version']"
                );
                if (clientVersion) {
                    const isEquation =
                        res.data.version ===
                        Number(clientVersion.content || "");
                    relove(isEquation);
                } else {
                    // eslint-disable-next-line quotes
                    reject(
                        new Error('"<mate>" label version content not obtained')
                    );
                }
            })
            .catch(() => {
                reject(new Error("Failed to request version information"));
            });
    });
}

const checkVersion = debounce(async () => {
    const checkVersion = await verifyVersion();
    if (!checkVersion) {
        Modal.confirm({
            title: "温馨提示！",
            icon: createVNode(ExclamationCircleOutlined),
            content: "检测到最新版本，刷新后立即使用",
            okText: "立即刷新",
            closable: false,
            onOk() {
                window.location.reload();
            },
        });
    }
}, 2000);

function initBaseData(code?: string) {
    // 字典
    store.dispatch("selectSource/getDataDictionary");
    //  获取部门tree
    //  store.dispatch('base/getDepartmentTree',code)
    // 获取学籍tree
    //  store.dispatch('base/getSchoolRollTree',code)
}

router.beforeEach(async (to, from, next) => {
    // releaseQRCode家长放行二维码
    if (
        to.path.indexOf("son") != -1 ||
        to.path.indexOf("socialLogin") != -1 ||
        to.path.indexOf("releaseQRCode") != -1 
    ) {
        next();
        return false;
    }
    NProgress.start();

    if (ls.get(ACCESS_TOKEN)) {
        const path = getRouterOne(to.path);
        if (path == to.path) {
            next();
        } else {
            next({ path });
        }
    } else {
        if (to.path !== "/login") {
            next({ path: "/login" });
        }
        next();
    }
});
const { getItem } = useStorage();
router.afterEach((to) => {
    const documentTitle = getItem("documentTitle");
    document.title = `${documentTitle} - ${to.meta.title}`;
    NProgress.done();
    if (import.meta.env.MODE === "development") {
    } else {
        checkVersion();
    }
});
const initRouter = async () => {
    // const schoolId = Local.get("schoolId");
    //  加ls.get('schoolId')为了解决在登录时木有选选项然后刷新导致页面报错无法进入

    if (ls.get(ACCESS_TOKEN) && localStorage.getItem("schoolId")) {
        await initRouters("cloud");
        await initGetCommentKeys();
        initBaseData();
        return Promise.resolve(router);
    } else {
        // 判断是否是通行家长扫码放行链接进入 ，如果是就直接放行

        if (window.location.hash.indexOf("releaseQRCode") !== -1) {
            return Promise.resolve(router);
        }

        if (
            window.location.hash.indexOf("son") == -1 &&
            window.location.hash.indexOf("socialLogin") == -1
        ) {
            // 如果不是走授权登录就直接强制改回正常的登录路径
            let url = window.location.href;
            let queryString = "";
            if (url.indexOf("?") !== -1) {
                queryString = url.substring(url.indexOf("?"));
            }

            localStorage.clear();
            !!queryString
                ? (window.location.href = `/#/login${queryString}`)
                : (window.location.href = "/#/login");
        }
        // 如果是钉钉环境跳转进入钉钉登录
        if (dd.env && dd.env.platform !== "notInDingTalk") {
            if (window.location.hash.indexOf("ddLogin") == -1) {
                let url = window.location.href;
                let queryString = "";
                if (url.indexOf("?") !== -1) {
                    queryString = url.substring(url.indexOf("?"));
                }
                localStorage.clear();
                !!queryString
                    ? (window.location.href = `/#/ddLogin${queryString}`)
                    : (window.location.href = "/#/ddLogin");
            }
        }
        return Promise.resolve(router);
    }
};
export default initRouter;
