// 重置滚动条开始

::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
    height: 50px;
    outline-offset: -2px;
    outline: 1px solid #d9d9d9;
    -webkit-border-radius: 4px;
    border: 1px solid @body-background;
}

::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track-piece {
    // background-color: @body-background;
    background-color: transparent;
    -webkit-border-radius: 0;
}

::-webkit-scrollbar-thumb:active {
    height: 50px;
    /* background-color  : #e1244e; */
    -webkit-border-radius: 4px;
}

// 重置滚动条结束
.h100 {
    height: 100%;
}

.reset-layout-content,
.reset-layout-header {
    background-color: @body-background;
    padding: 0 20px;
}

.caption {
    color: @text-color;
    font-size: 14px;
    padding: 10px 0;
    font-weight: bolder;
}

.ant-btn > .anticon + span,
.ant-btn > span + .anticon {
    margin-left: 0;
}

// 重置为主题色
// 下拉框  级联
.ant-cascader-menu-item-active,
.ant-select-item-option-selected,
.ant-select-item-option-active {
    color: var(--primary-color) !important;
}

.ant-cascader-menu-item-active {
    background-color: transparent !important;
}

.ant-select-tree-treenode {
    padding: 5px 0 !important;
}

.ant-select-tree-treenode-selected {
    .anticon-caret-down {
        margin-top: 12px;
    }
}

// tree
.icon-yellow {
    font-size: 12px;
    color: @warning-color;
}

.ant-select-tree-treenode-selected,
.ant-tree-treenode {
    padding: 0 !important;
    width: 100%;
    position: relative;
}

.multi-function-item:hover,
.ant-select-tree-treenode:hover,
.ant-tree-treenode:hover {
    background-color: #f6f6f6ff !important;
}
// 一行自适应隐藏
.ant-select-tree-title,
.ant-tree-title {
    width: 0;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ant-select-tree-node-selected,
.ant-tree-node-content-wrapper,
.ant-tree-node-selected {
    background-color: transparent !important;
    padding: 5px 8px !important;
    display: flex;
    flex: 1;
}

.ant-tree-treenode-selected:hover {
    .handle_icon {
        display: inline-block;
    }
}

.ant-select-tree-node-selected {
    padding: 5px 4px !important;
}

.multi-function-item.active,
.ant-select-tree-treenode-selected,
.ant-tree-treenode-selected {
    background-color: @acitve-background !important;

    .ant-tree-node-selected {
        color: @primary-color;

        .icon-yellow {
            color: @primary-color;
        }

        .handle_icon {
            display: inline-block;
        }
    }
}

// 小三角
.ant-tree-switcher {
    width: 16px;
    text-align: right;
    line-height: 32px !important;
}

// 三点 样式
.handle_icon {
    position: absolute;
    right: 0;
    color: rgba(0, 0, 0, 0.65);
    display: none;
}

// 删除确认框样式
.ant-modal-confirm-btns {
    .ant-btn-dangerous {
        background: @primary-color;
        border-color: @primary-color;
        color: @body-background;
    }
}

.ant-modal-mask .ant-modal-body {
    padding: 20px;
}

.ant-empty {
    margin-top: 8%;

    .ant-empty-description {
        color: #000000a6;
    }
}

.demp_tree {
    padding: 24px 16px;
    border-right: 1px solid @border-color-base;
    box-sizing: border-box;
    width: 276px;
    // height: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

.help {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: -webkit-fill-available;
    color: @suggestive-color;
    position: absolute;
    bottom: 0;

    .anticon {
        margin-right: 5px;
    }
}

.ant-form-item-has-error ~ .help {
    bottom: -11px !important;
}

// a-row 最小宽度
.rowMinW {
    // min-width:1067px !important;
    overflow: hidden;
}

// table 操作
.worker-table {
    padding: 0 15px;

    a {
        color: @primary-color;
    }
}

.button-link {
    color: @primary-color;
    height: auto;
    line-height: inherit;
    padding: 1px 12px 0 0 !important ;
}

// textarea 数字样式
.ant-input-textarea-show-count {
    position: relative;

    &::after {
        position: absolute;
        right: 17px;
        bottom: 20px;
    }
}

// 树的左侧距离
.ant-tree-indent-unit,
.ant-select-tree-indent-unit {
    width: 10px;
}

// table 表头加圆角
.ant-table-container table > thead > tr:first-child th:first-child {
    border-top-left-radius: 10px;
}
.ant-table-container table > thead > tr:first-child th:last-child {
    border-top-right-radius: 10px;
}

.ant-table-thead {
    tr > th {
        background: @gray-background;
        font-weight: 700;
        &:not(:last-child):not(.ant-table-selection-column):not(
                .ant-table-row-expand-icon-cell
            ):not([colspan])::before {
            display: none;
        }
    }
}
.ant-table-filter-column {
    justify-content: initial;
}

.ant-table-column-title {
    flex: initial;
}

.tagsReset {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin: 0;
    padding: 0;
    margin-bottom: 2px;
}

// 分页样式
.ant-pagination-item {
    a {
        color: @text-color;
    }
}

.ant-pagination-item-active {
    background: @primary-color;

    a:hover,
    a {
        color: @body-background;
    }
}

.iconfont {
    font-size: 14px;
    margin-right: 4px;
}

// 表格中的table 下拉
.filter-content {
    .ant-radio {
        display: none;
    }

    .ant-radio-checked + span {
        color: @primary-color;
    }
}

.ant-select-selection-item {
    border: none !important;
    background: transparent !important;
}

.ant-layout-header,
.ant-layout-sider {
    background: @body-background;
}

.ant-picker-active-bar {
    display: none;
}

// 重置全局下拉菜单a-dropdown最大高度
.ant-dropdown-menu {
    max-height: 300px;
    overflow-y: auto;
}

// 应用中的返回
.left-outlined {
    color: @primary-color;
    margin-right: 5px;
    cursor: pointer;
}

.ant-picker-range-separator {
    color: @disabled-color;
}

// .ant-picker-input input {
//     text-align: center;
// }

.filter-group .ant-radio-wrapper:hover {
    background-color: @gray-background;
}

// layout布局
.yd-layout {
    background: @body-background;
    position: relative;
    overflow: hidden;

    .yd-layout-header {
        padding: 0 20px;
        height: 56px;
        line-height: 56px;
        border-bottom: 1px solid @border-color-base;

        .left-outlined {
            color: @primary-color;
        }
    }

    .yd-layout-content {
        padding: 20px;
        background: @body-background;

        .clicking {
            color: @text-color;

            &.clickingAfter {
                &:visited {
                    color: #666666;
                }
            }
        }
    }

    .yd-layout-content-pdbtm {
        padding-bottom: 62px;
    }

    .yd-layout-footer {
        position: absolute;
        bottom: 0;
        width: 100%;
        background: @body-background;
        border-top: 1px solid @border-color-base;
        padding: 14px;
    }
}
// 返回箭头
.icon-xingzhuangjiehe19 {
    color: @primary-color;
    cursor: pointer;
}

// 最高层级
// .ant-message{
//     z-index: 9999 !important;
// }
.xgplayer {
    height: 100% !important;
}

.mask {
    z-index: 999;
}

// 抽屉关闭按钮的位置重置
.ant-drawer-close {
    position: absolute;
    right: 0;
}

// 抽屉页脚的位置重置
.ant-drawer-footer {
    text-align: center;
}

// icon图 没有图片链接时用背景图 extend
.table_icon,
.icon_content {
    display: block;
    width: 100%;
    height: 100%;
    min-width: 48px;
    overflow: hidden;
    border-radius: 18px;
}

.table_icon {
    background-image: url(/image/iconbarn.png);
    background-position-x: -9px;
    background-position-y: -13px;
    background-size: initial;
    background-repeat-x: no-repeat;
    background-repeat-y: no-repeat;
    background-attachment: initial;
    background-origin: initial;
    background-clip: initial;
    margin-right: 10px;
    text-align: center;
    display: inline-block;
    position: relative;
    cursor: pointer;
    // font-size: x-large;
    font-size: 24px;
    color: @body-background;
    vertical-align: middle;
    display: flex;
    justify-content: center;
    align-items: center;
}
.tree_icon {
    margin-right: 6px;
}
// 3.0
// 自适应高
.adaptive_high {
    height: calc(100vh - 85px);
    // background: @body-background;
    // border-radius: 20px;
    // margin:0 12px;
}
// form 块级中的行内
.form_inline {
    flex-direction: initial !important;
    align-items: baseline;
    position: relative;
    margin: 0 !important;
    height: 50px;
    .ant-form-item-explain {
        position: absolute;
        bottom: -15px;
        left: -80px;
    }
}
// 禁用背景颜色
.ant-radio-disabled .ant-radio-inner,
.ant-checkbox-disabled .ant-checkbox-inner,
.ant-picker.ant-picker-disabled,
.ant-input[disabled],
.ant-select-multiple.ant-select-disabled.ant-select:not(
        .ant-select-customize-input
    )
    .ant-select-selector,
.ant-select-disabled.ant-select:not(.ant-select-customize-input)
    .ant-select-selector,
.ant-input-number-disabled {
    background: @gray-background;
}
// 3.0
.layout_content_view {
    .layout-header-radius {
        border-radius: 20px 20px 0 0;
    }
}
.ant-drawer-title {
    font-weight: 600;
}
.administratorName {
    position: relative;

    .administratorName_clos {
        width: 20px;
        height: 20px;
        line-height: 20px;
        position: absolute;
        top: 7px;
        right: 14px;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.25);
        z-index: 9999;
    }
}
.visitor_pd16 {
    padding: 20px 16px !important;
}
// Iframe
.permission {
    border-radius: 10px;
    overflow: hidden;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}

.input_group_wrapper {
    .input_left {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        border-right: 0;
        width: calc(50% - 15px);
    }

    .input_right {
        border-left: 0;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        width: calc(50% - 15px);
    }

    .c_icon {
        width: 30px;
        height: 32px;
        text-align: center;
        font-size: 18px;
        color: #bfbfbf;
        pointer-events: none;
        border-top: 1px solid #d9d9d9;
        border-bottom: 1px solid #d9d9d9;
    }
}
.tips {
    font-size: 12px;
    color: @text-color-secondary;
}
// table表头中的筛选箭头
.ant-table-column-sorter {
    color: @text-color;
}
