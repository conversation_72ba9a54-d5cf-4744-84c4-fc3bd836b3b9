/*
 * @Descripttion:api
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-06-21 10:32:36
 */
const api = {
    // 检查当前用户是否已申请注销
    checkUserLogout: '/cloud/user/checkUserLogout',
    // 撤销用户注销
    cancelLogout: '/cloud/user/cancelLogout',
    // 获取学校配置
    getConfigList: '/cloud/school/config/list',
    // 更新学校配置
    upConfigList: '/cloud/school/config/updater',
    // 通用导出模板（导入中的下载模版）
    generalExportUrl: '/cloud/common/export/template',
    // 通用导出模板（导出中的下载模版）
    generalExceltUrl: '/cloud/common/export/excel',
    // 通用导入模板
    generalImportUrl: '/cloud/common/import',
    // 中性版接口
    getWebsiteConfig: '/cloud/manage/website/config/getInfo',
    // 学籍进度条
    progressUrl: '/cloud/common/import/progress',
    // 异常下载
    importErrorLog: '/cloud/common/export/importErrorLog',
    // 留言管理
    leaveWordList: '/cloud/leaveWord/leaveWordAdmin', // 留言列表
    updateWord: '/cloud/leaveWord/update', // 回复（修改）留言
    delWord: '/cloud/leaveWord/delete', // 删除留言
    delreply: '/cloud/leaveWord/deleteReply', // 删除留言回复
    getLeaveWordSetting: '/cloud/leaveWord/getLeaveWordSetting', // 根据类型获取留言匿名设置
    updateLeaveWordSetting: '/cloud/leaveWord/updateLeaveWordSetting', // 设置留言匿名设置

    login: '/auth/oauth/token', // 登录
    getCurrentUser: '/cloud/user/getCurrentUser', // 获取用户信息
    checkUserLogin: '/cloud/menu/checkUserLogin', // 校验用户是否可以登录
    getRouters: '/cloud/menu/getRouters',
    qrcode: '/auth/qrcode/gene', // 获取二维码
    check: '/auth/qrcode/check', // 获取轮询的二维码

    // 财务报表
    classifyList: '/cloud/finance/doc/classify/list', // 分类列表
    classifyDel: '/cloud/finance/doc/classify/delete', // 删除分类
    classifyEdit: '/cloud/finance/doc/classify/update', // 编辑分类
    classifyCreate: '/cloud/finance/doc/classify/create', // 创建分类
    documentList: '/cloud/finance/doc/page', // 文档列表
    documentCreate: '/cloud/finance/doc/create', // 新增文档
    documentDel: '/cloud/finance/doc/delete', // 删除文档(批量)
    documentMove: '/cloud/finance/doc/update', // 移动文档（批量）

    // 通用文件上传
    uploadFile: '/cloud-web/admin-file/upload', // 通用文件上传
    uploadProgress: '/cloud/import/getSchedule', // 上传进度
    exportErrorData: '/cloud/import/exportErrorData', // 下载导入错误数据

    teachClassesList: '/cloud/v3/classes/teachClassesList', // 任教老师-获取班级

    // 教职工管理
    facultyList: '/cloud/employee/page', // 教职工列表
    createFaculty: '/cloud/employee/create', // 添加教职工
    facultyUpdate: '/cloud/employee/update', // 编辑教职工
    facultyDelete: '/cloud/employee/delete', // 删除教职工
    facultyDetails: '/cloud/employee/get', // 教职工详情
    facultyAdjust: '/cloud/employee/adjust', // 教职工调整部门
    employeeListByDept: '/cloud/dept/getEmployeeListByDeptId', // 获取本部门员工
    employeeInvite: '/cloud/employee/invite', // 邀请成员（教师）
    studentInvite: '/cloud/student/invite', // 邀请成员（学生or家长）
    employeeCutTime: '/cloud/employee/cutTime', // 切换过期时间

    // 外部人员
    // externalFaceInfo: '/cloud/external/faceInfo', // 获取人员人脸
    // externalGroupList: '/cloud/external/groupList', // 外部人员组列表
    // externalDeletePage: '/cloud/external/page', // 外部人员分组
    // externalPages: '/cloud/external/page', // 外部人员分组
    // new外部人员组
    externalSearch: '/cloud/external/externalSearch', // 外部人员搜索
    externalGroupNumberList: '/cloud/externalGroup/externalGroupNumberList', // 外部人员组列表(带成员数量)
    createExternalGroup: '/cloud/externalGroup/create', // 添加外部人员组
    updateExternalGroup: '/cloud/externalGroup/update', // 编辑外部人员组
    deleteExternalGroup: '/cloud/externalGroup/delete', // 删除外部人员组
    externalPage: '/cloud/external/externalPage', // 外部人员列表
    addExternalPersonnel: '/cloud/external/addExternalPersonnel', // 添加外部人员
    externalDeleteExternal: '/cloud/external/deleteExternal', // 删除外部人员
    updateExternalExternal: '/cloud/external/updateExternal', // 编辑外部人员
    getExternalDetail: '/cloud/external/getExternalDetail', // 查询外部人员详情
    batchAddExternalPersonnel: '/cloud/external/batchAddExternalPersonnel', // 批量添加外部人员

    // 组织架构 部门
    admineptDoList: '/cloud/app/dept/list', // 组织架构tree
    admineptDoSearch: '/cloud/dept/searchDeptUserList', // 部门搜索
    admineptDoCreate: '/cloud/dept/create', // 部门添加
    admineptDoEdit: '/cloud/dept/update', // 部门修改
    admineptDoDelete: '/cloud/dept/delete', // 部门删除
    admineptUpdateDeptSort: '/cloud/dept/updateDeptSort', // 部门排序

    // 学籍
    // 大学生毕业
    graduation: '/cloud/study/graduation',
    schoolRoll: '/cloud/app/roll/listTree', // 学籍tree
    schoolRollv1: '/cloud/app/roll/v1/listTree', // 学籍tree
    schoolRollDetails: '/cloud/roll/getInfo', // 获取学籍详情
    schoolRollTemplate: '/cloud/roll/getRollTemplate', // 学籍模板初始化
    updateRollSort: '/cloud/roll/updateRollSort', // 学籍拖拽
    schoolRollCreateTemplate: '/cloud/roll/createRollTemplate', // 保存学籍模板
    schoolRollCreate: '/cloud/roll/create', // 新增学籍
    schoolRollUpdate: '/cloud/roll/update', // 修改学籍
    schoolRollDelete: '/cloud/roll/delete', // 删除学籍
    schoolRollPerson: '/cloud/roll/updateLeader', // 学籍设置负责人
    rollSelectData: '/cloud/roll/getRollSelectData', // 学籍下拉框数据
    rollUpload: '/cloud/employee/upload', // 教职工导入文件上传
    rollImportEmployee: '/cloud/employee/importEmployee', // 教职工数据导入
    rollExportEmployee: '/cloud/employee/export', // 教职工-导出
    deptUpload: '/cloud/dept/upload', // 部门导入文件上传
    deptImport: '/cloud/dept/import', // 部门数据导入
    statistics: '/cloud/student/page/statistics', // 学生列表-信息统计
    getSubjectTeacherId: '/cloud/teacherSubject/getEmployeeBySubjectId', // 根据科目获取相关的任课老师
    getRollAcademics: '/cloud/roll/getRollAcademics', // 获取K12的学段信息
    // 获取学校模板组件
    getCommentKey: '/cloud/school/template/getCommentKey',
    getJsonCommentKey: '/cloud/school/template/getJsonCommentKey',
    // 学生列表
    pageStudent: '/cloud/student/page', // 学生列表
    studentDetails: '/cloud/student/getInfo', // 查看学生详情
    createStudent: '/cloud/student/create', // 新增学生
    updateStudent: '/cloud/student/update', // 编辑学生
    studentelternDelete: '/cloud/eltern/delete', // 删除家长信息
    studentelternCreate: '/cloud/eltern/create', // 新增/更新家长信息
    deleteStudent: '/cloud/student/delete', // 删除学生
    studentClass: '/cloud/student/chanage/class', // 学生转班
    studentlistRollTree: '/cloud/study/listRollTree', // 学生毕业树
    studentlistSearch: '/cloud/student/search', // 学生毕业树

    studentUpload: '/cloud/student/upload', // 学生导入文件上传
    studentImportEmployee: '/cloud/student/importStudent', // 学生导入
    studentExportEmployee: '/cloud/student/exportStudent', // 学生导出
    teacherImportEmployee: '/cloud/roll/upload', // 任课老师导入
    teacherExportEmployee: '/cloud/roll/exportTeacher', // 任课老师导出

    // 升学
    studentStudyCheck: '/cloud/study/getInfo', // 学生升学查看
    studentCreate: '/cloud/study/create', // 学生升学添加修改
    studentGraduation: '/cloud/student/graduation', // 学生毕业
    studentRetirement: '/cloud/student/chanage/dropout', // 学生退学，休学
    studentTransfer: '/cloud/student/chanage/transfer', // 学生转学

    // 角色
    listRoleType: '/cloud/roleType/list', // 角色列表
    getUserIdByRoleId: '/cloud/role/getUserIdByRoleId',  // 根据角色id获取用户id

    // 查询数据字典
    dataDictionary: '/cloud/SystemDict/get', // select 数据
    areaList: '/cloud/area/list', // select 数据

    // 应用中心
    appCenterList: '/cloud/appCenter/list', // 应用中心——列表
    appCenterGei: '/cloud/appCenter/get', // 应用中心——详情
    appCenterUpdateById: '/cloud/appCenter/updateById', // 应用中心——停用启用
    updateDataScope: '/cloud/appCenter/updateDataScope', // 应用中心——可见范围
    appGroupList: '/cloud/appGroup/list', // 应用中心——权限组列表
    createAppGroup: '/cloud/appGroup/create', // 应用中心——添加权限组
    updateAppGroup: '/cloud/appGroup/update', // 应用中心——编辑权限组
    seeAppGroup: '/cloud/appGroup/getInfo', // 应用中心——回显权限组
    deleteAppGroup: '/cloud/appGroup/delete', // 应用中心——删除权限组

    // 角色管理
    roleTypeList: '/cloud/roleType/list', // 角色管理——侧边栏
    roleCreateRole: '/cloud/role/create', // 角色管理——新增角色
    roleTypeCreate: '/cloud/roleType/create', // 角色管理——新增角色类别
    roleTypeUpdate: '/cloud/roleType/update', // 角色管理——编辑
    roleTypeDelete: '/cloud/roleType/delete', // 角色管理——角色分类删除
    roleDeleteUserRole: '/cloud/role/deleteUserRole', // 角色成员——删除
    roleCreateUserRole: '/cloud/role/createUserRole', // 角色成员——添加用户
    dataUpdate: '/cloud/data/update', // 数据权限——设置

    // 获取权限资源
    menuList: '/cloud/menu/getMenuList', // 数据权限——设置
    updateRoleMenu: '/cloud/role/updateRoleMenu', // 分配功能权限
    dataGetData: '/cloud/data/getData', // 数据权限——列表
    // 角色管理——功能权限

    // 作息表
    routineList: '/cloud/routine/routinePage', // 作息表——表格数据
    createRoutine: '/cloud/routine/createRoutine', // 作息表——新增作息表
    deleteRoutine: '/cloud/routine/deleteRoutine', // 作息表——批量删除
    detailsRoutine: '/cloud/routine/routineInfo', // 信息表——编辑回显作息表
    updateRoutine: '/cloud/routine/updateRoutine', // 作息表——编辑修改作息表

    // 历史员工
    historyEmployeePage: '/cloud/history/employeePage', // 历史员工-教师
    historyYearList: '/cloud/history/yearList', // 学生学年列表
    historyStudentPage: '/cloud/history/studentPage', // 学生列表
    historyClassesList: '/cloud/history/classesList', // 班级列表
    historyRecoveryTeacher: '/cloud/history/recovery/teacher', // 教职工复员
    historyRecoveryStudent: '/cloud/history/recovery/student', // 学生复学
    historyEmployeeDept: '/cloud/history/employee/dept', // 老师
    historyStudentDept: '/cloud/history/student/dept', // 学生

    // 用户管理
    userManagementList: '/cloud/user/page', // 用户列表
    resetPassword: '/cloud/user/resetPassword', // 密码重置
    updatePhone: '/cloud/user/updatePhone', // 用户修改手机号
    userUnlock: '/cloud/user/unlock', // 用户解锁
    // 个人中心——个人信息
    userupdate: '/cloud/user/updateCurrentUser', // 个人中心——用户更新
    // 个人中心——人脸采集
    faceInformation: '/cloud/face/faceInformation', // 个人中心——人脸采集上传
    getFace: '/cloud/face/get', // 个人中心——人脸采集回显
    uploadFace: '/cloud/face/uploadFace', // 个人中心——单个人脸采集
    // 修改密码
    updatePassword: '/cloud/user/updatePassword',

    // 学校介绍
    selectIntroduce: '/cloud/introduce/selectIntroduce', // 学校介绍——获取数据
    updateIntroduce: '/cloud/introduce/update', // 学校介绍——编辑

    // 科目信息表
    subjectListNoPag: '/cloud/subject/subjectList', // 科目信息-不分页
    queryTeacherSubject: '/cloud/v3/classes/queryTeacherSubject', // 科目信息-不分页
    subjectList: '/cloud/subject/subjectPage', // 科目信息表——表格数据
    createSubject: '/cloud/subject/createSubject', // 科目信息表——新增数据
    deleteSubject: '/cloud/subject/deleteSubject', // 科目信息表——删除数据
    updateSubject: '/cloud/subject/updateSubject', // 科目信息表——编辑数据
    getSubjectInfo: '/cloud/subject/get', // 科目信息表——回显数据
    importSubject: '/cloud/site/importSite', // 场地管理——场地批量导入
    uploadSubject: '/cloud/site/upload', // 场地管理——场地批量导入
    teacherSubjectList: '/cloud/teacherSubject/page', // 科目信息——任课老师
    updateTeacherSubject: '/cloud/teacherSubject/update', // 科目信息——编辑任课老师
    echoTeacherSubject: '/cloud/teacherSubject/get', // 科目信息——回显任课老师

    // 学年学期
    yearList: '/cloud/year/list', // 学年学期——列表学年
    addYear: '/cloud/year/create', // 学年学期——添加学年
    updateYear: '/cloud/year/update', // 学年学期——编辑学年
    strikeOutYear: '/cloud/year/delete', // 学年学期——删除学年
    semesterList: '/cloud/semester/page', // 学年学期——列表学期
    addSemester: '/cloud/semester/create', // 学年学期——添加学期
    updateStatus: '/cloud/semester/updateStatus', // 学年学期——修改学期状态
    getInfo: '/cloud/semester/getInfo', // 学年学期——学期回显
    updateSemester: '/cloud/semester/update', // 学年学期——编辑学期
    deleteSemester: '/cloud/semester/delete', // 学年学期——删除学期

    // 设备管理——班牌（人脸库）
    classCardList: '/cloud/v2/equipment/page', // 班牌设备——班牌列表
    createClassCard: '/cloud/v2/equipment/create', // 班牌设备——添加设备
    getOne: '/cloud/v2/equipment/get', // 班牌设备——回显设备
    updateClassCard: '/cloud/v2/equipment/update', // 班牌设备——编辑设备
    deleteClassCard: '/cloud/v2/equipment/delete', // 班牌设备——删除设备
    equipmentType: '/cloud/v2/equipment/type/list', // 班牌设备——设备类型
    getBrandDominateScreen: '/brand/brand/getBrandDominateScreen', // 班牌设置——霸屏管理
    deviceList: '/cloud/v2/equipment/page', // 班牌设备列表
    // getRecordPage: '/cloud/faceSync/getRecordPage', // 人脸同步列表
    machineList: '/cloud/v2/machine/page', // 考勤机 人脸机设备列表
    syncAll: '/cloud/faceSync/sync', // 同步全部人脸(同步单个人脸)
    syncv2All: '/cloud/faceSync/v2/sync', // 班级德育同步全部人脸
    clearFace: '/cloud/faceSync/sync/clear', // 同步人脸（清空）
    bindFaceInfo: '/cloud/faceDevice/V3/bindFaceInfo', // 关联人脸库
    getFaceInfo: '/cloud/face/facePage', // 获取自定义组人员信息

    // 场地管理
    buildingList: '/cloud/site/cloudBuildingList', // 场地管理——建筑栋楼列表
    // 大学版
    // buildingSchoolBuildScene: '/cloud/site/listSchoolBuildScene', // 场地管理——学校建筑-场景列表数据 // 被后端改掉了用下面的
    buildingSchoolBuildScene: '/cloud/site/listPassscene', // 场地管理——学校建筑-场景列表数据

    buildingLists: '/cloud/site/buildingList', // 场地管理——建筑栋楼列表
    getCampusList: '/cloud/site/rollList', // 场地管理——校区列表
    createBuilding: '/cloud/site/createBuilding', // 场地管理——建筑栋楼添加
    updateBuilding: '/cloud/site/updateBuilding', // 场地管理——建筑栋楼编辑
    deleteBuilding: '/cloud/site/deleteBuilding', // 场地管理——建筑栋楼删除
    updateBuildingManager: '/cloud/site/updateBuildingManager', // 场地管理——楼栋管理员
    sitePage: '/cloud/site/sitePage', // 场地管理——场地列表
    siteDetails: '/cloud/site/getSite', // 场地管理——场地-详情
    createSite: '/cloud/site/createSite', // 场地管理——场地添加
    updateSite: '/cloud/site/updateSite', // 场地管理——场地编辑
    siteDevicePage: '/cloud/siteDevice/page', // 场地设备分页
    siteDeviceList: '/cloud/siteDevice/list', // 场地设备列表
    siteDeviceCreate: '/cloud/siteDevice/create', // 新增场地设备
    siteDeviceDelete: '/cloud/siteDevice/delete', // 删除场地设备
    siteDeviceUpdate: '/cloud/siteDevice/update', // 编辑场地设备
    siteTypeList: '/cloud/site/siteTypeList', // 场地管理——场地类型列表
    siteTypePage: '/cloud/site/siteTypePage', // 场地管理——场地类型分页
    createSiteType: '/cloud/site/createSiteType', // 场地管理——场地类型添加
    updateSiteType: '/cloud/site/updateSiteType', // 场地管理——场地类型编辑
    deleteSite: '/cloud/site/deleteSite', // 场地管理——场地删除
    deleteSiteType: '/cloud/site/deleteSiteType', // 场地管理——场地类型删除
    updateTurnSite: '/cloud/site/updateTurnSite', // 场地管理——场地转场
    importSite: '/cloud/site/importSite', // 场地管理——场地批量导入
    uploadSite: '/cloud/site/upload', // 场地管理——场地批量导入
    createBatchSite: '/cloud/site/createBatchSite', // 场地管理——场地批量添加
    updateBatchSite: '/cloud/site/updateBatchSite', // 场地管理——场地批量编辑
    siteBookingType: '/cloud/siteBookingType/page', // 场地预约——场地预约类型管理
    siteTypeCreate: '/cloud/siteBookingType/create', // 场地预约——新增场地预约类型
    buildingSiteList: '/cloud/siteBookingType/buildingSiteList', // 场地预约——获取建筑场地
    sitePages: '/cloud/siteBooking/pageSchoolSiteBooking', // 场地预约——获取场地分页
    siteTypeDetail: '/cloud/siteBookingType/get', // 场地预约——场地预约类型详情
    editSiteType: '/cloud/siteBookingType/update', // 场地预约——编辑场地预约类型
    delSiteType: '/cloud/siteBookingType/delete', // 场地预约——删除场地预约类型
    createApply: '/cloud/siteBooking/create', // 场地预约——新增场地预约申请
    getBookingSiteNums: '/cloud/siteBookingType/getBookingSiteNum', // 获取地预约类型列表及预约数量
    getYesBookingSiteNum: '/cloud/siteBookingType/getYesBookingSiteNum', // 统计场地预约类型当前的可预约的场地数量
    myParticipatePage: '/cloud/siteBooking/myParticipatePage', // 我参与的预约分页
    getfreeSiteInfo: '/cloud/site/getSite', // 详情
    myBookingPage: '/cloud/siteBooking/myBookingPage', // 我申请的预约分页
    checkApproved: '/cloud/siteBookingType/checkApproved', // 判断场地类型是否封禁
    bookingUpdate: '/cloud/siteBooking/update', // 编辑（审核）场地预约申请
    bookingDetail: '/cloud/siteBooking/get', // 预约详情
    bookingSignPage: '/cloud/siteBooking/bookingSignPage', // 签到表分页
    bookingRecordPage: '/cloud/siteBooking/bookingRecordPage', // 预约记录
    bookingApprovalPage: '/cloud/siteBooking/bookingApprovalPage', // 预约审核分页
    freeSite: '/cloud/siteBooking/freeSiteBookingPage', // 场地闲置分页
    dateOccupyList: '/cloud/siteBooking/dateOccupyList', // 占用时间

    // 设备——人脸机
    barrierSync: '/cloud/faceSync/barrierSync', // 人脸同步-同步人脸（人脸机）
    restartFace: '/cloud/v2/equipment/device/restart', // 人脸重启
    renewFace: '/cloud/v2/equipment/device/updateAppVersion', // 人脸更新
    siteList: '/cloud/v2/machine/site/list?type=2', // 人脸机——场地列表
    synchronousFace: '/cloud/v2/equipment/device/synchronousFaceInfo', // 同步设备信息
    pageList: '/cloud/v2/machine/page', // 人脸机列表
    createMachine: '/cloud/v2/machine/create', // 添加人脸机
    detailMachine: '/cloud/v2/machine/detail', // 回显人脸机
    updateMachine: '/cloud/v2/machine/update', // 编辑人脸机
    deleteMachine: '/cloud/v2/machine/delete', // 删除人脸机
    resetBarrierPassword: '/cloud/v2/machine/resetBarrierPassword', // 修改闸机密码
    queryBarrierRuleList: '/cloud/v2/machine/queryBarrierRuleList', // 获取闸机规则

    // 课表管理
    timetableExist: '/cloud/timetable/exist', // 课表的场地列表
    timetableWeekTime: '/cloud/timetable/getWeekTime', // 每周课表
    deleteTimetable: '/cloud/timetable/deleteTimetable', // 清除课表
    timetableExport: '/cloud/timetable/export', // 课表数据导出
    timetableImport: '/cloud/timetable/import', // 课表数据导入
    timetableUpload: '/cloud/timetable/upload', // 课表文件上传
    timetableSemester: '/cloud/semester/getSemesterList', // 课表学期
    timetableRoutineBySemesterId: '/cloud/routine/getRoutineBySemesterId', // 课表作息表
    newGetRoutineList: '/cloud/routine/getRoutineList', // 课表作息表

    // 人脸库
    getFacePageList: '/cloud/face/facePage',
    getFaceCustomGroup: '/cloud/faceGroup/list',
    createFaceGroup: '/cloud/faceGroup/create',
    updateFaceGroup: '/cloud/faceGroup/update',
    removeFaceGroup: '/cloud/faceGroup/delete',
    sendFaceMessage: '/cloud/face/send',
    addCustomPeople: '/cloud/face/addCustomPeople',
    deleteCustomPeople: '/cloud/face/deleteCustomPeople',
    createFace: '/cloud/face/createFace',
    getNoCollectFaceNumber: '/cloud/face/number',

    // 图片上传
    fileUpload: '/cloud/file/upload',
    // fileUpload: ' /file/common/fileupload',

    // 校历
    saveSchoolCalendar: '/cloud/calendar/create', // 校历新增
    getSemesterAndSchoolYear: '/cloud/calendar/query', // 校历备注列表
    // importCalendarEvent: "management/cloud-system/school/calendar/importCalendarEvent", // 校历导入
    removeSchoolCalendar: '/cloud/calendar/delete', // 校历删除
    editSchoolRemark: '/cloud/calendar/update', // 编辑校历
    uploadCalendarFile: '/cloud/calendar/upload', // 上传图片
    schoolCalendarImport: '/cloud/calendar/schoolCalendarImport', // 校历导入

    // 通行规则
    listPassSite: '/cloud/v3/rule/schoolPassscene/listPassSite', // 侧边栏
    getSiteList: '/cloud/v2/machine/site/list', // 场地
    pageRuleInfo: '/cloud/v3/rule/schoolPasssceneRule/pageRuleInfo', // 列表
    createPasssceneRule:
        '/cloud/v3/rule/schoolPasssceneRule/createPasssceneRule', // 添加规则
    getHolidayInfo: '/cloud/v2/barrier/rules/getHolidayInfo', // 获取法定节假日
    getPassRuleDetailById:
        '/cloud/v3/rule/schoolPasssceneRule/getPassRuleDetailById', // 获取规则详情
    updatePassRuleInfo: '/cloud/v3/rule/schoolPasssceneRule/updatePassRuleInfo', // 更新规则
    deletePassRuleInfo: '/cloud/v3/rule/schoolPasssceneRule/deletePassRuleInfo', // 删除规则
    updateRuleIsEnableInfo:
        '/cloud/v3/rule/schoolPasssceneRule/updateRuleIsEnableInfo', // 启用禁用规则
    sceneCreate: '/cloud/app/scene/create', // 创建场景应用
    scenePage: '/cloud/app/scene/page', // 场景应用列表
    sceneDelete: '/cloud/app/scene/delete', // 删除场景应用
    sceneUpdate: '/cloud/app/scene/update', // 修改场景应用
    sceneDetails: '/cloud/app/scene/get', // 场景应用详情
    sceneSystemApp: '/cloud/app/scene/listSystemApp', // 集成到应用

    // 请假
    procGrouList: '/cloud/procGrou/list', // 审批组列表
    procPage: '/cloud/proc/page', // 审批应用列表
    procUpdate: '/cloud/proc/update', // 请假审批流程-编辑
    procGetInfo: '/cloud/proc/getInfo', // 审批详情
    procUserAppr: '/cloud/userAppr/page', // 审批记录
    exportProcUserApp: '/cloud/userAppr/export', // 审批记录导出
    procUpdateStatus: '/cloud/proc/updateStatus', // 改变状态
    procUserApprGetInfo: '/cloud/userAppr/getInfo', // 审批记录详情
    selectProcNameList: '/cloud/proc/selectProcNameList', // 审批类型集合

    // 日程
    scheduleList: '/cloud/schedule/selectMainScheduleList', // 日程列表
    createSchedule: '/cloud/schedule/createSchedule', // 日程新增修改
    getScheduleDetail: '/cloud/schedule/getScheduleDetail', // 日程详情
    changeScheduleState: '/cloud/schedule/changeScheduleState', // 更新日程状态
    copySchedule: '/cloud/schedule/copySchedule', // 复制日程
    deleteSchedule: '/cloud/schedule/deleteSchedule', // 删除日程

    // 标签管理
    scheduleLabel: '/cloud/schedule/getLabel', // 查询标签列表
    scheduleDdeleteLabel: '/cloud/schedule/deleteLabel', // 删除标签
    scheduleLCreateLabelBatch: '/cloud/schedule/createLabelBatch', // 添加、编辑标签

    // 标签设置
    scheduleSetting: '/cloud/schedule/getScheduleSetting', // 查询个人设置
    createScheduleSetting: '/cloud/schedule/createScheduleSetting', // 修改添加个人设置

    // 待办
    // todoPage: '/cloud/v2/proc/pageTask', // 待办列表 2.0
    todoPage: '/cloud/ruTask/page', // 待办列表3.0
    changeActivityState: '/cloud/proc/changeActivityState', // 回退
    completeTask: '/cloud/proc/completeTask', // 同意/拒绝
    todoGetInfo: '/cloud/userAppr/v2/getInfo', // 审核详情
    todoGetInfoOld: '/cloud/userAppr/getInfo', // 审核详情old
    todoPatrolDetail: '/cloud/patrol/taskSite/get', // 巡查详情
    todoVisitorDetail: '/app/visitor/records/get', // 访客详情

    // 学生考勤
    createStudentAttendance: '/attweb/attendance/createAttendance',
    deleteStudentAttendance: '/attweb/attendance/deleteAttendance',
    queryStudentAttendance: '/attweb/attendance/pageAttendance',
    getAttendanceDeviceList: '/attweb/attendance/getDeviceList',
    getAttendanceInfo: '/attweb/attendance/getAttendance',
    updateStudentAttendance: '/attweb/attendance/updateAttendance',

    // 学生课程考勤
    queryStudentClassroomAttendance: '/attweb/attendance/pageCourse',
    getSClassroomAttendanceConfig: '/attweb/attendance/courseSetting',
    updateSClassroomAttendanceConfig: '/attweb/attendance/updateCourseSetting',
    updateSClassroomAttendanceSubject: '/attweb/attendance/updateCourse',

    // 信息发布
    informationDelivery: '/cloud/mess/publish/create', // 发布信息
    informationUpdateDelivery: '/cloud/mess/publish/update', // 编辑发布信息
    informationDeliveryList: '/cloud/mess/publish/page', // 信息列表
    informationDeliveryListAll: '/cloud/mess/publish/page/all', // 所有发布
    informationDeliveryDelete: '/cloud/mess/publish/delete', // 删除信息
    informationDeliveryUpdateTop: '/cloud/mess/publish/updateTop', // 置顶/取消置顶
    informationDeliveryRetract: '/cloud/mess/publish/updateRetract', // 撤回信息
    informationDeliveryReceive: '/cloud/mess/receive/page', // 所有信息
    informationDeliveryDetails: '/cloud/mess/publish/getInfo', // 我发布的信息详情页
    informationDeliveryReceiveDetails: '/cloud/mess/receive/info', // 我收到的信息详情页
    updateIncrementconfirms: '/cloud/mess/receive/updateIncrementconfirms', // 我收到的信息详情确认
    informationNotifyInfo: '/cloud/mess/publish/notifyInfo', // 通知部门/班级列表
    notifyEmployees: '/cloud/mess/publish/notifyEmployees', // 通知人员-部门
    notifyElterns: '/cloud/mess/publish/notifyElterns', // 通知人员-班级
    notifyUnconfirms: '/cloud/mess/publish/notifyUnconfirms', // 提醒未确认人员
    buildingMachine: '/cloud/v2/machine/buildingMachine', // 按场地查询班牌
    schoolRollBrand: '/cloud/v2/machine/schoolRollBrand', // 按学籍查询班牌
    schoolDevice: '/cloud/mess/publish/device', // 查询已发送设备
    schoolmessTypeLablelist: '/cloud/mess/lable/messTypeLablelist', // 模板类型列表
    checkDeviceType: '/cloud/mess/publish/checkDeviceType', // 是否展示设备类型
    getDeviceTreeV3: '/cloud/mess/publish/getDeviceTreeV3', // 获取设备列表

    // 一体机
    integratedMchineCreate: '/cloud/mess/publish/create', // 一体机画屏
    integratedMchineTree: '/cloud/v2/machine/getAioMachineTree', // 一体机设备树形列表

    // 图库海报
    messLableTemplateLimit: '/cloud/mess/template/messLableTemplateLimit', // 图库海报模板分类列表
    messLableTemplateHome: '/cloud/mess/template/index', // 图库海报模板首页
    messLableTemplateMore: '/cloud/mess/template/page', // 图库海报模板更多
    messLableTemplateDetails: '/cloud/mess/template', // 图库海报模板详情页
    updateStartDominateScreen: '/cloud/mess/publish/updateStartDominateScreen', // 开启霸屏
    updateStopDominateScreen: '/cloud/mess/publish/updateStopDominateScreen', // 结束霸屏

    // 通行统计
    getUserPermissionInformation:
        '/cloud/v2/pass/web/getUserPermissionInformation', // 获取班级和年级
    queryWebPassData: '/cloud/v2/pass/web/queryWebPassData', // 获取统计
    siteListData: '/cloud/v3/rule/schoolPassscene/listPassSite', // 统计场景

    // 通行记录
    pageAccessRecordsInfo: '/cloud/v2/web/barrier/pageAccessRecordsInfo', // 通行记录列表
    importAccessRecordsExcel:
        '/cloud/v2/web/barrier/access/exportAccessRecordsExcel', // 获取导出的文件

    // 通行权限配置
    selectRuleDataList: '/cloud/appGroup/selectRuleDataList', // 权限列表
    ruleDataUpdate: '/cloud/appGroup/update', // 添加、编辑
    ruleDataInfo: '/cloud/appGroup/getInfo', // 回显
    getPassSite: '/cloud/v3/rule/schoolPassscene/getPassSite', // 学校名称

    // 教师考勤排班
    attWorkList: '/attweb/attendanceWorkScheduling/list', // 考勤排班列表
    attWorkInfo: '/attweb/attendanceWorkScheduling/get', // 考勤排班详情
    addAttWork: '/attweb/attendanceWorkScheduling', // 考勤排班新增
    upAttWork: '/attweb/attendanceWorkScheduling/update', // 考勤排班修改
    delAttWork: '/attweb/attendanceWorkScheduling/delete', // 考勤排班删除

    // 教师考勤组
    attendanceGroupAll: '/attweb/attendanceWorkGroup/findAll', // 查询所有考勤组列表
    attendanceGroup: '/attweb/attendanceWorkGroup/list', // 教师考勤组列表
    attendanceGroupInfo: '/attweb/attendanceWorkGroup/get', // 教师考勤组详情
    attendanceGroupAdd: '/attweb/attendanceWorkGroup', // 新增教师考勤组
    attendanceGroupUpdate: '/attweb/attendanceWorkGroup/update', // 修改教师考勤组
    attendanceGroupDel: '/attweb/attendanceWorkGroup/delete', // 删除教师考勤组
    schoolAttendanceScheduling: '/attweb/attendanceWorkScheduling/list', // 考勤排班列表
    getAttendanceDevice: '/attweb/attendanceWorkGroup/getAttendanceDevice', // 查询考勤机
    getWorkOvertimeRuleBySchoolId: '/attweb/attendanceWorkRule/getWorkOvertimeRuleBySchoolId', // 根据学校id获取加班规则
    updateCardReplacementRule: '/attweb/attendanceWorkRule/updateCardReplacementRule', // 更新补卡规则
    getCardReplacementRuleBySchoolId: '/attweb/attendanceWorkRule/getCardReplacementRuleBySchoolId', // 根据学校id获取补卡规则
    updateWorkOvertimeRule: '/attweb/attendanceWorkRule/updateWorkOvertimeRule', // 更新加班规则

    // 教师考勤统计
    attByDay: '/attweb/attendanceWorkAnalysis/byDay', // 每日统计
    attByMonth: '/attweb/attendanceWorkAnalysis//byMonth', // 每月统计
    attCustomize: '/attweb/attendanceWorkAnalysis//byFreedom', // 自定义统计
    correctDayData: '/attweb/attendanceWorkAnalysis/correctDayData', // 纠正每日统计

    // 同步人脸机
    getDeviceList: '/cloud/faceSync/getDeviceList', // 左边固定的侧边栏
    getRecordPage: '/cloud/faceSync/getRecordPage', // 人脸同步
    bindFace: '/cloud/faceDevice/V2/bindFaceInfo', // 班牌关联人脸库
    brand: '/cloud/faceSync/brand', // 同步接口
    getBindDeviceFaceInfo: '/cloud/faceDevice/V2/getBindDeviceFaceInfo', // 获取设备绑定人脸信息

    attExportByMonth: '/attweb/attendanceWorkAnalysis/byMonth/export', // 每月统计导出
    attExportByDay: '/attweb/attendanceWorkAnalysis/byDay/export', // 每日统计导出

    // 考勤规则
    setAttendanceGroup: '/attweb/attendanceWorkRule/setAttendanceGroup', // 考勤规则设置考勤组
    getRuleInfo: '/attweb/attendanceWorkRule/get', // 考勤规则详情
    getRuleList: '/attweb/attendanceWorkRule/list', // 考勤规则列表

    // 考勤明细
    pageDataDetail: '/attweb/eventStatistical/pageDataDetail', // 考勤明细分页
    dataDetailCount: '/attweb/eventStatistical/dataDetailCount', // 考勤明细统计

    // 考勤统计(图表)
    statisticalMonth: '/attweb/eventStatistical/statisticalMonth', // 月统计
    statisticalDay: '/attweb/eventStatistical/statisticalDay', // 日统计
    statisticalDayDetail: '/attweb/eventStatistical/statisticalDayDetail',

    // 首页
    getTeacherTimetable: '/cloud/app/timetable/getTeacherTimetable', // 我的课表
    todaySchedule: '/cloud/schedule/todaySchedule', // 今日日程
    getClassesByUserId: '/cloud/roll/getClassesByUserId', // 班级下拉框
    getAppHome: '/cloud/appCenter/getAppHome', // 常见应用
    addAppHome: '/cloud/appCenter/addApp', // 新增常见应用
    deleteAppHome: '/cloud/appCenter/deleteApp', // 删除常见应用
    getSelectSequence: '/attweb/eventStatistical/selectSequence', // 节次下拉框
    getAttendanceHome: '/attweb/eventStatistical/attendanceHome', // 课堂考勤统计
    getGradeList: '/cloud/app/timetable/getClassList', // 获取班级年级

    // 班牌
    classInfo: '/cloud/app/roll/getClassesInfo', // 班级信息
    getSloganList: '/brand/classes/watchword/list', // 班级口号列表
    delSloganList: '/brand/classes/watchword/delete', // 删除班级口号
    setSloganList: '/brand/classes/watchword/setting', // 设置班级口号
    createClassSign: '/brand/brand/create', // 增加班牌
    updateClassSign: '/brand/brand/update', // 设置班牌
    batchSet: '/brand/brand/switchs/update', // 批量设置定时开关机
    longRestart: '/brand/brand/switchs/restart', // 批量远程重启
    longclose: '/brand/brand/switchs/close', // 批量远程关机
    switchsLock: '/brand/brand/switchs/lock', // 批量开关锁
    classModeUpdate: '/brand/brand/switchs/classModeUpdate', // 上课模式
    ClassSignList: '/brand/brand/page', // 班牌列表
    getBrandInfo: '/brand/brand/getBrand', // 班牌信息
    getUnSite: '/brand/brand/unbinding/device/site', // 未创建班牌账号的场地
    getSite: '/brand/brand/binding/device', // 场地班牌列表
    getBindingSiteTree: '/brand/brand/getBindingSiteTree', // 获取绑定场地的班牌账号
    getBindingSiteTreeeV2: '/brand/brand/getDeviceTreeV2', // 获取绑定场地的班牌账号
    getRemindList: '/brand/remind/list', // 提醒-列表
    createClassRemind: '/brand/remind/create', // 提醒-创建
    delClassRemind: '/brand/remind/delete', // 删除提醒
    classRemindInfo: '/brand/remind/detail', // 提醒详情
    setClassesIcon: '/brand/classes/upload/icon', // 班级头像上传
    getLayoutList: '/brand/layoutclasssign/template/list', // 班牌布局首页模板列表
    getLayoutModuleList: '/brand/layoutclasssign/module/list', // 班牌布局添加编辑页模块列表
    creatLayout: '/brand/layoutclasssign/template/create', // 班牌布局模板创建
    getClassifyList: '/brand/layoutclasssign/classify/list', // 班牌布局添加编辑页分类列表
    classLayoutInfo: '/brand/layoutclasssign/templateView', // 班牌布局编辑页获取详情
    delClassTemplate: '/brand/layoutclasssign/template/delete', // 班牌布局编辑页获取详情
    updateClassTemplate: '/brand/layoutclasssign/template/update', // 班牌布局编辑页获取详情
    publishClassTemplate: '/brand/layoutclasssign/template/update', // 发布到班牌
    setBrandDominateScreen: '/brand/brand/setBrandDominateScreen', // 设置霸屏模式
    customPage: '/brand/custom/mode/page', // 自定义模式
    customUpdate: '/brand/custom/mode/status/update', // 更新自定义模式启停状态
    createPage: '/brand/custom/mode/create', // 添加自定义模式
    createDel: '/brand/custom/mode/delete', // 删除自定义模式
    editUpdate: '/brand/custom/mode/update', // 编辑自定义模式
    getSignImageList: '/brand/layout/background/image/list', // 获取背景图
    getModeCreate: '/brand/v2/custom/mode/create', // 自定义霸屏
    getModePage: '/brand/v2/custom/mode/page', // 自定义霸屏模式分页
    getModeDelete: '/brand/v2/custom/mode/delete', // 自定义霸屏模式删除
    getModeStatusUpdate: '/brand/v2/custom/mode/status/update', // 自定义霸屏模式更新状态
    getModeinfo: '/brand/v2/custom/mode/get', // 详情
    getModeupdate: '/brand/v2/custom/mode/update', // 编辑
    getLevelList: '/brand/v2/custom/mode/levelList', // 获取一下可以用的优先级
    // 开门记录
    openDoorPage: '/brand/openDoor/records/page', // 分页开门记录
    openDoorCreate: '/brand/openDoor/records/create', // 新增开门记录

    // 学生作品
    studentworkPage: '/brand/studentwork/page', // 列表
    studentworkCreate: '/brand/studentwork/create', // 创建
    studentworkDelete: '/brand/studentwork/delete', // 删除
    studentdutyList: '/brand/studentduty/list', // 获取值日生列表
    studentdutyCreate: '/brand/studentduty/create', // 创建值日生
    studentdutyProjectList: '/brand/studentduty/project/list', // 值日项目列表
    studentdutyProjectDel: '/brand/studentduty/project/delete', // 删除值日项目
    studentdutyDel: '/brand/studentduty/delete', // 删除值日生
    projectIconList: '/brand/studentduty/project/icon/list', // 值日项目icon
    dutyProjectCreate: '/brand/studentduty/project/create', // 创建值日项目
    albumClassifyList: '/brand/album/classify/list', // 相册视频分类列表
    deleteIds: '/brand/v3/album/classify/deleteIds', // 批量删除相册
    albumClassifyCreate: '/brand/album/classify/create', // 创建视频分类
    albumClassifyUpdate: '/brand/album/classify/update', // 编辑视频分类
    albumPage: '/brand/album/page', // 视频列表
    albumClassifyDel: '/brand/album/classify/delete', // 删除分类
    albumCreate: '/brand/album/create', // 创建视频
    albumDelete: '/brand/album/delete', // 删除视频
    updateAlbumClassify: '/brand/album/updateClassify', // 移动分组
    albumUpdateIsShow: '/brand/album/updateIsShow', // 是否显示
    countdownCreate: '/brand/countdown/create', // 添加倒计时
    countdownList: '/brand/countdown/list', // 倒计时列表
    countdownDelete: '/brand/countdown/delete', // 倒计时删除
    updateIsHome: '/brand/album/updateIsHome', // 设为首页视频

    // 打卡
    punchClockTemplateList:
        '/attweb/attendanceSignTask/querySignTaskTemplateList', // 打卡任务模板列表
    addPpunchTheClock: '/attweb/attendanceSignTask/add', // 添加打卡任务
    updatePpunchTheClock: '/attweb/attendanceSignTask/update', // 编辑打卡任务
    deletePpunchTheClock: '/attweb/attendanceSignTask/delete', // 删除打卡任务
    punchTheClockList: '/attweb/attendanceSignTask/list', // 打卡任务列表
    punchTheClockDetails: '/attweb/attendanceSignTask/get', // 打卡任务详情
    punchTheClockReport: '/attweb/attendanceSignTaskReport/report', // 打卡任务总览
    punchTheClockDeptReport: '/attweb/attendanceSignTaskReport/deptReport', // 打卡任务总览部门
    punchTheClockClassReport: '/attweb/attendanceSignTaskReport/classReport', // 打卡任务总览班级
    punchTheClockPages: '/attweb/attendanceSignTaskReport/deptOrClassPage', // 打卡任务总览班级部门-分页

    // 班牌自定义模块
    customizeClassifyCreate: '/brand/layoutclasssign/customize/classify/create', // 添加模块
    customizeClassifyDel: '/brand/layoutclasssign/customize/classify/delete', // 删除模块
    layoutclasssignClassifyList:
        '/brand/layoutclasssign/customize/classify/list', // 获取模板列表
    customizeModuleCreate: '/brand/layoutclasssign/customize/module/create', // 创建模块内容
    customizeModuleDel: '/brand/layoutclasssign/customize/module/delete', // 删除模块内容
    layoutclasssignModuleList: '/brand/layoutclasssign/module/list', // 模块列表
    customizeModuleUpdate: '/brand/layoutclasssign/customize/module/update', // 编辑模块内容
    // 问卷
    questionnaireList: '/csq/informationReport/list', // 学校、教职工、家长上报列表
    questionnaireEmployee: '/csq/informationReport/employeePage', // 教职工上报重新列表
    questionnaireEmployeeDetail: '/csq/informationReport/employeeDetailPage', // 教职工查看详情列表
    questionnaireElternPage: '/csq/informationReport/elternPage', // 家长上报重新列表
    questionnaireElternDetailPage: '/csq/informationReport/elternDetailPage', // 家长查看详情列表
    questionnaireCreateemployeeOreltern:
        '/csq/informationReport/createEmployeeOrEltern', // 提交上报教职工或家长
    questionnaireCreateschool: '/csq/informationReport/createSchool', // 上报学校信息
    questionnaireSchooldetails: '/csq/informationReport/schoolDetails', // 学校查看详情
    questExportEmployeeOrEltern:
        '/csq/informationReport/exportEmployeeOrEltern', // 导出信息不全的人员

    // 学生评价
    createAssessment: '/cloud/evaluationGroup/create', // 创建评比组
    createAssessmentlist: '/cloud/evaluationGroup/list', // 评比组列表
    createAssessmentActivity: '/cloud/evaluationActivity',
    updateAssessmentActivity: '/cloud/evaluationActivity/update',
    getAssessmentActivityList: '/cloud/evaluationActivity/list',
    withdrawAssessmentActivity: '/cloud/evaluationActivity/withdraw',
    publishAssessmentActivity: '/cloud/evaluationActivity/publish',
    getAssessmentActivityInfo: '/cloud/evaluationActivity/detail',
    removeAssessmentActivity: '/cloud/evaluationActivity/delete',
    getProgressList: '/cloud/evaluationActivity/progress', // 活动详情
    queryActivityCycle: '/cloud/evaluationActivity/queryActivityCycle', // 查看活动周期
    scoringDetail: '/cloud/evaluationActivity/scoringDetail', // 查看打分明细
    progressDetail: '/cloud/evaluationActivity/identityDetail', // 活动详情打分情况 （班级） 优化后
    // progressDetail: "/cloud/evaluationActivity/classDetail", // 活动详情打分情况 （班级）
    // 学生评价结束

    // 学生评价 - 学生评价指标设置
    getAssessmentCategoryList: '/cloud/evaluationCategory/list',
    getAssessmentRuleList: '/cloud/evaluationRule/list',
    getAssessmentMedalList: '/cloud/evaluationMedal/list',

    createAssessmentCategory: '/cloud/evaluationCategory',
    createAssessmentRule: '/cloud/evaluationRule',
    createAssessmentMedal: '/cloud/evaluationMedal',

    removeAssessmentCategory: '/cloud/evaluationCategory/delete',
    removeAssessmentRule: '/cloud/evaluationRule/delete',
    removeAssessmentMedal: '/cloud/evaluationMedal/delete',

    getMedalDefaultList: '/cloud/evaluationLogo/list',
    //
    getAssessmentMedalAllList: '/cloud/evaluationMedal/activityMedalList',
    getAssessmentRuleAllList: '/cloud/evaluationRule/activityRuleList',
    getAssessmentCategoryAllList: '/cloud/evaluationCategory/listAll',

    // 荣誉墙接口
    addHonorWall: '/cloud/honor/create', // 添加荣誉墙
    upHonorWall: '/cloud/honor/update', // 编辑荣誉墙
    honorList: '/cloud/honor/page', // 荣誉墙列表
    honorWallInfo: '/cloud/honor/get', // 荣誉墙详情
    delHonorWall: '/cloud/honor/delete', // 删除荣誉墙

    // 考试管理
    getGradeTree: '/cloud/exam/baseGradeTree', // 获取年级树
    getSubList: '/cloud/exam/baseSubjectList', // 获取科目信息
    getBuildList: '/cloud/exam/baseBuildingList', // 获取楼栋列表
    getCloudBuildList: '/cloud/site/cloudBuildingList', // 获取楼栋列表树
    getThisSiteList: '/cloud/exam/baseClassesSiteList', // 获取本班排位列表
    getExamPageList: '/cloud/exam/page', // 考试列表分页
    delExam: '/cloud/exam/deleteById', // 删除考试
    startExam: '/cloud/exam/examStart', // 开启考试
    getExamInfo: '/cloud/exam/getBaseInfo', // 获取考试基础信息
    getExamDetInfo: '/cloud/exam/getDetailsInfo', // 获取考试设置信息
    createUnified: '/cloud/exam/createUnified', // 新增统一考试时间
    updateUnified: '/cloud/exam/updateUnified', // 修改统一考试时间
    createSeparate: '/cloud/exam/createSeparate', // 新增按年级设置考试时间
    updateSeparate: '/cloud/exam/updateSeparate', // 修改按年级设置考试时间
    // 考务
    getExamGradeList: '/cloud/examManage/gradeList', // 考试年级列表（搜索条件）
    getSubjectInvigilatorList: '/cloud/examManage/subjectInvigilatorList', // 科目监考老师列表
    exportSubjectInvigilator: '/cloud/examManage/exportSubjectInvigilator', // 导出科目监考老师列表
    siteInvigilatorPage: '/cloud/examManage/siteInvigilatorPage', // 场地监考老师分页
    exportSiteInvigilator: '/cloud/examManage/exportSiteInvigilator', // 导出场地监考老师
    getExamSiteList: '/cloud/examManage/siteList', // 考试考场列表（搜索）
    getExamClassesList: '/cloud/examManage/classesList', // 获取班级列表(搜索)
    getExamSeatingPage: '/cloud/examManage/examSeatingPage', // 考场座位表分页
    exportExamSeating: '/cloud/examManage/exportExamSeating', // 导出考试座位表分页
    examSitePage: '/cloud/examManage/examSitePage', // 考场信息分页
    exportExamSite: '/cloud/examManage/exportExamSite', // 导出考场信息表
    examSeatStickers: '/cloud/examManage/examSeatStickers', // 考场座位贴
    exportExamSeatStickers: '/cloud/examManage/exportExamSeatStickers', // 导出座位贴
    getsubjectList: '/cloud/examManage/subjectList', // 考试科目列表（搜索）
    examSignInPage: '/cloud/examManage/examSignInPage', // 签到表分页
    exportExamSignIn: '/cloud/examManage/exportExamSignIn', // 导出签到表

    // 登录日志
    logList: '/cloud/systemOperationLog/loginPage',
    operationLogList: '/cloud/systemOperationLog/operationPage',
    operationModule: '/cloud/systemOperationLog/moduleList',
    downloadTemplate: '/cloud/timetable/downloadTemplate',

    // 投票
    createVote: '/cloud/vote/create',
    updateVote: '/cloud/vote/update',
    getVoteInfo: '/cloud/vote/get',
    deleteVote: '/cloud/vote/delete',
    voteList: '/cloud/vote/page',
    countVoteOptionList: '/cloud/vote/countVoteOption',
    getVoteQuantityList: '/cloud/vote/voteOptionCountNumPage',
    exportVote: '/cloud/vote/export',
    updateVoteStatus: '/cloud/vote/updateVoteStatus',
    getVoteQRCode: '/cloud/vote/getVoteQRCode',

    // 借还书机
    getBorrowBooksInfo: '/cloud/v2/machine/schoolBorrowReturnMachine',

    // 3.0班级管理的api
    getEventList: '/attweb/attendance/v3/getEventList',
    getDayStatisticsPage: '/attweb/attendance/v3/dayStatisticsPage',
    getdayStatistics: '/attweb/attendance/v3/dayStatistics',
    getdateStatistics: '/attweb/attendance/v3/dateStatistics',
    getdateStatisticsPage: '/attweb/attendance/v3/dateStatisticsPage',
    getdateStatisticsPageDetail:
        '/attweb/attendance/v3/dateStatisticsPageDetail',
    getClassesDetail: '/cloud/v3/classes/getClassesDetail',
    getClassesList: 'brand/classes/watchword/list',
    getClassesSet: '/brand/classes/watchword/setting',
    schoolList: '/cloud/v3/mess/receive/classMasterPage', // 学校通知列表
    getClassList: '/cloud/v3/mess/publish/classPage', // 班级通知列表
    // 班级荣誉
    getclassCreate: '/cloud/v3/honor/classCreate',
    getclassMasterHonorPage: '/cloud/v3/honor/classMasterHonorPage',
    getclassUpdate: '/cloud/v3/honor/classUpdate',
    getclassHonorCount: '/cloud/v3/honor/classHonorCount',
    getclassHonorPage: '/cloud/v3/honor/classHonorPage',

    // 我的学生api
    getclassStudentPage: '/cloud/v3/student/classStudentPage',
    getsearchstudent: '/cloud/student/search', // 根据班级id查学生的接口

    // 获取班主任的班级信息
    getqueryClassMaterList: '/cloud/v3/classes/queryClassMaterList',
    getgetClassesDetail: '/cloud/v3/classes/getClassesDetail',

    // 班主任首页
    getclassMasterNotice: '/cloud/v3/classes/classMasterNotice', // 班主任重要通知'
    queryClassesInfoWindowList: '/cloud/v3/classes/queryClassesInfoWindowList', // 班主任重要通知
    getmasterHome: '/brand/v3/studentDuty/master/home', // 值日生
    getmasterhomepage: '/attweb/attendance/v3/master/homepage', // 首页考勤的数据
    getv3WeekTime: '/cloud/timetable/getWeekTime', // 班主任每周课表
    v3classCreate: '/cloud/v3/mess/publish/classCreate', // 班主任班级通知添加
    getV3classPage: '/cloud/v3/mess/publish/classPage', // 班主任班级通知添加
    getV3Template: '/cloud/mess/template/page', // 班主任 班级模板
    viewTemplate: '/cloud/mess/template', // 预览
    targetComponent: '/cloud/moralEducationActivityPC/targetComponent', // 班主任首页德育评价组件

    // 任教管理
    getteachClassesList: '/attweb/teach/statistical/teachClassesList', // 查任课老师有几个班级
    gettimetableDay: '/attweb/teach/statistical/timetableDay', // 考勤统计-日统计头部
    exportDayStatistics: '/attweb/attendance/v3/exportDayStatistics', // 导出
    gettimetableDayPage: '/attweb/teach/statistical/timetableDayPage', // 考勤统计-日统计分页
    gettimetableDate: '/attweb/teach/statistical/timetableDate', // 考勤统计-周月统计头部
    gettimetableDatePage: '/attweb/teach/statistical/timetableDatePage', // 考勤统计-周月分页统计
    gettimetableDatePageDetail:
        '/attweb/teach/statistical/timetableDatePageDetail', // 考勤统计-周月分页统计详情分页
    getClassStudentPage: '/cloud/v3/student/classStudentPage', // 查任课老师学生
    queryClassTeachList: '/cloud/v3/classes/queryClassTeachList', // 任课老师首页-班级信息
    classTeacherNotice: '/cloud/v3/classes/classTeacherNotice', // 任课老师重要通知
    todayTimeByUserId: '/cloud/timetable/todayTimeByUserId', // 获取老师今天的课表
    getExaminationPage: '/cloud/exam/teach/homepage', // 任教管理首页-考试
    teacherClassPublishPCTask:
        '/attweb/attendanceSignTask/pc/teacherClassPublishPCTask', // 首页获取今天的打卡任务
    tehomepage: '/attweb/teach/statistical/homepage', // 任教管理获取考勤
    teacherTodayWork: '/cloud/v3/work/teacherTodayWork', // 获取今天发布的作业

    // 巡查
    projectPage: '/cloud/patrol/project/page', // 计划分页
    projectStatusStop: '/cloud/patrol/project/status/update', // 启用停用计划
    projectDeleteTask: '/cloud/patrol/task/cancelTask', // 启用停用计划
    projectPlanDetails: '/cloud/patrol/project/get', // 查看计划详情
    projectTaskInfo: '/cloud/patrol/task/getTaskInfo', // 获取任务信息
    projectDelete: '/cloud/patrol/project/delete', // 删除计划
    projectFormSetting: '/cloud/patrol/project/form/setting', // 设置反馈表单
    projectUpdate: '/cloud/patrol/project/update', // 编辑计划
    projectUserSetting: '/cloud/patrol/project/user/setting', // 执行人员
    projectSiteSetting: '/cloud/patrol/project/site/setting', // 设置场地
    recordPage: '/cloud/patrol/task/record/page', // 分页巡查记录
    projectTaskPage: '/cloud/patrol/task/page', // 分页巡查任务
    projectTypeList: '/cloud/patrol/type/list', // 分页巡查
    projectCancelTask: '/cloud/patrol/task/cancelTask', // 任务作废
    projectTypeDelete: '/cloud/patrol/type/delete', // 删除分类
    projectTypeCreate: '/cloud/patrol/type/create', // 新增分类
    patrolPermission: '/cloud/patrol/project/permission', // 获取巡查路由权限
    projectTypeUpdate: '/cloud/patrol/type/update', // 编辑分类
    queryDefaultApp: '/cloud/patrol/type/queryDefaultApp', // 查询学校是否开通默认巡查应用
    initDefaultApp: '/cloud/patrol/type/initDefaultApp', // 学校开通默认巡查应用
    listNoSingleApp: '/cloud/app/scene/listNoSingleApp', // 学校开通默认巡查应用

    // 写一手 智慧宿舍的接口
    getpageDormPass: '/cloud/dorm/pass/pageDormPass', // 通行统计分页
    getDetail: '/cloud/dorm/pass/getDetail', // 查看具体寝室详情
    getqueryWebPassData: '/cloud/v2/pass/web/queryWebPassData', // 查看具体寝室统计列表数据
    getpageAccessRecordsInfo: '/cloud/v2/web/barrier/pageAccessRecordsInfo', // 通行记录接口

    createPassscene: '/cloud/site/createPassscene', // 新增场景
    pagePassscene: '/cloud/site/pagePassscene', // 场景的分页接口
    updatePassscene: '/cloud/site/updatePassscene', // 修改场景
    deletePassscene: '/cloud/site/deletePassscene', // 删除场景
    listPassscene: '/cloud/site/listPassscene', //  获取学校场景列表接口
    buildNextStep: '/cloud/dormitory/buildingManagement/nextStep', //  批量设置场地 下一步
    buildNextadd: '/cloud/dormitory/buildingManagement/add', //  批量设置场地 保存
    // 访客系统
    visitorCreate: '/cloud/visitor/setting/global/create', // 访客机全局设置
    visitorDetails: '/cloud/visitor/setting/global/get', // 获取访客机全局设置信息
    visitorRecordsList: '/cloud/visitor/records/page', // 分页访客记录
    visitorRecordsStatistics: '/cloud/visitor/records/statistics', // 访客统计数据
    // 分组班委
    classCommitteeAdd: '/cloud/v3/class/committee/classCommitteeAdd', // 添加班委
    classCommitteeUpdate: '/cloud/v3/class/committee/classCommitteeUpdate', // 修改班委
    classCommitteeDel: '/cloud/v3/class/committee/classCommitteeDel', // 删除班委
    classCommitteedetail: '/cloud/v3/class/committee/detail', // 班委详情
    classCommitteeBrowseList: '/cloud/v3/class/committee/classCommitteeBrowseList', // 班委一览表
    exportCommitteeBrowseList: '/cloud/v3/class/committee/exportCommitteeBrowseList', // 班委一览表
    classCommitteePage: '/cloud/v3/class/committee/classCommitteePage', // 班委列表分页
    classgroupPage: '/cloud/v3/class/group/page', // 小组列表分页
    classgroupAdd: '/cloud/v3/class/group/add', // 添加分组
    queryGroupDetail: '/cloud/v3/class/group/queryGroupDetail', // 小组详情
    queryGroupUpdate: '/cloud/v3/class/group/update', // 修改小组
    queryGroupDel: '/cloud/v3/class/group/del', // 删除小组
    // 任教管理的作业
    teWorkPage: '/cloud/v3/work/page', // 作业列表分页
    createTeWork: '/cloud/v3/work/create', // 发布作业
    schoolWorkWithdraw: '/cloud/v3/work/schoolWorkWithdraw', // 撤回作业
    deleteWork: '/cloud/v3/work/delete', // 删除作业
    queryWorkInClassList: '/cloud/v3/work/queryWorkInClassList', // 根据当前作业id 查询 到这个作业发布到哪几个班级了
    getSchoolClassWork: '/cloud/v3/work/getSchoolClassWork', // 根据班级id 查这个作业的详情
    classMasterLatestNotice: '/cloud/v3/classes/classMasterLatestNotice', // 班主任滚动条通知
    classTeacherLatestNotice: '/cloud/v3/classes/classTeacherLatestNotice', // 任教滚动条通知
    updateVideoIsPush: '/brand/album/updateVideoIsPush', // 视频推信息窗口
    // 会议 设备管理
    listMeetingBuilding: '/cloud/meetingSystem/listMeetingBuilding', // 获取建筑下的场地
    updateMeetingPassRuleSetting: '/cloud/meetingSystem/updateMeetingPassRuleSetting', // 提交会议机通行规则设置
    getMeetingPassRuleSetting: '/cloud/meetingSystem/getMeetingPassRuleSetting', // 获取会议机通行规则设置

    brandAuthority: '/cloud/brandAuthority/create', // 班牌权限新增
    brandAuthorityPage: '/cloud/brandAuthority/page', // 班牌权限分页
    brandAuthorityUp: '/cloud/brandAuthority/update', // 班牌权限修改
    brandAuthorityDe: '/cloud/brandAuthority/delete', // 班牌权限删除

    createStudentduty: '/brand/v3/studentduty/create', // 值日生创建
    updateStudentduty: '/brand/v3/studentduty/update', // 值日生修改
    deStudentduty: '/brand/v3/studentduty/deleteInfo', // 值日生删除
    listStudentduty: '/brand/v3/studentduty/list', // 值日生列表
    // homeStudentduty: '/brand/v3/studentDuty/master/home' // 值日生首页

    // 教务首页的一些接口
    getRegistrarSemesterHome: '/cloud/semester/getRegistrarSemesterHome', // 学年概述
    buildingCountHome: '/cloud/site/buildingCountHome', // 统计建筑数量
    appCenterGetAppHome: '/cloud/appCenter/getAppHome', // 常用应用
    registrarHomepage: '/cloud/exam/registrar/homepage', // 考试列表
    getDeptEmployeeCount: '/cloud/dept/getDeptEmployeeCount', // 获取部门老师的数量
    getRollInfoCount: '/cloud/v3/classes/getRollInfoCount', // 获取统计学籍学生数量
    statisticalEventList: '/attweb/registrar/statistical/getEventList', // 获取学生考勤事件组
    dayStatisticStudentHome: '/attweb/registrar/statistical/dayStatisticStudentHome', // 获取学生考勤事件统计
    getWorkGroup: '/attweb/attendanceWorkGroup/list', // 获取教师考勤分组
    dayStatisticTeacherHome: '/attweb/attendanceWorkAnalysis/dayStatisticTeacherHome', // 获取教师考勤统计详情
    listByCode: '/cloud/appCenter/listByCode', // 根据应用的code 查询这个应用的信息
    teachSubjectClassList: '/cloud/v3/classes/teachSubjectClassList', // 这个是用来用科目id 查询这个科目下面的班级列表的数据,作业/任务都可以用这个获取
    classRegistrarReceivePage: '/cloud/v3/classes/classRegistrarReceivePage', // 教务的通知




    // 考场管理
    createExamSiteManage: '/cloud/examSiteManage/create', // 创建考场
    getexamSiteManagePage: '/cloud/examSiteManage/page', // 考场管理里面的分页接口
    batchUpdateExamSiteManage: '/cloud/examSiteManage/batchUpdate', // 批量修改容量 布局这些信息
    deleteExamSiteManage: '/cloud/examSiteManage/delete', // 删除删除删除删除


    // 新增大型考试的接口
    createExam: 'cloud/exam/v2/createExam', // 新增考试的一些基本信息
    updateExamInfo: 'cloud/exam/v2/updateExam', // 修改考试的一些基本信息

    updateExamSubject: '/cloud/exam/v2/updateExamSubject', // 科目信息的那些
    getExamPersonCountInfo: '/cloud/exam/v2/getExamPersonCountInfo', //
    getDistributeExamSiteDetail: '/cloud/exam/v2/getDistributeExamSiteDetail', // 获取分配详情
    listExamSite: '/cloud/exam/v2/listExamSite', // 考场列表

    distributeExamSite: '/cloud/exam/v2/distributeExamSite', // 确定给他分配考场

    deleteExamSite: '/cloud/exam/v2/deleteExamSite', // 确定给他分配考场
    changeExamGroupArrange: '/cloud/exam/v2/changeExamGroupArrange', //
    pageExamSitePerson: '/cloud/exam/v2/pageExamSitePerson', //
    pageExamGroupAdmin: '/cloud/exam/v2/pageExamGroupAdmin', // 找人的列表

    updateExamGroupAdmin: '/cloud/exam/v2/updateExamGroupAdmin', // 更新考试组下的监考 巡考员信息

    switchExamSeat: '/cloud/examManage/v2/switchExamSeat', // 换座位

    getExamSubject: '/cloud/exam/v2/getExamSubject',
    listExamBuilding: '/cloud/examSiteManage/listExamBuilding',
    listExamSiteType: '/cloud/examSiteManage/listExamSiteType',
    deleteBatch: '/cloud/exam/deleteBatch',
    publishExam: '/cloud/exam/v2/publishExam',
    viewExamSeat: '/cloud/examManage/v2/viewExamSeat',  // 查看学生座位图

    // 评价的数据中心数据
    pageEvalPersonScore: '/cloud/evalStatistic/pageEvalPersonScore', // 分页查询累计获得积分最多的用户
    getEmsCount: '/cloud/evalStatistic/getEmsCount', // 获取兑换次数、勋章总数、积分卡总数
    listEvalPersonPortion: '/cloud/evalStatistic/listEvalPersonPortion', // 参与评价活动占比-饼图
    pageEvalMedalPersonCount: '/cloud/evalMedal/person/pageEvalMedalPersonCount', // 勋章统计
    listEvalActivity: '/cloud/evalActivity/listEvalActivity', // 获取评价活动列表
    getstatisticsList: '/cloud/evalDayRulePerson/getEvalIndicatorDayPersonHomeCount', // 获取评分统计
    dayScoreMaxCount: '/cloud/evalDayRulePerson/dayScoreMaxCount', // 获取得分排名前三的用户
    dayScoreMaxCountPage: '/cloud/evalDayRulePerson/dayScoreMaxCountPage', // 获取得分分页


    // 监控预警的这些个接口
    managerPermission: '/cloud/hk/manager/permission', // 是否有设置预警负责人的权限
    managerUpdate: '/cloud/hk/manager/update', // 预警消息设置负责人
    managerList: '/cloud/hk/manager/list', // 预警负责人列表
    managerPage: '/cloud/hk/message/page', // 预警消息分页列表

    getEnableAnnouncement: '/cloud/manage/announcement/getEnableAnnouncement', // 公告



    // 活动报名1.0的接口
    selectTree: '/cloud/v3/tree/selectTree', // 3.1-选人组件
    selectTreeSearch: '/cloud/v3/tree/selectTree/search', // 3.1-选人组件-搜索
    enrollCreate: '/cloud/activity/enroll/create', // 活动报名 - 新增
    enrollDetail: '/cloud/activity/enroll/detail', // 活动报名- 详情
    enrollPage: '/cloud/activity/enroll/page', // 活动报名 - 分页列表
    enrollUpdate: '/cloud/activity/enroll/update', // 活动报名 - 编辑
    enrollClose: '/cloud/activity/enroll/close', // 活动报名 - 关闭活动
    enrollDelete: '/cloud/activity/enroll/delete', // 活动报名 - 删除.

    enrollUserpage: '/cloud/activity/enrollUser/page', // 报名表 - 分页列表
    enrollUsercancelEnroll: '/cloud/activity/enrollUser/cancelEnroll', // 报名表 - 取消报名
    enrollUserresumeEnroll: '/cloud/activity/enrollUser/resumeEnroll', // 报名表 - 恢复报名
    enrollexport: '/cloud/common/export/excel', // 导出

}

export default api
