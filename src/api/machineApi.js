import request from "@/utils/request"
import api from "./index.ts"

// 班牌设备列表
export function deviceList(data) {
    return request({
        url: api.deviceList,
        method: "post",
        data,
    });
}
// 设备列表
export function pageList(data) {
    return request({
        url: api.pageList,
        method: "post",
        data
    })
}

// 新增设备
export function createMachine(data) {
    return request({
        url: api.createMachine,
        method: "post",
        data
    })
}

// 回显设备
export function detailMachine(data) {
    return request({
        url: api.detailMachine,
        method: "get",
        params: data
    })
}

// 编辑
export function updateMachine(data) {
    return request({
        url: api.updateMachine,
        method: "post",
        data
    })
}

// 删除
export function deleteMachine(data) {
    // 
    return request({
        // url: api.deleteMachine,  将改为下面的接口（班牌的删除）
        url: '/cloud/v2/equipment/delete',
        method: "post",
        data
    })
}

// 场地数据
export function siteList(params) {
    return request({
        url: api.siteList,
        method: "get",
        params
    })
}

// 获取设备列表
export function getDeviceList(params) {
    return request({
        url: '/attweb/attendance/getDeviceList',
        method: "get",
        params
    })
}

export function siteList1(params) {
    return request({
        url: api.getSiteList,
        method: "get",
        params
    })
}

// 获取学校的设备类型列表
export function getTypeList(params) {
    return request({
        url: '/cloud/machine/type/list',
        method: "get",
        params
    })
}

// 绑定设备
export function postBindDevice(data) {
    return request({
        url: '/cloud/v2/machine/bindDeviceById',
        method: "post",
        data
    })
}

// 解绑设备
export function getUnbindDevice(data) {
    return request({
        url: '/cloud/v2/machine/unbindDeviceByNo',
        method: "post",
        data
    })
}

// 获取人脸机品牌列表接口
export function getBrandlistBySchool(data) {
    return request({
        url: '/manage/school/device/brand/listBySchool',
        method: "post",
        data
    })
}

// 字典
export function getDictDataPage(data) {
    return request({
        url: '/system/systemDict/dictDataPage',
        method: 'post',
        data
    })
}

// 批量绑定设备
export function batchBindDevice(data) {
    return request({
        url: '/cloud/v2/machine/batchBindDevice',
        method: 'post',
        data
    })
}
// 设备分页
export function pageSchoolHkMonitorPoint(data) {
    return request({
        url: '/cloud/patrol/hkMonitorPoint/pageSchoolHkMonitorPoint',
        method: 'post',
        data
    })
}
// 同步监控点
export function sendSchoolHkMonitorPoint(params) {
    return request({
        url: '/cloud/patrol/hkMonitorPoint/sendSchoolHkMonitorPoint',
        method: "get",
        params
    })
}
// 绑定|解绑场地
export function updateHkMonitorPointSite(data) {
    return request({
        url: '/cloud/patrol/hkMonitorPoint/updateHkMonitorPointSite',
        method: 'post',
        data
    })
}
// 删除监控点
export function deleteSchoolHkMonitorPoint(params) {
    return request({
        url: '/cloud/patrol/hkMonitorPoint/deleteSchoolHkMonitorPoint',
        method: "get",
        params
    })
}
// 获取监控同步配置信息
export function getSchoolHkConfigBySchoolId(params) {
    return request({
        url: '/cloud/patrol/hkMonitorPoint/getSchoolHkConfigBySchoolId',
        method: "get",
        params
    })
}
// 更新监控同步配置
export function updateSchoolHkConfig(data) {
    return request({
        url: '/cloud/patrol/hkMonitorPoint/updateSchoolHkConfig',
        method: 'post',
        data
    })
}
// 根据监控点获取视频流地址
export function getHkMonitorPointCamerasTalk(data) {
    return request({
        url: '/cloud/patrol/hkMonitorPoint/getHkMonitorPointCamerasTalk',
        method: 'post',
        data
    })
}
// 获取全校监控点--树
export function getHkMonitorPointTree(params) {
    return request({
        url: '/cloud/patrol/hkMonitorPoint/getHkMonitorPointTree',
        method: "get",
        params
    })
}
// 获取监控列表
export function listSchoolHkMonitorPoint(data) {
    return request({
        url: '/cloud/patrol/hkMonitorPoint/listSchoolHkMonitorPoint',
        method: 'post',
        data
    })
}
// 获取已上墙的监控点列表
export function getSelectOnWallList(params) {
    return request({
        url: '/cloud/patrol/hkMonitorPoint/selectOnWallList',
        method: "get",
        params
    })
}

// 监控上墙|下墙
export function updateHkMonitorPointOnWall(data) {
    return request({
        url: '/cloud/patrol/hkMonitorPoint/updateHkMonitorPointOnWall',
        method: 'post',
        data
    })
}