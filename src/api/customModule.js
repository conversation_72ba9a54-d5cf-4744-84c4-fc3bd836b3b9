import request from "@/utils/request"
import api from "./index.ts"

export function customizeClassifyCreate(data) {
    return request({
        url: api.customizeClassifyCreate,
        method: "post",
        data
    })
}

export function customizeClassifyDel(data) {
    return request({
        url: api.customizeClassifyDel,
        method: "post",
        data
    })
}

export function layoutclasssignClassifyList(data) {
    return request({
        url: api.layoutclasssignClassifyList,
        method: "post",
        data
    })
}

export function customizeModuleCreate(data) {
    return request({
        url: api.customizeModuleCreate,
        method: "post",
        data
    })
}

export function customizeModuleDel(data) {
    return request({
        url: api.customizeModuleDel,
        method: "post",
        data
    })
}

export function layoutclasssignModuleList(data) {
    return request({
        url: api.layoutclasssignModuleList,
        method: "post",
        data
    })
}

export function customizeModuleUpdate(data) {
    return request({
        url: api.customizeModuleUpdate,
        method: "post",
        data
    })
}