/*
 * @Descripttion: 打卡 punchTheClock
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-05-17 10:39:22
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-05-21 11:36:19
 */
import request from '@/utils/request'
import api from './index.ts'

// 获取打卡任务列表
export function getPunchTheClockList(data) {
    return request({
        url: api.punchTheClockList,
        method: 'post',
        data
    })
}

// 获取班级
export function getTeachClassesList(data) {
    return request({
        url: api.teachClassesList,
        method: 'get',
        params: data
    })
}

// 打卡任务详情
export function getPunchTheClockDetails(params) {
    return request({
        url: api.punchTheClockDetails + '/' + params,
        method: 'get'
    })
}

// 添加打卡任务
export function addPpunchTheClock(data) {
    return request({
        url: api.addPpunchTheClock,
        method: 'post',
        data
    })
}

// 编辑打卡任务
export function updatePpunchTheClock(data) {
    return request({
        url: api.updatePpunchTheClock,
        method: 'post',
        data
    })
}

// 删除打卡任务
export function deletePpunchTheClock(data) {
    return request({
        url: api.deletePpunchTheClock,
        method: 'post',
        data
    })
}

// 获取科目
export function getSubjectListNoPag(params) {
    return request({
        url: api.subjectListNoPag,
        method: 'get',
        params
    })
}

// 获取科目
export function queryTeacherSubject(params) {
    return request({
        url: api.queryTeacherSubject,
        method: 'get',
        params
    })
}

// 打卡任务总览
export function getPunchTheClockReport(data) {
    return request({
        url: api.punchTheClockReport,
        method: 'post',
        data
    })
}

// 打卡任务总览部门
export function getPunchTheClockDeptReport(data) {
    return request({
        url: api.punchTheClockDeptReport,
        method: 'post',
        data
    })
}

// 打卡任务总览班级
export function getPunchTheClockClassReport(data) {
    return request({
        url: api.punchTheClockClassReport,
        method: 'post',
        data
    })
}
// 打卡任务总览班级
export function getPunchTheClockPages(data) {
    return request({
        url: api.punchTheClockPages,
        method: 'post',
        data
    })
}

// 打卡模版
export function punchClockTemplateList(data) {
    return request({
        url: api.punchClockTemplateList,
        method: 'get',
        params: data
    })
}
