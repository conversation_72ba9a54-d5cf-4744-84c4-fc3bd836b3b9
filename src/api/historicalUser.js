import request from '@/utils/request'
import api from './index.ts'

// 获取学生年级树
export function postHistoryYearList(data) {
    return request({
        url: api.historyYearList,
        method: 'post',
        data
    })
}

// 获取班级列表
export function postHistoryClassesList(data) {
    return request({
        url: api.historyClassesList,
        method: 'post',
        data
    })
}

// 获取学生列表
export function postHistoryStudentPage(data) {
    return request({
        url: api.historyStudentPage,
        method: 'post',
        data
    })
}

// 获取学生详情数据
export function getStudentDetail(params) {
    return request({
        url: api.studentDetails,
        method: 'get',
        params
    })
}

// 历史成员列表
export function postHistoryEmployeePage(data) {
    return request({
        url: api.historyEmployeePage,
        method: 'post',
        data
    })
}

// 历史学生复学
export function historyRecoveryStudent(data) {
    return request({
        url: api.historyRecoveryStudent,
        method: 'post',
        data
    })
}

// 历史教职工复员
export function historyRecoveryTeacher(data) {
    return request({
        url: api.historyRecoveryTeacher,
        method: 'post',
        data
    })
}

// 历史所属部门
export function historicalDepartment(data) {
    return request({
        url: api.historyEmployeeDept,
        method: 'post',
        data
    })
}

// 历史所属班级
export function historyStudentDept(data) {
    return request({
        url: api.historyStudentDept,
        method: 'post',
        data
    })
}
