import request from '@/utils/request'
import api from './index.ts'

export function attendanceGroupList(data) {
    return request({
        url: api.attendanceGroup,
        method: 'post',
        data
    })
}

export function attendanceGroupInfo(params) {
    return request({
        url: api.attendanceGroupInfo,
        method: 'get',
        params
    })
}

export function attendanceGroupAdd(data) {
    return request({
        url: api.attendanceGroupAdd,
        method: 'post',
        data
    })
}

export function attendanceGroupUpdate(data) {
    return request({
        url: api.attendanceGroupUpdate,
        method: 'post',
        data
    })
}

export function attendanceGroupDel(data) {
    return request({
        url: api.attendanceGroupDel,
        method: 'post',
        data
    })
}

export function schoolAttendanceScheduling(data) {
    return request({
        url: api.schoolAttendanceScheduling,
        method: 'post',
        data
    })
}

export function getAttendanceDevice() {
    return request({
        url: api.getAttendanceDevice,
        method: 'get'
    })
}
