/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-02-16 16:06:44
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-02-19 15:50:38
 */

import request from "@/utils/request";
import Qs from "qs";
import api from "./index.ts";
// 判斷是否調用mock數據

// 列表
export function routineList(data) {
    return request({
        url: api.routineList,
        method: "post",
        data,
    });
}
// 新增
export function createRoutine(data) {
    return request({
        url: api.createRoutine,
        method: "post",
        data,
    });
}
// 回显
export function detailsRoutine(data) {
    return request({
        url: api.detailsRoutine,
        method: "get",
        params: data,
    });
}
// 编辑
export function updateRoutine(data) {
    return request({
        url: api.updateRoutine,
        method: "post",
        data,
    });
}
// 删除
export function deleRoutine(data) {
    return request({
        url: api.deleteRoutine,
        method: "post",
        data,
    });
}

// 导入场地学生课表
export function downloadTemplate(params, fileName) {
    request({
        url: api.downloadTemplate,
        method: "get",
        responseType: "arraybuffer",
        params,
    }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            })
        );
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute(
            "download",
            fileName ? `${fileName}.xlsx` : "excel.xlsx"
        );
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
}

export function uploadProgress(params) {
    return request({
        url: `${api.uploadProgress}/${params}`,
        method: "get",
    });
}

export function continueImport(url, params) {
    return request({
        url,
        method: "get",
        params,
    });
}

export function timetableRoutineBySemesterId(params) {
    return request({
        url: api.timetableRoutineBySemesterId,
        method: "get",
        params,
    });
}

export function timetableSemester(params) {
    return request({
        url: api.timetableSemester,
        method: "get",
        params,
    });
}

export function uploadSchedule(url, data) {
    return request({
        url,
        method: "post",
        data,
    });
}


export function newGetRoutineList(params) {
    return request({
        url: api.newGetRoutineList,
        method: "get",
        params,
    });
}