/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-27 10:35:38
 * @LastEditors: jingrou
 * @LastEditTime: 2023-05-18 11:23:16
 */
import Qs from "qs";
import api from "./index";
import axios from "@/utils/request";
import addAuthCode from "@/utils/addAuthCode"
export function login(params) {
    return axios({
        url: api.login,
        method: "post",
        data: Qs.stringify(params),
    });
}

export function getWebsiteConfig(data) {
    return axios({
        url: api.getWebsiteConfig,
        method: "post",
        data,
    });
}

export function getCurrentUser(params) {
    return axios({
        url: api.getCurrentUser,
        method: "get",
        data: params,
    });
}

export function getRouters(id) {
    return axios({
        url: api.getRouters + `?schoolId=${id}`,
        method: "get",
        data: {},
    });
}

// 登录获取二维码
export function getGrcode() {
    return axios({
        url: api.qrcode,
        method: "get",
        data: {},
    });
}
// 登录获取轮询二维码
export function getCheck(random) {
    return axios({
        url: `${api.check}?random=${random}`,
        method: "get",
        data: {},
    });
}

// oss上传接口
export function gotStsTokent(data) {
    return axios({
        url: "/manage/file/getOssScrip",
        method: "get",
        data,
    });
}

//  校验用户是否可以登录
export function checkUserLogin(data) {
    return axios({
        url: api.checkUserLogin,
        method: "get",
        params: data,
    });
}

//  获取学校模板组件
export function getCommentKey(params) {
    return axios({
        url: api.getCommentKey,
        method: "get",
        params,
    });
}
//  获取学校JSON模板组件
export function getJsonCommentKey(data) {
    return axios({
        url: api.getJsonCommentKey,
        method: "post",
        data,
    });
}

//
export function userManagement(data) {
    return axios({
        url: api.userManagementList,
        method: "post",
        data,
    });
}

//  重置密码
export function getResetPassword(params) {
    return axios({
        url: api.resetPassword,
        method: "get",
        params,
    });
}

// 用户解锁
export function getUserUnlock(data) {
    return axios({
        url: api.userUnlock,
        method: "post",
        data,
    });
}

// 修改手机号
export function updatePhone(data) {
    return axios({
        url: api.updatePhone,
        method: "post",
        data,
    });
}

// 获取角色
export function roleTypeList(params) {
    return axios({
        url: api.roleTypeList,
        method: "get",
        params,
    });
}

// 获取学校配置
export function getConfigList(data) {
    return axios({
        url: api.getConfigList,
        method: "post",
        data,
    });
}

// 更新学校配置
export function upConfigList(data) {
    return axios({
        url: api.upConfigList,
        method: "post",
        data,
    });
}

export function listNoSingleApp(params) {
    return axios({
        url: api.listNoSingleApp,
        method: "get",
        params,
    });
}

// 检查当前用户是否已申请注销
export function checkUserLogout(params) {
    return axios({
        url: api.checkUserLogout,
        method: "get",
        params,
    });
}

// 撤销用户注销
export function cancelLogout(params) {
    return axios({
        url: api.cancelLogout,
        method: "get",
        params,
    });
}

//获取老师
export function postUserPage(data) {
    return axios({
        url: "/system/dept/user/page",
        method: "post",
        data,
    });
}

export async function getNewRouters(headers) {
    const data = await axios({
        url: "/system/menu/getRouters",
        method: "get",
        headers,
    });
    addAuthCode(data.data)
    return Promise.resolve(data)
}


//修改密码
export function updatePassword(data) {
    return axios({
        url: "/cloud/user/updateNewPassword",
        method: "post",
        data,
    });
}

//忘记密码
export function forgetPassword(data) {
    return axios({
        url: "/cloud/user/forgetPassword",
        method: "post",
        data,
    });
}



// 获取短信
export function smsMessage(data) {
    return axios({
        url: "/cloud/sms/external/message",
        method: "post",
        data,
    });
}

// 系统公告
export function getEnableAnnouncement(data) {
    return axios({
        url: api.getEnableAnnouncement,
        method: "post",
        data,
    });
}
// 校验是否需要升学

export function getUpgradeCheckUpgrade(params) {
    return axios({
        url: '/cloud/upgrade/checkUpgrade',
        method: "get",
        params,
    });
}
// 是否需要设置密码
export function systemConfigByModuleConfig(params) {
    return axios({
        url: "/cloud/system-config/systemConfigByModuleConfig",
        method: "get",
        params,
    });
}