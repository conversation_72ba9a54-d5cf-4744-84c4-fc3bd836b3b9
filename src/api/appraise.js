
import request from '@/utils/request'
import api from './index.ts'


// 分页查询累计获得积分最多的用户
export function pageEvalPersonScore(data) {
    return request({
        url: api.pageEvalPersonScore,
        method: 'post',
        data
    })
}


export function getEmsCount(data) {
    return request({
        url: api.getEmsCount,
        method: 'post',
        data
    })
}

export function listEvalPersonPortion(data) {
    return request({
        url: api.listEvalPersonPortion,
        method: 'post',
        data
    })
}


export function pageEvalMedalPersonCount(data) {
    return request({
        url: api.pageEvalMedalPersonCount,
        method: 'post',
        data
    })
}




export function listEvalActivity(data) {
    return request({
        url: api.listEvalActivity,
        method: 'get',
        params: data
    })
}


export function getstatisticsList(data) {
    return request({
        url: api.getstatisticsList,
        method: 'get',
        params: data
    })
}



export function dayScoreMaxCount(data) {
    return request({
        url: api.dayScoreMaxCount,
        method: 'get',
        params: data
    })
}


export function dayScoreMaxCountPage(data) {
    return request({
        url: api.dayScoreMaxCountPage,
        method: 'post',
        data
    })
}
