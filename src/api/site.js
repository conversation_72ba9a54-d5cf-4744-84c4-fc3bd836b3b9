/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jing<PERSON>
 * @Date: 2022-02-19 17:44:48
 * @LastEditors: jingrou
 * @LastEditTime: 2022-11-11 10:54:48
 */
import request from '@/utils/request'
import api from './index.ts'
// import { version } from '../../package.json'
// 判斷是否調用mock數據

// 获取楼栋列表树
export function getCloudBuildList(data) {
    return request({
        url: api.getCloudBuildList,
        method: 'get',
        params: data
    })
}
// 建筑楼栋列表
export function buildingList(data) {
    return request({
        url: api.buildingList,
        method: 'get',
        params: data
    })
}

// 获取校区列表
export function getCampusList(data) {
    return request({
        url: api.getCampusList,
        method: 'get',
        params: data
    })
}

// 添加建筑楼栋
export function createBuilding(data) {
    return request({
        url: api.createBuilding,
        method: 'post',
        data
    })
}
//  编辑建筑楼栋
export function updateBuilding(data) {
    return request({
        url: api.updateBuilding,
        method: 'post',
        data
    })
}
//  删除建筑楼栋
export function deleteBuilding(data) {
    return request({
        url: api.deleteBuilding,
        method: 'post',
        data
    })
}
// 场地列表
export function sitePage(data) {
    return request({
        url: api.sitePage,
        method: 'post',
        data
    })
}
// 场地详情
export function siteDetails(data) {
    return request({
        url: api.siteDetails,
        method: 'get',
        params: data
    })
}
// 场地添加
export function createSite(data) {
    return request({
        url: api.createSite,
        method: 'post',
        data
    })
}
// 场地编辑
export function updateSite(data) {
    return request({
        url: api.updateSite,
        method: 'post',
        data
    })
}
// 场地设备列表
export function siteDeviceList(data) {
    return request({
        url: api.siteDeviceList,
        method: 'get',
        params: data
    })
}
// 场地设备分页
export function siteDevicePage(data) {
    return request({
        url: api.siteDevicePage,
        method: 'post',
        data
    })
}
// 新增场地设备
export function siteDeviceCreate(data) {
    return request({
        url: api.siteDeviceCreate,
        method: 'post',
        data
    })
}
// 删除场地设备
export function siteDeviceDelete(data) {
    return request({
        url: api.siteDeviceDelete,
        method: 'post',
        data
    })
}

// 编辑场地设备
export function siteDeviceUpdate(data) {
    return request({
        url: api.siteDeviceUpdate,
        method: 'post',
        data
    })
}

// 场地类型列表-分页
export function siteTypePage(data) {
    return request({
        url: api.siteTypePage,
        method: 'post',
        data
    })
}
// 场地类型列表
export function siteTypeList(data) {
    return request({
        url: api.siteTypeList,
        method: 'get',
        params: data
    })
}
// 场地类型添加
export function createSiteType(data) {
    return request({
        url: api.createSiteType,
        method: 'post',
        data
    })
}
// 场地类型编辑
export function updateSiteType(data) {
    return request({
        url: api.updateSiteType,
        method: 'post',
        data
    })
}
// 场地类型编辑
export function deleteSite(data) {
    return request({
        url: api.deleteSite,
        method: 'post',
        data
    })
}
// 场地类型删除
export function deleteSiteType(data) {
    return request({
        url: api.deleteSiteType,
        method: 'post',
        data
    })
}
// 场地转场
export function updateTurnSite(data) {
    return request({
        url: api.updateTurnSite,
        method: 'post',
        data
    })
}
// 场地批量添加
export function createBatchSite(data) {
    return request({
        url: api.createBatchSite,
        method: 'post',
        data
    })
}
// 场地批量编辑
export function updateBatchSite(data) {
    return request({
        url: api.updateBatchSite,
        method: 'post',
        data
    })
}

// 楼栋管理员
export function updateBuildingManager(data) {
    return request({
        url: api.updateBuildingManager,
        method: 'post',
        data
    })
}
