/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jing<PERSON>
 * @Date: 2022-03-23 14:22:44
 * @LastEditors: jingrou
 * @LastEditTime: 2022-03-24 09:54:22
 */
import request from "@/utils/request";
import api from "./index.ts";

// 待办列表
export function todoPage(data) {
    return request({
        url: api.todoPage,
        method: "post",
        data,
    });
}
// 回退
export function activityState(data) {
    return request({
        url: api.changeActivityState,
        method: "post",
        data,
    });
}
// 同意/拒绝
export function completeTask(data) {
    return request({
        url: api.completeTask,
        method: "post",
        data,
    });
}
// 审核详情
export function todoGetInfo(data) {
    return request({
        url: api.todoGetInfo,
        method: "get",
        params: data,
    });
}
// 审核详情old
export function todoGetInfoOld(data) {
    return request({
        url: api.todoGetInfoOld,
        method: "get",
        params: data,
    });
}

// 巡查详情
export function todoPatrolDetail(data) {
    return request({
        url: api.todoPatrolDetail,
        method: "get",
        params: data,
    });
}

// 访客详情
export function todoVisitorDetail(data) {
    return request({
        url: api.todoVisitorDetail,
        method: "get",
        params: data,
    });
}

// 访客详情
export function getVisitorRecords(params) {
    return request({
        url: '/app/visitor/records/get',
        method: "get",
        params
    });
}

// 访客详情
export function getVisitorGlobalGet(params) {
    return request({
        url: '/app/visitor/setting/global/get',
        method: "get",
        params
    });
}
