/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-03-31 09:36:26
 * @LastEditors: jingrou
 * @LastEditTime: 2022-04-13 15:27:50
 */
import request from "@/utils/request";
import api from "./index.ts";
// 判斷是否調用mock數據

// 班级年级学校  出入校统计
export function queryWebPassData(data) {
    return request({
        url: api.queryWebPassData,
        method: "post",
        data,
    });
}

export function getUserPermissionInformation(data) {
    return request({
        url: api.getUserPermissionInformation,
        method: "get",
        params: data,
    });
}
export function siteListData(data) {
    return request({
        url: api.siteListData,
        method: "get",
        params: data,
    });
}
export function pageAccessRecordsInfo(data) {
    return request({
        url: api.pageAccessRecordsInfo,
        method: "post",
        data,
    });
}
export function importAccessRecordsExcel(data) {
    return request({
        url: api.importAccessRecordsExcel,
        method: "post",
        data,
    });
}
export function appGroupList(data) {
    return request({
        url: api.appGroupList,
        method: "post",
        data,
    });
}

export function selectRuleDataList(data) {
    return request({
        url: api.selectRuleDataList,
        method: "post",
        data,
    });
}
export function ruleDataUpdate(data) {
    return request({
        url: api.ruleDataUpdate,
        method: "post",
        data,
    });
}
export function deleteAppGroup(data) {
    return request({
        url: api.deleteAppGroup,
        method: "post",
        data,
    });
}
export function ruleDataInfo(data) {
    return request({
        url: api.ruleDataInfo,
        method: "get",
        params: data,
    });
}
//
export function getPassSite(data) {
    return request({
        url: api.getPassSite,
        method: "get",
        params: data,
    });
}

// 通行全校-学生和老师的统计 优化后_韩总
export function statisticsWholeCount(data) {
    return request({
        url: "/cloud/traffic/statistics/whole/count",
        method: "post",
        data,
    });
}
// 通行全校-分年级的统计 优化后_韩总
export function statisticsWholeEachGrade(data) {
    return request({
        url: "/cloud/traffic/statistics/whole/eachGrade",
        method: "post",
        data,
    });
}

// 通行全校-分部门的统计 优化后_韩总
export function statisticsWholeEachDept(data) {
    return request({
        url: "/cloud/traffic/statistics/whole/eachDept",
        method: "post",
        data,
    });
}

// 通行全校-分设备的统计 优化后_韩总
export function statisticsWholeEachDevice(data) {
    return request({
        url: "/cloud/traffic/statistics/whole/eachDevice",
        method: "post",
        data,
    });
}
// 通行全校-分时段统计 优化后_韩总
export function statisticsWholeEachTime(data) {
    return request({
        url: "/cloud/traffic/statistics/whole/eachTime",
        method: "post",
        data,
    });
}
// 通行年级-学生的统计 优化后_韩总
export function statisticsGradeCount(data) {
    return request({
        url: "/cloud/traffic/statistics/grade/count",
        method: "post",
        data,
    });
}
// 通行年级-分班级的统计 优化后_韩总
export function statisticsGradeEachClass(data) {
    return request({
        url: "/cloud/traffic/statistics/grade/eachClass",
        method: "post",
        data,
    });
}

// 通行班级-学生的统计 优化后_韩总
export function statisticsClassesCount(data) {
    return request({
        url: "/cloud/traffic/statistics/classes/count",
        method: "post",
        data,
    });
}

// 通行班级-学生的列表统计 优化后_韩总
export function statisticsClassesStudentDetail(data) {
    return request({
        url: "/cloud/traffic/statistics/classesStudent/detail",
        method: "post",
        data,
    });
}

// 通行记录导出 优化后_韩总
export function trafficRecordExport(data) {
    return request({
        url: "/cloud/traffic/record/export",
        responseType: "blob",
        method: "post",
        data,
    });
}

// 分页通行记录 优化后_韩总
export function trafficRecordPage(data) {
    return request({
        url: "/cloud/traffic/record/page",
        method: "post",
        data,
    });
}
