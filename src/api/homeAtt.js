/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-04-10 19:53:51
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-08-09 17:58:24
 */
import request from '@/utils/request'
import api from './index.ts'

// 班级下拉列表
export function getClassesByUserId() {
    return request({
        url: api.getClassesByUserId + '?code=studentAttendance',
        method: 'get'
    })
}
// 节次下拉列表
export function getSelectSequence(data) {
    return request({
        url: api.getSelectSequence,
        method: 'post',
        data
    })
}

// 班级统计的数据
export function getAttendanceHome(data) {
    return request({
        url: api.getAttendanceHome,
        method: 'post',
        data
    })
}

export function getGradeList() {
    return request({
        url: api.getGradeList + '?code=schoolTable',
        method: 'get'
    })
}

// 获取学籍tree
export function getSchoolRollTree(code) {
    return request({
        url: code ? api.schoolRoll + `?code=${code}&isRule=${true}` : api.schoolRoll,
        method: 'get'
    })
}
