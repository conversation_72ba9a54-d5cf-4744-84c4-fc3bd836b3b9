import request from "@/utils/request";

// 获取放行设置
export function getQuickPassSettingByType(data) {
    return request({
        url: "/cloud/quick/passSetting/getSchoolQuickPassSettingByType",
        method: "post",
        data,
    });
}

// 更新放行设置
export function getPassSettingUpdateSet(data) {
    return request({
        url: "/cloud/quick/passSetting/update",
        method: "post",
        data,
    });
}

// 启动|禁用放行
export function getPassSettingUpdateIsEnabled(data) {
    return request({
        url: "/cloud/quick/passSetting/updateIsEnabled",
        method: "post",
        data,
    });
}

// 获取放行二维码
export function getPassSettingQrcodePassGene(params) {
    return request({
        url: "/cloud/quick/passSetting/qrcodePassGene",
        method: "get",
        params,
    });
}

// 校验放行二维码是否过期
export function getPassSettingQrcodeCheck(params) {
    return request({
        url: "/cloud/quick/passSetting/qrcodeCheck",
        method: "get",
        params,
    });
}

// 通行记录分页
export function getPassPage(data) {
    return request({
        url: "/cloud/quick/pass/page",
        method: "post",
        data,
    });
}

// 获取放行二维码页面链接地址
export function getQrcodeUrlHome(params) {
    return request({
        url: "/cloud/quick/passSetting/getQrcodeUrlHome",
        method: "get",
        params,
    });
}

// 查询放行权限的老师列表
export function getTeacherList(params) {
    return request({
        url: "/cloud/quick/teacher/list",
        method: "get",
        params,
    });
}

// 老师放行权限-添加
export function teacherCreate(data) {
    return request({
        url: "/cloud/quick/teacher/create",
        method: "post",
        data,
    });
}

// 走读生分页
export function leaveStudentPage(data) {
    return request({
        url: "/cloud/quick/leaveStudent/page",
        method: "post",
        data,
    });
}

// 添加走读生
export function leaveStudentCreate(data) {
    return request({
        url: "/cloud/quick/leaveStudent/create",
        method: "post",
        data,
    });
}

// 修改走读生过期期限
export function updateExpireDate(data) {
    return request({
        url: "/cloud/quick/leaveStudent/updateExpireDate",
        method: "post",
        data,
    });
}

// 删除走读生
export function leaveStudentDelete(data) {
    return request({
        url: "/cloud/quick/leaveStudent/delete",
        method: "post",
        data,
    });
}

// 修改走读生状态
export function leaveStudentUpdateStatus(data) {
    return request({
        url: "/cloud/quick/leaveStudent/updateStatus",
        method: "post",
        data,
    });
}


// 获取所有走读生
export function leaveStudentList(data) {
    return request({
        url: "/cloud/quick/leaveStudent/list",
        method: "post",
        data,
    });
}
