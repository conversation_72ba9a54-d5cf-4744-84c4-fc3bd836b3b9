/*
 * @Descripttion: defHttp
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-28 14:03:39
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-03-07 14:59:40
 */

import { ls } from '@/utils/util'
import config from '@/config/config'
import request from '@/utils/request'
import Qs from 'qs'
// 判斷是否調用mock數據
const loc: any = import.meta.env.VITE_BASE_API

export function post(url: string, params?: object) {
    return request({
        url,
        method: 'post',
        data: params
    })
}

export function get(url: string, params?: object) {
    return request({
        url,
        method: 'get',
        params
    })
}

export function put(url: string, params?: object) {
    return request({
        url,
        method: 'put',
        data: params
    })
}
export function postQs(url: string, params?: object) {
    return request({
        url,
        method: 'post',
        data: Qs.stringify(params)
    })
}
// 下载导入
export function exportData(url: string, params?: object, fileName?: any) {
    request({
        url,
        method: 'get',
        responseType: 'arraybuffer',
        params
    }).then((blob: any) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })
        )
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
            'download',
            fileName ? `${fileName}.xlsx` : 'excel.xlsx'
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    })
}

export function postUpload(url, data) {
    return request({
        url,
        method: 'post',
        data
    })
}

export function getImport(url, params) {
    return request({
        url,
        method: 'get',
        params
    })
}
// 大学版的进度条
export function getProgresst(url, data) {
    return request({
        url,
        method: 'post',
        data
    })
}

// 大学版下载异常数据
export function importErrorLog(
    url: string,
    data?: object,
    fileName?: any,
    type?: any
) {
    const { ACCESS_TOKEN } = config
    ls.get(ACCESS_TOKEN)
    const params = {
        url,
        method: 'post',
        responseType: 'arraybuffer',
        data
    }
    if (type) {
        params.headers = {
            Authorization: `Bearer ${ls.get(ACCESS_TOKEN)}`
        }
    }
    return new Promise((resolve, reject) => {
        request(params).then((blob: any) => {
            const url = window.URL.createObjectURL(
                new Blob([blob], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                })
            )
            const link = document.createElement('a')
            link.style.display = 'none'
            link.href = url
            link.setAttribute(
                'download',
                fileName ? `${fileName}.xlsx` : 'excel.xlsx'
            )
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            resolve(blob)
        })
    })
}
