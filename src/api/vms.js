/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jing<PERSON>
 * @Date: 2023-03-15 16:09:29
 * @LastEditors: jingrou
 * @LastEditTime: 2023-03-15 16:28:49
 */
import request from "@/utils/request";
import api from "./index.ts";

// 新增设备
export function createMachine(data) {
    return request({
        url: api.createMachine,
        method: "post",
        data,
    });
}

// 编辑
export function updateMachine(data) {
    return request({
        url: api.updateMachine,
        method: "post",
        data,
    });
}
// 设备列表
export function pageList(data) {
    return request({
        url: api.pageList,
        method: "post",
        data,
    });
}
// 删除
export function deleteMachine(data) {
    return request({
        url: api.deleteMachine,
        method: "get",
        params: data,
    });
}

// 场地数据
export function siteList(params) {
    return request({
        url: api.siteList,
        method: "get",
        params,
    });
}

// 绑定设备
export function postBindDevice(data) {
    return request({
        url: "/cloud/v2/machine/bindDeviceById",
        method: "post",
        data,
    });
}

// 解绑设备
export function getUnbindDevice(data) {
    return request({
        url: "/cloud/v2/machine/unbindDeviceByNo",
        method: "post",
        data,
    });
}
// VMS的通道列表
export function getListByVmse(data) {
    return request({
        url: "/cloud/device/channel/listByVms",
        method: "post",
        data,
    });
}
// 批量绑定设备
export function getBatchBinding(data) {
    return request({
        url: "/cloud/device/channel/batchBinding",
        method: "post",
        data,
    });
}
