import request from "@/utils/request";
import api from "./index.ts";
// 考勤排班列表
export function attWorkList(data) {
    return request({
        url: api.attWorkList,
        method: "post",
        data,
    });
}
// 考勤排班详情
export function attWorkInfo(data) {
    return request({
        url: api.attWorkInfo,
        method: "get",
        params: data,
    });
}
// 考勤排班新增
export function addAttWork(data) {
    return request({
        url: api.addAttWork,
        method: "post",
        data,
    });
}
// 考勤排班修改
export function upAttWork(data) {
    return request({
        url: api.upAttWork,
        method: "post",
        data,
    });
}
// 考勤排班删除
export function delAttWork(data) {
    return request({
        url: api.delAttWork,
        method: "post",
        data,
    });
}
// 每日统计
export function attByDay(data) {
    return request({
        url: api.attByDay,
        method: "post",
        data,
    });
}
// 纠正每日统计
export function correctDayData(data) {
    return request({
        url: api.correctDayData,
        method: "post",
        data,
    });
}
// 每月统计
export function attByMonth(data) {
    return request({
        url: api.attByMonth,
        method: "post",
        data,
    });
}
// 查询所有考勤组列表
export function attendanceGroupAll(data) {
    return request({
        url: api.attendanceGroupAll,
        method: "post",
        data,
    });
}

// 考勤规则设置考勤组
export function setAttendanceGroup(data) {
    return request({
        url: api.setAttendanceGroup,
        method: "post",
        data,
    });
}

// 考勤规则详情
export function getRuleInfo(data) {
    return request({
        url: api.getRuleInfo,
        method: "get",
        params: data,
    });
}

// 考勤规则列表
export function getRuleList(data) {
    return request({
        url: api.getRuleList,
        method: "post",
        data,
    });
}

// 每日统计导出
export function attExportByDay(data) {
    return request({
        url: api.attExportByDay,
        method: "post",
        responseType: "arraybuffer",
        data,
    }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            })
        );
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "每日统计.xlsx");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
}
// 每月统计导出
export function attExportByMonth(data) {
    return request({
        url: api.attExportByMonth,
        method: "post",
        responseType: "arraybuffer",
        data,
    }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            })
        );
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "月度汇总.xlsx");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
}
// 通用统计导出
export function statisticalExport(url, data, text) {
    return request({
        url,
        method: "post",
        responseType: "arraybuffer",
        data,
    }).then((response) => {
        // const contentDisposition = response.headers["content-disposition"];
        // console.log(contentDisposition);
        const url = window.URL.createObjectURL(
            new Blob([response], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            })
        );
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", text);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
}
// 获取加班设置
export function getWorkOvertime(params) {
    return request({
        url: api.getWorkOvertimeRuleBySchoolId,
        method: "get",
        params,
    });
}
//获取补卡设置
export function getCardReplacement(params) {
    return request({
        url: api.getCardReplacementRuleBySchoolId,
        method: "get",
        params,
    });
}

// 加班设置
export function postWorkOvertime(data) {
    return request({
        url: api.updateWorkOvertimeRule,
        method: "post",
        data,
    });
}
// 补卡设置
export function postCardReplacement(data) {
    return request({
        url: api.updateCardReplacementRule,
        method: "post",
        data,
    });
}

// 每月统计
export function attCustomize(data) {
    return request({
        url: api.attCustomize,
        method: "post",
        data,
    });
}