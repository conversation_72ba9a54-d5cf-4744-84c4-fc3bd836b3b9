/*
 * @Descripttion: 信息发布 notice
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-03-29 09:30:30
 * @LastEditors: jingrou
 * @LastEditTime: 2022-07-22 17:42:00
 */
import request from "@/utils/request";
import api from "./index.ts";
// 上传文件
export function getFileCommonUpload(data) {
    return request({
        url: '/file/common/upload',
        method: "post",
        data,
    });
}
// 上传文件
export function getFileUpload(data) {
    return request({
        url: api.fileUpload,
        method: "post",
        data,
    });
}

// 获取信息列表
export function getInformationList(data) {
    return request({
        url: api.informationDeliveryList,
        method: "post",
        data,
    });
}

// 获取所有信息列表
export function getInformationListAll(data) {
    return request({
        url: api.informationDeliveryListAll,
        method: "post",
        data,
    });
}
// 获取我收到信息列表
export function getInformationListReceive(data) {
    return request({
        url: api.informationDeliveryReceive,
        method: "post",
        data,
    });
}

// 获取发布信息详情
export function getInformationDeliveryDetails(data) {
    return request({
        url: api.informationDeliveryDetails,
        method: "post",
        data,
    });
}

// 获取收到信息详情
export function getInformationDeliveryReceiveDetails(data) {
    return request({
        url: api.informationDeliveryReceiveDetails,
        method: "post",
        data,
    });
}
// 获取收到信息详情确认
export function getUpdateIncrementconfirms(data) {
    return request({
        url: api.updateIncrementconfirms,
        method: "post",
        data,
    });
}

// 发布信息
export function postInformationDelivery(data) {
    return request({
        url: api.informationDelivery,
        method: "post",
        data,
    });
}
// 编辑发布信息
export function postInformationUpdateDelivery(data) {
    return request({
        url: api.informationUpdateDelivery,
        method: "post",
        data,
    });
}
// 删除信息
export function postInformationDeliveryDelete(data) {
    return request({
        url: api.informationDeliveryDelete,
        method: "post",
        data,
    });
}

// 置顶/取消置顶
export function postInformationDeliveryUpdateTop(data) {
    return request({
        url: api.informationDeliveryUpdateTop,
        method: "post",
        data,
    });
}

// 撤回信息
export function postInformationDeliveryRetract(data) {
    return request({
        url: api.informationDeliveryRetract,
        method: "post",
        data,
    });
}

// 通知部门/班级列表
export function getInformationNotify(data) {
    return request({
        url: api.informationNotifyInfo,
        method: "post",
        data,
    });
}

// 通知人员-部门
export function getNotifyEmployees(data) {
    return request({
        url: api.notifyEmployees,
        method: "post",
        data,
    });
}

// 通知人员-班级
export function getNotifyElterns(data) {
    return request({
        url: api.notifyElterns,
        method: "post",
        data,
    });
}

// 图库海报模板首页
export function getMessLableTemplateHome(data) {
    return request({
        url: api.messLableTemplateHome,
        method: "post",
        data,
    });
}

// 图库海报模板分类列表
export function getMessLableTemplateLimit(data) {
    return request({
        url: api.messLableTemplateLimit,
        method: "post",
        data,
    });
}

// 图库海报模板更多
export function getMessLableTemplateMore(data) {
    return request({
        url: api.messLableTemplateMore,
        method: "post",
        data,
    });
}

// 图库海报模板详情页
export function getMessLableTemplateDetails(id) {
    return request({
        url: `${api.messLableTemplateDetails}/${id}`,
        method: "get",
    });
}

// 开启霸屏
export function getUpdateStartDominateScreen(data) {
    return request({
        url: api.updateStartDominateScreen,
        method: "post",
        data,
    });
}

// 结束霸屏
export function getUpdateStopDominateScreen(data) {
    return request({
        url: api.updateStopDominateScreen,
        method: "post",
        data,
    });
}

// 提醒未确认人员
export function getNotifyUnconfirms(data) {
    return request({
        url: api.notifyUnconfirms,
        method: "post",
        data,
    });
}

// 查询已发送设备
export function getSchoolDevice(data) {
    return request({
        url: api.schoolDevice,
        method: "post",
        data,
    });
}

// 模板类型列表
export function getSchoolmessTypeLablelist(data) {
    return request({
        url: api.schoolmessTypeLablelist,
        method: "post",
        data,
    });
}

// 一体机创建
export function integratedMchineCreate(data) {
    return request({
        url: api.integratedMchineCreate,
        method: "post",
        data,
    });
}

// 一体机设备树形列表
export function getIntegratedMchineTree(params) {
    return request({
        url: api.integratedMchineTree,
        method: "get",
        params,
    });
}

// 获取学校通知列表
export function getSchoolList(data) {
    return request({
        url: api.schoolList,
        method: "post",
        data,
    });
}

// 获取班级通知列表
export function getInformationClassList(data) {
    return request({
        url: api.getClassList,
        method: "post",
        data,
    });
}

// 获取班级通知列表
export function getMessLabelTemplateList(params) {
    return request({
        url: "/cloud/v3/mess/template/messLabelTemplateList",
        method: "get",
        params,
    });
}

// 获取是否展示设备类型
export function checkDeviceType(params) {
    return request({
        url: api.checkDeviceType,
        method: "get",
        params,
    });
}

// 获取设备列表
export function getDeviceTreeV3(data) {
    return request({
        url: api.getDeviceTreeV3,
        method: "post",
        data,
    });
}
// 信发审核
export function getProcessTable(params) {
    return request({
        url: api.checkDeviceType,
        method: "get",
        params,
    });
}

// 获取班牌列表
export function brandList(data) {
    return request({
        url: '/brand/brand/list',
        method: "post",
        data,
    });
}
// 获取设备列表
export function machineList(data) {
    return request({
        url: '/cloud/v2/machine/list',
        method: "post",
        data,
    });
}