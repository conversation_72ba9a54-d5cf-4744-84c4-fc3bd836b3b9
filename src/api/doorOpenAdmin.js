import request from "@/utils/request";

// 开门管理列表
export function getOpenDoorList(data) {
    return request({
        url: "/cloud/device/openDoorLog/page",
        method: "post",
        data,
    });
}
// 开门密码管理列表
export function getopenDoorAdminList(data) {
    return request({
        url: "/cloud/device/openDoorPwd/page ",
        method: "post",
        data,
    });
}
// 删除开门密码管理
export function doorPasswordRemove(data) {
    return request({
        url: "/cloud/device/openDoorPwd/update",
        method: "post",
        data,
    });
}

// 新增开门密码管理
export function addDoorPassword(data) {
    return request({
        url: "/cloud/device/openDoorPwd/create",
        method: "post",
        data,
    });
}

//  查看设备
export function postPasswordDeviceList(data) {
    return request({
        url: "/cloud/device/password/page",
        method: "post",
        data,
    });
}

//  批量下发密码
export function postBatchPasswordIssuance(data) {
    return request({
        url: "/cloud/device/password/issuance",
        method: "post",
        data,
    });
}
// 下发记录列表
export function postIssuanceRecordList(data) {
    return request({
        url: "/cloud/device/password/records/page",
        method: "post",
        data,
    });
}

// 获取绑定场地的班牌账号
export function getBindingSiteTreeeV2(data) {
    return request({
        url: "/cloud/v2/machine/page",
        method: "post",
        data,
    });
}
