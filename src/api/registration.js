import request from "@/utils/request";
import api from "./index.ts";

export function enrollCreate(data) {
    return request({
        url: api.enrollCreate,
        method: "post",
        data,
    });
}

export function enrollDetail(data) {
    return request({
        url: api.enrollDetail,
        method: "post",
        data,
    });
}

export function enrollPage(data) {
    return request({
        url: api.enrollPage,
        method: "post",
        data,
    });
}

export function enrollUpdate(data) {
    return request({
        url: api.enrollUpdate,
        method: "post",
        data,
    });
}

export function enrollClose(data) {
    return request({
        url: api.enrollClose,
        method: "post",
        data,
    });
}

export function enrollDelete(data) {
    return request({
        url: api.enrollDelete,
        method: "post",
        data,
    });
}

export function selectTree(data) {
    return request({
        url: api.selectTree,
        method: "post",
        data,
    });
}

export function selectTreeSearch(data) {
    return request({
        url: api.selectTreeSearch,
        method: "post",
        data,
    });
}

export function enrollUserpage(data) {
    return request({
        url: api.enrollUserpage,
        method: "post",
        data,
    });
}

export function enrollUsercancelEnroll(data) {
    return request({
        url: api.enrollUsercancelEnroll,
        method: "post",
        data,
    });
}

export function enrollUserresumeEnroll(data) {
    return request({
        url: api.enrollUserresumeEnroll,
        method: "post",
        data,
    });
}

export function enrollexport(data) {
    return request({
        url: api.enrollexport,
        responseType: "arraybuffer",
        headers: {
            "Content-Type": "application/json; application/octet-stream",
        },
        method: "post",
        data,
    });
}
