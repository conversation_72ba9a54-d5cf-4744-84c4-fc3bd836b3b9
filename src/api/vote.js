/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jing<PERSON>
 * @Date: 2023-03-23 09:22:58
 * @LastEditors: jingrou
 * @LastEditTime: 2023-03-27 16:35:37
 */

import request from '@/utils/request'
import exp from 'constants'
import api from './index.ts'

// 字典查询
export function dataDictionary(data) {
    return request({
        url: api.dataDictionary,
        method: 'post',
        data
    })
}

// 新增投票
export function createVote(data) {
    return request({
        url: api.createVote,
        method: 'post',
        data
    })
}

// 编辑投票
export function updateVote(data) {
    return request({
        url: api.updateVote,
        method: 'post',
        data
    })
}

// 投票详情
export function getVoteInfo(data) {
    return request({
        url: api.getVoteInfo,
        method: 'post',
        data
    })
}

// 投票列表
export function getVoteList(data) {
    return request({
        url: api.voteList,
        method: 'post',
        data
    })
}

// 删除投票
export function deleteVote(data) {
    return request({
        url: api.deleteVote,
        method: 'post',
        data
    })
}

// 投票选项统计
export function getCountVoteOption(data) {
    return request({
        url: api.countVoteOptionList,
        method: 'get',
        params: data
    })
}

// 投票数量
export function getVoteQuantityList(data) {
    return request({
        url: api.getVoteQuantityList,
        method: 'post',
        data
    })
}

// 投票暂停
export function updateVoteStatus(data) {
    return request({
        url: api.updateVoteStatus,
        method: 'post',
        data
    })
}

// 预览二维码
export function getVoteQRCode(data) {
    return request({
        url: api.getVoteQRCode,
        method: 'get',
        params: data
    })
}

// 导出投票
export function exportVote(url, data, fileName) {
    request({
        url,
        method: 'get',
        responseType: 'arraybuffer',
        params: data
    }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })
        )
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
            'download',
            fileName ? `${fileName}.xlsx` : 'excel.xlsx'
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    })
}
