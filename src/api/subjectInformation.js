/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jing<PERSON>
 * @Date: 2022-02-17 16:21:07
 * @LastEditors: jingrou
 * @LastEditTime: 2023-02-21 19:21:00
 */
import request from "@/utils/request";
import Qs from "qs";
import api from "./index.ts";

// 科目信息列表
export function subjectList(data) {
    return request({
        url: api.subjectList,
        method: "post",
        data,
    });
}
// 新增科目信息
export function createSubject(data) {
    return request({
        url: api.createSubject,
        method: "post",
        data,
    });
}
// 回显科目信息
export function getSubjectInfo(data) {
    return request({
        url: api.getSubjectInfo,
        method: "get",
        params: data,
    });
}

// 字典查询
export function dataDictionary(data) {
    return request({
        url: api.dataDictionary,
        method: "post",
        data,
    });
}

// 删除
export function deleteSubject(data) {
    return request({
        url: api.deleteSubject,
        method: "post",
        data,
    });
}
// 编辑
export function updateSubject(data) {
    return request({
        url: api.updateSubject,
        method: "post",
        data,
    });
}
// 任课老师 列表
export function teacherSubjectList(data) {
    return request({
        url: api.teacherSubjectList,
        method: "post",
        data,
    });
}
// 编辑任课老师
export function updateTeacherSubject(data) {
    return request({
        url: api.updateTeacherSubject,
        method: "post",
        data,
    });
}
// 回显任课老师
export function echoTeacherSubject(data) {
    return request({
        url: api.echoTeacherSubject,
        method: "get",
        params: data,
    });
}

// 获取任课老师
export function getSubjectTeacherId(data) {
    return request({
        url: api.getSubjectTeacherId,
        method: "get",
        params: data,
    });
}

// 编辑课表科目与老师
export function updateSubjectTimetable(data) {
    return request({
        url: "/cloud/timetable/updateTimetable",
        method: "post",
        data,
    });
}

// 查询班级列表
export function getClassesList(data) {
    return request({
        url: "/cloud/roll/getClassesList",
        method: "post",
        data,
    });
}

// 是否一键清空所有
export function deleteTimetable(data) {
    return request({
        url: api.deleteTimetable,
        method: "post",
        data,
    });
}


// 是否一键清空所有
export function getTimetableStudentList(data) {
    return request({
        url:'/cloud/timetable/getTimetableStudentList',
        method: "post",
        data,
    });
}