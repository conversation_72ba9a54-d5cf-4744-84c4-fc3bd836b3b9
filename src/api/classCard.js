/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-02-18 16:07:31
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-03-09 15:40:35
 */
import request from '@/utils/request'
import Qs from 'qs'
import api from './index.ts'
// 判斷是否調用mock數據

// 列表
export function classCardList (data) {
    return request({
        url: api.classCardList,
        method: 'post',
        data
    })
}

// 添加设备
export function createClassCard (data) {
    return request({
        url: api.createClassCard,
        method: 'post',
        data
    })
}

// 回显设备
export function getOne (data) {
    return request({
        url: api.getOne,
        method: 'get',
        params: data
    })
}

// 修改设置
export function updateClassCard (data) {
    return request({
        url: api.updateClassCard,
        method: 'post',
        data
    })
}
// 删除设备
export function deleteClassCard (data) {
    return request({
        url: api.deleteClassCard,
        method: 'post',
        data
    })
}
// 设备类型
export function equipmentType (data) {
    return request({
        url: api.equipmentType,
        method: 'get',
        params: data
    })
}
