import request from '@/utils/request'
import api from './index.ts'

// 分类列表
export function classifyList(data) {
    return request({
        url: api.classifyList,
        method: 'post',
        data
    })
}
// 创建分类
export function classifyCreate(data) {
    return request({
        url: api.classifyCreate,
        method: 'post',
        data
    })
}
// 编辑分类
export function classifyEdit(data) {
    return request({
        url: api.classifyEdit,
        method: 'post',
        data
    })
}
// 删除分类
export function classifyDel(data) {
    return request({
        url: api.classifyDel,
        method: 'post',
        data
    })
}
// 文档列表
export function documentList(data) {
    return request({
        url: api.documentList,
        method: 'post',
        data
    })
}
// 新增文档
export function documentCreate(data) {
    return request({
        url: api.documentCreate,
        method: 'post',
        data
    })
}
// 删除文档
export function documentDel(data) {
    return request({
        url: api.documentDel,
        method: 'post',
        data
    })
}
// 移动文档
export function documentMove(data) {
    return request({
        url: api.documentMove,
        method: 'post',
        data
    })
}
