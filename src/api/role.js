import request from "@/utils/request";
import api from "./index.ts";

// 获取学校、教职工、家长上报列表
export function getRoleTypeList(params) {
    return request({
        url: api.roleTypeList,
        method: "get",
        params,
    });
}
//   删除角色类别
export function postRoleTypeDelete(data) {
    return request({
        url: api.roleTypeDelete,
        method: "post",
        data,
    });
}
//   新增角色类别
export function postRoleTypeCreate(data) {
    return request({
        url: api.roleTypeCreate,
        method: "post",
        data,
    });
}
//   编辑角色类别
export function postRoleTypeUpdate(data) {
    return request({
        url: api.roleTypeUpdate,
        method: "post",
        data,
    });
}
//   新增角色
export function postRoleTypeCreateRole(data) {
    return request({
        url: api.roleCreateRole,
        method: "post",
        data,
    });
}
//   选人后的参数提交
export function postDataUpdate(data) {
    return request({
        url: api.dataUpdate,
        method: "post",
        data,
    });
}
// 获取数据权限列表
export function getDataGetData(params) {
    return request({
        url: api.dataGetData,
        method: "get",
        params,
    });
}
//   
export function postRoleCreateUserRole(data) {
    return request({
        url: api.roleCreateUserRole,
        method: "post",
        data,
    });
}
//   删除角色成员
export function postRoleDeleteUserRole(data) {
    return request({
        url: api.roleDeleteUserRole,
        method: "post",
        data,
    });
}
//   获取角色成员列表
export function postFacultyList(data) {
    return request({
        url: api.facultyList,
        method: "post",
        data,
    });
}


// 初始时获取数据
export function getMenuList(params) {
    return request({
        url: api.menuList,
        method: "get",
        params,
    });
}
//   保存
export function postUpdateRoleMenu(data) {
    return request({
        url: api.updateRoleMenu,
        method: "post",
        data,
    });
}


//  根据角色id获取 角色下的用户id
export function getUserIdByRoleId(params) {
    return request({
        url: api.getUserIdByRoleId,
        method: "get",
        params,
    });
}

