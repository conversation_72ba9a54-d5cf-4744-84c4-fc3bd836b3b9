import request from '@/utils/request'
import { ExportOutlined } from '@ant-design/icons-vue'
import api from './index.ts'

// 获取值日生列表
export function getStudentdutyList(data) {
    return request({
        url: api.studentdutyList,
        method: 'post',
        data
    })
}

// 新增值日生
export function studentdutyCreate(data) {
    return request({
        url: api.studentdutyCreate,
        method: 'post',
        data
    })
}

// 获取值日项目列表
export function studentdutyProjectList(data) {
    return request({
        url: api.studentdutyProjectList,
        method: 'post',
        data
    })
}

// 删除值日生
export function studentdutyDel(data) {
    return request({
        url: api.studentdutyDel,
        method: 'post',
        data
    })
}

// 删除值日项目
export function studentdutyProjectDel(data) {
    return request({
        url: api.studentdutyProjectDel,
        method: 'post',
        data
    })
}

// 获取值日项目icon
export function projectIconList() {
    return request({
        url: api.projectIconList,
        method: 'post'
    })
}

// 创建值日项目
export function dutyProjectCreate(data) {
    return request({
        url: api.dutyProjectCreate,
        method: 'post',
        data
    })
}
