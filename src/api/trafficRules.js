/*
 * @Author: your name
 * @Date: 2022-03-09 09:52:02
 * @LastEditTime: 2022-03-31 09:36:30
 * @LastEditors: jingrou
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \cloud-system-2.0\src\api\trafficRules.js
 */
import request from "@/utils/request";
import Qs from "qs";
import api from "./index.ts";
// 侧边栏
export function listPassSite(data) {
    return request({
        url: api.listPassSite,
        method: "get",
        params: data,
    });
}
// 场地
export function getSiteList(data) {
    return request({
        url: api.getSiteList,
        method: "get",
        params: data,
    });
}
// 列表
export function pageRuleInfo(data) {
    return request({
        url: api.pageRuleInfo,
        method: "post",
        data,
    });
}
// 添加普通规则
export function createPasssceneRule(data) {
    return request({
        url: api.createPasssceneRule,
        method: "post",
        data,
    });
}
// 获取法定节假日
export function getHolidayInfo(data) {
    return request({
        url: api.getHolidayInfo,
        method: "get",
        params: data,
    });
}
// 获取规则详情
export function getPassRuleDetailById(data) {
    return request({
        url: api.getPassRuleDetailById,
        method: "post",
        data,
    });
}
// 更新规则
export function updatePassRuleInfo(data) {
    return request({
        url: api.updatePassRuleInfo,
        method: "post",
        data,
    });
}
// 删除规则
export function deletePassRuleInfo(data) {
    return request({
        url: api.deletePassRuleInfo,
        method: "post",
        data,
    });
}
// 启用禁用规则
export function updateRuleIsEnableInfo(data) {
    return request({
        url: api.updateRuleIsEnableInfo,
        method: "post",
        data,
    });
}
// -------------------------------------------------------------------
// 修改闸机密码
export function resetBarrierPassword(data) {
    return request({
        url: api.resetBarrierPassword,
        method: "post",
        data,
    });
}
// 获取闸机规则
export function queryBarrierRuleList(data) {
    return request({
        url: api.queryBarrierRuleList,
        method: "get",
        params: data,
    });
}

// 创建场景应用
export function getSceneCreate(data) {
    return request({
        url: api.sceneCreate,
        method: "post",
        data,
    });
}
// 场景应用列表
export function getScenePage(data) {
    return request({
        url: api.scenePage,
        method: "post",
        data,
    });
}
// 删除场景应用
export function getSceneDelete(data) {
    return request({
        url: api.sceneDelete,
        method: "post",
        data,
    });
}
// 修改场景应用
export function getSceneUpdate(data) {
    return request({
        url: api.sceneUpdate,
        method: "post",
        data,
    });
}

// 场景应用详情
export function getSceneDetails(params) {
    return request({
        url: api.sceneDetails,
        method: "get",
        params,
    });
}

// 获取学校建筑-场景列表数据
export function buildingSchoolBuildScene(params) {
    return request({
        url: api.buildingSchoolBuildScene,
        method: "get",
        params,
    });
}

// 集成到应用
export function sceneSystemApp(params) {
    return request({
        url: api.sceneSystemApp,
        method: "get",
        params,
    });
}
// 画屏设置-批量更新
export function machineUpdateApp(data) {
    return request({
        url: "/cloud/painted/screen/machine/updateApp",
        method: "post",
        data,
    });
}

// 画屏设置-重启
export function machineRestart(data) {
    return request({
        url: "/cloud/painted/screen/machine/restart",
        method: "post",
        data,
    });
}
