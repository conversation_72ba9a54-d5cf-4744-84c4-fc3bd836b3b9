/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2021-11-29 16:18:00
 * @LastEditors: jingrou
 * @LastEditTime: 2022-04-07 16:32:07
 */
import request from '@/utils/request'
import Qs from 'qs'
import api from './index.ts'

export function selectSchoolEventList(parameter) {
    return request({
        url: api.selectSchoolEventList,
        method: 'post',
        data: parameter
    })
}
export function saveSchoolCalendar(data) {
    return request({
        url: api.saveSchoolCalendar,
        method: 'post',
        data
    })
}

export function getSemesterAndSchoolYear(parameter) {
    return request({
        url: api.getSemesterAndSchoolYear + '/' + parameter,
        method: 'get',
        data: {}
    })
}

export function importCalendarEvent(data) {
    return request({
        url: api.importCalendarEvent,
        method: 'post',
        data
    })
}

// 导出
export function exportInfo(data, fileName) {
    return request({
        url: 'cloud/calendar/exportCalendar',
        method: 'post',
        responseType: 'arraybuffer',
        data
    }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })
        )
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
            'download',
            fileName ? `${fileName}.xlsx` : 'excel.xlsx'
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    })
}
// 删除
export function removeSchoolCalendar(data) {
    return request({
        url: api.removeSchoolCalendar,
        method: 'post',
        data
    })
}

// 编辑

export function editSchoolRemark(data) {
    return request({
        url: api.editSchoolRemark,
        method: 'post',
        data
    })
}
