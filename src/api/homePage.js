/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jing<PERSON>
 * @Date: 2022-04-08 10:27:37
 * @LastEditors: jingrou
 * @LastEditTime: 2022-04-20 14:32:54
 */
import api from "./index";
import axios from "@/utils/request";

export function getTeacherTimetable(data) {
    return axios({
        url: api.getTeacherTimetable,
        method: "get",
        params: data,
    });
}
// 班级课表数据
export function timetableWeekTime(data) {
    return axios({
        url: api.timetableWeekTime,
        method: "post",
        data,
    });
}

// 班级场地
export function timetableExist(data) {
    return axios({
        url: api.timetableExist,
        method: 'get'
    })
}
// 今日日程
export function todaySchedule(data) {
    return axios({
        url: api.todaySchedule,
        method: "get",
        params: data,
    });
}
// 班级下拉框
export function getClassesByUserId(data) {
    return axios({
        url: api.getClassesByUserId,
        method: "get",
        params: data,
    });
}

// 常见应用
export function getAppHome(data) {
    return axios({
        url: api.getAppHome,
        method: "get",
        params: data,
    });
}

// 新增常见应用
export function addAppHome(data) {
    return axios({
        url: api.addAppHome,
        method: "post",
        data,
    });
}
// 删除常见应用
export function deleteAppHome(data) {
    return axios({
        url: api.deleteAppHome,
        method: "post",
        data,
    });
}
