/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-11-03 14:26:35
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-11-03 14:27:18
 */
import request from '@/utils/request'
import api from './index.ts'

// 登录日志列表
export function logList(data) {
    return request({
        url: api.logList,
        method: 'post',
        data
    })
}
// 操作日志列表
export function operationLogList(data) {
    return request({
        url: api.operationLogList,
        method: 'post',
        data
    })
}
// 操作模块数据
export function operationModule(data) {
    return request({
        url: api.operationModule,
        method: 'get',
        params: data
    })
}

export function noticeLogList(data) {
    return request({
        url: api.logList,
        method: 'post',
        data
    })
}
export function saveLogSendConfig(data) {
    return request({
        url: "/cloud/systemOperationLog/saveLogSendConfig",
        method: 'post',
        data
    })
}
export function getLogSendConfig(data) {
    return request({
        url: "/cloud/systemOperationLog/getLogSendConfig",
        method: 'post',
        data
    })
}
// 通知查看-分页
export function systemOperationSend(data) {
    return request({
        url: "/cloud/systemOperationLog/sendPage",
        method: 'post',
        data
    })
}