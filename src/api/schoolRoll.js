/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-04-13 18:15:07
 * @LastEditors: jingrou
 * @LastEditTime: 2023-02-06 17:22:20
 */
import api from './index'
import axios from '@/utils/request'

export function getStudentUserList(data) {
    return axios({
        url: api.pageStudent,
        method: 'post',
        data
    })
}

export function studentlistSearch(data) {
    return axios({
        url: api.studentlistSearch,
        method: 'post',
        data
    })
}

export function updateRollSort(data) {
    return axios({
        url: api.updateRollSort,
        method: 'post',
        data
    })
}

export function schoolRollDelete(data) {
    return axios({
        url: api.schoolRollDelete,
        method: 'post',
        data
    })
}

export function studentDetails(params) {
    return axios({
        url: api.studentDetails,
        method: 'get',
        params
    })
}
export function studentlistRollTree(params) {
    return axios({
        url: api.studentlistRollTree,
        method: 'get',
        params
    })
}
// 大学生毕业
export function graduation(data) {
    return axios({
        url: api.graduation,
        method: 'post',
        data
    })
}
export function studentGraduation(data) {
    return axios({
        url: api.studentGraduation,
        method: 'post',
        data
    })
}

export function studentClass(data) {
    return axios({
        url: api.studentClass,
        method: 'post',
        data
    })
}

export function deleteStudent(data) {
    return axios({
        url: api.deleteStudent,
        method: 'post',
        data
    })
}
export function studentRetirement(data) {
    return axios({
        url: api.studentRetirement,
        method: 'post',
        data
    })
}

export function schoolRollCreateTemplate(data) {
    return axios({
        url: api.schoolRollCreateTemplate,
        method: 'post',
        data
    })
}

export function schoolRollTemplate(params) {
    return axios({
        url: api.schoolRollTemplate,
        method: 'get',
        params
    })
}

export function schoolRollPerson(data) {
    return axios({
        url: api.schoolRollPerson,
        method: 'post',
        data
    })
}

export function subjectList(data) {
    return axios({
        url: api.subjectList,
        method: 'post',
        data
    })
}

export function getFileUpload(data) {
    return axios({
        url: api.fileUpload,
        method: 'post',
        data
    })
}

export function studentelternDelete(data) {
    return axios({
        url: api.studentelternDelete,
        method: 'post',
        data
    })
}

export function createStudent(data) {
    return axios({
        url: api.createStudent,
        method: 'post',
        data
    })
}
export function studentelternCreate(data) {
    return axios({
        url: api.studentelternCreate,
        method: 'post',
        data
    })
}
export function updateStudent(data) {
    return axios({
        url: api.updateStudent,
        method: 'post',
        data
    })
}

export function areaList(params) {
    return axios({
        url: api.areaList,
        method: 'get',
        params
    })
}

export function schoolRollUpdate(data) {
    return axios({
        url: api.schoolRollUpdate,
        method: 'post',
        data
    })
}

export function schoolRollCreate(data) {
    return axios({
        url: api.schoolRollCreate,
        method: 'post',
        data
    })
}

export function schoolRollDetails(params) {
    return axios({
        url: api.schoolRollDetails,
        method: 'get',
        params
    })
}

export function rollSelectData(params) {
    return axios({
        url: api.rollSelectData,
        method: 'get',
        params
    })
}

export function rollAcademics(params) {
    return axios({
        url: api.getRollAcademics,
        method: 'get',
        params
    })
}

export function studentCreate(data) {
    return axios({
        url: api.studentCreate,
        method: 'post',
        data
    })
}

export function studentStudyCheck(params) {
    return axios({
        url: api.studentStudyCheck,
        method: 'get',
        params
    })
}

export function studentTransfer(data) {
    return axios({
        url: api.studentTransfer,
        method: 'post',
        data
    })
}
// 学生列表-信息统计
export function statistics(data) {
    return axios({
        url: api.statistics,
        method: 'post',
        data
    })
}
// 升学-分页
export function getUpgradePage(data) {
    return axios({
        url: '/cloud/upgrade/page',
        method: 'post',
        data
    })
}

// 升学-获取
export function getUpgradeOperate(params) {
    return axios({
        url: '/cloud/upgrade/operate',
        method: 'get',
        params
    })
}
// 升学预览
export function getUpgradePreview(params) {
    return axios({
        url: '/cloud/upgrade/preview',
        method: 'get',
        params
    })
}

// 升学预览
export function getUpgradeUpgrade(params) {
    return axios({
        url: '/cloud/upgrade/upgrade',
        method: 'get',
        params
    })
}

// 毕业-选学段
export function getGraduateRollList(params) {
    return axios({
        url: '/cloud/upgrade/graduateRollList',
        method: 'get',
        params
    })
}

// 确认毕业
export function postUpgradeGraduate(data) {
    return axios({
        url: '/cloud/upgrade/graduate',
        method: 'post',
        data
    })
}

// 获取学校配置
export function postSchoolConfigList(data) {
    return axios({
        url: '/cloud/school/config/list',
        method: 'post',
        data
    })
}

// 修改学校配置
export function postSchoolConfigUpdater(data) {
    return axios({
        url: '/cloud/school/config/updater',
        method: 'post',
        data
    })
}

// 更新通讯录可见性范围
export function contactVisibilityUpdate(data) {
    return axios({
        url: '/cloud/contactVisibility/update',
        method: 'post',
        data
    })
}

// 获取学校通讯录可见性配置
export function contactVisibilityGet(params) {
    return axios({
        url: '/cloud/contactVisibility/get',
        method: 'get',
        params
    })
}