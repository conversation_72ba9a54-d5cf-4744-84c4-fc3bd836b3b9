import Qs from 'qs'
import api from './index'
import axios from '@/utils/request'

export function admineptDoDelete(data) {
    return axios({
        url: api.admineptDoDelete,
        method: 'post',
        data
    })
}
export function admineptUpdateDeptSort(data) {
    return axios({
        url: api.admineptUpdateDeptSort,
        method: 'post',
        data
    })
}
export function facultyDelete(data) {
    return axios({
        url: api.facultyDelete,
        method: 'post',
        data
    })
}
export function facultyAdjust(data) {
    return axios({
        url: api.facultyAdjust,
        method: 'post',
        data
    })
}

export function facultyDetails(params) {
    return axios({
        url: api.facultyDetails,
        method: 'get',
        params
    })
}

export function listRoleType(params) {
    return axios({
        url: api.listRoleType,
        method: 'get',
        params
    })
}
export function subjectListNoPag(params) {
    return axios({
        url: api.subjectListNoPag,
        method: 'get',
        params
    })
}
export function areaList(params) {
    return axios({
        url: api.areaList,
        method: 'get',
        params
    })
}

export function createFaculty(data) {
    return axios({
        url: api.createFaculty,
        method: 'post',
        data
    })
}
export function facultyUpdate(data) {
    return axios({
        url: api.facultyUpdate,
        method: 'post',
        data
    })
}

export function admineptDoEdit(data) {
    return axios({
        url: api.admineptDoEdit,
        method: 'post',
        data
    })
}

export function admineptDoCreate(data) {
    return axios({
        url: api.admineptDoCreate,
        method: 'post',
        data
    })
}
export function employeeListByDept(params) {
    return axios({
        url: api.employeeListByDept,
        method: 'get',
        params
    })
}
export function admineptDoSearch(data) {
    return axios({
        url: api.admineptDoSearch,
        method: 'post',
        data
    })
}
// 邀请成员（教师）
export function employeeInvite(params) {
    return axios({
        url: api.employeeInvite,
        method: 'get',
        params
    })
}

// 邀请成员（学籍）
export function studentInvite(params) {
    return axios({
        url: api.studentInvite,
        method: 'get',
        params
    })
}

// 邀请切换过期时间
export function employeeCutTime(data) {
    return axios({
        url: api.employeeCutTime,
        method: 'post',
        data
    })
}
