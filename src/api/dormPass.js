/* eslint-disable */
import request from "@/utils/request";
import api from "./index.ts";

export function getpageDormPass(data) {
    return request({
        url: api.getpageDormPass,
        method: "post",
        data,
    });
}
export function getDetail(data) {
    return request({
        url: api.getDetail,
        method: "post",
        data,
    });
}
export function getqueryWebPassData(data) {
    return request({
        url: api.getqueryWebPassData,
        method: "post",
        data,
    });
}
export function getpageAccessRecordsInfo(data) {
    return request({
        url: api.getpageAccessRecordsInfo,
        method: "post",
        data,
    });
}

export function getClassesDetail(data) {
    return request({
        url: api.getClassesDetail,
        method: "get",
        params: data,
    });
}