
import request from '@/utils/request'
import api from './index.ts'
// 获取年级树
export function getGradeTree() {
    return request({
        url: api.getGradeTree,
        method: 'get'
    })
}

// 获取科目信息
export function getSubList() {
    return request({
        url: api.getSubList,
        method: 'get'
    })
}
// 获取本班排位table列表
export function getThisSiteList(data) {
    return request({
        url: api.getThisSiteList,
        method: 'get',
        params: data
    })
}

// 获取楼栋列表
export function getBuildList(data) {
    return request({
        url: api.getBuildList,
        method: 'get',
        params: data
    })
}

// 考试列表分页
export function getExamPageList(data) {
    return request({
        url: api.getExamPageList,
        method: 'post',
        data
    })
}

// 删除考试
export function delExam(data) {
    return request({
        url: api.delExam,
        method: 'get',
        params: data
    })
}

// 开启考试
export function startExam(data) {
    return request({
        url: api.startExam,
        method: 'get',
        params: data
    })
}

// 获取考试基础信息
export function getExamInfo(data) {
    return request({
        url: api.getExamInfo,
        method: 'get',
        params: data
    })
}

// 获取考试设置信息
export function getExamDetInfo(data) {
    return request({
        url: api.getExamDetInfo,
        method: 'get',
        params: data
    })
}

// 新增统一考试时间
export function createUnified(data) {
    return request({
        url: api.createUnified,
        method: 'post',
        data
    })
}

// 修改统一考试时间
export function updateUnified(data) {
    return request({
        url: api.updateUnified,
        method: 'post',
        data
    })
}

// 新增按年级设置考试时间
export function createSeparate(data) {
    return request({
        url: api.createSeparate,
        method: 'post',
        data
    })
}

// 修改按年级设置考试时间
export function updateSeparate(data) {
    return request({
        url: api.updateSeparate,
        method: 'post',
        data
    })
}

// 考试年级列表（搜索条件）
export function getExamGradeList(data) {
    return request({
        url: api.getExamGradeList,
        method: 'get',
        params: data
    })
}

// 科目监考老师列表
export function getSubjectInvigilatorList(data) {
    return request({
        url: api.getSubjectInvigilatorList,
        method: 'post',
        data
    })
}

// 导出科目监考老师列表
export function exportSubjectInvigilator(data) {
    return request({
        url: api.exportSubjectInvigilator,
        responseType: 'blob',
        method: 'get',
        params: data
    })
}

// 场地监考老师分页
export function siteInvigilatorPage(data) {
    return request({
        url: api.siteInvigilatorPage,
        method: 'post',
        data
    })
}

// 导出场地监考老师
export function exportSiteInvigilator(data) {
    return request({
        url: api.exportSiteInvigilator,
        responseType: 'blob',
        method: 'get',
        params: data
    })
}

// 考试考场列表（搜索）
export function getExamSiteList(data) {
    return request({
        url: api.getExamSiteList,
        method: 'post',
        data
    })
}

// 获取班级列表(搜索)
export function getExamClassesList(data) {
    return request({
        url: api.getExamClassesList,
        method: 'post',
        data
    })
}

// 考场座位表分页
export function getExamSeatingPage(data) {
    return request({
        url: api.getExamSeatingPage,
        method: 'post',
        data
    })
}

// 导出考试座位表分页
export function exportExamSeating(data) {
    return request({
        url: api.exportExamSeating,
        responseType: 'blob',
        method: 'post',
        data
    })
}

// 考场信息分页
export function examSitePage(data) {
    return request({
        url: api.examSitePage,
        method: 'post',
        data
    })
}

// 导出考场信息表
export function exportExamSite(data) {
    return request({
        url: api.exportExamSite,
        responseType: 'blob',
        method: 'post',
        data
    })
}

// 考场座位贴
export function examSeatStickers(data) {
    return request({
        url: api.examSeatStickers,
        method: 'post',
        data
    })
}

// 导出座位贴
export function exportExamSeatStickers(data) {
    return request({
        url: api.exportExamSeatStickers,
        responseType: 'blob',
        method: 'post',
        data
    })
}

// 考试科目列表（搜索）
export function getsubjectList(data) {
    return request({
        url: api.getsubjectList,
        method: 'post',
        data
    })
}

// 签到表分页
export function examSignInPage(data) {
    return request({
        url: api.examSignInPage,
        method: 'post',
        data
    })
}

// 导出签到表
export function exportExamSignIn(data) {
    return request({
        url: api.exportExamSignIn,
        method: 'post',
        responseType: 'arraybuffer',
        data
    })
}


// 考场管理
export function getCloudBuildList(data) {
    return request({
        url: api.getCloudBuildList,
        method: 'get',
        params: data
    })
}


export function siteTypeList(data) {
    return request({
        url: api.siteTypeList,
        method: 'get',
        params: data
    })
}


export function sitePage(data) {
    return request({
        url: api.sitePage,
        method: 'post',
        data
    })
}


export function createExamSiteManage(data) {
    return request({
        url: api.createExamSiteManage,
        method: 'post',
        data
    })
}


export function getexamSiteManagePage(data) {
    return request({
        url: api.getexamSiteManagePage,
        method: 'post',
        data
    })
}

export function batchUpdateExamSiteManage(data) {
    return request({
        url: api.batchUpdateExamSiteManage,
        method: 'post',
        data
    })
}
export function deleteExamSiteManage(data) {
    return request({
        url: api.deleteExamSiteManage,
        method: 'post',
        data
    })
}


export function createExam(data) {
    return request({
        url: api.createExam,
        method: 'post',
        data
    })
}


export function updateExamSubject(data) {
    return request({
        url: api.updateExamSubject,
        method: 'post',
        data
    })
}


export function getExamPersonCountInfo(data) {
    return request({
        url: api.getExamPersonCountInfo,
        method: 'get',
        params: data
    })
}

export function getDistributeExamSiteDetail(data) {
    return request({
        url: api.getDistributeExamSiteDetail,
        method: 'post',
        data
    })
}



export function listExamSite(data) {
    return request({
        url: api.listExamSite,
        method: 'post',
        data
    })
}

export function distributeExamSite(data) {
    return request({
        url: api.distributeExamSite,
        method: 'post',
        data
    })
}

export function deleteExamSite(data) {
    return request({
        url: api.deleteExamSite,
        method: 'post',
        data
    })
}


export function changeExamGroupArrange(data) {
    return request({
        url: api.changeExamGroupArrange,
        method: 'post',
        data
    })
}


export function pageExamSitePerson(data) {
    return request({
        url: api.pageExamSitePerson,
        method: 'post',
        data
    })
}


export function pageExamGroupAdmin(data) {
    return request({
        url: api.pageExamGroupAdmin,
        method: 'post',
        data
    })
}




export function updateExamGroupAdmin(data) {
    return request({
        url: api.updateExamGroupAdmin,
        method: 'post',
        data
    })
}



// 换座位

export function switchExamSeat(data) {
    return request({
        url: api.switchExamSeat,
        method: 'post',
        data
    })
}



export function updateExamInfo(data) {
    return request({
        url: api.updateExamInfo,
        method: 'post',
        data
    })
}


export function getExamSubject(data) {
    return request({
        url: api.getExamSubject,
        method: 'get',
        params: data
    })
}

export function listExamBuilding(data) {
    return request({
        url: api.listExamBuilding,
        method: 'get',
        params: data
    })
}

export function listExamSiteType(data) {
    return request({
        url: api.listExamSiteType,
        method: 'get',
        params: data
    })
}

export function deleteBatch(data) {
    return request({
        url: api.deleteBatch,
        method: 'post',
        data
    })
}


export function publishExam(data) {
    return request({
        url: api.publishExam,
        method: 'get',
        params: data
    })
}

export function viewExamSeat(data) {
    return request({
        url: api.viewExamSeat,
        method: 'post',
        data
    })
}
