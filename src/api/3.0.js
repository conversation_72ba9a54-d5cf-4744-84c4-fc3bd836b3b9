/* eslint-disable */
import request from "@/utils/request";
import api from "./index.ts";

// 班主任考勤的一些接口
export function getEventList(data) {
    return request({
        url: api.getEventList,
        method: "post",
        data,
    });
}

export function getDayStatisticsPage(data) {
    return request({
        url: api.getDayStatisticsPage,
        method: "post",
        data,
    });
}

export function getdayStatistics(data) {
    return request({
        url: api.getdayStatistics,
        method: "post",
        data,
    });
}

export function getdateStatistics(data) {
    return request({
        url: api.getdateStatistics,
        method: "post",
        data,
    });
}

export function getdateStatisticsPage(data) {
    return request({
        url: api.getdateStatisticsPage,
        method: "post",
        data,
    });
}

export function getdateStatisticsPageDetail(data) {
    return request({
        url: api.getdateStatisticsPageDetail,
        method: "post",
        data,
    });
}

// 班主任班级信息的一些接口
export function getClassesDetail(data) {
    return request({
        url: api.getClassesDetail,
        method: "get",
        params: data,
    });
}

export function getClassesList(data) {
    return request({
        url: api.getClassesList,
        method: "post",
        data,
    });
}

export function getClassesSet(data) {
    return request({
        url: api.getClassesSet,
        method: "post",
        data,
    });
}

export function getclassCreate(data) {
    return request({
        url: api.getclassCreate,
        method: "post",
        data,
    });
}
export function getclassMasterHonorPage(data) {
    return request({
        url: api.getclassMasterHonorPage,
        method: "post",
        data,
    });
}

export function getclassUpdate(data) {
    return request({
        url: api.getclassUpdate,
        method: "post",
        data,
    });
}

export function getclassHonorCount(data) {
    return request({
        url: api.getclassHonorCount,
        method: "get",
        params: data,
    });
}

export function getclassHonorPage(data) {
    return request({
        url: api.getclassHonorPage,
        method: "post",
        data,
    });
}

export function getclassStudentPage(data) {
    return request({
        url: api.getclassStudentPage,
        method: "post",
        data,
    });
}

export function getqueryClassMaterList(data) {
    return request({
        url: api.getqueryClassMaterList,
        method: "get",
        params: data,
    });
}

export function getgetClassesDetail(data) {
    return request({
        url: api.getgetClassesDetail,
        method: "get",
        params: data,
    });
}

export function getclassMasterNotice(data) {
    return request({
        url: api.getclassMasterNotice,
        method: "get",
        params: data,
    });
}

export function queryClassesInfoWindowList(data) {
    return request({
        url: api.queryClassesInfoWindowList,
        method: "get",
        params: data,
    });
}

export function getmasterHome(data) {
    return request({
        url: api.getmasterHome,
        method: "get",
        params: data,
    });
}

export function getmasterhomepage(data) {
    return request({
        url: api.getmasterhomepage,
        method: "post",
        data,
    });
}

export function getv3WeekTime(data) {
    return request({
        url: api.getv3WeekTime,
        method: "post",
        data,
    });
}

export function gettimetableDay(data) {
    return request({
        url: api.gettimetableDay,
        method: "post",
        data,
    });
}

export function gettimetableDayPage(data) {
    return request({
        url: api.gettimetableDayPage,
        method: "post",
        data,
    });
}

export function gettimetableDate(data) {
    return request({
        url: api.gettimetableDate,
        method: "post",
        data,
    });
}

export function gettimetableDatePage(data) {
    return request({
        url: api.gettimetableDatePage,
        method: "post",
        data,
    });
}

export function gettimetableDatePageDetail(data) {
    return request({
        url: api.gettimetableDatePageDetail,
        method: "post",
        data,
    });
}

export function getteachClassesList(data) {
    return request({
        url: api.getteachClassesList,
        method: "get",
        params: data,
    });
}
export function getgetteachClassesList(data) {
    return request({
        url: api.teachClassesList,
        method: "get",
        params: data,
    });
}

export function getClassStudentPage(data) {
    return request({
        url: api.getClassStudentPage,
        method: "post",
        data,
    });
}

export function queryClassTeachList(data) {
    return request({
        url: api.queryClassTeachList,
        method: "get",
        params: data,
    });
}

export function classTeacherNotice(data) {
    return request({
        url: api.classTeacherNotice,
        method: "get",
        params: data,
    });
}

export function getExaminationPage(params) {
    return request({
        url: api.getExaminationPage,
        method: "get",
        params,
    });
}

export function todayTimeByUserId(data) {
    return request({
        url: api.todayTimeByUserId,
        method: 'get',
        params: data
    })
}

export function teacherClassPublishPCTask(data) {
    return request({
        url: api.teacherClassPublishPCTask,
        method: 'get',
        params: data
    })
}

export function tehomepage(data) {
    return request({
        url: api.tehomepage,
        method: "post",
        data,
    });
}

export function v3classCreate(data) {
    return request({
        url: api.v3classCreate,
        method: "post",
        data,
    });
}

export function getV3classPage(data) {
    return request({
        url: api.getV3classPage,
        method: "post",
        data,
    });
}

export function getV3Template(data) {
    return request({
        url: api.getV3Template,
        method: "post",
        data,
    });
}
export function viewTemplate(id) {
    return request({
        url: `${api.messLableTemplateDetails}/${id}`,
        method: 'get',
    })
}

export function teacherTodayWork(data) {
    return request({
        url: api.teacherTodayWork,
        method: "get",
        params: data,
    });
}

export function targetComponent(data) {
    return request({
        url: api.targetComponent,
        method: "post",
        data,
    });
}

export function getsearchstudent(data) {
    return request({
        url: api.getsearchstudent,
        method: "post",
        data,
    });
}

export function classCommitteeAdd(data) {
    return request({
        url: api.classCommitteeAdd,
        method: "post",
        data,
    });
}
export function classCommitteeUpdate(data) {
    return request({
        url: api.classCommitteeUpdate,
        method: "post",
        data,
    });
}
export function classCommitteeDel(data) {
    return request({
        url: api.classCommitteeDel,
        method: "post",
        data,
    });
}
export function classCommitteedetail(data) {
    return request({
        url: api.classCommitteedetail,
        method: "post",
        data,
    });
}

export function classCommitteePage(data) {
    return request({
        url: api.classCommitteePage,
        method: "post",
        data,
    });
}
export function classgroupPage(data) {
    return request({
        url: api.classgroupPage,
        method: "post",
        data,
    });
}
export function classgroupAdd(data) {
    return request({
        url: api.classgroupAdd,
        method: "post",
        data,
    });
}
export function queryGroupDetail(data) {
    return request({
        url: api.queryGroupDetail,
        method: "post",
        data,
    });
}
export function queryGroupUpdate(data) {
    return request({
        url: api.queryGroupUpdate,
        method: "post",
        data,
    });
}
export function queryGroupDel(data) {
    return request({
        url: api.queryGroupDel,
        method: "post",
        data,
    });
}

export function classCommitteeBrowseList(data) {
    return request({
        url: api.classCommitteeBrowseList,
        method: "get",
        params: data,
    });
}

export function teWorkPage(data) {
    return request({
        url: api.teWorkPage,
        method: "post",
        data,
    });
}

export function queryWorkInClassList(data) {
    return request({
        url: api.queryWorkInClassList,
        method: "get",
        params: data,
    });
}

export function getSchoolClassWork(data) {
    return request({
        url: api.getSchoolClassWork,
        method: "get",
        params: data,
    });
}

export function queryTeacherSubject(data) {
    return request({
        url: api.queryTeacherSubject,
        method: "get",
        params: data,
    });
}

export function createTeWork(data) {
    return request({
        url: api.createTeWork,
        method: "post",
        data,
    });
}

export function schoolWorkWithdraw(data) {
    return request({
        url: api.schoolWorkWithdraw,
        method: "post",
        data,
    });
}

export function deleteWork(data) {
    return request({
        url: api.deleteWork,
        method: "post",
        data,
    });
}

export function exportCommitteeBrowseList(data) {
    return request({
        url: api.exportCommitteeBrowseList,
        method: "post",
        data,
        responseType: 'arraybuffer',
        headers: { 'Content-Type': 'application/json; application/octet-stream' }
    });
}

export function classMasterLatestNotice(data) {
    return request({
        url: api.classMasterLatestNotice,
        method: "get",
        params: data,
    });
}

export function classTeacherLatestNotice(data) {
    return request({
        url: api.classTeacherLatestNotice,
        method: "get",
        params: data,
    });
}
export function updateVideoIsPush(data) {
    return request({
        url: api.updateVideoIsPush,
        method: "post",
        data,
    });
}

export function createPassscene(data) {
    return request({
        url: api.createPassscene,
        method: "post",
        data,
    });
}
export function pagePassscene(data) {
    return request({
        url: api.pagePassscene,
        method: "post",
        data,
    });
}

export function updatePassscene(data) {
    return request({
        url: api.updatePassscene,
        method: "post",
        data,
    });
}

export function deletePassscene(data) {
    return request({
        url: api.deletePassscene,
        method: "post",
        data,
    });
}

export function listPassscene(data) {
    return request({
        url: api.listPassscene,
        method: "get",
        params: data,
    });
}

export function buildNextStep(data) {
    return request({
        url: api.buildNextStep,
        method: "post",
        data,
    });
}

export function buildNextadd(data) {
    return request({
        url: api.buildNextadd,
        method: "post",
        data,
    });
}

export function createStudentduty(data) {
    return request({
        url: api.createStudentduty,
        method: "post",
        data,
    });
}

export function updateStudentduty(data) {
    return request({
        url: api.updateStudentduty,
        method: "post",
        data,
    });
}

export function deStudentduty(data) {
    return request({
        url: api.deStudentduty,
        method: "post",
        data,
    });
}

export function listStudentduty(data) {
    return request({
        url: api.listStudentduty,
        method: "post",
        data,
    });
}

export function homeStudentduty(data) {
    return request({
        url: api.homeStudentduty,
        method: "get",
        params: data,
    });
}


export function getRegistrarSemesterHome() {
    return request({
        url: api.getRegistrarSemesterHome,
        method: "get",
    });
}
export function buildingCountHome() {
    return request({
        url: api.buildingCountHome,
        method: "get",
    });
}
export function appCenterGetAppHome(data) {
    return request({
        url: api.appCenterGetAppHome,
        method: "get",
        params: data,
    });
}
export function registrarHomepage(data) {
    return request({
        url: api.registrarHomepage,
        method: "get",
        params: data,
    });
}
export function getDeptEmployeeCount() {
    return request({
        url: api.getDeptEmployeeCount,
        method: "get",
    });
}
export function getRollInfoCount() {
    return request({
        url: api.getRollInfoCount,
        method: "get",
    });
}
export function statisticalEventList() {
    return request({
        url: api.statisticalEventList,
        method: "get",
    });
}

export function dayStatisticStudentHome(data) {
    return request({
        url: api.dayStatisticStudentHome,
        method: "post",
        data,
    });
}
export function getWorkGroup(data) {
    return request({
        url: api.getWorkGroup,
        method: "post",
        data
    });
}

export function dayStatisticTeacherHome(data) {
    return request({
        url: api.dayStatisticTeacherHome,
        method: "get",
        params: data,
    });
}

export function listByCode(data) {
    return request({
        url: api.listByCode,
        method: "get",
        params: data,
    });
}

export function teachSubjectClassList(data) {
    return request({
        url: api.teachSubjectClassList,
        method: "get",
        params: data,
    });
}
export function classRegistrarReceivePage() {
    return request({
        url: api.classRegistrarReceivePage,
        method: "get",
    });
}

export function exportDayStatistics(data) {
    return request({
        url: api.exportDayStatistics,
        responseType: 'arraybuffer',
        method: "post",
        data
    });
}

