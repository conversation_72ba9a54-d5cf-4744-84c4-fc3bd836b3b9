/*
 * @Descripttion:学生评价指标设置
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-07-14 10:08:50
 * @LastEditors: jingrou
 * @LastEditTime: 2022-07-20 14:21:58
 */
import request from '@/utils/request'
import api from './index.ts'

/** _______________________ 学生屏蔽活動*/
// 创建评比组
export function createAssessment(data) {
    return request({
        url: api.createAssessment,
        method: 'post',
        data
    })
}
// 评比组列表
export function createAssessmentlist(data) {
    return request({
        url: api.createAssessmentlist,
        method: 'post',
        data
    })
}

export function createAssessmentActivity(data) {
    return request({
        url: api.createAssessmentActivity,
        method: 'post',
        data
    })
}

export function updateAssessmentActivity(data) {
    return request({
        url: api.updateAssessmentActivity,
        method: 'post',
        data
    })
}

export function getAssessmentActivityList(data) {
    return request({
        url: api.getAssessmentActivityList,
        method: 'post',
        data
    })
}

export function removeAssessmentActivity(data) {
    return request({
        url: api.removeAssessmentActivity,
        method: 'post',
        data
    })
}

export function withdrawAssessmentActivity(data) {
    return request({
        url: api.withdrawAssessmentActivity,
        method: 'post',
        data
    })
}

export function publishAssessmentActivity(data) {
    return request({
        url: api.publishAssessmentActivity,
        method: 'post',
        data
    })
}

export function getAssessmentActivityInfo(id) {
    return request({
        url: api.getAssessmentActivityInfo + `?id=${id}`,
        method: 'get'
    })
}

// 活动详情
export function getProgressList(data) {
    return request({
        url: api.getProgressList,
        method: 'post',
        data
    })
}
// 打分情况
export function progressDetail(data) {
    return request({
        url: api.progressDetail,
        method: 'post',
        data
    })
}
// 查看活动周期
export function queryActivityCycle(data) {
    return request({
        url: api.queryActivityCycle,
        method: 'post',
        data
    })
}
// 查看打分明细
export function scoringDetail(data) {
    return request({
        url: api.scoringDetail,
        method: 'post',
        data
    })
}

/** _______________________ 学生奖章设置*/

// 学生类目列表
export function getAssessmentCategoryList(data) {
    return request({
        url: api.getAssessmentCategoryList,
        method: 'post',
        data
    })
}

// 评分细则列表
export function getAssessmentRuleList(data) {
    return request({
        url: api.getAssessmentRuleList,
        method: 'post',
        data
    })
}

export function getAssessmentRuleAllList(data) {
    return request({
        url: api.getAssessmentRuleAllList,
        method: 'post',
        data
    })
}

export function getAssessmentCategoryAllList(data) {
    return request({
        url: api.getAssessmentCategoryAllList,
        method: 'post',
        data
    })
}

// 奖章设置列表
export function getAssessmentMedalList(data) {
    return request({
        url: api.getAssessmentMedalList,
        method: 'post',
        data
    })
}

export function getAssessmentMedalAllList(data) {
    return request({
        url: api.getAssessmentMedalAllList,
        method: 'post',
        data
    })
}

export function createAssessmentCategory(data) {
    return request({
        url: api.createAssessmentCategory,
        method: 'post',
        data
    })
}

export function createAssessmentRule(data) {
    return request({
        url: api.createAssessmentRule,
        method: 'post',
        data
    })
}

export function createAssessmentMedal(data) {
    return request({
        url: api.createAssessmentMedal,
        method: 'post',
        data
    })
}

//
export function removeAssessmentCategory(data) {
    return request({
        url: api.removeAssessmentCategory,
        method: 'post',
        data
    })
}
export function removeAssessmentRule(data) {
    return request({
        url: api.removeAssessmentRule,
        method: 'post',
        data
    })
}
export function removeAssessmentMedal(data) {
    return request({
        url: api.removeAssessmentMedal,
        method: 'post',
        data
    })
}

// 获取奖章样例默认列表
export function getMedalDefaultList(data) {
    return request({
        url: api.getMedalDefaultList,
        method: 'post',
        data
    })
}
