import request from "@/utils/request";

// 创建社团分类
export function postClubCategoryCreate(data) {
    return request({
        url: "/cloud/club/club-category/create",
        method: "post",
        data,
    });
}

// 修改社团分类
export function postClubCategoryUpdate(data) {
    return request({
        url: "/cloud/club/club-category/update",
        method: "post",
        data,
    });
}

//社团分类列表
export function postClubCategoryPage(data) {
    return request({
        url: "/cloud/club/club-category/page",
        method: "post",
        data,
    });
}
// 启用禁用社团分类
export function postClubCategoryEnabled(data) {
    return request({
        url: "/cloud/club/club-category/set-enabled",
        method: "post",
        data,
    });
}
// 删除社团分类
export function postClubCategoryDelete(data) {
    return request({
        url: "/cloud/club/club-category/delete",
        method: "post",
        data,
    });
}

// 社团列表
export function postClubPage(data) {
    return request({
        url: "/cloud/club/club/page",
        method: "post",
        data,
    });
}

// 社团详细信息
export function postClubGet(data) {
    return request({
        url: "/cloud/club/club/get",
        method: "post",
        data,
    });
}

// 创建社团
export function postClubCreate(data) {
    return request({
        url: "/cloud/club/club/create",
        method: "post",
        data,
    });
}

// 修改社团
export function postClubUpdate(data) {
    return request({
        url: "/cloud/club/club/update",
        method: "post",
        data,
    });
}

// 删除社团
export function postClubDelete(data) {
    return request({
        url: "/cloud/club/club/delete",
        method: "post",
        data,
    });
}

// 启用禁用社团
export function postClubSetEnabled(data) {
    return request({
        url: "/cloud/club/club/set-enabled",
        method: "post",
        data,
    });
}

// 上传文件
export function getFileUpload(data) {
    return request({
        url: "/cloud/file/upload",
        method: "post",
        data,
    });
}

// 获取社团规模
export function postClubSizeList(data) {
    return request({
        url: "/cloud/club/club-size/list",
        method: "post",
        data,
    });
}

// 保存社团规模
export function postClubSizeSave(data) {
    return request({
        url: "/cloud/club/club-size/save",
        method: "post",
        data,
    });
}

// 社团审核列表
export function postClubApplicationPage(data) {
    return request({
        url: "/cloud/club/club-application/page",
        method: "post",
        data,
    });
}

// 社团审核详细信息
export function postClubApplicationGet(data) {
    return request({
        url: "/cloud/club/club-application/get",
        method: "post",
        data,
    });
}

// 审核社团
export function postClubApplicationReview(data) {
    return request({
        url: "/cloud/club/club-application/review",
        method: "post",
        data,
    });
}
