import api from './index'
import request from '@/utils/request'
// 获取访客机全局设置信息
export function geteVisitorDetails(params) {
    return request({
        url: api.visitorDetails,
        method: 'get',
        params
    })
}
// 访客机全局设置
export function geteVisitorCreate(data) {
    return request({
        url: api.visitorCreate,
        method: 'post',
        data
    })
}
// 分页访客记录
export function geteVisitorRecordsList(data) {
    return request({
        url: api.visitorRecordsList,
        method: 'post',
        data
    })
}
// 访客统计数据
export function geteVisitorRecordsStatistics(data) {
    return request({
        url: api.visitorRecordsStatistics,
        method: 'post',
        data
    })
}


