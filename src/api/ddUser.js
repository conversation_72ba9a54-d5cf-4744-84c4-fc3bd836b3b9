/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-27 10:35:38
 * @LastEditors: jingrou
 * @LastEditTime: 2023-05-18 11:23:16
 */
import Qs from "qs";
import api from "./index";
import axios from "@/utils/ddRequest";
import addAuthCode from "@/utils/addAuthCode"
export function login(params) {
    return axios({
        url: api.login,
        method: "post",
        data: Qs.stringify(params),
    });
}

export function getCurrentUser(params) {
    return axios({
        url: api.getCurrentUser,
        method: "get",
        data: params,
    });
}

//  校验用户是否可以登录
export function checkUserLogin(data) {
    return axios({
        url: api.checkUserLogin,
        method: "get",
        params: data,
    });
}

// 检查当前用户是否已申请注销
export function checkUserLogout(params) {
    return axios({
        url: api.checkUserLogout,
        method: "get",
        params,
    });
}

// 撤销用户注销
export function cancelLogout(params) {
    return axios({
        url: api.cancelLogout,
        method: "get",
        params,
    });
}