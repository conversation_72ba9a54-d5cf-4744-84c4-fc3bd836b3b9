/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jing<PERSON>
 * @Date: 2022-04-04 17:58:13
 * @LastEditors: jingrou
 * @LastEditTime: 2022-10-29 09:44:20
 */
import request from '@/utils/request'
import api from './index.ts'

// 人脸同步侧边栏
export function getDeviceList(data) {
    return request({
        url: api.getDeviceList,
        method: 'get',
        params: data
    })
}

// 人脸机
export function faceMachinePage(data) {
    return request({
        url: api.pageList,
        method: 'post',
        data
    })
}
export function getRecordPage(data) {
    return request({
        url: api.getRecordPage,
        method: 'post',
        data
    })
}
// 班牌关联人脸库
export function bindFace(data) {
    return request({
        url: api.bindFace,
        method: 'post',
        data
    })
}
export function brand(data) {
    return request({
        url: api.brand,
        method: 'post',
        data
    })
}
// 同步设备信息
export function synchronousFace(data) {
    return request({
        url: api.synchronousFace,
        method: 'post',
        data
    })
}

export function getBindDeviceFaceInfo(data) {
    return request({
        url: api.getBindDeviceFaceInfo,
        method: 'post',
        data
    })
}

// 人脸同步-同步人脸（人脸机）
export function getBarrierSyncInfo(data) {
    return request({
        url: api.barrierSync,
        method: 'post',
        data
    })
}

// 人脸 重启
export function restartFace(data) {
    return request({
        url: api.restartFace,
        method: 'post',
        data
    })
}

// 人脸 更新
export function renewFace(data) {
    return request({
        url: api.renewFace,
        method: 'post',
        data
    })
}
