/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-05-05 13:50:06
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-05-10 16:21:13
 */
import request from "@/utils/request";
import api from "./index.ts";
// 班级信息
export function classInfo(data) {
    return request({
        url: api.classInfo,
        method: "get",
        params: data,
    });
}

// 班级口号列表
export function getSloganList(data) {
    return request({
        url: api.getSloganList,
        method: "post",
        data,
    });
}

// 上传班级头像
export function setClassesIcon(data) {
    return request({
        url: api.setClassesIcon,
        method: "post",
        data,
    });
}

// 上传文件
export function getFileUpload(data) {
    return request({
        url: api.fileUpload,
        method: "post",
        data,
    });
}

// 删除班级口号
export function delSloganList(data) {
    return request({
        url: api.delSloganList,
        method: "post",
        data,
    });
}

// 设置班级口号
export function setSloganList(data) {
    return request({
        url: api.setSloganList,
        method: "post",
        data,
    });
}

// 增加班牌
export function createClassSign(data) {
    return request({
        url: api.createClassSign,
        method: "post",
        data,
    });
}

// 设置班牌
export function updateClassSign(data) {
    return request({
        url: api.updateClassSign,
        method: "post",
        data,
    });
}

// 班牌信息
export function getBrandInfo(data) {
    return request({
        url: api.getBrandInfo,
        method: "get",
        params: data,
    });
}
// 霸屏管理
export function getBrandDominateScreen(data) {
    return request({
        url: api.getBrandDominateScreen,
        method: "get",
        params: data,
    });
}

// 批量设置定时开关机
export function batchSet(data) {
    return request({
        url: api.batchSet,
        method: "post",
        data,
    });
}

// 上课模式
export function classModeUpdate(data) {
    return request({
        url: api.classModeUpdate,
        method: "post",
        data,
    });
}

// 批量远程重启
export function longRestart(data) {
    return request({
        url: api.longRestart,
        method: "post",
        data,
    });
}

// 批量远程关机
export function longclose(data) {
    return request({
        url: api.longclose,
        method: "post",
        data,
    });
}

// 批量开关锁
export function switchsLock(data) {
    return request({
        url: api.switchsLock,
        method: "post",
        data,
    });
}

// 班牌列表
export function ClassSignList(data) {
    return request({
        url: api.ClassSignList,
        method: "post",
        data,
    });
}

// 未创建班牌账号的场地
export function getUnSite(data = {}) {
    return request({
        url: api.getUnSite,
        method: "post",
        params: data,
    });
}

// 场地班牌列表
export function getSite(data) {
    return request({
        url: api.getSite,
        method: "post",
        data,
    });
}

// 获取绑定场地的班牌账号
export function getBindingSiteTree(data) {
    return request({
        url: api.getBindingSiteTree,
        method: "post",
        data,
    });
}

// 获取绑定场地的班牌账号
export function getBindingSiteTreeeV2(data) {
    return request({
        url: api.getBindingSiteTreeeV2,
        method: "post",
        data,
    });
}

// 提醒列表
export function getRemindList(data) {
    return request({
        url: api.getRemindList,
        method: "post",
        data,
    });
}

// 学生作品-列表
export function getStudentworkPage(data) {
    return request({
        url: api.studentworkPage,
        method: "post",
        data,
    });
}
// 学生作品-删除
export function studentworkDelete(data) {
    return request({
        url: api.studentworkDelete,
        method: "post",
        data,
    });
}

// 学生作品-创建
export function studentworkCreate(data) {
    return request({
        url: api.studentworkCreate,
        method: "post",
        data,
    });
}

// 创建提醒
export function createClassRemind(data) {
    return request({
        url: api.createClassRemind,
        method: "post",
        data,
    });
}

// 删除提醒
export function delClassRemind(data) {
    return request({
        url: api.delClassRemind,
        method: "post",
        data,
    });
}

// 提醒详情
export function classRemindInfo(data) {
    return request({
        url: api.classRemindInfo,
        method: "get",
        params: data,
    });
}

// 班牌布局首页模板列表
export function getLayoutList(data) {
    return request({
        url: api.getLayoutList,
        method: "post",
        data,
    });
}

// 班牌布局添加编辑页模块列表
export function getLayoutModuleList(data) {
    return request({
        url: api.getLayoutModuleList,
        method: "post",
        data,
    });
}

// 班牌布局模板创建
export function creatLayout(data) {
    return request({
        url: api.creatLayout,
        method: "post",
        data,
    });
}

// 班牌布局添加编辑页分类列表
export function getClassifyList(data) {
    return request({
        url: api.getClassifyList,
        method: "post",
        data,
    });
}

// 班牌布局编辑页获取详情
export function classLayoutInfo(id) {
    return request({
        url: `${api.classLayoutInfo}/${id}`,
        method: "get",
    });
}

// 班牌布局模块删除接口
export function delClassTemplate(data) {
    return request({
        url: api.delClassTemplate,
        method: "post",
        data,
    });
}

// 班牌布局模块更新接口
export function updateClassTemplate(data) {
    return request({
        url: api.updateClassTemplate,
        method: "post",
        data,
    });
}
// 模板 - 发布到班牌
export function publishClassTemplate(data) {
    return request({
        url: api.publishClassTemplate,
        method: "post",
        data,
    });
}
// 霸屏管理
export function setBrandDominateScreen(data) {
    return request({
        url: api.setBrandDominateScreen,
        method: "post",
        data,
    });
}
// 自定义模式
export function customPage(data) {
    return request({
        url: api.customPage,
        method: "post",
        data,
    });
}
// 删除自定义模式
export function createDel(data) {
    return request({
        url: api.createDel,
        method: "post",
        data,
    });
}
// 编辑自定义模式
export function editUpdate(data) {
    return request({
        url: api.editUpdate,
        method: "post",
        data,
    });
}

// 添加自定义模式
export function createPage(data) {
    return request({
        url: api.createPage,
        method: "post",
        data,
    });
}
// 更新自定义模式启停状态
export function customUpdate(data) {
    return request({
        url: api.customUpdate,
        method: "post",
        data,
    });
}
// 班牌设备列表
export function deviceList(data) {
    return request({
        url: api.deviceList,
        method: "post",
        data,
    });
}
// 考勤机
export function machineList(data) {
    return request({
        url: api.machineList,
        method: "post",
        data,
    });
}
// 人脸同步列表
export function getRecordPage(data) {
    return request({
        url: api.getRecordPage,
        method: "post",
        data,
    });
}
// 同步全部人脸
export function syncAll(data) {
    return request({
        url: api.syncAll,
        method: "post",
        data,
    });
}
// 德育同步人脸
export function syncv2All(data) {
    return request({
        url: api.syncv2All,
        method: "post",
        data,
    });
}
// 同步人脸(清空)
export function clearFace(data) {
    return request({
        url: api.clearFace,
        method: "post",
        data,
    });
}
// 关联人脸库
export function bindFaceInfo(data) {
    return request({
        url: api.bindFaceInfo,
        method: "post",
        data,
    });
}
// 获取自定义组人员信息
export function getFaceInfo(data) {
    return request({
        url: api.getFaceInfo,
        method: "post",
        data,
    });
}

// 获取背景图
export function getSignImageList(data) {
    return request({
        url: api.getSignImageList,
        method: "post",
        data,
    });
}

export function brandAuthority(data) {
    return request({
        url: api.brandAuthority,
        method: "post",
        data,
    });
}

export function brandAuthorityPage(data) {
    return request({
        url: api.brandAuthorityPage,
        method: "post",
        data,
    });
}

export function brandAuthorityUp(data) {
    return request({
        url: api.brandAuthorityUp,
        method: "post",
        data,
    });
}

export function brandAuthorityDe(data) {
    return request({
        url: api.brandAuthorityDe,
        method: "get",
        params: data,
    });
}

export function getModeCreate(data) {
    return request({
        url: api.getModeCreate,
        method: "post",
        data,
    });
}
export function getModePage(data) {
    return request({
        url: api.getModePage,
        method: "post",
        data,
    });
}
export function getModeDelete(data) {
    return request({
        url: api.getModeDelete,
        method: "post",
        data,
    });
}

export function getModeStatusUpdate(data) {
    return request({
        url: api.getModeStatusUpdate,
        method: "post",
        data,
    });
}

export function getModeinfo(data) {
    return request({
        url: api.getModeinfo,
        method: "get",
        params: data,
    });
}

export function getModeupdate(data) {
    return request({
        url: api.getModeupdate,
        method: "post",
        data,
    });
}

export function getLevelList() {
    return request({
        url: api.getLevelList,
        method: "get",
    });
}

// 导出班牌
export function brandClassSignExport(data) {
    return request({
        url: "/brand/brand/export",
        responseType: "blob",
        method: "post",
        data,
    });
}

// 批量修改密码
export function postBatchPasswords(data) {
    return request({
        url: "",
        method: "post",
        data,
    });
}
// 班牌 获取模块详情
export function getCustomizeModule(params) {
    return request({
        url: "/brand/layoutclasssign/v2/customize/module/get",
        method: "get",
        params,
    });
}

// 创建自定义模块-链接 图片 图文
export function createCustomizeModule(data) {
    return request({
        url: "/brand/layoutclasssign/v2/customize/module/create",
        method: "post",
        data,
    });
}

// 编辑自定义模块-链接
export function updateCustomizeModule(data) {
    return request({
        url: "/brand/layoutclasssign/v2/customize/module/update",
        method: "post",
        data,
    });
}
