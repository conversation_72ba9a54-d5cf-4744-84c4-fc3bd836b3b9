/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jing<PERSON>
 * @Date: 2022-02-17 20:44:03
 * @LastEditors: jingrou
 * @LastEditTime: 2022-02-19 17:31:04
 */
import request from '@/utils/request'
import Qs from 'qs'
import api from './index.ts'
// 判斷是否調用mock數據

// 列表学年
export function yearList (data) {
    return request({
        url: api.yearList,
        method: 'post',
        data
    })
}
// 添加学年
export function addYear (data) {
    return request({
        url: api.addYear,
        method: 'post',
        data
    })
}

// 编辑学年
export function updateYear (data) {
    return request({
        url: api.updateYear,
        method: 'post',
        data
    })
}
// 删除学年
export function strikeOutYear (data) {
    return request({
        url: api.strikeOutYear,
        method: 'post',
        data
    })
}
// 列表学年
export function semesterList (data) {
    return request({
        url: api.semesterList,
        method: 'post',
        data
    })
}
// 添加学期
export function addSemester (data) {
    return request({
        url: api.addSemester,
        method: 'post',
        data
    })
}
// 回显学期
export function getInfo (data) {
    return request({
        url: api.getInfo,
        method: 'get',
        params: data
    })
}
// 修改学期状态
export function updateStatus (data) {
    return request({
        url: api.updateStatus,
        method: 'post',
        data
    })
}
// 编辑学期
export function updateSemester (data) {
    return request({
        url: api.updateSemester,
        method: 'post',
        data
    })
}
// 删除学期
export function deleteSemester (data) {
    return request({
        url: api.deleteSemester,
        method: 'post',
        data
    })
}

// 查一下这个学期 有没有绑定作息表 返回有数组就是绑定了,返回空数组那就是没有绑定呗
export function timetableRoutineBySemesterId(data) {
    return request({
        url: api.timetableRoutineBySemesterId,
        method: 'get',
        params: data
    })
}
