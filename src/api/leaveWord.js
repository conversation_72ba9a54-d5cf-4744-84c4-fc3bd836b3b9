import request from "@/utils/request";
import api from "./index.ts";

// 留言列表
export function leaveWordList(data) {
    return request({
        url: api.leaveWordList,
        method: "post",
        data,
    });
}

// 回复（修改）留言
export function updateWord(data) {
    return request({
        url: api.updateWord,
        method: "post",
        data,
    });
}

// 删除留言
export function delWord(id) {
    return request({
        url: `${api.delWord}?id=${id}`,
        method: "get",
    });
}

// 删除留言回复
export function delreply(id) {
    return request({
        url: `${api.delreply}?id=${id}`,
        method: "get",
    });
}

// 匿名展示
export function postAnonymousapi(data) {
    return request({
        url: api.updateLeaveWordSetting,
        method: "post",
        data,
    });
}

// 获取匿名展示
export function getAnonymousapi(params) {
    return request({
        url: api.getLeaveWordSetting,
        method: "get",
        params,
    });
}
