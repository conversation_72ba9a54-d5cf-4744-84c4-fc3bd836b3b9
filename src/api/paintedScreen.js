// 画屏
import request from '@/utils/request'

// 画屏设置--分页
export function screenMachinePage(data) {
  return request({
    url: '/cloud/painted/screen/machine/page',
    method: 'post',
    data
  })
}
// 画屏设置-重启
export function screenMachineRestart(data) {
  return request({
    url: '/cloud/painted/screen/machine/restart',
    method: 'post',
    data
  })
}
// 画屏设置-关机
export function screenMachineClose(data) {
  return request({
    url: '/cloud/painted/screen/machine/close',
    method: 'post',
    data
  })
}
// 画屏设置-批量设置定时开关机
export function screenMachineUpdateSwitch(data) {
  return request({
    url: '/cloud/painted/screen/machine/batchUpdateSwitch',
    method: 'post',
    data
  })
}
// 画屏设置-编辑
export function screenMachineUpdate(data) {
  return request({
    url: '/cloud/painted/screen/machine/update',
    method: 'post',
    data
  })
}


// 画屏设置-获取详情
export function screenMachineDetail(params) {
  return request({
    url: '/cloud/painted/screen/machine/detail',
    method: 'get',
    params
  })
}