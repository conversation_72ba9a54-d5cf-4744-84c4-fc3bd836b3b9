/*
 * @Descripttion: 问卷 questionnaire
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-05-17 10:39:22
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-05-21 11:36:19
 */
import request from "@/utils/request";
import api from "./index.ts";

// 获取学校、教职工、家长上报列表
export function getQuestionnaireList(data) {
    return request({
        url: api.questionnaireList,
        method: "get",
        data,
    });
}
//   获取未上报教职工详情列表
export function getQuestionnaireEmployee(data) {
    return request({
        url: api.questionnaireEmployee,
        method: "post",
        data,
    });
}
// / 获取已上报教职工详情列表
export function getQuestionnaireEmployeeDetail(data) {
    return request({
        url: api.questionnaireEmployeeDetail,
        method: "post",
        data,
    });
}

// 获取未上报家长上报列表
export function getQuestionnaireElternPage(data) {
    return request({
        url: api.questionnaireElternPage,
        method: "post",
        data,
    });
}
// 获取已上报家长上报列表
export function getQuestionnaireElternDetail(data) {
    return request({
        url: api.questionnaireElternDetailPage,
        method: "post",
        data,
    });
}
// 提交上报教职工或家长
export function questionnaireCreateEmpElt(data) {
    return request({
        url: `${api.questionnaireCreateemployeeOreltern}?type=${data}`,
        method: "get",
        data,
    });
}

// 上报学校信息
export function questionnaireCreateschool(data) {
    return request({
        url: api.questionnaireCreateschool,
        method: "post",
        data,
    });
}

// 上报学校查看详情
export function getQuestionnaireSchooldetails(data) {
    return request({
        url: api.questionnaireSchooldetails,
        method: "get",
        data,
    });
}

// 导出信息不全的人员
export function getQuestExportEmployeeOrEltern(data, params, fileName) {
    request({
        url: `${api.questExportEmployeeOrEltern}?type=${data}`,
        method: 'get',
        responseType: 'arraybuffer',
        params
    }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })
        )
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', fileName ? `${fileName}.xlsx` : 'excel.xlsx')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    })
}