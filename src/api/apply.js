/*
 * @Descripttion: 应用相关api
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-03-23 16:23:53
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-04-23 20:36:43
 */

import request from "@/utils/request";
import api from "./index.ts";
export function getApplyGroupList(data) {
    return request({
        url: api.appCenterList,
        method: "get",
        params: data,
    });
}

export function updataApplyStatus(data) {
    return request({
        url: api.appCenterUpdateById,
        method: "post",
        data,
    });
}

export function loginThirdPartyApply(data) {
    return request({
        url: `/open/app/login/web`,
        method: "post",
        data,
    });
}
