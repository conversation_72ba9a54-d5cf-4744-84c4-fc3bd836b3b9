/*
 * @Descripttion:  学生考取api
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-03-29 16:32:37
 * @LastEditors: jingrou
 * @LastEditTime: 2023-03-17 17:13:50
 */
import Qs from "qs" // eslint-disable-line no-unused-vars
import api from "./index"
import axios from "@/utils/request"

// 字典查询
export function dataDictionary(data) {
    return axios({
        url: api.dataDictionary,
        method: "post",
        data
    })
}

/* 出入校
---------------------------------------------------------------- */

export function createStudentAttendance(params) {
    return axios({
        url: api.createStudentAttendance,
        method: "post",
        data: params
    })
}
export function deleteStudentAttendance(params) {
    return axios({
        url: api.deleteStudentAttendance,
        method: "post",
        data: params
    })
}

export function queryStudentAttendance(params) {
    return axios({
        url: api.queryStudentAttendance,
        method: "post",
        data: params
    })
}
export function getAttendanceDeviceList(params) {
    return axios({
        url: api.getAttendanceDeviceList,
        method: "get",
        params
    })
}

export function getAttendanceInfo(params) {
    return axios({
        url: api.getAttendanceInfo + "?" + Qs.stringify(params),
        method: "get"
    })
}

export function updateStudentAttendance(params) {
    return axios({
        url: api.updateStudentAttendance,
        method: "post",
        data: params
    })
}

/* 事件考勤
---------------------------------------------------------------- */

/* 课程考勤
---------------------------------------------------------------- */
export function queryStudentClassroomAttendance(params) {
    return axios({
        url: api.queryStudentClassroomAttendance,
        method: "post",
        data: params
    })
}

export function getSClassroomAttConfig(params) {
    return axios({
        url: api.getSClassroomAttendanceConfig,
        method: "get",
        params
    })
}

export function updateSClassroomAttConfig(params) {
    return axios({
        url: api.updateSClassroomAttendanceConfig,
        method: "post",
        data: params
    })
}
export function updateSClassroomAttSubject(params) {
    return axios({
        url: api.updateSClassroomAttendanceSubject,
        method: "post",
        data: params
    })
}

/* 考勤统计
---------------------------------------------------------------- */
// 月统计
export function statisticalMonth(data) {
    return axios({
        url: api.statisticalMonth,
        method: "post",
        data
    })
}
// 日统计
export function statisticalDay(data) {
    return axios({
        url: api.statisticalDay,
        method: "post",
        data
    })
}

export function queryStatisticalDayDetail(data) {
    return axios({
        url: api.statisticalDayDetail,
        method: "post",
        data
    })
}

/* 考勤明细
---------------------------------------------------------------- */
export function pageDataDetail(data) {
    return axios({
        url: api.pageDataDetail,
        method: "post",
        data
    })
}
export function dataDetailCount(data) {
    return axios({
        url: api.dataDetailCount,
        method: "post",
        data
    })
}
// 导出
export function exportInfo(data, fileName) {
    return axios({
        url: "/attweb/eventStatistical/export",
        method: "post",
        responseType: "arraybuffer",
        data
    }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            })
        )
        const link = document.createElement("a")
        link.style.display = "none"
        link.href = url
        link.setAttribute(
            "download",
            fileName ? `${fileName}.xlsx` : "excel.xlsx"
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    })
}
