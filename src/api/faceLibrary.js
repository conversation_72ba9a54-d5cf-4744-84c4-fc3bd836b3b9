/*
 * @Descripttion: 人脸库api
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-02-22 19:41:23
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-05-13 09:53:26
 */
import api from "./index";
import axios from "@/utils/request";

export function getFacePageList(params) {
    return axios({
        url: api.getFacePageList,
        method: "post",
        data: params,
    });
}

// 获取学籍tree
export function getSchoolRollTree(code) {
    return axios({
        url: code ? api.schoolRoll + `?code=${code}` : api.schoolRoll,
        method: "get",
        data: {},
    });
}
// 获取教职工tree
export function getDepartmentTree(code) {
    return axios({
        url: code ? api.admineptDoList + `?code=${code}` : api.admineptDoList,
        method: "get",
        data: {},
    });
}
// 获取部门人员
export function getDepartmentPersonnel(params) {
    return axios({
        url: "/cloud/employee/page",
        method: "post",
        data: params,
    });
}
// 获取学生
export function getSchoolRollPersonnel(params) {
    return axios({
        url: "/cloud/student/search",
        method: "post",
        data: params,
    });
}
export function getSchoolRollStudentPage(params) {
    return axios({
        url: "/cloud/student/page",
        method: "post",
        data: params,
    });
}
// 自定义组
export function getCustomGroupTree(params) {
    return axios({
        url: api.getFaceCustomGroup,
        method: "get",
        data: params,
    });
}

export function createFaceGroup(params) {
    return axios({
        url: api.createFaceGroup,
        method: "post",
        data: params,
    });
}
export function updateFaceGroup(params) {
    return axios({
        url: api.updateFaceGroup,
        method: "post",
        data: params,
    });
}

export function removeFaceGroup(params) {
    return axios({
        url: api.removeFaceGroup,
        method: "post",
        data: params,
    });
}
export function sendFaceMessage(params) {
    return axios({
        url: api.sendFaceMessage,
        method: "post",
        data: params,
    });
}

export function addCustomPeople(params) {
    return axios({
        url: api.addCustomPeople,
        method: "post",
        data: params,
    });
}
export function deleteCustomPeople(params) {
    return axios({
        url: api.deleteCustomPeople,
        method: "post",
        data: params,
    });
}

export function createFace(params, cb) {
    return axios({
        url: api.createFace,
        method: "post",
        data: params,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        onUploadProgress(progressEvent) {
            const percentCompleted = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
            );
            cb && cb(percentCompleted);
        },
    });
}

export function getNoCollectFaceNumber(params) {
    return axios({
        url: api.getNoCollectFaceNumber,
        method: "post",
        data: params,
    });
}
// 班牌场地
export function getCustomMethod(url, method, params) {
    return axios({
        url,
        method,
        data: params,
    });
}

// 班牌班级
export function getSchoolRollBrand(url, method, params) {
    return axios({
        url,
        method,
        data: params,
    });
}

// 请假通行配置更新
export function updateLeavePassConfig(data) {
    return axios({
        url: "/cloud/leavePassConfig/update",
        method: "post",
        data,
    });
}

// 请假通行配置获取
export function getLeavePassConfig(params) {
    return axios({
        url: "/cloud/leavePassConfig/get",
        method: "get",
        params,
    });
}
