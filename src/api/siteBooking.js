/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-05-05 13:50:06
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-05-10 16:21:13
 */
import request from "@/utils/request"
import api from "./index.ts"

// 场地预约类型分页
export function siteBookingType(data) {
    return request({
        url: api.siteBookingType,
        method: "post",
        data
    })
}
// 新增场地预约类型
export function siteTypeCreate(data) {
    return request({
        url: api.siteTypeCreate,
        method: "post",
        data
    })
}
// 场地分页
export function sitePages(data) {
    return request({
        url: api.sitePages,
        method: "post",
        data
    })
}
// 编辑场地
export function editSiteType(data) {
    return request({
        url: api.editSiteType,
        method: "post",
        data
    })
}
// 新增场地预约申请
export function createApply(data) {
    return request({
        url: api.createApply,
        method: "post",
        data
    })
}
// 我参与的预约分页
export function myParticipatePage(data) {
    return request({
        url: api.myParticipatePage,
        method: "post",
        data
    })
}
// 我申请的预约分页
export function myBookingPage(data) {
    return request({
        url: api.myBookingPage,
        method: "post",
        data
    })
}
// 编辑（审核）场地预约申请
export function bookingUpdate(data) {
    return request({
        url: api.bookingUpdate,
        method: "post",
        data
    })
}
// 预约记录
export function bookingRecordPage(data) {
    return request({
        url: api.bookingRecordPage,
        method: "post",
        data
    })
}
// 场地占用时间
export function dateOccupyList(data) {
    return request({
        url: api.dateOccupyList,
        method: "post",
        data
    })
}
// 预约审核分页
export function bookingApprovalPage(data) {
    return request({
        url: api.bookingApprovalPage,
        method: "post",
        data
    })
}
// 场地闲置分页
export function freeSite(data) {
    return request({
        url: api.freeSite,
        method: "post",
        data
    })
}
// 签到表分页
export function bookingSignPage(data) {
    return request({
        url: api.bookingSignPage,
        method: "post",
        data
    })
}
// 预约详情
export function bookingDetail(data) {
    return request({
        url: api.bookingDetail,
        method: "get",
        params: data
    })
}
// 场地预约类型详情
export function siteTypeDetail(data) {
    return request({
        url: api.siteTypeDetail,
        method: "get",
        params: data
    })
}
// 判断场地类型是否封禁
export function checkApproved(data) {
    return request({
        url: api.checkApproved,
        method: "get",
        params: data
    })
}
export function getfreeSiteInfo(data) {
    return request({
        url: api.getfreeSiteInfo,
        method: "get",
        params: data
    })
}
// del场地预约类型
export function delSiteType(data) {
    return request({
        url: api.delSiteType,
        method: "get",
        params: data
    })
}
// 获取场地类型
export function siteTypeList(data) {
    return request({
        url: api.siteTypeList,
        method: "get",
        params: data
    })
}
// 获取建筑场地
export function buildingSiteList(data) {
    return request({
        url: api.buildingSiteList,
        method: "get",
        params: data
    })
}
// 获取地预约类型列表及预约数量
export function getBookingSiteNums(data) {
    return request({
        url: api.getBookingSiteNums,
        method: "get",
        params: data
    })
}
// 获取地预约类型列表及预约数量
export function getYesBookingSiteNum(data) {
    return request({
        url: api.getYesBookingSiteNum,
        method: "get",
        params: data
    })
}
// --------------------------------------------------------------------

// 班级信息
export function classInfo(data) {
    return request({
        url: api.classInfo,
        method: "get",
        params: data
    })
}

// 班级口号列表
export function getSloganList(data) {
    return request({
        url: api.getSloganList,
        method: "post",
        data
    })
}

// 上传班级头像
export function setClassesIcon(data) {
    return request({
        url: api.setClassesIcon,
        method: "post",
        data
    })
}

// 上传文件
export function getFileUpload(data) {
    return request({
        url: api.fileUpload,
        method: "post",
        data
    })
}

// 删除班级口号
export function delSloganList(data) {
    return request({
        url: api.delSloganList,
        method: "post",
        data
    })
}

// 设置班级口号
export function setSloganList(data) {
    return request({
        url: api.setSloganList,
        method: "post",
        data
    })
}

// 增加班牌
export function createClassSign(data) {
    return request({
        url: api.createClassSign,
        method: "post",
        data
    })
}

// 设置班牌
export function updateClassSign(data) {
    return request({
        url: api.updateClassSign,
        method: "post",
        data
    })
}

// 班牌信息
export function getBrandInfo(data) {
    return request({
        url: api.getBrandInfo,
        method: "get",
        params: data
    })
}

// 批量设置定时开关机
export function batchSet(data) {
    return request({
        url: api.batchSet,
        method: "post",
        data
    })
}

// 上课模式
export function classModeUpdate(data) {
    return request({
        url: api.classModeUpdate,
        method: "post",
        data
    })
}

// 批量远程重启
export function longRestart(data) {
    return request({
        url: api.longRestart,
        method: "post",
        data
    })
}

// 批量远程关机
export function longclose(data) {
    return request({
        url: api.longclose,
        method: "post",
        data
    })
}

// 班牌列表
export function ClassSignList(data) {
    return request({
        url: api.ClassSignList,
        method: "post",
        data
    })
}

// 未创建班牌账号的场地
export function getUnSite(data) {
    return request({
        url: api.getUnSite,
        method: "post",
        data
    })
}

// 场地班牌列表
export function getSite(data) {
    return request({
        url: api.getSite,
        method: "post",
        data
    })
}

// 获取绑定场地的班牌账号
export function getBindingSiteTree(data) {
    return request({
        url: api.getBindingSiteTree,
        method: "post",
        data
    })
}

// 提醒列表
export function getRemindList(data) {
    return request({
        url: api.getRemindList,
        method: "post",
        data
    })
}

// 学生作品-列表
export function getStudentworkPage(data) {
    return request({
        url: api.studentworkPage,
        method: "post",
        data
    })
}
// 学生作品-删除
export function studentworkDelete(data) {
    return request({
        url: api.studentworkDelete,
        method: "post",
        data
    })
}

// 学生作品-创建
export function studentworkCreate(data) {
    return request({
        url: api.studentworkCreate,
        method: "post",
        data
    })
}

// 创建提醒
export function createClassRemind(data) {
    return request({
        url: api.createClassRemind,
        method: "post",
        data
    })
}

// 删除提醒
export function delClassRemind(data) {
    return request({
        url: api.delClassRemind,
        method: "post",
        data
    })
}

// 提醒详情
export function classRemindInfo(data) {
    return request({
        url: api.classRemindInfo,
        method: "get",
        params: data
    })
}

// 班牌布局首页模板列表
export function getLayoutList(data) {
    return request({
        url: api.getLayoutList,
        method: "post",
        data
    })
}

// 班牌布局添加编辑页模块列表
export function getLayoutModuleList(data) {
    return request({
        url: api.getLayoutModuleList,
        method: "post",
        data
    })
}

// 班牌布局模板创建
export function creatLayout(data) {
    return request({
        url: api.creatLayout,
        method: "post",
        data
    })
}

// 班牌布局添加编辑页分类列表
export function getClassifyList(data) {
    return request({
        url: api.getClassifyList,
        method: "post",
        data
    })
}

// 班牌布局编辑页获取详情
export function classLayoutInfo(id) {
    return request({
        url: `${api.classLayoutInfo}/${id}`,
        method: "get"
    })
}

// 班牌布局模块删除接口
export function delClassTemplate(data) {
    return request({
        url: api.delClassTemplate,
        method: "post",
        data
    })
}

// 班牌布局模块更新接口
export function updateClassTemplate(data) {
    return request({
        url: api.updateClassTemplate,
        method: "post",
        data
    })
}
// 模板 - 发布到班牌
export function publishClassTemplate(data) {
    return request({
        url: api.publishClassTemplate,
        method: "post",
        data
    })
}
