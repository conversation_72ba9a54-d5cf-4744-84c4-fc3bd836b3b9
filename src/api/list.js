import request from "@/utils/request";
import api from "./index.ts";

// 功能权限
export function updateAppGroupInfo(data) {
  return request({
    url: api.updateAppGroup,
    method: "post",
    data,
  });
}

// 选择角色接口
export function roleTypeListInfo(params) {
  return request({
    url: api.roleTypeList,
    method: "get",
    params,
  });
}
// 删除权限组
export function deleteAppGroupInfo(data) {
  return request({
    url: api.deleteAppGroup,
    method: "post",
    data,
  });
}
export function appCenterUpdateInfo(data) {
  return request({
    url: api.appCenterUpdateById,
    method: "post",
    data,
  });
}

// 应用详情接口
export function appCenterGeiInfo(params) {
  return request({
    url: api.appCenterGei,
    method: "get",
    params,
  });
}
// 可见范围接口
export function updateDataScopeInfo(data) {
  return request({
    url: api.updateDataScope,
    method: "post",
    data,
  });
}

// 权限组列表接口
export function appGroupListInfo(data) {
  return request({
    url: api.appGroupList,
    method: "post",
    data,
  });
}
// 功能权限
export function createAppGroupInfo(data) {
  return request({
    url: api.createAppGroup,
    method: "post",
    data,
  });
}
// 回显权限组接口
export function seeAppGroupInfo(params) {
  return request({
    url: api.seeAppGroup,
    method: "get",
    params,
  });
}
// 回显权限组接口
export function getMenuListInfo(data) {
  return request({
    url: '/system/menu/getMenuList',
    method: "post",
    data,
  });
}

// 获取角色管理员列表
export function getListAppUserRole(params) {
  return request({
    url: '/cloud/shareApp/listAppUserRole',
    method: "get",
    params,
  });
}

// 编辑角色管理员
export function shareAppUpdateAppUserRole(data) {
  return request({
    url: '/cloud/shareApp/updateAppUserRole',
    method: "post",
    data,
  });
}

// 获取角色管理员列表
export function shareAppRefreshAppUserRole(params) {
  return request({
    url: '/cloud/shareApp/refreshAppUserRole',
    method: "get",
    params,
  });
}