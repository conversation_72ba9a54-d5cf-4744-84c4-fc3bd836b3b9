// 巡查
import request from "@/utils/request";
import api from "./index.ts";

/**
 * 新增分类
 * @param {*} data
 * @returns
 */
export function projectTypeCreate(data) {
    return request({
        url: api.projectTypeCreate,
        method: "post",
        data,
    });
}

/**
 * 编辑分类
 * @param {*} data
 * @returns
 */
export function projectTypeUpdate(data) {
    return request({
        url: api.projectTypeUpdate,
        method: "post",
        data,
    });
}

/**
 * 删除分类
 * @param {*} data
 * @returns
 */
export function deleteSort(data) {
    return request({
        url: api.projectTypeDelete,
        method: "post",
        data,
    });
}

/**
 * 删除分类
 * @param {*} data
 * @returns
 */
export function createProject(data) {
    return request({
        url: "/cloud/patrol/project/create",
        method: "post",
        data,
    });
}

/**
 * 设置场地
 * @param {*} data
 * @returns
 */
export function setSit(data) {
    return request({
        url: "/cloud/patrol/project/site/setting",
        method: "post",
        data,
    });
}

// 查询计划分页
export function projectPage(data) {
    return request({
        url: api.projectPage,
        method: "post",
        data,
    });
}

// 启用停用计划
export function projectStatusStop(data) {
    return request({
        url: api.projectStatusStop,
        method: "post",
        data,
    });
}
// 删除任务
export function projectDeleteTask(data) {
    return request({
        url: api.projectDeleteTask,
        method: "post",
        data,
    });
}
// 查看计划详情
export function projectPlanDetails(params) {
    return request({
        url: api.projectPlanDetails,
        method: "get",
        params,
    });
}

// 获取任务信息
export function projectTaskInfo(params) {
    return request({
        url: api.projectTaskInfo,
        method: "get",
        params,
    });
}

// 设置反馈表单
export function projectFormSetting(data) {
    return request({
        url: api.projectFormSetting,
        method: "post",
        data,
    });
}

// 删除计划
export function projectDelete(data) {
    return request({
        url: api.projectDelete,
        method: "post",
        data,
    });
}
// 编辑计划
export function projectUpdate(data) {
    return request({
        url: api.projectUpdate,
        method: "post",
        data,
    });
}

// 执行人员
export function projectUserSetting(data) {
    return request({
        url: api.projectUserSetting,
        method: "post",
        data,
    });
}

// 设置场地
export function projectSiteSetting(data) {
    return request({
        url: api.projectSiteSetting,
        method: "post",
        data,
    });
}

// 分页巡查记录
export function recordPage(data) {
    return request({
        url: api.recordPage,
        method: "post",
        data,
    });
}

// 分页巡查任务
export function projectTaskPage(data) {
    return request({
        url: api.projectTaskPage,
        method: "post",
        data,
    });
}

// 分类列表
export function projectTypeList(params) {
    return request({
        url: api.projectTypeList,
        method: "get",
        params,
    });
}
// 任务作废
export function projectCancelTask(data) {
    return request({
        url: api.projectCancelTask,
        method: "post",
        data,
    });
}

// 获取巡查路由权限
export function patrolPermission(data) {
    return request({
        url: api.patrolPermission,
        method: "post",
        data,
    });
}

// 查询学校是否开通默认巡查应用
export function queryDefaultApp(data) {
    return request({
        url: api.queryDefaultApp,
        method: "post",
        data,
    });
}

// 学校开通默认巡查应用
export function initDefaultApp(data) {
    return request({
        url: api.initDefaultApp,
        method: "post",
        data,
    });
}
/**
 * 视频上墙
 **/
// 获取场地巡查下的VMS通道
export function getPatrolSiteDevice(data) {
    return request({
        url: "/cloud/patrol/project/listPatrolProjectSiteDevice",
        method: "post",
        data,
    });
}


// 查询布局列表
export function getPatrolLayoutList(data) {
    return request({
        url: "/cloud/patrol/layout/list",
        method: "post",
        data,
    });
}
// 保存布局
export function getPatrolLayoutSave(data) {
    return request({
        url: "/cloud/patrol/layout/batchSave",
        // url: "/cloud/patrol/layout/save",
        method: "post",
        data,
    });
}

// 删除
export function getPatrolLayoutDelete(data) {
    return request({
        url: "/cloud/patrol/layout/delete",
        method: "post",
        data,
    });
}
