import request from '@/utils/request'

// 考试类型列表
export function exalmTypeList(params) {
    return request({
        url: '/cloud/score/exam/typeList',
        method: 'get',
        params
    })
}
// 考试类型列表
export function exalmSubjectList(params) {
    return request({
        url: '/cloud/score/exam/subjectList',
        method: 'get',
        params
    })
}

// 考试管理-分页
export function exalmPage(data) {
    return request({
        url: '/cloud/score/exam/page',
        method: 'post',
        data
    })
}

// 考试管理-添加
export function exalmCreate(data) {
    return request({
        url: '/cloud/score/exam/create',
        method: 'post',
        data
    })
}

// 考试管理-修改
export function exalmUpdate(data) {
    return request({
        url: '/cloud/score/exam/update',
        method: 'post',
        data
    })
}

// 考试管理-删除
export function exalmDelete(data) {
    return request({
        url: '/cloud/score/exam/delete',
        method: 'post',
        data
    })
}

// 通知设置-获取
export function getExamScoreInform(params) {
    return request({
        url: '/cloud/score/exam/getScoreInform',
        method: 'get',
        params
    })
}

// 通知设置-修改
export function updateExamScoreInform(data) {
    return request({
        url: '/cloud/score/exam/updateScoreInform',
        method: 'post',
        data
    })
}

// 考试管理-导入考试
export function examImportScore(data) {
    return request({
        url: '/cloud/score/exam/importScore',
        method: 'post',
        data
    })
}

// 考试管理-下载模版
export function examDowntScore(data) {
    return request({
        url: '/cloud/score/exam/importScore',
        method: 'post',
        data
    })
}

// 考试管理-获取详情
export function examDetails(params) {
    return request({
        url: '/cloud/score/exam/getInfo',
        method: 'get',
        params
    })
}

// 成绩管理-列表
export function examDetailsScoreLists(data) {
    return request({
        url: '/cloud/score/exam/getScoreList',
        method: 'post',
        data
    })
}

// 成绩管理-分页列表
export function examDetailsScorePage(data) {
    return request({
        url: '/cloud/score/exam/scorePage',
        method: 'post',
        data
    })
}

// 成绩管理-班级列表
export function examClassesList(params) {
    return request({
        url: '/cloud/score/exam/getClassesList',
        method: 'get',
        params
    })
}

// 成绩管理-发送通知
export function examSendInform(params) {
    return request({
        url: '/cloud/score/exam/sendInform',
        method: 'get',
        params
    })
}
// 获取导入模板
export function examTemplate(data) {
    return request({
        url: '/cloud/common/export/template',
        method: 'post',
        data
    })
}

// 获取出模板
export async function examExport(url, fileName) {
    const params = {
    }
    request({
        url,
        method: 'get',
        responseType: 'arraybuffer',
        params
    }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })
        )
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
            'download',
            fileName ? `${fileName}.xlsx` : 'excel.xlsx'
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    })
}
