import request from '@/utils/request'
import api from './index.ts'

export function albumClassifyList(data) {
    return request({
        url: api.albumClassifyList,
        method: 'post',
        data
    })
}

export function albumClassifyCreate(data) {
    return request({
        url: api.albumClassifyCreate,
        method: 'post',
        data
    })
}

export function albumClassifyUpdate(data) {
    return request({
        url: api.albumClassifyUpdate,
        method: 'post',
        data
    })
}

export function albumPage(data) {
    return request({
        url: api.albumPage,
        method: 'post',
        data
    })
}

export function albumClassifyDel(data) {
    return request({
        url: api.albumClassifyDel,
        method: 'post',
        data
    })
}

export function albumCreate(data) {
    return request({
        url: api.albumCreate,
        method: 'post',
        data
    })
}

export function albumDelete(data) {
    return request({
        url: api.albumDelete,
        method: 'post',
        data
    })
}

export function updateAlbumClassify(data) {
    return request({
        url: api.updateAlbumClassify,
        method: 'post',
        data
    })
}

export function uploadFiles(data) {
    return request({
        url: api.fileUpload,
        method: 'post',
        data
    })
}

export function albumUpdateIsShow(data) {
    return request({
        url: api.albumUpdateIsShow,
        method: 'post',
        data
    })
}

// 设为首页视频
export function updateIsHome(data) {
    return request({
        url: api.updateIsHome,
        method: 'post',
        data
    })
}

// 批量删除
export function batchDeleteIds(data) {
    return request({
        url: api.deleteIds,
        method: 'post',
        data
    })
}
