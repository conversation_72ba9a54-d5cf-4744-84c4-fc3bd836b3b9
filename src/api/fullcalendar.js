// fullcalendar
import request from '@/utils/request'
import api from './index.ts'

// 分类列表
export function createSchedule(data) {
    return request({
        url: api.createSchedule,
        method: 'post',
        data
    })
}

// 复制并创建
export function copySchedule(params) {
    return request({
        url: `${api.copySchedule}/${params}`,
        method: 'get'
    })
}
// 删除
export function deleteSchedule(params) {
    return request({
        url: `${api.deleteSchedule}/${params}`,
        method: 'get'
    })
}

// 置顶
export function changeScheduleState(data) {
    return request({
        url: api.changeScheduleState,
        method: 'post',
        data
    })
}
// 日程详情
export function scheduleDetail(params) {
    return request({
        url: `${api.getScheduleDetail}/${params}`,
        method: 'get'
    })
}
// 场地
export function buildingList(params) {
    return request({
        url: api.buildingLists,
        method: 'get',
        params
    })
}

// 提交设置日程
export function createScheduleSetting(data) {
    return request({
        url: api.createScheduleSetting,
        method: 'post',
        data
    })
}

// 查询个人设置
export function scheduleSetting(params) {
    return request({
        url: api.scheduleSetting,
        method: 'get',
        params
    })
}
// 删除标签
export function scheduleDdeleteLabel(data) {
    return request({
        url: `${api.scheduleDdeleteLabel}/${data}`,
        method: 'post'
    })
}
// 获取标签列表
export function scheduleLabel(params) {
    return request({
        url: api.scheduleLabel,
        method: 'get',
        params
    })
}

// 添加标签
export function scheduleLCreateLabelBatch(data) {
    return request({
        url: api.scheduleLCreateLabelBatch,
        method: 'post',
        data
    })
}

// 日程列表
export function scheduleList(data) {
    return request({
        url: api.scheduleList,
        method: 'post',
        data
    })
}
