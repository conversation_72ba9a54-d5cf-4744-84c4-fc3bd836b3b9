
import request from '@/utils/request'
import api from './index.ts'
// 添加荣誉墙
export function addHonorWall(data) {
    return request({
        url: api.addHonorWall,
        method: 'post',
        data
    })
}
// 编辑荣誉墙
export function upHonorWall(data) {
    return request({
        url: api.upHonorWall,
        method: 'post',
        data
    })
}
// 荣誉墙列表
export function honorList(data) {
    return request({
        url: api.honorList,
        method: 'post',
        data
    })
}
// 删除荣誉墙
export function delHonorWall(data) {
    return request({
        url: api.delHonorWall,
        method: 'get',
        params: data
    })
}
// 荣誉墙详情
export function honorWallInfo(data) {
    return request({
        url: api.honorWallInfo,
        method: 'get',
        params: data
    })
}

// 荣誉墙自定义图片上传
export function getFileUpload(data) {
    return request({
        url: api.fileUpload,
        method: 'post',
        data
    })
}
