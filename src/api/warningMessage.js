import request from "@/utils/request";
import api from "./index.ts";

export function managerPermission(data) {
    return request({
        url: api.managerPermission,
        method: "get",
        params: data,
    });
}

export function managerUpdate(data) {
    return request({
        url: api.managerUpdate,
        method: "post",
        data,
    });
}

export function managerList(data) {
    return request({
        url: api.managerList,
        method: "get",
        params: data,
    });
}

export function managerPage(data) {
    return request({
        url: api.managerPage,
        method: "post",
        data,
    });
}

