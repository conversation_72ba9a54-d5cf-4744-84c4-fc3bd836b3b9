// 外部人员 outsiders
import request from '@/utils/request'
import api from './index.ts'
// 外部人员组列表带成员数量
export function externalGroupNumberList(params) {
    return request({
        url: api.externalGroupNumberList,
        method: 'get',
        params
    })
}
// 添加外部人员组
export function createExternalGroup(data) {
    return request({
        url: api.createExternalGroup,
        method: 'post',
        data
    })
}
// 编辑外部人员组
export function updateExternalGroup(data) {
    return request({
        url: api.updateExternalGroup,
        method: 'post',
        data
    })
}

// 删除外部人员组
export function deleteExternalGroup(data) {
    return request({
        url: api.deleteExternalGroup,
        method: 'post',
        data
    })
}

// 外部人员列表
export function externalPage(data) {
    return request({
        url: api.externalPage,
        method: 'post',
        data
    })
}

// 添加外部人员
export function addExternalPersonnel(data) {
    return request({
        url: api.addExternalPersonnel,
        method: 'post',
        data
    })
}

// 删除外部人员
export function externalDeleteExternal(data) {
    return request({
        url: api.externalDeleteExternal,
        method: 'post',
        data
    })
}

// 编辑外部人员
export function updateExternalExternal(data) {
    return request({
        url: api.updateExternalExternal,
        method: 'post',
        data
    })
}

// 查询外部人员详情
export function getExternalDetail(params) {
    return request({
        url: api.getExternalDetail,
        method: 'get',
        params
    })
}

// 批量添加外部人员
export function batchAddExternalPersonnel(data) {
    return request({
        url: api.batchAddExternalPersonnel,
        method: 'post',
        data
    })
}

// 外部人员搜索
export function externalSearch(data) {
    return request({
        url: api.externalSearch,
        method: 'post',
        data
    })
}
