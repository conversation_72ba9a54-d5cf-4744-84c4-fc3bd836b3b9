import request from "@/utils/request";
import api from "./index.ts";
// 分页开门记录
export function getOpenDoorPage(data) {
    return request({
        url: api.openDoorPage,
        method: "post",
        data,
    });
}

// 新增开门记录
export function getOpenDoorCreate(data) {
    return request({
        url: api.openDoorCreate,
        method: "post",
        data,
    });
}
// 获取开门时间设置
export function getOpenTime(params) {
    return request({
        url: "/cloud/timetableConfig/get",
        method: "get",
        params,
    });
}

// 更新开门时间设置
export function postOpenTime(data) {
    return request({
        url: "/cloud/timetableConfig/update",
        method: "post",
        data,
    });
}


// 开门设置导出
export function openDoorExport(data) {
    return request({
        url: "/brand/openDoor/records/export",
        responseType: "blob",
        method: "post",
        data,
    });
}
