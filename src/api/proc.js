/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-04-20 20:14:46
 * @LastEditors: jingrou
 * @LastEditTime: 2022-11-02 15:13:05
 */
import request from '@/utils/request'
import api from './index.ts'

export function procGrouList() {
    return request({
        url: api.procGrouList,
        method: 'get'
    })
}

export function procPage(data) {
    return request({
        url: api.procPage,
        method: 'post',
        data
    })
}

export function procUpdate(data) {
    return request({
        url: api.procUpdate,
        method: 'post',
        data
    })
}

export function procGetInfo(params) {
    return request({
        url: api.procGetInfo,
        method: 'get',
        params
    })
}

export function procUserAppr(data) {
    return request({
        url: api.procUserAppr,
        method: 'post',
        data
    })
}

export function procUpdateStatus(data) {
    return request({
        url: api.procUpdateStatus,
        method: 'post',
        data
    })
}

export function procUserApprGetInfo(params) {
    return request({
        url: api.procUserApprGetInfo,
        method: 'get',
        params
    })
}

export function selectProcNameList() {
    return request({
        url: api.selectProcNameList,
        method: 'post'
    })
}

export function exportData(url, data, fileName) {
    request({
        url,
        method: 'post',
        responseType: 'arraybuffer',
        data
    }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })
        )
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
            'download',
            fileName ? `${fileName}.xlsx` : 'excel.xlsx'
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    })
}
